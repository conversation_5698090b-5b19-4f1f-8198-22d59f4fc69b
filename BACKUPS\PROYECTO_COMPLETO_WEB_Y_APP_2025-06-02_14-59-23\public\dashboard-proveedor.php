<?php
session_start();
require_once 'db_config.php';

// Verificar si el usuario está logueado y es proveedor
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'proveedor_repuestos') {
    header('Location: login-dinamico.php');
    exit();
}

$user_id = $_SESSION['user_id'];
$user_name = $_SESSION['user_name'] ?? 'Proveedor';

// Obtener estadísticas del proveedor
try {
    // Contar pedidos recibidos
    $stmt = $pdo->prepare("SELECT COUNT(*) as total_pedidos FROM pedidos WHERE proveedor_id = ?");
    $stmt->execute([$user_id]);
    $total_pedidos = $stmt->fetch()['total_pedidos'] ?? 0;

    // Contar productos en inventario
    $stmt = $pdo->prepare("SELECT COUNT(*) as total_productos FROM repuestos WHERE supplier_id = ?");
    $stmt->execute([$user_id]);
    $total_productos = $stmt->fetch()['total_productos'] ?? 0;

    // Calcular ventas del mes
    $stmt = $pdo->prepare("
        SELECT COALESCE(SUM(total), 0) as ventas_mes 
        FROM pedidos 
        WHERE proveedor_id = ? 
        AND MONTH(fecha_pedido) = MONTH(CURRENT_DATE()) 
        AND YEAR(fecha_pedido) = YEAR(CURRENT_DATE())
        AND estado = 'entregado'
    ");
    $stmt->execute([$user_id]);
    $ventas_mes = $stmt->fetch()['ventas_mes'] ?? 0;

    // Obtener pedidos recientes
    $stmt = $pdo->prepare("
        SELECT p.*, u.nombre as cliente_nombre, u.telefono as cliente_telefono
        FROM pedidos p
        LEFT JOIN users u ON p.user_id = u.id
        WHERE p.proveedor_id = ?
        ORDER BY p.fecha_pedido DESC
        LIMIT 5
    ");
    $stmt->execute([$user_id]);
    $pedidos_recientes = $stmt->fetchAll();

} catch (PDOException $e) {
    $error_message = "Error al cargar datos: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Proveedor - RepuMovil</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            min-height: 100vh;
        }

        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .logo h1 {
            color: white;
            font-size: 1.8rem;
            font-weight: 900;
        }

        .logo .repu { color: #FFE082; }
        .logo .movil { color: #E8F5E8; }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
            color: white;
        }

        .proveedor-badge {
            background: white;
            color: #4CAF50;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .welcome-section {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .welcome-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .welcome-subtitle {
            color: #666;
            font-size: 1.1rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #4CAF50;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }

        .functions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .function-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
            cursor: pointer;
            border-left: 5px solid #4CAF50;
        }

        .function-card:hover {
            transform: translateY(-5px);
        }

        .function-card.primary {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            border-left: 5px solid white;
        }

        .function-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }

        .function-title {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .function-description {
            font-size: 0.9rem;
            opacity: 0.8;
            line-height: 1.4;
        }

        .recent-orders {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .section-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .order-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid #eee;
            transition: background 0.3s ease;
        }

        .order-item:hover {
            background: #f8f9fa;
        }

        .order-item:last-child {
            border-bottom: none;
        }

        .order-info h4 {
            color: #333;
            margin-bottom: 0.3rem;
        }

        .order-info p {
            color: #666;
            font-size: 0.9rem;
        }

        .order-status {
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
            text-transform: uppercase;
        }

        .status-nuevo { background: #E3F2FD; color: #1976D2; }
        .status-preparando { background: #FFF3E0; color: #F57C00; }
        .status-listo { background: #E8F5E8; color: #388E3C; }
        .status-entregado { background: #F3E5F5; color: #7B1FA2; }

        .logout-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 2rem;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #000;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .header {
                padding: 1rem;
                flex-direction: column;
                gap: 1rem;
            }
            
            .stats-grid,
            .functions-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="logo">
            <i class="fas fa-store" style="font-size: 2rem; color: #FFE082;"></i>
            <h1><span class="repu">Repu</span><span class="movil">Movil</span></h1>
        </div>
        <div class="user-info">
            <span class="proveedor-badge">PROVEEDOR</span>
            <span>Hola, <?php echo htmlspecialchars($user_name); ?></span>
            <button class="logout-btn" onclick="location.href='logout.php'">
                <i class="fas fa-sign-out-alt"></i> Salir
            </button>
        </div>
    </div>

    <div class="container">
        <!-- Welcome Section -->
        <div class="welcome-section">
            <h2 class="welcome-title">Panel de Proveedor</h2>
            <p class="welcome-subtitle">
                Gestiona tu inventario, pedidos y entregas desde un solo lugar
            </p>
        </div>

        <!-- Stats Grid -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">📋</div>
                <div class="stat-number"><?php echo $total_pedidos; ?></div>
                <div class="stat-label">Pedidos Recibidos</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">📦</div>
                <div class="stat-number"><?php echo $total_productos; ?></div>
                <div class="stat-label">Productos en Stock</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">💰</div>
                <div class="stat-number">$<?php echo number_format($ventas_mes, 0, ',', '.'); ?></div>
                <div class="stat-label">Ventas del Mes</div>
            </div>
        </div>

        <!-- Functions Grid -->
        <div class="functions-grid">
            <!-- Pedidos Recibidos -->
            <div class="function-card primary" onclick="openModal('pedidos')">
                <div class="function-icon">📋</div>
                <h3 class="function-title">🚀 Gestión de Pedidos</h3>
                <p class="function-description">
                    <strong>¡Sistema completo disponible!</strong><br>
                    Estados • Repartidores • Tracking • Tiempo real
                </p>
            </div>

            <!-- Mi Inventario -->
            <div class="function-card" onclick="location.href='inventario-proveedor.php'">
                <div class="function-icon">📦</div>
                <h3 class="function-title">Mi Inventario</h3>
                <p class="function-description">
                    Administra tu stock de repuestos. Agrega, edita y controla el inventario.
                </p>
            </div>

            <!-- Mis Clientes -->
            <div class="function-card" onclick="openModal('clientes')">
                <div class="function-icon">👥</div>
                <h3 class="function-title">Mis Clientes</h3>
                <p class="function-description">
                    Lista de talleres y mecánicos que compran tus productos regularmente.
                </p>
            </div>

            <!-- Estadísticas -->
            <div class="function-card" onclick="openModal('estadisticas')">
                <div class="function-icon">📊</div>
                <h3 class="function-title">Estadísticas</h3>
                <p class="function-description">
                    Reportes de ventas, productos más vendidos y análisis de rendimiento.
                </p>
            </div>

            <!-- Gestión de Entregas -->
            <div class="function-card" onclick="openModal('entregas')">
                <div class="function-icon">🚚</div>
                <h3 class="function-title">Gestión de Entregas</h3>
                <p class="function-description">
                    Coordina las entregas a clientes y gestiona zonas de cobertura.
                </p>
            </div>

            <!-- Configuración -->
            <div class="function-card" onclick="openModal('configuracion')">
                <div class="function-icon">⚙️</div>
                <h3 class="function-title">Configuración</h3>
                <p class="function-description">
                    Ajustes del negocio, horarios, métodos de pago y preferencias.
                </p>
            </div>
        </div>

        <!-- Recent Orders -->
        <div class="recent-orders">
            <h3 class="section-title">
                <i class="fas fa-clock"></i>
                Pedidos Recientes
            </h3>
            
            <?php if (empty($pedidos_recientes)): ?>
                <p style="text-align: center; color: #666; padding: 2rem;">
                    No hay pedidos recientes
                </p>
            <?php else: ?>
                <?php foreach ($pedidos_recientes as $pedido): ?>
                    <div class="order-item">
                        <div class="order-info">
                            <h4><?php echo htmlspecialchars($pedido['cliente_nombre'] ?? 'Cliente'); ?></h4>
                            <p>
                                <i class="fas fa-clock"></i> 
                                <?php echo date('d/m/Y H:i', strtotime($pedido['fecha_pedido'])); ?>
                                | 
                                <i class="fas fa-dollar-sign"></i>
                                $<?php echo number_format($pedido['total'], 0, ',', '.'); ?>
                            </p>
                        </div>
                        <div class="order-status status-<?php echo $pedido['estado']; ?>">
                            <?php echo ucfirst($pedido['estado']); ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <!-- Modal Universal -->
    <div id="universalModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <div id="modalContent">
                <!-- El contenido se carga dinámicamente -->
            </div>
        </div>
    </div>

    <script>
        function openModal(type) {
            const modal = document.getElementById('universalModal');
            const content = document.getElementById('modalContent');
            
            let modalContent = '';
            
            switch(type) {
                case 'pedidos':
                    modalContent = `
                        <h2><i class="fas fa-list"></i> 🚀 Sistema Completo de Gestión de Pedidos</h2>
                        <p style="color: #4CAF50; font-weight: bold; margin-bottom: 1rem;">¡Ya está disponible! Accede al sistema completo con todas las funcionalidades:</p>
                        <div style="background: #f8f9fa; padding: 1rem; border-radius: 8px; margin: 1rem 0;">
                            <h4 style="color: #333; margin-bottom: 0.5rem;">✅ Funcionalidades Implementadas:</h4>
                            <ul style="margin: 0.5rem 0; padding-left: 2rem; line-height: 1.6;">
                                <li><strong>📋 Listado completo</strong> de pedidos entrantes</li>
                                <li><strong>📊 Estados de pedidos:</strong> Nuevo → En Preparación → En Camino → Entregado</li>
                                <li><strong>🚚 Asignación de repartidores</strong> con información completa</li>
                                <li><strong>📍 Ubicación del repartidor</strong> en tiempo real</li>
                                <li><strong>⏱️ Tiempo estimado</strong> de entrega</li>
                                <li><strong>🔍 Filtros por estado</strong> y estadísticas</li>
                                <li><strong>📱 Contacto directo</strong> con clientes</li>
                                <li><strong>📋 Modal detallado</strong> con toda la información</li>
                            </ul>
                        </div>
                        <div style="background: linear-gradient(135deg, #4CAF50, #45a049); padding: 1rem; border-radius: 8px; color: white; text-align: center; margin: 1rem 0;">
                            <p style="margin: 0; font-style: italic;">"Cada pedido es una oportunidad de hacer crecer tu negocio"</p>
                        </div>
                        <div style="display: flex; gap: 0.5rem; margin-top: 1.5rem;">
                            <button onclick="window.location.href='proveedor-pedidos.php'"
                                    style="flex: 2; background: #4CAF50; color: white; border: none; padding: 1rem; border-radius: 8px; cursor: pointer; font-weight: bold; font-size: 1rem;">
                                🚀 Acceder a Gestión de Pedidos
                            </button>
                            <button onclick="closeModal()"
                                    style="flex: 1; background: #666; color: white; border: none; padding: 1rem; border-radius: 8px; cursor: pointer;">
                                Cerrar
                            </button>
                        </div>
                    `;
                    break;
                case 'clientes':
                    modalContent = `
                        <h2><i class="fas fa-users"></i> Mis Clientes</h2>
                        <p>Lista de clientes frecuentes:</p>
                        <div style="margin: 1rem 0;">
                            <div style="padding: 1rem; background: #f8f9fa; border-radius: 8px; margin-bottom: 0.5rem;">
                                <strong>🔧 Taller Mecánico Central</strong><br>
                                <small>15 pedidos • $45.200 total</small>
                            </div>
                            <div style="padding: 1rem; background: #f8f9fa; border-radius: 8px; margin-bottom: 0.5rem;">
                                <strong>👨‍🔧 Mecánico Juan Pérez</strong><br>
                                <small>8 pedidos • $18.700 total</small>
                            </div>
                        </div>
                    `;
                    break;
                case 'estadisticas':
                    modalContent = `
                        <h2><i class="fas fa-chart-bar"></i> Estadísticas de Ventas</h2>
                        <div style="margin: 1rem 0;">
                            <p><strong>Resumen del Mes:</strong></p>
                            <ul style="list-style: none; padding: 0;">
                                <li style="padding: 0.5rem; border-bottom: 1px solid #eee;">
                                    <strong>Ventas Totales:</strong> $<?php echo number_format($ventas_mes, 0, ',', '.'); ?>
                                </li>
                                <li style="padding: 0.5rem; border-bottom: 1px solid #eee;">
                                    <strong>Pedidos Completados:</strong> <?php echo $total_pedidos; ?>
                                </li>
                                <li style="padding: 0.5rem; border-bottom: 1px solid #eee;">
                                    <strong>Productos en Stock:</strong> <?php echo $total_productos; ?>
                                </li>
                            </ul>
                        </div>
                    `;
                    break;
                case 'entregas':
                    modalContent = `
                        <h2><i class="fas fa-truck"></i> Gestión de Entregas</h2>
                        <p>Configuración de entregas:</p>
                        <ul style="margin: 1rem 0; padding-left: 2rem;">
                            <li>Zonas de cobertura</li>
                            <li>Horarios de entrega</li>
                            <li>Costos por zona</li>
                            <li>Tiempo estimado de entrega</li>
                        </ul>
                        <p><em>Funcionalidad en desarrollo...</em></p>
                    `;
                    break;
                case 'configuracion':
                    modalContent = `
                        <h2><i class="fas fa-cog"></i> Configuración</h2>
                        <p>Ajustes del negocio:</p>
                        <ul style="margin: 1rem 0; padding-left: 2rem;">
                            <li>Datos del negocio</li>
                            <li>Horarios de atención</li>
                            <li>Métodos de pago</li>
                            <li>Zonas de entrega</li>
                            <li>Notificaciones</li>
                        </ul>
                        <p><em>Funcionalidad en desarrollo...</em></p>
                    `;
                    break;
            }
            
            content.innerHTML = modalContent;
            modal.style.display = 'block';
        }
        
        function closeModal() {
            document.getElementById('universalModal').style.display = 'none';
        }
        
        // Cerrar modal al hacer clic fuera
        window.onclick = function(event) {
            const modal = document.getElementById('universalModal');
            if (event.target == modal) {
                modal.style.display = 'none';
            }
        }
    </script>
</body>
</html>
