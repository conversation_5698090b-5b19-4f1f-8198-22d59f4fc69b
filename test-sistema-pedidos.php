<?php
// PASO 3: Script de testing para sistema de pedidos
// Pruebas automáticas de aceptación/rechazo de pedidos

echo "📦 TESTING COMPLETO DEL SISTEMA DE PEDIDOS\n";
echo "==========================================\n\n";

$baseUrl = 'http://localhost/mechanical-workshop/public/api/pedidos-delivery.php';
$testResults = [];

/**
 * Función para hacer requests HTTP
 */
function makeRequest($url, $method = 'GET', $data = null) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    if ($method === 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        if ($data) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        }
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'success' => !$error && $httpCode === 200,
        'http_code' => $httpCode,
        'response' => $response,
        'error' => $error
    ];
}

/**
 * Función para mostrar resultado de test
 */
function showTestResult($testName, $result, $details = '') {
    $status = $result['success'] ? '✅ PASS' : '❌ FAIL';
    echo sprintf("%-50s %s\n", $testName, $status);
    
    if (!$result['success']) {
        echo "   Error: " . ($result['error'] ?: "HTTP {$result['http_code']}") . "\n";
    }
    
    if ($details) {
        echo "   $details\n";
    }
    
    echo "\n";
    return $result['success'];
}

// TEST 1: Crear pedido de prueba
echo "1️⃣ PROBANDO CREACIÓN DE PEDIDOS\n";
echo "-------------------------------\n";

$pedidoData = [
    'action' => 'crear_pedido_test',
    'cliente' => 'Taller de Testing',
    'direccion' => 'Av. Testing 123, Centro',
    'total' => 2500,
    'prioridad' => 'alta'
];

$result1 = makeRequest($baseUrl, 'POST', $pedidoData);
$success1 = showTestResult('POST crear_pedido_test', $result1);

$pedidoCreado = null;
if ($success1) {
    $data1 = json_decode($result1['response'], true);
    if ($data1 && $data1['success']) {
        $pedidoCreado = $data1['data']['pedido_id'];
        echo "   📦 Pedido creado con ID: $pedidoCreado\n";
        echo "   👤 Cliente: {$data1['data']['cliente']}\n";
        echo "   💰 Total: \${$data1['data']['total']}\n";
        echo "   🎯 Prioridad: {$data1['data']['prioridad']}\n\n";
    }
}

// TEST 2: Obtener pedidos disponibles
echo "2️⃣ PROBANDO OBTENCIÓN DE PEDIDOS DISPONIBLES\n";
echo "--------------------------------------------\n";

$result2 = makeRequest($baseUrl . '?action=get_pedidos_disponibles&delivery_id=1&limite=5');
$success2 = showTestResult('GET get_pedidos_disponibles', $result2);

$pedidosDisponibles = [];
if ($success2) {
    $data2 = json_decode($result2['response'], true);
    if ($data2 && $data2['success']) {
        $pedidosDisponibles = $data2['data'];
        echo "   📋 Pedidos disponibles: " . count($pedidosDisponibles) . "\n";
        
        foreach ($pedidosDisponibles as $index => $pedido) {
            if ($index < 3) { // Mostrar solo los primeros 3
                echo "   📦 Pedido #{$pedido['id']}: {$pedido['cliente_nombre']} - \${$pedido['total']} ({$pedido['prioridad']})\n";
            }
        }
        echo "\n";
    }
}

// TEST 3: Aceptar pedido
echo "3️⃣ PROBANDO ACEPTACIÓN DE PEDIDO\n";
echo "--------------------------------\n";

$pedidoParaAceptar = $pedidoCreado ?: ($pedidosDisponibles[0]['id'] ?? null);

if ($pedidoParaAceptar) {
    $aceptarData = [
        'action' => 'aceptar_pedido',
        'pedido_id' => $pedidoParaAceptar,
        'delivery_id' => 1
    ];
    
    $result3 = makeRequest($baseUrl, 'POST', $aceptarData);
    $success3 = showTestResult('POST aceptar_pedido', $result3);
    
    if ($success3) {
        $data3 = json_decode($result3['response'], true);
        if ($data3 && $data3['success']) {
            echo "   ✅ Pedido #{$data3['data']['pedido_id']} aceptado por delivery {$data3['data']['delivery_id']}\n";
            echo "   📊 Nuevo estado: {$data3['data']['nuevo_estado']}\n\n";
        }
    }
} else {
    echo "   ⚠️ No hay pedidos disponibles para aceptar\n\n";
    $success3 = false;
}

// TEST 4: Crear otro pedido para rechazar
echo "4️⃣ PROBANDO RECHAZO DE PEDIDO\n";
echo "-----------------------------\n";

$pedidoRechazoData = [
    'action' => 'crear_pedido_test',
    'cliente' => 'Cliente para Rechazo',
    'direccion' => 'Av. Rechazo 456',
    'total' => 1800,
    'prioridad' => 'normal'
];

$resultRechazo = makeRequest($baseUrl, 'POST', $pedidoRechazoData);
$pedidoParaRechazar = null;

if ($resultRechazo['success']) {
    $dataRechazo = json_decode($resultRechazo['response'], true);
    if ($dataRechazo && $dataRechazo['success']) {
        $pedidoParaRechazar = $dataRechazo['data']['pedido_id'];
    }
}

if ($pedidoParaRechazar) {
    $rechazarData = [
        'action' => 'rechazar_pedido',
        'pedido_id' => $pedidoParaRechazar,
        'delivery_id' => 2,
        'motivo' => 'distancia',
        'observaciones' => 'Muy lejos para el testing'
    ];
    
    $result4 = makeRequest($baseUrl, 'POST', $rechazarData);
    $success4 = showTestResult('POST rechazar_pedido', $result4);
    
    if ($success4) {
        $data4 = json_decode($result4['response'], true);
        if ($data4 && $data4['success']) {
            echo "   ❌ Pedido #{$data4['data']['pedido_id']} rechazado por delivery {$data4['data']['delivery_id']}\n";
            echo "   🔍 Motivo: {$data4['data']['motivo']}\n";
            echo "   📊 Total rechazos: {$data4['data']['total_rechazos']}\n\n";
        }
    }
} else {
    echo "   ⚠️ No se pudo crear pedido para rechazar\n\n";
    $success4 = false;
}

// TEST 5: Actualizar estado de pedido
echo "5️⃣ PROBANDO ACTUALIZACIÓN DE ESTADO\n";
echo "-----------------------------------\n";

if ($pedidoParaAceptar) {
    $actualizarData = [
        'action' => 'actualizar_estado_pedido',
        'pedido_id' => $pedidoParaAceptar,
        'delivery_id' => 1,
        'estado' => 'en_camino',
        'observaciones' => 'Delivery en camino al cliente'
    ];
    
    $result5 = makeRequest($baseUrl, 'POST', $actualizarData);
    $success5 = showTestResult('POST actualizar_estado_pedido', $result5);
    
    if ($success5) {
        $data5 = json_decode($result5['response'], true);
        if ($data5 && $data5['success']) {
            echo "   📦 Pedido #{$pedidoParaAceptar} actualizado\n";
            echo "   📊 Estado anterior: {$data5['data']['estado_anterior']}\n";
            echo "   📊 Estado nuevo: {$data5['data']['estado_nuevo']}\n\n";
        }
    }
} else {
    echo "   ⚠️ No hay pedido aceptado para actualizar\n\n";
    $success5 = false;
}

// TEST 6: Verificar base de datos
echo "6️⃣ PROBANDO VERIFICACIÓN DE BASE DE DATOS\n";
echo "-----------------------------------------\n";

try {
    $pdo = new PDO("mysql:host=localhost;dbname=mechanical_workshop;charset=utf8", 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Contar pedidos por estado
    $stmt = $pdo->query("SELECT estado, COUNT(*) as total FROM pedidos GROUP BY estado");
    $estadosPedidos = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
    
    // Contar rechazos
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM pedido_rechazos");
    $totalRechazos = $stmt->fetchColumn();
    
    // Contar historial
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM pedido_historial");
    $totalHistorial = $stmt->fetchColumn();
    
    echo "   ✅ Conexión a base de datos: OK\n";
    echo "   📊 Estados de pedidos:\n";
    foreach ($estadosPedidos as $estado => $total) {
        echo "      - $estado: $total\n";
    }
    echo "   ❌ Total rechazos registrados: $totalRechazos\n";
    echo "   📋 Entradas en historial: $totalHistorial\n\n";
    
    $success6 = true;
    
} catch (Exception $e) {
    echo "   ❌ Error de base de datos: " . $e->getMessage() . "\n\n";
    $success6 = false;
}

// TEST 7: Crear múltiples pedidos para testing masivo
echo "7️⃣ PROBANDO CREACIÓN MASIVA DE PEDIDOS\n";
echo "--------------------------------------\n";

$clientes = [
    'Taller Norte', 'AutoService Sur', 'Mecánica Este', 
    'Repuestos Oeste', 'Taller Centro'
];

$pedidosCreados = 0;
$errores = 0;

foreach ($clientes as $cliente) {
    $pedidoMasivo = [
        'action' => 'crear_pedido_test',
        'cliente' => $cliente,
        'direccion' => "Dirección de $cliente",
        'total' => rand(500, 4000),
        'prioridad' => ['normal', 'alta', 'urgente'][rand(0, 2)]
    ];
    
    $resultMasivo = makeRequest($baseUrl, 'POST', $pedidoMasivo);
    
    if ($resultMasivo['success']) {
        $pedidosCreados++;
    } else {
        $errores++;
    }
}

echo "   📦 Pedidos creados exitosamente: $pedidosCreados/5\n";
echo "   ❌ Errores: $errores\n\n";

$success7 = $pedidosCreados > 0;

// RESUMEN FINAL
echo "🏆 RESUMEN DE PRUEBAS DEL SISTEMA DE PEDIDOS\n";
echo "============================================\n";

$totalTests = 7;
$passedTests = 0;

if ($success1) $passedTests++;
if ($success2) $passedTests++;
if ($success3) $passedTests++;
if ($success4) $passedTests++;
if ($success5) $passedTests++;
if ($success6) $passedTests++;
if ($success7) $passedTests++;

echo "✅ Pruebas exitosas: $passedTests/$totalTests\n";
echo "📊 Porcentaje de éxito: " . round(($passedTests / $totalTests) * 100, 1) . "%\n\n";

if ($passedTests === $totalTests) {
    echo "🎉 ¡TODAS LAS PRUEBAS PASARON! EL SISTEMA DE PEDIDOS ESTÁ FUNCIONANDO PERFECTAMENTE! 🔥\n\n";
    echo "✅ FUNCIONALIDADES VERIFICADAS:\n";
    echo "   📦 Creación de pedidos de prueba\n";
    echo "   📋 Obtención de pedidos disponibles\n";
    echo "   ✅ Aceptación de pedidos por deliveries\n";
    echo "   ❌ Rechazo de pedidos con motivos\n";
    echo "   📊 Actualización de estados de pedidos\n";
    echo "   🗄️ Almacenamiento en base de datos\n";
    echo "   📈 Creación masiva de pedidos\n\n";
} else {
    echo "⚠️ Algunas pruebas fallaron. Revisar configuración.\n\n";
}

echo "🚀 PRÓXIMO PASO: Probar en la app React Native\n";
echo "📱 Comando: cd RepuMovilExpo && npx expo start\n";
echo "🎯 Verificar: Dashboard delivery → Modal de pedidos\n";
echo "🌐 Panel web: http://localhost/mechanical-workshop/public/test-pedidos.php\n\n";

echo "💪 ¡SISTEMA DE ACEPTACIÓN/RECHAZO DE PEDIDOS LISTO PARA PRODUCCIÓN!\n";
?>
