<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RepuMovil - Test Notificaciones Push 🔔</title>
    <style>
        :root {
            --primary-color: #FF6B35;
            --secondary-color: #FFA500;
            --dark-color: #2c3e50;
            --light-color: #f8f9fa;
            --success-color: #28a745;
            --danger-color: #dc3545;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
            color: white;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .title {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .test-panel {
            background: white;
            color: var(--dark-color);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-weight: bold;
            margin-bottom: 8px;
            color: var(--primary-color);
        }

        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .form-textarea {
            min-height: 80px;
            resize: vertical;
        }

        .btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .btn:hover {
            background: #e55a2b;
            transform: translateY(-2px);
        }

        .btn-success {
            background: var(--success-color);
        }

        .btn-success:hover {
            background: #218838;
        }

        .result-panel {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
            backdrop-filter: blur(10px);
        }

        .result-title {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .result-content {
            background: rgba(0,0,0,0.1);
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }

        .tokens-list {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .token-item {
            background: rgba(255,255,255,0.1);
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 10px;
            font-size: 0.9rem;
        }

        .quick-tests {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🔔 Test Notificaciones Push</h1>
            <p class="subtitle">Panel de pruebas para notificaciones de RepuMovil</p>
        </div>

        <!-- Panel de tokens registrados -->
        <div class="test-panel">
            <h3>📱 Tokens Registrados</h3>
            <div id="tokens-container">
                <p>Cargando tokens...</p>
            </div>
            <button class="btn btn-success" onclick="cargarTokens()">🔄 Actualizar Tokens</button>
        </div>

        <!-- Tests rápidos -->
        <div class="test-panel">
            <h3>⚡ Tests Rápidos</h3>
            <div class="quick-tests">
                <button class="btn" onclick="enviarTestBasico()">🧪 Test Básico</button>
                <button class="btn" onclick="enviarNuevoPedido()">📦 Nuevo Pedido</button>
                <button class="btn" onclick="enviarPedidoUrgente()">⚡ Pedido Urgente</button>
                <button class="btn" onclick="enviarBonificacion()">🎁 Bonificación</button>
            </div>
        </div>

        <!-- Formulario personalizado -->
        <div class="test-panel">
            <h3>🎯 Notificación Personalizada</h3>
            <form id="notification-form">
                <div class="form-group">
                    <label class="form-label">Delivery ID:</label>
                    <select class="form-select" id="delivery-id">
                        <option value="1">Juan Pérez (ID: 1)</option>
                        <option value="2">María González (ID: 2)</option>
                        <option value="3">Carlos Rodríguez (ID: 3)</option>
                        <option value="4">Ana López (ID: 4)</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">Título:</label>
                    <input type="text" class="form-input" id="titulo" value="🚚 Notificación de Prueba">
                </div>

                <div class="form-group">
                    <label class="form-label">Mensaje:</label>
                    <textarea class="form-textarea" id="mensaje">Esta es una notificación de prueba desde el panel de administración.</textarea>
                </div>

                <div class="form-group">
                    <label class="form-label">Prioridad:</label>
                    <select class="form-select" id="prioridad">
                        <option value="normal">Normal</option>
                        <option value="alta">Alta</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">Datos Extra (JSON):</label>
                    <textarea class="form-textarea" id="datos-extra">{"tipo": "test", "timestamp": "2024-01-20T15:30:00Z"}</textarea>
                </div>

                <button type="button" class="btn" onclick="enviarNotificacionPersonalizada()">📤 Enviar Notificación</button>
            </form>
        </div>

        <!-- Panel de resultados -->
        <div id="result-panel" class="result-panel" style="display: none;">
            <div class="result-title">📋 Resultado:</div>
            <div id="result-content" class="result-content"></div>
        </div>
    </div>

    <script>
        // PASO 1: Cargar tokens registrados
        async function cargarTokens() {
            try {
                const response = await fetch('api/notifications.php?action=get_tokens');
                const result = await response.json();
                
                const container = document.getElementById('tokens-container');
                
                if (result.success && result.data.length > 0) {
                    container.innerHTML = result.data.map(token => `
                        <div class="token-item">
                            <strong>${token.nombre}</strong> (ID: ${token.delivery_id})<br>
                            <small>Plataforma: ${token.device_platform || 'N/A'}</small><br>
                            <small>Registrado: ${new Date(token.fecha_registro).toLocaleString()}</small><br>
                            <small>Token: ${token.expo_push_token.substring(0, 50)}...</small>
                        </div>
                    `).join('');
                } else {
                    container.innerHTML = '<p>No hay tokens registrados</p>';
                }
            } catch (error) {
                console.error('Error cargando tokens:', error);
                document.getElementById('tokens-container').innerHTML = '<p>Error cargando tokens</p>';
            }
        }

        // PASO 1: Enviar notificación básica de prueba
        async function enviarTestBasico() {
            await enviarNotificacion({
                delivery_id: 1,
                titulo: '🧪 Test Básico',
                mensaje: 'Esta es una notificación de prueba básica',
                prioridad: 'normal',
                data: {
                    tipo: 'test_basico',
                    timestamp: new Date().toISOString()
                }
            });
        }

        // PASO 1: Enviar notificación de nuevo pedido
        async function enviarNuevoPedido() {
            await enviarNotificacion({
                delivery_id: 1,
                titulo: '🚨 ¡NUEVO PEDIDO ASIGNADO!',
                mensaje: 'Pedido #1025 listo para recoger - $350 - 2.5km',
                prioridad: 'alta',
                data: {
                    tipo: 'nuevo_pedido',
                    pedido_id: 1025,
                    cliente: 'Taller de Prueba',
                    direccion: 'Av. Test 123',
                    ganancia: 350,
                    distancia: 2.5
                }
            });
        }

        // PASO 1: Enviar notificación de pedido urgente
        async function enviarPedidoUrgente() {
            await enviarNotificacion({
                delivery_id: 1,
                titulo: '⚡ ¡PEDIDO URGENTE!',
                mensaje: 'Cliente esperando - Bonus +$100 - 1.2km',
                prioridad: 'alta',
                data: {
                    tipo: 'pedido_urgente',
                    pedido_id: 1026,
                    cliente: 'Cliente Urgente',
                    bonus: 100,
                    distancia: 1.2
                }
            });
        }

        // PASO 1: Enviar notificación de bonificación
        async function enviarBonificacion() {
            await enviarNotificacion({
                delivery_id: 1,
                titulo: '🎁 ¡BONUS DISPONIBLE!',
                mensaje: 'Zona premium con bonificación extra +$150',
                prioridad: 'normal',
                data: {
                    tipo: 'bonificacion',
                    zona: 'Centro Premium',
                    bonus: 150
                }
            });
        }

        // PASO 1: Enviar notificación personalizada
        async function enviarNotificacionPersonalizada() {
            const deliveryId = document.getElementById('delivery-id').value;
            const titulo = document.getElementById('titulo').value;
            const mensaje = document.getElementById('mensaje').value;
            const prioridad = document.getElementById('prioridad').value;
            const datosExtra = document.getElementById('datos-extra').value;

            let data = {};
            try {
                data = JSON.parse(datosExtra);
            } catch (e) {
                mostrarResultado('❌ Error: Datos extra no son JSON válido');
                return;
            }

            await enviarNotificacion({
                delivery_id: parseInt(deliveryId),
                titulo: titulo,
                mensaje: mensaje,
                prioridad: prioridad,
                data: data
            });
        }

        // PASO 1: Función genérica para enviar notificación
        async function enviarNotificacion(payload) {
            try {
                mostrarResultado('⏳ Enviando notificación...');

                const response = await fetch('api/notifications.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'send_notification',
                        ...payload
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    mostrarResultado(`✅ Notificación enviada exitosamente!\n\n${JSON.stringify(result, null, 2)}`);
                } else {
                    mostrarResultado(`❌ Error enviando notificación:\n\n${result.message}`);
                }
            } catch (error) {
                console.error('Error:', error);
                mostrarResultado(`❌ Error de conexión:\n\n${error.message}`);
            }
        }

        // PASO 1: Mostrar resultado
        function mostrarResultado(contenido) {
            const panel = document.getElementById('result-panel');
            const content = document.getElementById('result-content');
            
            content.textContent = contenido;
            panel.style.display = 'block';
            
            // Scroll al resultado
            panel.scrollIntoView({ behavior: 'smooth' });
        }

        // PASO 1: Cargar tokens al iniciar
        document.addEventListener('DOMContentLoaded', function() {
            cargarTokens();
        });
    </script>
</body>
</html>
