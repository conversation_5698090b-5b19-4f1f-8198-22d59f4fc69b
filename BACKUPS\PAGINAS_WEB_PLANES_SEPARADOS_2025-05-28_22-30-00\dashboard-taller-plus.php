<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RepuMovil Plus - Dashboard Taller</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .navbar-custom {
            background: linear-gradient(135deg, #FF6B35 0%, #FFA500 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-size: 24px;
            font-weight: bold;
            color: white !important;
        }

        .logo-repu {
            color: white;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .logo-movil {
            color: #FFE4B5;
        }

        .plus-badge {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            color: #333;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
            margin-left: 10px;
            box-shadow: 0 2px 10px rgba(255, 215, 0, 0.3);
        }

        .welcome-section {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 25px;
            padding: 40px;
            margin: 20px 0;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            text-align: center;
            position: relative;
            overflow: hidden;
            border: 3px solid #FFD700;
        }

        .welcome-section::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 215, 0, 0.1), transparent);
            animation: shine 4s infinite;
        }

        @keyframes shine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .welcome-title {
            font-size: 36px;
            color: #333;
            margin-bottom: 15px;
            position: relative;
            z-index: 1;
        }

        .logo-plus {
            background: linear-gradient(135deg, #FF6B35, #FFA500, #FFD700);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 900;
            font-size: 40px;
            text-shadow: 0 0 30px rgba(255, 215, 0, 0.5);
            position: relative;
        }

        .plus-text {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 900;
            font-size: 28px;
            margin-left: 10px;
        }

        .welcome-subtitle {
            font-size: 18px;
            color: #666;
            position: relative;
            z-index: 1;
            font-style: italic;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }

        .dashboard-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: none;
            position: relative;
            overflow: hidden;
        }

        .dashboard-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .dashboard-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(135deg, #FF6B35 0%, #FFA500 100%);
        }

        .card-icon {
            font-size: 50px;
            margin-bottom: 15px;
            display: block;
        }

        .icon-pedidos { color: #4CAF50; }
        .icon-ordenes { color: #2196F3; }
        .icon-clientes { color: #9C27B0; }
        .icon-registro { color: #FF5722; }
        .icon-turnos { color: #00BCD4; }
        .icon-reportes { color: #795548; }
        .icon-ingresos { color: #4CAF50; }
        .icon-estadisticas { color: #FF9800; }
        .icon-inventario { color: #607D8B; }
        .icon-calificaciones { color: #FFD700; }

        .card-title {
            font-size: 20px;
            font-weight: 700;
            color: #333;
            margin-bottom: 12px;
        }

        .card-description {
            color: #666;
            font-size: 14px;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .btn-card {
            background: linear-gradient(135deg, #FF6B35 0%, #FFA500 100%);
            border: none;
            border-radius: 12px;
            padding: 12px 20px;
            color: white;
            font-weight: 600;
            width: 100%;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .btn-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(255, 107, 53, 0.4);
            color: white;
        }

        .special-card {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            border: 3px solid #FFD700;
        }

        .special-card .card-title,
        .special-card .card-description {
            color: white;
        }

        .special-card .btn-card {
            background: white;
            color: #4CAF50;
        }

        .special-card .btn-card:hover {
            background: #f8f9fa;
            color: #4CAF50;
        }

        .premium-card {
            background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
            color: #333;
            border: 3px solid #FF6B35;
        }

        .premium-card .card-title,
        .premium-card .card-description {
            color: #333;
        }

        .premium-card .btn-card {
            background: #FF6B35;
            color: white;
        }

        .stats-row {
            display: flex;
            justify-content: space-around;
            margin: 30px 0;
            flex-wrap: wrap;
            gap: 15px;
        }

        .stat-item {
            text-align: center;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            padding: 20px;
            border-radius: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            flex: 1;
            min-width: 150px;
            border: 2px solid #FFD700;
        }

        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #FF6B35;
            display: block;
        }

        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }

        .changuito-icon {
            background: linear-gradient(135deg, #FF6B35, #FFA500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 20px;
            margin-right: 5px;
        }

        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }

            .welcome-title {
                font-size: 28px;
            }

            .logo-plus {
                font-size: 32px;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-wrench me-2"></i>
                <span class="logo-repu">Repu</span><span class="logo-movil">Movil</span>
                <span class="plus-badge">✨ PLUS</span>
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="#" onclick="logout()">
                    <i class="fas fa-sign-out-alt me-1"></i>Cerrar Sesión
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Mensaje de Bienvenida -->
        <div class="welcome-section">
            <h1 class="welcome-title">
                <span class="logo-plus">RepuMovil</span><span class="plus-text">Plus</span>
                <i class="fas fa-crown" style="color: #FFD700; margin-left: 10px;"></i>
            </h1>
            <p class="welcome-subtitle">
                La solución completa para tu taller, desde repuestos hasta administración.
            </p>
        </div>

        <!-- Estadísticas Rápidas -->
        <div class="stats-row">
            <div class="stat-item">
                <span class="stat-number">5</span>
                <div class="stat-label">Órdenes Pendientes</div>
            </div>
            <div class="stat-item">
                <span class="stat-number">12</span>
                <div class="stat-label">Clientes del Día</div>
            </div>
            <div class="stat-item">
                <span class="stat-number">$45,000</span>
                <div class="stat-label">Ingresos del Mes</div>
            </div>
            <div class="stat-item">
                <span class="stat-number">4.9</span>
                <div class="stat-label">Calificación</div>
            </div>
        </div>

        <!-- Dashboard Principal -->
        <div class="dashboard-grid">
            <!-- Pedido de Repuestos -->
            <div class="dashboard-card special-card">
                <i class="fas fa-shopping-cart card-icon icon-pedidos"></i>
                <h3 class="card-title">📋 Pedido de Repuestos</h3>
                <p class="card-description">
                    Busca y solicita repuestos con verificación de stock en tiempo real.
                    <span class="changuito-icon">🛒</span>Sistema changuito avanzado.
                </p>
                <button class="btn btn-card" onclick="abrirPedidoRepuestos()">
                    <i class="fas fa-search me-2"></i>Buscar Repuestos
                </button>
            </div>

            <!-- Órdenes de Trabajo -->
            <div class="dashboard-card">
                <i class="fas fa-clipboard-list card-icon icon-ordenes"></i>
                <h3 class="card-title">📋 Órdenes de Trabajo Pendientes</h3>
                <p class="card-description">
                    Gestiona todas las órdenes de trabajo activas y pendientes de tu taller.
                </p>
                <button class="btn btn-card" onclick="verOrdenesTrabajo()">
                    <i class="fas fa-tasks me-2"></i>Ver Órdenes
                </button>
            </div>

            <!-- Clientes del Día -->
            <div class="dashboard-card">
                <i class="fas fa-users card-icon icon-clientes"></i>
                <h3 class="card-title">👥 Clientes del Día</h3>
                <p class="card-description">
                    Revisa los clientes programados para hoy y sus servicios.
                </p>
                <button class="btn btn-card" onclick="verClientesDia()">
                    <i class="fas fa-calendar-day me-2"></i>Ver Clientes
                </button>
            </div>

            <!-- Registro de Clientes -->
            <div class="dashboard-card premium-card">
                <i class="fas fa-user-plus card-icon icon-registro"></i>
                <h3 class="card-title">👤 Registro de Clientes</h3>
                <p class="card-description">
                    Sistema completo de gestión de clientes con datos de vehículos y historial.
                </p>
                <button class="btn btn-card" onclick="abrirRegistroClientes()">
                    <i class="fas fa-plus me-2"></i>Gestionar Clientes
                </button>
            </div>

            <!-- Registro de Turnos -->
            <div class="dashboard-card">
                <i class="fas fa-calendar-alt card-icon icon-turnos"></i>
                <h3 class="card-title">📅 Registro de Turnos del Día</h3>
                <p class="card-description">
                    Calendario interactivo para gestionar turnos y citas de clientes.
                </p>
                <button class="btn btn-card" onclick="abrirCalendarioTurnos()">
                    <i class="fas fa-calendar me-2"></i>Ver Calendario
                </button>
            </div>

            <!-- Reportes Diarios -->
            <div class="dashboard-card premium-card">
                <i class="fas fa-file-pdf card-icon icon-reportes"></i>
                <h3 class="card-title">💰 Detalle Diarios</h3>
                <p class="card-description">
                    Exporta reportes diarios en PDF/Excel para análisis de rendimiento.
                </p>
                <button class="btn btn-card" onclick="generarReporteDiario()">
                    <i class="fas fa-download me-2"></i>Generar Reporte
                </button>
            </div>

            <!-- Ingresos del Mes -->
            <div class="dashboard-card">
                <i class="fas fa-chart-line card-icon icon-ingresos"></i>
                <h3 class="card-title">💰 Ingresos del Mes</h3>
                <p class="card-description">
                    Análisis detallado de ingresos mensuales y proyecciones.
                </p>
                <button class="btn btn-card" onclick="verIngresosMes()">
                    <i class="fas fa-chart-bar me-2"></i>Ver Ingresos
                </button>
            </div>

            <!-- Estadísticas del Taller -->
            <div class="dashboard-card">
                <i class="fas fa-chart-pie card-icon icon-estadisticas"></i>
                <h3 class="card-title">📊 Estadísticas del Taller</h3>
                <p class="card-description">
                    Dashboard completo con métricas y KPIs de tu taller.
                </p>
                <button class="btn btn-card" onclick="verEstadisticas()">
                    <i class="fas fa-analytics me-2"></i>Ver Estadísticas
                </button>
            </div>

            <!-- Inventario de Herramientas -->
            <div class="dashboard-card premium-card">
                <i class="fas fa-tools card-icon icon-inventario"></i>
                <h3 class="card-title">🔧 Inventario de Herramientas</h3>
                <p class="card-description">
                    Control completo del inventario de herramientas y equipos.
                </p>
                <button class="btn btn-card" onclick="verInventario()">
                    <i class="fas fa-warehouse me-2"></i>Ver Inventario
                </button>
            </div>

            <!-- Calificaciones Recibidas -->
            <div class="dashboard-card">
                <i class="fas fa-star card-icon icon-calificaciones"></i>
                <h3 class="card-title">⭐ Calificaciones Recibidas</h3>
                <p class="card-description">
                    Sistema avanzado de calificaciones conectado con usuarios comunes.
                </p>
                <button class="btn btn-card" onclick="verCalificaciones()">
                    <i class="fas fa-star me-2"></i>Ver Calificaciones
                </button>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Funciones del Dashboard Taller Plus

        function abrirPedidoRepuestos() {
            // Misma función que el común pero con más opciones
            alert('🛒 Pedido de Repuestos PLUS\n\n✨ Funciones Premium:\n• Búsqueda avanzada con filtros\n• Comparación de precios entre proveedores\n• Historial de pedidos\n• Descuentos por volumen\n• Crédito automático\n• Notificaciones push');
        }

        function verOrdenesTrabajo() {
            const modalHTML = `
                <div class="modal fade" id="ordenesModal" tabindex="-1">
                    <div class="modal-dialog modal-xl">
                        <div class="modal-content">
                            <div class="modal-header" style="background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%); color: white;">
                                <h5 class="modal-title">
                                    <i class="fas fa-clipboard-list me-2"></i>Órdenes de Trabajo Pendientes
                                </h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-primary">
                                            <tr>
                                                <th>N° Orden</th>
                                                <th>Cliente</th>
                                                <th>Vehículo</th>
                                                <th>Servicio</th>
                                                <th>Estado</th>
                                                <th>Fecha</th>
                                                <th>Acciones</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td><strong>#001</strong></td>
                                                <td>Juan Pérez</td>
                                                <td>VW Gol 2015</td>
                                                <td>Cambio de aceite</td>
                                                <td><span class="badge bg-warning">En Proceso</span></td>
                                                <td>28/05/2025</td>
                                                <td>
                                                    <button class="btn btn-sm btn-success" onclick="completarOrden(1)">✅ Completar</button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><strong>#002</strong></td>
                                                <td>María García</td>
                                                <td>Ford Focus 2018</td>
                                                <td>Revisión de frenos</td>
                                                <td><span class="badge bg-info">Pendiente</span></td>
                                                <td>28/05/2025</td>
                                                <td>
                                                    <button class="btn btn-sm btn-primary" onclick="iniciarOrden(2)">🔧 Iniciar</button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><strong>#003</strong></td>
                                                <td>Carlos López</td>
                                                <td>Toyota Corolla 2020</td>
                                                <td>Reparación motor</td>
                                                <td><span class="badge bg-danger">Urgente</span></td>
                                                <td>27/05/2025</td>
                                                <td>
                                                    <button class="btn btn-sm btn-warning" onclick="verDetalleOrden(3)">👁️ Ver</button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHTML);
            const modal = new bootstrap.Modal(document.getElementById('ordenesModal'));
            modal.show();

            document.getElementById('ordenesModal').addEventListener('hidden.bs.modal', function () {
                this.remove();
            });
        }

        function verClientesDia() {
            alert('👥 Clientes del Día\n\n📅 Hoy tienes:\n• 9:00 - Juan Pérez (Cambio aceite)\n• 11:00 - María García (Revisión frenos)\n• 14:00 - Carlos López (Reparación motor)\n• 16:00 - Ana Martín (Service completo)\n\n✨ Plus: Notificaciones automáticas por WhatsApp');
        }

        function abrirRegistroClientes() {
            const modalHTML = `
                <div class="modal fade" id="clientesModal" tabindex="-1">
                    <div class="modal-dialog modal-xl">
                        <div class="modal-content">
                            <div class="modal-header" style="background: linear-gradient(135deg, #FF5722 0%, #E64A19 100%); color: white;">
                                <h5 class="modal-title">
                                    <i class="fas fa-user-plus me-2"></i>Registro de Clientes
                                </h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6 class="fw-bold mb-3">📝 Datos del Cliente</h6>
                                        <div class="mb-3">
                                            <label class="form-label">Nombre Completo</label>
                                            <input type="text" class="form-control" placeholder="Juan Pérez">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">DNI / CUIT</label>
                                            <input type="text" class="form-control" placeholder="12345678">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Teléfono (WhatsApp)</label>
                                            <div class="input-group">
                                                <input type="text" class="form-control" placeholder="+54 9 11 1234-5678">
                                                <button class="btn btn-success" type="button">
                                                    <i class="fab fa-whatsapp"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Correo Electrónico</label>
                                            <input type="email" class="form-control" placeholder="<EMAIL>">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Domicilio</label>
                                            <input type="text" class="form-control" placeholder="Av. Corrientes 1234">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <h6 class="fw-bold mb-3">🚗 Datos del Auto</h6>
                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label class="form-label">Marca</label>
                                                <select class="form-select">
                                                    <option>Volkswagen</option>
                                                    <option>Ford</option>
                                                    <option>Toyota</option>
                                                    <option>Chevrolet</option>
                                                </select>
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label class="form-label">Modelo</label>
                                                <input type="text" class="form-control" placeholder="Gol">
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label class="form-label">Año</label>
                                                <input type="number" class="form-control" placeholder="2020">
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label class="form-label">Patente</label>
                                                <input type="text" class="form-control" placeholder="ABC123">
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Tipo de Combustible</label>
                                            <select class="form-select">
                                                <option>Nafta</option>
                                                <option>GNC</option>
                                                <option>Diesel</option>
                                                <option>Híbrido</option>
                                                <option>Eléctrico</option>
                                            </select>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label class="form-label">Color</label>
                                                <input type="text" class="form-control" placeholder="Blanco">
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label class="form-label">Kilometraje</label>
                                                <input type="number" class="form-control" placeholder="50000">
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Fecha Última Visita</label>
                                            <input type="date" class="form-control">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Foto del Vehículo</label>
                                            <input type="file" class="form-control" accept="image/*">
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <h6 class="fw-bold">📋 Trabajos Realizados</h6>
                                    <textarea class="form-control" rows="3" placeholder="Detalle de trabajos realizados..."></textarea>
                                    <small class="text-muted">Se generará un PDF automáticamente</small>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                                <button type="button" class="btn btn-success" onclick="guardarCliente()">
                                    <i class="fas fa-save me-2"></i>Guardar Cliente
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHTML);
            const modal = new bootstrap.Modal(document.getElementById('clientesModal'));
            modal.show();

            document.getElementById('clientesModal').addEventListener('hidden.bs.modal', function () {
                this.remove();
            });
        }

        function abrirCalendarioTurnos() {
            const modalHTML = `
                <div class="modal fade" id="calendarioModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header" style="background: linear-gradient(135deg, #00BCD4 0%, #0097A7 100%); color: white;">
                                <h5 class="modal-title">
                                    <i class="fas fa-calendar-alt me-2"></i>Calendario de Turnos - Mayo 2025
                                </h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="calendar-container">
                                    <div class="calendar-header d-flex justify-content-between align-items-center mb-3">
                                        <button class="btn btn-outline-primary">← Anterior</button>
                                        <h5>Mayo 2025</h5>
                                        <button class="btn btn-outline-primary">Siguiente →</button>
                                    </div>
                                    <div class="calendar-grid">
                                        <div class="row text-center fw-bold mb-2">
                                            <div class="col">DO</div>
                                            <div class="col">LU</div>
                                            <div class="col">MA</div>
                                            <div class="col">MI</div>
                                            <div class="col">JU</div>
                                            <div class="col">VI</div>
                                            <div class="col">SA</div>
                                        </div>
                                        <div class="row">
                                            <div class="col calendar-day text-muted">27</div>
                                            <div class="col calendar-day text-muted">28</div>
                                            <div class="col calendar-day text-muted">29</div>
                                            <div class="col calendar-day text-muted">30</div>
                                            <div class="col calendar-day">1</div>
                                            <div class="col calendar-day">2</div>
                                            <div class="col calendar-day">3</div>
                                        </div>
                                        <div class="row">
                                            <div class="col calendar-day">4</div>
                                            <div class="col calendar-day">5</div>
                                            <div class="col calendar-day">6</div>
                                            <div class="col calendar-day">7</div>
                                            <div class="col calendar-day">8</div>
                                            <div class="col calendar-day">9</div>
                                            <div class="col calendar-day">10</div>
                                        </div>
                                        <div class="row">
                                            <div class="col calendar-day">11</div>
                                            <div class="col calendar-day">12</div>
                                            <div class="col calendar-day">13</div>
                                            <div class="col calendar-day">14</div>
                                            <div class="col calendar-day">15</div>
                                            <div class="col calendar-day">16</div>
                                            <div class="col calendar-day">17</div>
                                        </div>
                                        <div class="row">
                                            <div class="col calendar-day">18</div>
                                            <div class="col calendar-day">19</div>
                                            <div class="col calendar-day">20</div>
                                            <div class="col calendar-day">21</div>
                                            <div class="col calendar-day">22</div>
                                            <div class="col calendar-day">23</div>
                                            <div class="col calendar-day">24</div>
                                        </div>
                                        <div class="row">
                                            <div class="col calendar-day">25</div>
                                            <div class="col calendar-day">26</div>
                                            <div class="col calendar-day">27</div>
                                            <div class="col calendar-day today" onclick="verTurnosDelDia(28)">28</div>
                                            <div class="col calendar-day">29</div>
                                            <div class="col calendar-day">30</div>
                                            <div class="col calendar-day">31</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-4">
                                    <h6 class="fw-bold">📅 Turnos del día seleccionado:</h6>
                                    <div id="turnosDelDia">
                                        <p class="text-muted">Selecciona un día para ver los turnos</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <style>
                    .calendar-day {
                        padding: 15px 5px;
                        border: 1px solid #eee;
                        cursor: pointer;
                        transition: all 0.3s ease;
                    }
                    .calendar-day:hover {
                        background-color: #e3f2fd;
                    }
                    .today {
                        background-color: #2196F3 !important;
                        color: white !important;
                        font-weight: bold;
                    }
                </style>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHTML);
            const modal = new bootstrap.Modal(document.getElementById('calendarioModal'));
            modal.show();

            document.getElementById('calendarioModal').addEventListener('hidden.bs.modal', function () {
                this.remove();
            });
        }

        function verTurnosDelDia(dia) {
            const turnos = [
                { hora: '09:00', cliente: 'Juan Pérez', servicio: 'Cambio de aceite', precio: '$8,000' },
                { hora: '11:00', cliente: 'María García', servicio: 'Revisión frenos', precio: '$15,000' },
                { hora: '14:00', cliente: 'Carlos López', servicio: 'Reparación motor', precio: '$45,000' },
                { hora: '16:00', cliente: 'Ana Martín', servicio: 'Service completo', precio: '$25,000' }
            ];

            const turnosHTML = turnos.map(turno => `
                <div class="border rounded p-2 mb-2">
                    <div class="d-flex justify-content-between">
                        <div>
                            <strong>${turno.hora}</strong> - ${turno.cliente}<br>
                            <small class="text-muted">${turno.servicio}</small>
                        </div>
                        <div class="text-end">
                            <div class="fw-bold text-success">${turno.precio}</div>
                            <button class="btn btn-sm btn-outline-primary">Editar</button>
                        </div>
                    </div>
                </div>
            `).join('');

            document.getElementById('turnosDelDia').innerHTML = turnosHTML;
        }

        function generarReporteDiario() {
            alert('📊 Generando Reporte Diario...\n\n✨ Funciones Premium:\n• Exportar a PDF\n• Exportar a Excel\n• Gráficos de rendimiento\n• Comparación con días anteriores\n• Análisis de rentabilidad\n• Proyecciones automáticas\n\n📁 El archivo se descargará automáticamente');
        }

        function verIngresosMes() {
            alert('💰 Ingresos del Mes - Mayo 2025\n\n📊 Resumen:\n• Total facturado: $450,000\n• Servicios realizados: 85\n• Promedio por servicio: $5,294\n• Crecimiento vs mes anterior: +15%\n\n✨ Plus: Gráficos interactivos y proyecciones');
        }

        function verEstadisticas() {
            alert('📊 Estadísticas del Taller\n\n📈 KPIs Principales:\n• Eficiencia operativa: 92%\n• Satisfacción del cliente: 4.9/5\n• Tiempo promedio de servicio: 2.5 horas\n• Tasa de retorno de clientes: 78%\n• Margen de ganancia: 35%\n\n✨ Dashboard interactivo con gráficos en tiempo real');
        }

        function verInventario() {
            alert('🔧 Inventario de Herramientas\n\n📦 Estado actual:\n• Herramientas registradas: 156\n• En uso: 23\n• Disponibles: 133\n• Requieren mantenimiento: 5\n• Valor total del inventario: $85,000\n\n✨ Plus: Códigos QR, alertas de mantenimiento, historial de uso');
        }

        function verCalificaciones() {
            alert('⭐ Calificaciones Recibidas\n\n🌟 Resumen:\n• Calificación promedio: 4.9/5\n• Total de reseñas: 247\n• 5 estrellas: 89%\n• 4 estrellas: 8%\n• 3 estrellas: 2%\n• Menos de 3: 1%\n\n✨ Plus: Conectado con usuarios comunes, respuestas automáticas, análisis de sentimientos');
        }

        function completarOrden(id) {
            alert(`✅ Orden #${id.toString().padStart(3, '0')} marcada como completada\n\n📋 Acciones automáticas:\n• Cliente notificado por WhatsApp\n• Factura generada\n• Historial actualizado\n• Calificación solicitada`);
        }

        function iniciarOrden(id) {
            alert(`🔧 Iniciando trabajo en Orden #${id.toString().padStart(3, '0')}\n\n⏱️ Timer iniciado\n📱 Cliente notificado\n🔧 Estado: En proceso`);
        }

        function verDetalleOrden(id) {
            alert(`👁️ Detalle de Orden #${id.toString().padStart(3, '0')}\n\n🚗 Vehículo: Toyota Corolla 2020\n👤 Cliente: Carlos López\n🔧 Servicio: Reparación motor\n💰 Presupuesto: $45,000\n📅 Fecha límite: 30/05/2025\n⚠️ Estado: URGENTE`);
        }

        function guardarCliente() {
            alert('✅ Cliente guardado exitosamente\n\n📋 Acciones realizadas:\n• Datos almacenados en la base\n• PDF de historial generado\n• WhatsApp configurado\n• Recordatorios programados\n• Perfil del vehículo creado');

            bootstrap.Modal.getInstance(document.getElementById('clientesModal')).hide();
        }

        function logout() {
            if (confirm('¿Estás seguro que quieres cerrar sesión?')) {
                window.location.href = 'login-dinamico.php';
            }
        }

        // Animaciones al cargar la página
        document.addEventListener('DOMContentLoaded', function() {
            // Animación de entrada para las cards
            const cards = document.querySelectorAll('.dashboard-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';

                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
