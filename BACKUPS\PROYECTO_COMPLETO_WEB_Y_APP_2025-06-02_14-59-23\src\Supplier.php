<?php

namespace Src;

/**
 * Clase que representa un proveedor de repuestos en la plataforma
 */
class Supplier {
    private $id;
    private $name;
    private $location;
    private $contactInfo;
    private $rating;
    private $inventory = [];
    private $offers = [];

    /**
     * Constructor de la clase Supplier
     * 
     * @param string $name Nombre del proveedor
     * @param string $location Ubicación del proveedor
     * @param array $contactInfo Información de contacto
     */
    public function __construct($name, $location, $contactInfo) {
        $this->id = uniqid('supplier_');
        $this->name = $name;
        $this->location = $location;
        $this->contactInfo = $contactInfo;
        $this->rating = 0;
    }

    /**
     * Obtiene el ID único del proveedor
     * 
     * @return string
     */
    public function getId() {
        return $this->id;
    }

    /**
     * Obtiene el nombre del proveedor
     * 
     * @return string
     */
    public function getName() {
        return $this->name;
    }

    /**
     * Establece el nombre del proveedor
     * 
     * @param string $name
     * @return void
     */
    public function setName($name) {
        $this->name = $name;
    }

    /**
     * Obtiene la ubicación del proveedor
     * 
     * @return string
     */
    public function getLocation() {
        return $this->location;
    }

    /**
     * Establece la ubicación del proveedor
     * 
     * @param string $location
     * @return void
     */
    public function setLocation($location) {
        $this->location = $location;
    }

    /**
     * Obtiene la información de contacto del proveedor
     * 
     * @return array
     */
    public function getContactInfo() {
        return $this->contactInfo;
    }

    /**
     * Establece la información de contacto del proveedor
     * 
     * @param array $contactInfo
     * @return void
     */
    public function setContactInfo($contactInfo) {
        $this->contactInfo = $contactInfo;
    }

    /**
     * Obtiene la calificación del proveedor
     * 
     * @return float
     */
    public function getRating() {
        return $this->rating;
    }

    /**
     * Actualiza la calificación del proveedor
     * 
     * @param float $rating
     * @return void
     */
    public function updateRating($rating) {
        $this->rating = $rating;
    }

    /**
     * Agrega un repuesto al inventario
     * 
     * @param string $partNumber Número de parte
     * @param string $name Nombre del repuesto
     * @param float $price Precio del repuesto
     * @param int $quantity Cantidad disponible
     * @param array $compatibility Vehículos compatibles
     * @return string ID del repuesto
     */
    public function addPart($partNumber, $name, $price, $quantity, $compatibility = []) {
        $partId = uniqid('part_');
        $this->inventory[$partId] = [
            'id' => $partId,
            'partNumber' => $partNumber,
            'name' => $name,
            'price' => $price,
            'quantity' => $quantity,
            'compatibility' => $compatibility
        ];
        return $partId;
    }

    /**
     * Actualiza la cantidad de un repuesto en el inventario
     * 
     * @param string $partId ID del repuesto
     * @param int $quantity Nueva cantidad
     * @return bool
     */
    public function updatePartQuantity($partId, $quantity) {
        if (!isset($this->inventory[$partId])) {
            return false;
        }
        
        $this->inventory[$partId]['quantity'] = $quantity;
        return true;
    }

    /**
     * Actualiza el precio de un repuesto
     * 
     * @param string $partId ID del repuesto
     * @param float $price Nuevo precio
     * @return bool
     */
    public function updatePartPrice($partId, $price) {
        if (!isset($this->inventory[$partId])) {
            return false;
        }
        
        $this->inventory[$partId]['price'] = $price;
        return true;
    }

    /**
     * Obtiene todo el inventario
     * 
     * @return array
     */
    public function getInventory() {
        return $this->inventory;
    }

    /**
     * Busca repuestos por número de parte o nombre
     * 
     * @param string $query Término de búsqueda
     * @return array
     */
    public function searchParts($query) {
        $results = [];
        
        foreach ($this->inventory as $part) {
            if (stripos($part['partNumber'], $query) !== false || 
                stripos($part['name'], $query) !== false) {
                $results[] = $part;
            }
        }
        
        return $results;
    }

    /**
     * Crea una oferta para una solicitud de repuestos
     * 
     * @param string $requestId ID de la solicitud
     * @param string $workshopId ID del taller
     * @param string $partId ID del repuesto
     * @param float $price Precio ofrecido
     * @param int $quantity Cantidad ofrecida
     * @param int $deliveryTime Tiempo de entrega en horas
     * @return string ID de la oferta
     */
    public function createOffer($requestId, $workshopId, $partId, $price, $quantity, $deliveryTime) {
        if (!isset($this->inventory[$partId]) || $this->inventory[$partId]['quantity'] < $quantity) {
            return false;
        }
        
        $offerId = uniqid('offer_');
        $offer = [
            'id' => $offerId,
            'requestId' => $requestId,
            'workshopId' => $workshopId,
            'partId' => $partId,
            'price' => $price,
            'quantity' => $quantity,
            'deliveryTime' => $deliveryTime,
            'status' => 'pending',
            'createdAt' => date('Y-m-d H:i:s')
        ];
        
        $this->offers[$offerId] = $offer;
        return $offerId;
    }

    /**
     * Obtiene todas las ofertas realizadas
     * 
     * @return array
     */
    public function getOffers() {
        return $this->offers;
    }

    /**
     * Obtiene una oferta específica por su ID
     * 
     * @param string $offerId
     * @return array|null
     */
    public function getOffer($offerId) {
        return isset($this->offers[$offerId]) ? $this->offers[$offerId] : null;
    }

    /**
     * Actualiza el estado de una oferta
     * 
     * @param string $offerId ID de la oferta
     * @param string $status Nuevo estado
     * @return bool
     */
    public function updateOfferStatus($offerId, $status) {
        if (!isset($this->offers[$offerId])) {
            return false;
        }
        
        $this->offers[$offerId]['status'] = $status;
        
        // Si la oferta es aceptada, reducir la cantidad en el inventario
        if ($status === 'accepted') {
            $partId = $this->offers[$offerId]['partId'];
            $quantity = $this->offers[$offerId]['quantity'];
            $this->inventory[$partId]['quantity'] -= $quantity;
        }
        
        return true;
    }

    /**
     * Convierte el objeto a un array para JSON
     * 
     * @return array
     */
    public function toArray() {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'location' => $this->location,
            'contactInfo' => $this->contactInfo,
            'rating' => $this->rating
        ];
    }
}
