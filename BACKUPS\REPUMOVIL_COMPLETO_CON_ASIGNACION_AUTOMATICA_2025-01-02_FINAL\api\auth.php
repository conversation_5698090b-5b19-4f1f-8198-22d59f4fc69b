<?php
// api/auth.php - Funciones de autenticación para la API

/**
 * Valida el token de autenticación en la cabecera
 * @return bool Verdadero si el token es válido
 */
function validateToken() {
    // Obtener cabeceras
    $headers = getallheaders();
    $authHeader = $headers['Authorization'] ?? '';
    
    // Verificar formato del token (Bearer token)
    if (!preg_match('/Bearer\s(\S+)/', $authHeader, $matches)) {
        return false;
    }
    
    $token = $matches[1];
    
    // Verificar token en la base de datos
    $conn = connectDB();
    $stmt = $conn->prepare("SELECT user_id, expires_at FROM auth_tokens WHERE token = :token");
    $stmt->execute(['token' => $token]);
    $tokenData = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Verificar si el token existe y no ha expirado
    if (!$tokenData || strtotime($tokenData['expires_at']) < time()) {
        return false;
    }
    
    // Guardar ID de usuario para uso posterior
    global $current_user_id;
    $current_user_id = $tokenData['user_id'];
    
    return true;
}

/**
 * Genera un nuevo token para un usuario
 * @param int $userId ID del usuario
 * @return string Token generado
 */
function generateToken($userId) {
    // Generar token aleatorio
    $token = bin2hex(random_bytes(32));
    
    // Establecer fecha de expiración (30 días)
    $expiresAt = date('Y-m-d H:i:s', strtotime('+30 days'));
    
    // Guardar token en la base de datos
    $conn = connectDB();
    $stmt = $conn->prepare("INSERT INTO auth_tokens (user_id, token, expires_at) VALUES (:user_id, :token, :expires_at)");
    $stmt->execute([
        'user_id' => $userId,
        'token' => $token,
        'expires_at' => $expiresAt
    ]);
    
    return $token;
}

/**
 * Procesa el inicio de sesión
 * @param array $data Datos de inicio de sesión
 * @return array Respuesta con token o error
 */
function login($data) {
    // Validar datos requeridos
    if (!isset($data['username']) || !isset($data['password'])) {
        return ['status' => 'error', 'message' => 'Nombre de usuario y contraseña son requeridos'];
    }
    
    $username = $data['username'];
    $password = $data['password'];
    
    // Autenticar usuario
    $user = authenticateUser($username, $password);
    
    if (!$user) {
        return ['status' => 'error', 'message' => 'Credenciales inválidas'];
    }
    
    // Generar token
    $token = generateToken($user['id']);
    
    // Preparar datos de usuario para respuesta (sin contraseña)
    unset($user['password']);
    
    return [
        'status' => 'success',
        'message' => 'Inicio de sesión exitoso',
        'token' => $token,
        'user' => $user
    ];
}

/**
 * Procesa el registro de un nuevo usuario
 * @param array $data Datos de registro
 * @return array Respuesta con resultado
 */
function register($data) {
    // Validar datos requeridos
    if (!isset($data['username']) || !isset($data['email']) || !isset($data['password']) || !isset($data['user_type'])) {
        return ['status' => 'error', 'message' => 'Todos los campos son requeridos'];
    }
    
    $username = $data['username'];
    $email = $data['email'];
    $password = $data['password'];
    $userType = $data['user_type'];
    
    // Validar tipo de usuario
    if (!in_array($userType, ['workshop', 'supplier'])) {
        return ['status' => 'error', 'message' => 'Tipo de usuario inválido'];
    }
    
    // Verificar si el usuario ya existe
    $conn = connectDB();
    $stmt = $conn->prepare("SELECT id FROM users WHERE username = :username OR email = :email");
    $stmt->execute([
        'username' => $username,
        'email' => $email
    ]);
    
    if ($stmt->rowCount() > 0) {
        return ['status' => 'error', 'message' => 'El nombre de usuario o email ya está en uso'];
    }
    
    // Determinar el ID del rol según el tipo de usuario
    $roleId = ($userType === 'workshop') ? 2 : 3; // 2 = taller, 3 = proveedor
    
    // Iniciar transacción
    $conn->beginTransaction();
    
    try {
        // Crear usuario
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        $stmt = $conn->prepare("INSERT INTO users (username, email, password, role_id) VALUES (:username, :email, :password, :role_id)");
        $stmt->execute([
            'username' => $username,
            'email' => $email,
            'password' => $hashedPassword,
            'role_id' => $roleId
        ]);
        
        $userId = $conn->lastInsertId();
        
        // Crear perfil según tipo de usuario
        if ($userType === 'workshop') {
            $stmt = $conn->prepare("INSERT INTO workshops (user_id, name) VALUES (:user_id, :name)");
            $stmt->execute([
                'user_id' => $userId,
                'name' => $data['business_name'] ?? $username
            ]);
        } else {
            $stmt = $conn->prepare("INSERT INTO suppliers (user_id, name) VALUES (:user_id, :name)");
            $stmt->execute([
                'user_id' => $userId,
                'name' => $data['business_name'] ?? $username
            ]);
        }
        
        $conn->commit();
        
        return [
            'status' => 'success',
            'message' => 'Usuario registrado exitosamente',
            'user_id' => $userId
        ];
    } catch (Exception $e) {
        $conn->rollBack();
        return ['status' => 'error', 'message' => 'Error al registrar usuario: ' . $e->getMessage()];
    }
}