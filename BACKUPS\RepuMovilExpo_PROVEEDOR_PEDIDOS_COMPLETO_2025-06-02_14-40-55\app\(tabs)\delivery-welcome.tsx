import React from 'react';
import {
  SafeAreaView,
  StatusBar,
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  ImageBackground,
} from 'react-native';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';

const { width, height } = Dimensions.get('window');

export default function DeliveryWelcome() {
  const router = useRouter();

  const navigateToRegistro = () => {
    router.push('/delivery-registro');
  };

  const navigateToLogin = () => {
    router.push('/delivery-login');
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#FF6B35" />
      
      {/* Header con gradiente */}
      <LinearGradient
        colors={['#FF6B35', '#E53E3E', '#F7931E']}
        style={styles.header}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={styles.headerContent}>
          <View style={styles.logoContainer}>
            <Text style={styles.logoIcon}>🏍️</Text>
          </View>
          <Text style={styles.logoText}>
            <Text style={styles.logoRepu}>Repu</Text>
            <Text style={styles.logoMovil}>Movil</Text>
          </Text>
          <Text style={styles.logoSubtitle}>DELIVERY</Text>
          <Text style={styles.welcomeMessage}>
            ¡Únete a nuestra red de repartidores independientes!
          </Text>
        </View>
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Sección de Beneficios */}
        <View style={styles.benefitsSection}>
          <Text style={styles.sectionTitle}>🚀 ¿Por qué ser repartidor RepuMovil?</Text>
          
          <View style={styles.benefitCard}>
            <Text style={styles.benefitIcon}>💰</Text>
            <View style={styles.benefitContent}>
              <Text style={styles.benefitTitle}>Gana dinero extra</Text>
              <Text style={styles.benefitDescription}>
                Trabaja en tus horarios libres y genera ingresos adicionales
              </Text>
            </View>
          </View>

          <View style={styles.benefitCard}>
            <Text style={styles.benefitIcon}>⏰</Text>
            <View style={styles.benefitContent}>
              <Text style={styles.benefitTitle}>Horarios flexibles</Text>
              <Text style={styles.benefitDescription}>
                Tú decides cuándo y cuánto trabajar
              </Text>
            </View>
          </View>

          <View style={styles.benefitCard}>
            <Text style={styles.benefitIcon}>📱</Text>
            <View style={styles.benefitContent}>
              <Text style={styles.benefitTitle}>App fácil de usar</Text>
              <Text style={styles.benefitDescription}>
                Interfaz intuitiva para gestionar tus entregas
              </Text>
            </View>
          </View>

          <View style={styles.benefitCard}>
            <Text style={styles.benefitIcon}>🛡️</Text>
            <View style={styles.benefitContent}>
              <Text style={styles.benefitTitle}>Trabajo seguro</Text>
              <Text style={styles.benefitDescription}>
                Plataforma confiable con soporte 24/7
              </Text>
            </View>
          </View>
        </View>

        {/* Requisitos */}
        <View style={styles.requirementsSection}>
          <Text style={styles.sectionTitle}>📋 Requisitos básicos</Text>
          
          <View style={styles.requirementItem}>
            <Text style={styles.requirementIcon}>✅</Text>
            <Text style={styles.requirementText}>Mayor de 18 años</Text>
          </View>
          
          <View style={styles.requirementItem}>
            <Text style={styles.requirementIcon}>✅</Text>
            <Text style={styles.requirementText}>DNI argentino vigente</Text>
          </View>
          
          <View style={styles.requirementItem}>
            <Text style={styles.requirementIcon}>✅</Text>
            <Text style={styles.requirementText}>Vehículo propio (bici, moto o auto)</Text>
          </View>
          
          <View style={styles.requirementItem}>
            <Text style={styles.requirementIcon}>✅</Text>
            <Text style={styles.requirementText}>Smartphone con internet</Text>
          </View>
          
          <View style={styles.requirementItem}>
            <Text style={styles.requirementIcon}>✅</Text>
            <Text style={styles.requirementText}>CBU o Alias bancario</Text>
          </View>
        </View>

        {/* Botones de acción */}
        <View style={styles.actionSection}>
          <TouchableOpacity style={styles.primaryButton} onPress={navigateToRegistro}>
            <LinearGradient
              colors={['#FF6B35', '#E53E3E']}
              style={styles.buttonGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
            >
              <Text style={styles.primaryButtonText}>
                🚀 Registrarme como Repartidor
              </Text>
            </LinearGradient>
          </TouchableOpacity>

          <TouchableOpacity style={styles.secondaryButton} onPress={navigateToLogin}>
            <Text style={styles.secondaryButtonText}>
              🔐 Ya tengo cuenta - Iniciar Sesión
            </Text>
          </TouchableOpacity>
        </View>

        {/* Footer info */}
        <View style={styles.footerInfo}>
          <Text style={styles.footerText}>
            🔒 Toda tu información está protegida y será tratada con confidencialidad
          </Text>
          <Text style={styles.footerSubtext}>
            RepuMovil Delivery - Conectando talleres, repuestos y repartidores
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    paddingTop: 20,
    paddingBottom: 30,
    paddingHorizontal: 20,
  },
  headerContent: {
    alignItems: 'center',
  },
  logoContainer: {
    width: 80,
    height: 80,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 15,
  },
  logoIcon: {
    fontSize: 40,
  },
  logoText: {
    fontSize: 32,
    fontWeight: '800',
    color: 'white',
    marginBottom: 5,
  },
  logoRepu: {
    color: '#ffffff',
  },
  logoMovil: {
    color: '#FFF3E0',
  },
  logoSubtitle: {
    fontSize: 16,
    fontWeight: '600',
    color: 'white',
    letterSpacing: 2,
    marginBottom: 10,
  },
  welcomeMessage: {
    fontSize: 16,
    color: 'white',
    textAlign: 'center',
    opacity: 0.9,
    lineHeight: 22,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  benefitsSection: {
    marginTop: 20,
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#2D3748',
    marginBottom: 20,
    textAlign: 'center',
  },
  benefitCard: {
    flexDirection: 'row',
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 15,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  benefitIcon: {
    fontSize: 30,
    marginRight: 15,
  },
  benefitContent: {
    flex: 1,
  },
  benefitTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2D3748',
    marginBottom: 5,
  },
  benefitDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  requirementsSection: {
    marginBottom: 30,
  },
  requirementItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  requirementIcon: {
    fontSize: 20,
    marginRight: 15,
  },
  requirementText: {
    fontSize: 15,
    color: '#2D3748',
    fontWeight: '500',
  },
  actionSection: {
    marginBottom: 30,
  },
  primaryButton: {
    marginBottom: 15,
    borderRadius: 15,
    overflow: 'hidden',
  },
  buttonGradient: {
    paddingVertical: 18,
    paddingHorizontal: 30,
    alignItems: 'center',
  },
  primaryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '700',
  },
  secondaryButton: {
    backgroundColor: 'white',
    paddingVertical: 18,
    paddingHorizontal: 30,
    borderRadius: 15,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#FF6B35',
  },
  secondaryButtonText: {
    color: '#FF6B35',
    fontSize: 16,
    fontWeight: '600',
  },
  footerInfo: {
    alignItems: 'center',
    paddingBottom: 30,
  },
  footerText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 10,
    lineHeight: 20,
  },
  footerSubtext: {
    fontSize: 12,
    color: '#999',
    textAlign: 'center',
    fontStyle: 'italic',
  },
});
