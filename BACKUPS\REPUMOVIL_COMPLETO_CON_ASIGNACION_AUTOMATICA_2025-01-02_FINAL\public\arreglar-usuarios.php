<?php
require_once 'db_config.php';

echo "<h1>🔧 Arreglando Usuarios</h1>";

try {
    $pdo = connectDB();
    
    echo "<h2>1️⃣ Arreglando taller1 (ID: 2)</h2>";
    
    // Verificar si ya existe en workshops
    $stmt = $pdo->prepare("SELECT * FROM workshops WHERE user_id = 2");
    $stmt->execute();
    $workshop_exists = $stmt->fetch();
    
    if (!$workshop_exists) {
        // Crear registro en workshops para taller1
        $stmt = $pdo->prepare("
            INSERT INTO workshops (user_id, name, address, phone, description) 
            VALUES (?, ?, ?, ?, ?)
        ");
        
        $result = $stmt->execute([
            2, // user_id del taller1
            'Taller Mecánico Ejemplo',
            'Calle Ejemplo 123, San Juan, Argentina',
            '+54 9 ************',
            'Taller especializado en reparación de motores y sistemas de frenos'
        ]);
        
        if ($result) {
            echo "<p>✅ Registro de taller creado para taller1</p>";
        } else {
            echo "<p>❌ Error al crear registro de taller</p>";
        }
    } else {
        echo "<p>✅ El taller1 ya tiene registro en workshops</p>";
    }
    
    echo "<h2>2️⃣ Arreglando proveedor_test (ID: 31)</h2>";
    
    // Cambiar role_id del proveedor_test de 3 a 5
    $stmt = $pdo->prepare("UPDATE users SET role_id = 5 WHERE id = 31");
    $result = $stmt->execute();
    
    if ($result) {
        echo "<p>✅ Role del proveedor_test cambiado a 5 (proveedor)</p>";
    } else {
        echo "<p>❌ Error al cambiar role del proveedor</p>";
    }
    
    echo "<h2>3️⃣ Verificando cambios</h2>";
    
    // Verificar taller1
    $stmt = $pdo->prepare("
        SELECT u.username, u.email, r.name as role_name, w.name as workshop_name
        FROM users u
        LEFT JOIN roles r ON u.role_id = r.id
        LEFT JOIN workshops w ON u.id = w.user_id
        WHERE u.id = 2
    ");
    $stmt->execute();
    $taller = $stmt->fetch();
    
    echo "<div style='background: #e8f5e8; padding: 15px; margin: 10px 0; border-radius: 8px;'>";
    echo "<h3>🔧 Taller1 Verificado</h3>";
    echo "<p><strong>Username:</strong> {$taller['username']}</p>";
    echo "<p><strong>Email:</strong> {$taller['email']}</p>";
    echo "<p><strong>Role:</strong> {$taller['role_name']}</p>";
    echo "<p><strong>Workshop:</strong> " . ($taller['workshop_name'] ?? 'NO ENCONTRADO') . "</p>";
    echo "</div>";
    
    // Verificar proveedor_test
    $stmt = $pdo->prepare("
        SELECT u.username, u.email, r.name as role_name, s.name as supplier_name
        FROM users u
        LEFT JOIN roles r ON u.role_id = r.id
        LEFT JOIN suppliers s ON u.id = s.user_id
        WHERE u.id = 31
    ");
    $stmt->execute();
    $proveedor = $stmt->fetch();
    
    echo "<div style='background: #e3f2fd; padding: 15px; margin: 10px 0; border-radius: 8px;'>";
    echo "<h3>🏪 Proveedor_test Verificado</h3>";
    echo "<p><strong>Username:</strong> {$proveedor['username']}</p>";
    echo "<p><strong>Email:</strong> {$proveedor['email']}</p>";
    echo "<p><strong>Role:</strong> {$proveedor['role_name']}</p>";
    echo "<p><strong>Supplier:</strong> " . ($proveedor['supplier_name'] ?? 'NO ENCONTRADO') . "</p>";
    echo "</div>";
    
    echo "<h2>4️⃣ Actualizando lógica del login</h2>";
    echo "<p>🔄 Ahora necesitamos actualizar la lógica del login para que detecte correctamente los roles...</p>";
    
} catch (Exception $e) {
    echo "<div style='background: #ffebee; padding: 20px; border-radius: 8px; color: #d32f2f;'>";
    echo "<h2>❌ Error</h2>";
    echo "<p>{$e->getMessage()}</p>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Arreglar Usuarios - RepuMovil</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        
        h1 {
            color: #4CAF50;
            text-align: center;
            margin-bottom: 30px;
        }
        
        h2 {
            color: #333;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
        }
        
        p {
            background: white;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #4CAF50;
        }
    </style>
</head>
<body>
</body>
</html>
