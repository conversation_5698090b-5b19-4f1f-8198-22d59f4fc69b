import React, { useState } from 'react';
import {
  SafeAreaView,
  StatusBar,
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert,
  Dimensions,
  Switch,
} from 'react-native';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';

const { width } = Dimensions.get('window');

interface PerfilRepartidor {
  nombre: string;
  email: string;
  telefono: string;
  dni: string;
  vehiculo: string;
  calificacion: number;
  entregas_totales: number;
  fecha_registro: string;
  estado_cuenta: 'activa' | 'pendiente' | 'suspendida';
}

export default function DeliveryPerfil() {
  const router = useRouter();
  const [notificacionesPush, setNotificacionesPush] = useState(true);
  const [notificacionesEmail, setNotificacionesEmail] = useState(true);
  const [modoOscuro, setModoOscuro] = useState(false);
  const [disponibilidadAutomatica, setDisponibilidadAutomatica] = useState(false);

  // Datos de ejemplo del perfil
  const perfil: PerfilRepartidor = {
    nombre: '<PERSON>',
    email: '<EMAIL>',
    telefono: '+54 9 ************',
    dni: '12.345.678',
    vehiculo: 'Moto - Honda Wave 110',
    calificacion: 4.8,
    entregas_totales: 247,
    fecha_registro: '2023-08-15',
    estado_cuenta: 'activa'
  };

  const handleEditarPerfil = () => {
    Alert.alert(
      '✏️ Editar Perfil',
      'Esta función te permitirá modificar tu información personal.',
      [
        { text: 'Cancelar', style: 'cancel' },
        { text: 'Continuar', onPress: () => Alert.alert('🚧 En desarrollo', 'Esta función estará disponible pronto.') }
      ]
    );
  };

  const handleCambiarVehiculo = () => {
    Alert.alert(
      '🏍️ Cambiar Vehículo',
      'Para cambiar tu vehículo necesitarás subir nueva documentación.',
      [
        { text: 'Cancelar', style: 'cancel' },
        { text: 'Continuar', onPress: () => Alert.alert('🚧 En desarrollo', 'Esta función estará disponible pronto.') }
      ]
    );
  };

  const handleSubirDocumentos = () => {
    Alert.alert(
      '📄 Subir Documentos',
      'Aquí podrás actualizar tu documentación (DNI, licencia, seguro, etc.)',
      [
        { text: 'Cancelar', style: 'cancel' },
        { text: 'Continuar', onPress: () => Alert.alert('🚧 En desarrollo', 'Esta función estará disponible pronto.') }
      ]
    );
  };

  const handleCerrarSesion = () => {
    Alert.alert(
      '🚪 Cerrar Sesión',
      '¿Estás seguro que deseas cerrar sesión?',
      [
        { text: 'Cancelar', style: 'cancel' },
        { 
          text: 'Cerrar Sesión', 
          style: 'destructive',
          onPress: () => {
            Alert.alert('👋 Hasta luego', 'Sesión cerrada exitosamente', [
              { text: 'OK', onPress: () => router.push('/delivery-welcome') }
            ]);
          }
        }
      ]
    );
  };

  const renderEstrellas = (calificacion: number) => {
    const estrellas = [];
    const estrellasCompletas = Math.floor(calificacion);
    const tieneMediaEstrella = calificacion % 1 !== 0;

    for (let i = 0; i < estrellasCompletas; i++) {
      estrellas.push(<Text key={i} style={styles.estrella}>⭐</Text>);
    }

    if (tieneMediaEstrella) {
      estrellas.push(<Text key="media" style={styles.estrella}>⭐</Text>);
    }

    const estrellasVacias = 5 - Math.ceil(calificacion);
    for (let i = 0; i < estrellasVacias; i++) {
      estrellas.push(<Text key={`vacia-${i}`} style={styles.estrella}>☆</Text>);
    }

    return estrellas;
  };

  const getEstadoCuentaColor = () => {
    switch (perfil.estado_cuenta) {
      case 'activa': return '#4CAF50';
      case 'pendiente': return '#FF9800';
      case 'suspendida': return '#F44336';
      default: return '#9E9E9E';
    }
  };

  const getEstadoCuentaTexto = () => {
    switch (perfil.estado_cuenta) {
      case 'activa': return '✅ Cuenta Activa';
      case 'pendiente': return '⏳ Pendiente de Verificación';
      case 'suspendida': return '❌ Cuenta Suspendida';
      default: return 'Estado Desconocido';
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#FF6B35" />
      
      {/* Header */}
      <LinearGradient
        colors={['#FF6B35', '#E53E3E']}
        style={styles.header}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <Text style={styles.backButtonText}>← Volver</Text>
        </TouchableOpacity>
        
        <View style={styles.headerContent}>
          <View style={styles.avatarContainer}>
            <Text style={styles.avatarText}>👤</Text>
          </View>
          <Text style={styles.headerTitle}>Mi Perfil</Text>
          <Text style={styles.headerSubtitle}>Gestiona tu información personal</Text>
        </View>
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Información Personal */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>👤 Información Personal</Text>
          
          <View style={styles.infoCard}>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Nombre Completo</Text>
              <Text style={styles.infoValue}>{perfil.nombre}</Text>
            </View>
            
            <View style={styles.separator} />
            
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Email</Text>
              <Text style={styles.infoValue}>{perfil.email}</Text>
            </View>
            
            <View style={styles.separator} />
            
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Teléfono</Text>
              <Text style={styles.infoValue}>{perfil.telefono}</Text>
            </View>
            
            <View style={styles.separator} />
            
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>DNI</Text>
              <Text style={styles.infoValue}>{perfil.dni}</Text>
            </View>
            
            <TouchableOpacity style={styles.editButton} onPress={handleEditarPerfil}>
              <Text style={styles.editButtonText}>✏️ Editar Información</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Estado de la Cuenta */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>📊 Estado de la Cuenta</Text>
          
          <View style={styles.estadoCard}>
            <View style={styles.estadoHeader}>
              <View style={[styles.estadoIndicator, { backgroundColor: getEstadoCuentaColor() }]} />
              <Text style={styles.estadoTexto}>{getEstadoCuentaTexto()}</Text>
            </View>
            
            <View style={styles.estadoStats}>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{perfil.entregas_totales}</Text>
                <Text style={styles.statLabel}>Entregas Totales</Text>
              </View>
              
              <View style={styles.statItem}>
                <View style={styles.calificacionContainer}>
                  <Text style={styles.calificacionNumero}>{perfil.calificacion}</Text>
                  <View style={styles.estrellas}>
                    {renderEstrellas(perfil.calificacion)}
                  </View>
                </View>
                <Text style={styles.statLabel}>Calificación</Text>
              </View>
              
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>
                  {new Date(perfil.fecha_registro).toLocaleDateString('es-AR')}
                </Text>
                <Text style={styles.statLabel}>Miembro desde</Text>
              </View>
            </View>
          </View>
        </View>

        {/* Vehículo */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🏍️ Mi Vehículo</Text>
          
          <View style={styles.vehiculoCard}>
            <View style={styles.vehiculoInfo}>
              <Text style={styles.vehiculoIcon}>🏍️</Text>
              <View style={styles.vehiculoTexto}>
                <Text style={styles.vehiculoTipo}>{perfil.vehiculo}</Text>
                <Text style={styles.vehiculoEstado}>✅ Documentación al día</Text>
              </View>
            </View>
            
            <View style={styles.vehiculoButtons}>
              <TouchableOpacity style={styles.vehiculoButton} onPress={handleCambiarVehiculo}>
                <Text style={styles.vehiculoButtonText}>🔄 Cambiar Vehículo</Text>
              </TouchableOpacity>
              
              <TouchableOpacity style={styles.vehiculoButton} onPress={handleSubirDocumentos}>
                <Text style={styles.vehiculoButtonText}>📄 Actualizar Documentos</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Configuraciones */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>⚙️ Configuraciones</Text>
          
          <View style={styles.configCard}>
            <View style={styles.configItem}>
              <View style={styles.configInfo}>
                <Text style={styles.configLabel}>🔔 Notificaciones Push</Text>
                <Text style={styles.configDescription}>Recibir alertas de nuevos pedidos</Text>
              </View>
              <Switch
                value={notificacionesPush}
                onValueChange={setNotificacionesPush}
                trackColor={{ false: '#e2e8f0', true: '#FF6B35' }}
                thumbColor={notificacionesPush ? '#ffffff' : '#f4f3f4'}
              />
            </View>
            
            <View style={styles.separator} />
            
            <View style={styles.configItem}>
              <View style={styles.configInfo}>
                <Text style={styles.configLabel}>📧 Notificaciones por Email</Text>
                <Text style={styles.configDescription}>Resúmenes semanales y actualizaciones</Text>
              </View>
              <Switch
                value={notificacionesEmail}
                onValueChange={setNotificacionesEmail}
                trackColor={{ false: '#e2e8f0', true: '#FF6B35' }}
                thumbColor={notificacionesEmail ? '#ffffff' : '#f4f3f4'}
              />
            </View>
            
            <View style={styles.separator} />
            
            <View style={styles.configItem}>
              <View style={styles.configInfo}>
                <Text style={styles.configLabel}>🌙 Modo Oscuro</Text>
                <Text style={styles.configDescription}>Cambiar apariencia de la app</Text>
              </View>
              <Switch
                value={modoOscuro}
                onValueChange={setModoOscuro}
                trackColor={{ false: '#e2e8f0', true: '#FF6B35' }}
                thumbColor={modoOscuro ? '#ffffff' : '#f4f3f4'}
              />
            </View>
            
            <View style={styles.separator} />
            
            <View style={styles.configItem}>
              <View style={styles.configInfo}>
                <Text style={styles.configLabel}>🤖 Disponibilidad Automática</Text>
                <Text style={styles.configDescription}>Activarse automáticamente al abrir la app</Text>
              </View>
              <Switch
                value={disponibilidadAutomatica}
                onValueChange={setDisponibilidadAutomatica}
                trackColor={{ false: '#e2e8f0', true: '#FF6B35' }}
                thumbColor={disponibilidadAutomatica ? '#ffffff' : '#f4f3f4'}
              />
            </View>
          </View>
        </View>

        {/* Acciones */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🔧 Acciones</Text>
          
          <View style={styles.accionesCard}>
            <TouchableOpacity style={styles.accionButton}>
              <Text style={styles.accionIcon}>📞</Text>
              <Text style={styles.accionText}>Contactar Soporte</Text>
              <Text style={styles.accionArrow}>→</Text>
            </TouchableOpacity>
            
            <View style={styles.separator} />
            
            <TouchableOpacity style={styles.accionButton}>
              <Text style={styles.accionIcon}>📋</Text>
              <Text style={styles.accionText}>Términos y Condiciones</Text>
              <Text style={styles.accionArrow}>→</Text>
            </TouchableOpacity>
            
            <View style={styles.separator} />
            
            <TouchableOpacity style={styles.accionButton}>
              <Text style={styles.accionIcon}>🔒</Text>
              <Text style={styles.accionText}>Política de Privacidad</Text>
              <Text style={styles.accionArrow}>→</Text>
            </TouchableOpacity>
            
            <View style={styles.separator} />
            
            <TouchableOpacity style={[styles.accionButton, styles.cerrarSesionButton]} onPress={handleCerrarSesion}>
              <Text style={styles.accionIcon}>🚪</Text>
              <Text style={[styles.accionText, styles.cerrarSesionText]}>Cerrar Sesión</Text>
              <Text style={styles.accionArrow}>→</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Versión de la App */}
        <View style={styles.versionContainer}>
          <Text style={styles.versionText}>RepuMovil Delivery v1.0.0</Text>
          <Text style={styles.versionSubtext}>Hecho con ❤️ en San Juan, Argentina</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    paddingTop: 20,
    paddingBottom: 25,
    paddingHorizontal: 20,
  },
  backButton: {
    alignSelf: 'flex-start',
    marginBottom: 15,
  },
  backButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  headerContent: {
    alignItems: 'center',
  },
  avatarContainer: {
    width: 80,
    height: 80,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 15,
  },
  avatarText: {
    fontSize: 40,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '800',
    color: 'white',
    marginBottom: 5,
  },
  headerSubtitle: {
    fontSize: 16,
    color: 'white',
    opacity: 0.9,
    textAlign: 'center',
  },
  content: {
    flex: 1,
  },
  section: {
    paddingHorizontal: 20,
    marginBottom: 25,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#2D3748',
    marginBottom: 15,
  },
  infoCard: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
  },
  infoLabel: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  infoValue: {
    fontSize: 14,
    color: '#2D3748',
    fontWeight: '600',
    flex: 1,
    textAlign: 'right',
  },
  separator: {
    height: 1,
    backgroundColor: '#f0f0f0',
  },
  editButton: {
    backgroundColor: '#FF6B35',
    paddingVertical: 12,
    borderRadius: 10,
    alignItems: 'center',
    marginTop: 15,
  },
  editButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  estadoCard: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  estadoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  estadoIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 10,
  },
  estadoTexto: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2D3748',
  },
  estadoStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 18,
    fontWeight: '800',
    color: '#FF6B35',
    marginBottom: 5,
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  calificacionContainer: {
    alignItems: 'center',
  },
  calificacionNumero: {
    fontSize: 18,
    fontWeight: '800',
    color: '#FF6B35',
    marginBottom: 2,
  },
  estrellas: {
    flexDirection: 'row',
    marginBottom: 5,
  },
  estrella: {
    fontSize: 12,
    marginHorizontal: 1,
  },
  vehiculoCard: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  vehiculoInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  vehiculoIcon: {
    fontSize: 30,
    marginRight: 15,
  },
  vehiculoTexto: {
    flex: 1,
  },
  vehiculoTipo: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2D3748',
    marginBottom: 5,
  },
  vehiculoEstado: {
    fontSize: 12,
    color: '#4CAF50',
    fontWeight: '500',
  },
  vehiculoButtons: {
    flexDirection: 'row',
    gap: 10,
  },
  vehiculoButton: {
    flex: 1,
    backgroundColor: '#f8f9fa',
    paddingVertical: 12,
    borderRadius: 10,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  vehiculoButtonText: {
    fontSize: 12,
    color: '#666',
    fontWeight: '600',
  },
  configCard: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  configItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
  },
  configInfo: {
    flex: 1,
    marginRight: 15,
  },
  configLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2D3748',
    marginBottom: 2,
  },
  configDescription: {
    fontSize: 12,
    color: '#666',
  },
  accionesCard: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  accionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
  },
  accionIcon: {
    fontSize: 20,
    marginRight: 15,
  },
  accionText: {
    flex: 1,
    fontSize: 14,
    fontWeight: '500',
    color: '#2D3748',
  },
  accionArrow: {
    fontSize: 16,
    color: '#999',
  },
  cerrarSesionButton: {
    backgroundColor: '#FFEBEE',
    borderRadius: 10,
    marginTop: 10,
  },
  cerrarSesionText: {
    color: '#E53E3E',
    fontWeight: '600',
  },
  versionContainer: {
    alignItems: 'center',
    paddingVertical: 30,
  },
  versionText: {
    fontSize: 12,
    color: '#999',
    marginBottom: 5,
  },
  versionSubtext: {
    fontSize: 10,
    color: '#ccc',
    fontStyle: 'italic',
  },
});
