import React, { useState } from 'react';
import {
  SafeAreaView,
  StatusBar,
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Alert,
  ScrollView,
  Modal,
  TextInput,
  FlatList,
} from 'react-native';
import BuscarRepuestos from '../../components/BuscarRepuestos';

interface RepuestoItem {
  id: string;
  nombre: string;
  proveedor: string;
  precio: number;
  stock: number;
  stockStatus: 'alto' | 'bajo' | 'agotado';
}

interface ChanguitoItem {
  id: string;
  nombre: string;
  precio: number;
}

const repuestosEjemplo: RepuestoItem[] = [
  {
    id: '1',
    nombre: 'Bujía NGK VW Gol 2010',
    proveedor: 'AutoPartes Central',
    precio: 2500,
    stock: 15,
    stockStatus: 'alto'
  },
  {
    id: '2',
    nombre: 'Buj<PERSON> VW Gol 2010',
    proveedor: 'Repuestos del Norte',
    precio: 2800,
    stock: 3,
    stockStatus: 'bajo'
  },
  {
    id: '3',
    nombre: 'Filtro de Aceite VW Gol',
    proveedor: 'AutoPartes Central',
    precio: 1200,
    stock: 25,
    stockStatus: 'alto'
  }
];

export default function DashboardTallerComun() {
  const [modalVisible, setModalVisible] = useState(false);
  const [buscarRepuestosVisible, setBuscarRepuestosVisible] = useState(false);
  const [busqueda, setBusqueda] = useState('');
  const [changuito, setChanguito] = useState<ChanguitoItem[]>([]);
  const [metodoPago, setMetodoPago] = useState('efectivo');

  const agregarAlChanguito = (repuesto: any) => {
    const nuevoItem: ChanguitoItem = {
      id: Date.now().toString(),
      nombre: repuesto.nombre,
      precio: repuesto.precio
    };

    setChanguito([...changuito, nuevoItem]);
    Alert.alert('✅ Agregado', `${repuesto.nombre} agregado al changuito!`);
    setBuscarRepuestosVisible(false);
  };

  const eliminarDelChanguito = (id: string) => {
    setChanguito(changuito.filter(item => item.id !== id));
  };

  const calcularTotal = () => {
    return changuito.reduce((total, item) => total + item.precio, 0);
  };

  const realizarPedido = () => {
    if (changuito.length === 0) {
      Alert.alert('❌ Error', 'El changuito está vacío');
      return;
    }

    const total = calcularTotal();
    Alert.alert(
      '🎉 ¡Pedido Realizado!',
      `Total: $${total.toLocaleString()}\nMétodo de pago: ${metodoPago}\nItems: ${changuito.length}\n\nEl proveedor será notificado y te contactará pronto.`,
      [
        {
          text: 'OK',
          onPress: () => {
            setChanguito([]);
            setModalVisible(false);
          }
        }
      ]
    );
  };

  const verCalificaciones = () => {
    Alert.alert(
      '⭐ Calificaciones Recibidas',
      'Aquí podrás ver:\n• Calificaciones de clientes\n• Comentarios y reseñas\n• Promedio general\n• Sugerencias de mejora\n\n¡Mantén tu reputación alta para más clientes!'
    );
  };

  const renderRepuesto = ({ item }: { item: RepuestoItem }) => (
    <View style={styles.repuestoItem}>
      <View style={styles.repuestoInfo}>
        <Text style={styles.repuestoNombre}>{item.nombre}</Text>
        <Text style={styles.repuestoProveedor}>Proveedor: {item.proveedor}</Text>
        <View style={[
          styles.stockBadge,
          { backgroundColor: item.stockStatus === 'alto' ? '#4CAF50' : '#FF9800' }
        ]}>
          <Text style={styles.stockText}>
            {item.stockStatus === 'alto' ? 'En Stock' : 'Stock Bajo'}: {item.stock} unidades
          </Text>
        </View>
      </View>
      <View style={styles.repuestoPrecio}>
        <Text style={styles.precio}>${item.precio.toLocaleString()}</Text>
        <TouchableOpacity
          style={styles.btnAgregar}
          onPress={() => agregarAlChanguito(item)}
        >
          <Text style={styles.btnAgregarText}>🛒 Agregar</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderChanguitoItem = ({ item }: { item: ChanguitoItem }) => (
    <View style={styles.changuitoItem}>
      <Text style={styles.changuitoNombre}>{item.nombre}</Text>
      <View style={styles.changuitoPrecio}>
        <Text style={styles.precio}>${item.precio.toLocaleString()}</Text>
        <TouchableOpacity
          style={styles.btnEliminar}
          onPress={() => eliminarDelChanguito(item.id)}
        >
          <Text style={styles.btnEliminarText}>🗑️</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#FF6B35" />

      {/* Header */}
      <View style={styles.header}>
        <View style={styles.logoContainer}>
          <Text style={styles.logoIcon}>🔧</Text>
          <Text style={styles.logoText}>
            <Text style={styles.logoRepu}>Repu</Text>
            <Text style={styles.logoMovil}>Movil</Text>
          </Text>

        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Mensaje de Bienvenida */}
        <View style={styles.welcomeSection}>
          <Text style={styles.welcomeTitle}>
            <Text style={styles.logoDestacado}>RepuMovil</Text>
          </Text>
          <Text style={styles.welcomeSubtitle}>
            Repuestos que llegan a tu taller, cuando los necesitas.
          </Text>
        </View>

        {/* Estadísticas Rápidas */}
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>0</Text>
            <Text style={styles.statLabel}>Pedidos Realizados</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>{changuito.length}</Text>
            <Text style={styles.statLabel}>Repuestos en Changuito</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>4.8</Text>
            <Text style={styles.statLabel}>Calificación Promedio</Text>
          </View>
        </View>

        {/* Dashboard Cards */}
        <View style={styles.cardsContainer}>
          {/* Pedido de Repuestos */}
          <TouchableOpacity
            style={[styles.card, styles.specialCard]}
            onPress={() => setBuscarRepuestosVisible(true)}
          >
            <Text style={styles.cardIcon}>📋</Text>
            <Text style={[styles.cardTitle, styles.specialCardText]}>Pedido de Repuestos</Text>
            <Text style={[styles.cardDescription, styles.specialCardText]}>
              Busca y solicita repuestos directamente a proveedores verificados.
              🛒 Agrega al changuito y realiza tu pedido.
            </Text>
          </TouchableOpacity>

          {/* Calificaciones Recibidas */}
          <TouchableOpacity style={styles.card} onPress={verCalificaciones}>
            <Text style={styles.cardIcon}>⭐</Text>
            <Text style={styles.cardTitle}>Calificaciones Recibidas</Text>
            <Text style={styles.cardDescription}>
              Revisa las calificaciones y comentarios de tus clientes.
              Mantén tu reputación y mejora tu servicio.
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* Modal de Pedido de Repuestos */}
      <Modal
        animationType="slide"
        transparent={false}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>🛒 Pedido de Repuestos</Text>
            <TouchableOpacity onPress={() => setModalVisible(false)}>
              <Text style={styles.closeButton}>✕</Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            {/* Búsqueda */}
            <View style={styles.searchSection}>
              <Text style={styles.sectionTitle}>🔍 Buscar Repuesto:</Text>
              <TextInput
                style={styles.searchInput}
                placeholder="Ej: Bujía para VW Gol 2010"
                value={busqueda}
                onChangeText={setBusqueda}
              />
              <Text style={styles.searchHint}>Busca por marca, modelo, año o tipo de repuesto</Text>
            </View>

            {/* Resultados */}
            <View style={styles.resultsSection}>
              <Text style={styles.sectionTitle}>Resultados encontrados:</Text>
              <FlatList
                data={repuestosEjemplo}
                renderItem={renderRepuesto}
                keyExtractor={(item) => item.id}
                scrollEnabled={false}
              />
            </View>

            {/* Changuito */}
            <View style={styles.changuitoSection}>
              <Text style={styles.sectionTitle}>🛒 Mi Changuito:</Text>
              {changuito.length === 0 ? (
                <Text style={styles.changuitoEmpty}>No hay items en el changuito</Text>
              ) : (
                <FlatList
                  data={changuito}
                  renderItem={renderChanguitoItem}
                  keyExtractor={(item) => item.id}
                  scrollEnabled={false}
                />
              )}
              <View style={styles.totalSection}>
                <Text style={styles.totalText}>Total: ${calcularTotal().toLocaleString()}</Text>
              </View>
            </View>

            {/* Método de Pago */}
            <View style={styles.paymentSection}>
              <Text style={styles.sectionTitle}>💳 Método de Pago:</Text>
              {['efectivo', 'transferencia', 'tarjeta', 'cuenta_corriente'].map((metodo) => (
                <TouchableOpacity
                  key={metodo}
                  style={styles.paymentOption}
                  onPress={() => setMetodoPago(metodo)}
                >
                  <View style={[
                    styles.radioButton,
                    metodoPago === metodo && styles.radioButtonSelected
                  ]} />
                  <Text style={styles.paymentText}>
                    {metodo === 'efectivo' && '💵 Efectivo'}
                    {metodo === 'transferencia' && '🏦 Transferencia'}
                    {metodo === 'tarjeta' && '💳 Tarjeta'}
                    {metodo === 'cuenta_corriente' && '📋 Cuenta Corriente'}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            {/* Botones */}
            <View style={styles.buttonSection}>
              <TouchableOpacity
                style={styles.btnCancelar}
                onPress={() => setModalVisible(false)}
              >
                <Text style={styles.btnCancelarText}>Cancelar</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.btnPedido}
                onPress={realizarPedido}
              >
                <Text style={styles.btnPedidoText}>✅ Realizar Pedido</Text>
              </TouchableOpacity>
            </View>
          </ScrollView>
        </SafeAreaView>
      </Modal>

      {/* Componente de Búsqueda de Repuestos */}
      <BuscarRepuestos
        visible={buscarRepuestosVisible}
        onClose={() => setBuscarRepuestosVisible(false)}
        onAgregarAlChanguito={agregarAlChanguito}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f7fa',
  },
  header: {
    backgroundColor: '#FF6B35',
    paddingVertical: 20,
    paddingHorizontal: 20,
    borderBottomLeftRadius: 25,
    borderBottomRightRadius: 25,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoIcon: {
    fontSize: 28,
    marginRight: 10,
  },
  logoText: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  logoRepu: {
    color: 'white',
  },
  logoMovil: {
    color: '#FFE4B5',
  },
  planBadge: {
    backgroundColor: 'white',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 15,
    marginLeft: 10,
  },
  planText: {
    color: '#FF6B35',
    fontWeight: 'bold',
    fontSize: 12,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  welcomeSection: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 25,
    marginVertical: 20,
    alignItems: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
  },
  welcomeTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
  },
  logoDestacado: {
    color: '#FF6B35',
    fontSize: 32,
    fontWeight: '900',
  },
  welcomeSubtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 20,
  },
  statItem: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 15,
    alignItems: 'center',
    flex: 1,
    marginHorizontal: 5,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FF6B35',
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
    marginTop: 5,
  },
  cardsContainer: {
    paddingBottom: 30,
  },
  card: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    marginBottom: 15,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    borderLeftWidth: 4,
    borderLeftColor: '#FF6B35',
  },
  specialCard: {
    backgroundColor: '#4CAF50',
    borderLeftColor: 'white',
  },
  cardIcon: {
    fontSize: 40,
    marginBottom: 10,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  cardDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  specialCardText: {
    color: 'white',
  },
  // Modal Styles
  modalContainer: {
    flex: 1,
    backgroundColor: '#f5f7fa',
  },
  modalHeader: {
    backgroundColor: '#4CAF50',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingTop: 40,
  },
  modalTitle: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
  },
  closeButton: {
    color: 'white',
    fontSize: 24,
    fontWeight: 'bold',
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  searchSection: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  searchInput: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 15,
    fontSize: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  searchHint: {
    fontSize: 12,
    color: '#666',
    marginTop: 5,
  },
  resultsSection: {
    marginBottom: 20,
  },
  repuestoItem: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 15,
    marginBottom: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  repuestoInfo: {
    flex: 1,
  },
  repuestoNombre: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  repuestoProveedor: {
    fontSize: 12,
    color: '#666',
    marginBottom: 5,
  },
  stockBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 10,
    alignSelf: 'flex-start',
  },
  stockText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
  },
  repuestoPrecio: {
    alignItems: 'flex-end',
    justifyContent: 'center',
  },
  precio: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#4CAF50',
    marginBottom: 5,
  },
  btnAgregar: {
    backgroundColor: '#2196F3',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
  },
  btnAgregarText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  changuitoSection: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 15,
    marginBottom: 20,
  },
  changuitoEmpty: {
    color: '#666',
    fontStyle: 'italic',
    textAlign: 'center',
    padding: 20,
  },
  changuitoItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  changuitoNombre: {
    flex: 1,
    fontSize: 14,
    color: '#333',
  },
  changuitoPrecio: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  btnEliminar: {
    marginLeft: 10,
    padding: 5,
  },
  btnEliminarText: {
    fontSize: 16,
  },
  totalSection: {
    borderTopWidth: 2,
    borderTopColor: '#FF6B35',
    paddingTop: 10,
    marginTop: 10,
  },
  totalText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FF6B35',
    textAlign: 'right',
  },
  paymentSection: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 15,
    marginBottom: 20,
  },
  paymentOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#ddd',
    marginRight: 10,
  },
  radioButtonSelected: {
    backgroundColor: '#FF6B35',
    borderColor: '#FF6B35',
  },
  paymentText: {
    fontSize: 14,
    color: '#333',
  },
  buttonSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingBottom: 20,
  },
  btnCancelar: {
    backgroundColor: '#666',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderRadius: 10,
    flex: 0.45,
  },
  btnCancelarText: {
    color: 'white',
    textAlign: 'center',
    fontWeight: 'bold',
  },
  btnPedido: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderRadius: 10,
    flex: 0.45,
  },
  btnPedidoText: {
    color: 'white',
    textAlign: 'center',
    fontWeight: 'bold',
  },
});
