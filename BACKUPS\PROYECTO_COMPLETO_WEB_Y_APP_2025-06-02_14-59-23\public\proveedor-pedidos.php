<?php
session_start();
require_once 'db_config.php';

// Verificar si el usuario está logueado y es proveedor
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'proveedor_repuestos') {
    header('Location: login-dinamico.php');
    exit();
}

$user_id = $_SESSION['user_id'];
$user_name = $_SESSION['user_name'] ?? 'Proveedor';

// Manejar acciones AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    switch ($_POST['action']) {
        case 'cambiar_estado':
            $pedido_id = $_POST['pedido_id'];
            $nuevo_estado = $_POST['nuevo_estado'];
            
            try {
                $stmt = $pdo->prepare("UPDATE pedidos SET estado = ? WHERE id = ? AND proveedor_id = ?");
                $stmt->execute([$nuevo_estado, $pedido_id, $user_id]);
                echo json_encode(['success' => true, 'message' => 'Estado actualizado correctamente']);
            } catch (PDOException $e) {
                echo json_encode(['success' => false, 'message' => 'Error al actualizar estado']);
            }
            exit();
            
        case 'asignar_repartidor':
            $pedido_id = $_POST['pedido_id'];
            $repartidor_id = $_POST['repartidor_id'];
            
            try {
                $stmt = $pdo->prepare("UPDATE pedidos SET repartidor_id = ?, estado = 'en_camino' WHERE id = ? AND proveedor_id = ?");
                $stmt->execute([$repartidor_id, $pedido_id, $user_id]);
                echo json_encode(['success' => true, 'message' => 'Repartidor asignado correctamente']);
            } catch (PDOException $e) {
                echo json_encode(['success' => false, 'message' => 'Error al asignar repartidor']);
            }
            exit();
    }
}

// Obtener pedidos del proveedor
try {
    $filtro_estado = $_GET['estado'] ?? 'todos';
    
    $sql = "
        SELECT p.*, 
               u.nombre as cliente_nombre, 
               u.telefono as cliente_telefono,
               u.direccion as cliente_direccion,
               u.user_type as cliente_tipo,
               r.nombre as repartidor_nombre,
               r.telefono as repartidor_telefono,
               r.vehiculo as repartidor_vehiculo,
               r.calificacion as repartidor_calificacion,
               r.ubicacion_actual as repartidor_ubicacion
        FROM pedidos p
        LEFT JOIN users u ON p.user_id = u.id
        LEFT JOIN repartidores r ON p.repartidor_id = r.id
        WHERE p.proveedor_id = ?
    ";
    
    if ($filtro_estado !== 'todos') {
        $sql .= " AND p.estado = ?";
    }
    
    $sql .= " ORDER BY p.fecha_pedido DESC";
    
    $stmt = $pdo->prepare($sql);
    if ($filtro_estado !== 'todos') {
        $stmt->execute([$user_id, $filtro_estado]);
    } else {
        $stmt->execute([$user_id]);
    }
    $pedidos = $stmt->fetchAll();
    
    // Obtener repartidores disponibles
    $stmt = $pdo->prepare("SELECT * FROM repartidores WHERE estado = 'disponible'");
    $stmt->execute();
    $repartidores_disponibles = $stmt->fetchAll();
    
    // Calcular estadísticas
    $estadisticas = [
        'nuevos' => 0,
        'en_preparacion' => 0,
        'en_camino' => 0,
        'entregados' => 0
    ];
    
    foreach ($pedidos as $pedido) {
        if (isset($estadisticas[$pedido['estado']])) {
            $estadisticas[$pedido['estado']]++;
        }
    }
    
} catch (PDOException $e) {
    $error_message = "Error al cargar pedidos: " . $e->getMessage();
    $pedidos = [];
    $repartidores_disponibles = [];
}

// Función para obtener el color del estado
function getEstadoColor($estado) {
    switch ($estado) {
        case 'nuevo': return '#2196F3';
        case 'en_preparacion': return '#FF9800';
        case 'en_camino': return '#9C27B0';
        case 'entregado': return '#4CAF50';
        default: return '#666';
    }
}

// Función para obtener el texto del estado
function getEstadoTexto($estado) {
    switch ($estado) {
        case 'nuevo': return 'NUEVO';
        case 'en_preparacion': return 'EN PREPARACIÓN';
        case 'en_camino': return 'EN CAMINO';
        case 'entregado': return 'ENTREGADO';
        default: return strtoupper($estado);
    }
}

// Función para obtener el siguiente estado
function getSiguienteEstado($estado_actual) {
    switch ($estado_actual) {
        case 'nuevo': return 'en_preparacion';
        case 'en_preparacion': return 'en_camino';
        case 'en_camino': return 'entregado';
        default: return $estado_actual;
    }
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestión de Pedidos - RepuMovil</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            padding: 1.5rem 2rem;
            color: white;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .back-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            cursor: pointer;
            text-decoration: none;
            transition: background 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .logo-container {
            flex: 1;
            text-align: center;
        }

        .logo-text {
            font-size: 1.5rem;
            font-weight: 800;
        }

        .motivational-section {
            text-align: center;
            margin-top: 1rem;
        }

        .motivational-text {
            font-style: italic;
            opacity: 0.9;
            font-size: 0.9rem;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 800;
            color: #4CAF50;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #666;
            font-weight: 500;
        }

        .filters-container {
            margin-bottom: 2rem;
        }

        .filter-buttons {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .filter-btn {
            background: white;
            border: 1px solid #e2e8f0;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            cursor: pointer;
            text-decoration: none;
            color: #666;
            font-weight: 600;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .filter-btn.active,
        .filter-btn:hover {
            background: #4CAF50;
            color: white;
            border-color: #4CAF50;
        }

        .pedidos-container {
            display: grid;
            gap: 1.5rem;
        }

        .pedido-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border-left: 4px solid #4CAF50;
            transition: transform 0.3s ease;
            cursor: pointer;
        }

        .pedido-card:hover {
            transform: translateY(-2px);
        }

        .pedido-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .pedido-id {
            font-size: 0.8rem;
            color: #666;
            font-weight: 600;
        }

        .pedido-cliente {
            font-size: 1.1rem;
            font-weight: 700;
            color: #2D3748;
            margin-top: 0.2rem;
        }

        .estado-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 12px;
            color: white;
            font-size: 0.7rem;
            font-weight: 700;
            text-transform: uppercase;
        }

        .pedido-info {
            margin-bottom: 1rem;
        }

        .pedido-info p {
            margin-bottom: 0.3rem;
            color: #666;
            font-size: 0.9rem;
        }

        .pedido-total {
            font-size: 1.2rem;
            font-weight: 700;
            color: #4CAF50;
            margin: 0.5rem 0;
        }

        .repartidor-info {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
        }

        .pedido-actions {
            display: flex;
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.8rem;
            font-weight: 600;
            transition: all 0.3s ease;
            flex: 1;
        }

        .btn-contactar {
            background: #2196F3;
            color: white;
        }

        .btn-contactar:hover {
            background: #1976D2;
        }

        .btn-siguiente {
            background: #4CAF50;
            color: white;
        }

        .btn-siguiente:hover {
            background: #45a049;
        }

        .btn-asignar {
            background: #FF9800;
            color: white;
        }

        .btn-asignar:hover {
            background: #F57C00;
        }

        .heart-message {
            text-align: center;
            padding: 2rem;
            margin-top: 2rem;
        }

        .heart-text {
            color: rgba(255, 255, 255, 0.8);
            font-style: italic;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }
            
            .stats-container {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .filter-buttons {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- Header con gradiente -->
    <div class="header">
        <div class="header-content">
            <a href="dashboard-proveedor.php" class="back-btn">
                <i class="fas fa-arrow-left"></i> Volver
            </a>
            <div class="logo-container">
                <div class="logo-text">
                    📋 <span style="color: #ffffff;">Gestión de</span>
                    <span style="color: #FFF3E0;"> Pedidos</span>
                </div>
            </div>
        </div>

        <!-- Frase motivadora -->
        <div class="motivational-section">
            <div class="motivational-text">
                "Cada pedido es una oportunidad de hacer crecer tu negocio"
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Estadísticas rápidas -->
        <div class="stats-container">
            <div class="stat-card">
                <div class="stat-number"><?php echo $estadisticas['nuevos'] ?? 0; ?></div>
                <div class="stat-label">Nuevos</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $estadisticas['en_preparacion'] ?? 0; ?></div>
                <div class="stat-label">Preparando</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $estadisticas['en_camino'] ?? 0; ?></div>
                <div class="stat-label">En Camino</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $estadisticas['entregados'] ?? 0; ?></div>
                <div class="stat-label">Entregados</div>
            </div>
        </div>

        <!-- Filtros -->
        <div class="filters-container">
            <div class="filter-buttons">
                <a href="?estado=todos" class="filter-btn <?php echo ($filtro_estado === 'todos') ? 'active' : ''; ?>">
                    Todos
                </a>
                <a href="?estado=nuevo" class="filter-btn <?php echo ($filtro_estado === 'nuevo') ? 'active' : ''; ?>">
                    Nuevos
                </a>
                <a href="?estado=en_preparacion" class="filter-btn <?php echo ($filtro_estado === 'en_preparacion') ? 'active' : ''; ?>">
                    En Preparación
                </a>
                <a href="?estado=en_camino" class="filter-btn <?php echo ($filtro_estado === 'en_camino') ? 'active' : ''; ?>">
                    En Camino
                </a>
                <a href="?estado=entregado" class="filter-btn <?php echo ($filtro_estado === 'entregado') ? 'active' : ''; ?>">
                    Entregados
                </a>
            </div>
        </div>

        <!-- Lista de pedidos -->
        <div class="pedidos-container">
            <?php if (empty($pedidos)): ?>
                <div style="text-align: center; padding: 3rem; background: white; border-radius: 15px;">
                    <i class="fas fa-inbox" style="font-size: 3rem; color: #ccc; margin-bottom: 1rem;"></i>
                    <h3 style="color: #666;">No hay pedidos</h3>
                    <p style="color: #999;">No se encontraron pedidos con el filtro seleccionado.</p>
                </div>
            <?php else: ?>
                <?php foreach ($pedidos as $pedido): ?>
                    <div class="pedido-card" onclick="abrirDetallePedido(<?php echo htmlspecialchars(json_encode($pedido)); ?>)">
                        <div class="pedido-header">
                            <div>
                                <div class="pedido-id">#<?php echo $pedido['id']; ?></div>
                                <div class="pedido-cliente">
                                    <?php echo ($pedido['cliente_tipo'] === 'taller') ? '🏪' : '👨‍🔧'; ?>
                                    <?php echo htmlspecialchars($pedido['cliente_nombre'] ?? 'Cliente'); ?>
                                </div>
                            </div>
                            <div class="estado-badge" style="background-color: <?php echo getEstadoColor($pedido['estado']); ?>">
                                <?php echo getEstadoTexto($pedido['estado']); ?>
                            </div>
                        </div>

                        <div class="pedido-info">
                            <p><i class="fas fa-clock"></i> <?php echo date('H:i', strtotime($pedido['fecha_pedido'])); ?> - <?php echo date('d/m/Y', strtotime($pedido['fecha_pedido'])); ?></p>
                            <p><i class="fas fa-map-marker-alt"></i> <?php echo htmlspecialchars($pedido['cliente_direccion'] ?? 'Dirección no disponible'); ?></p>
                            <div class="pedido-total">💰 $<?php echo number_format($pedido['total'], 0, ',', '.'); ?></div>
                            <p><i class="fas fa-credit-card"></i> <?php echo ucfirst($pedido['metodo_pago'] ?? 'efectivo'); ?></p>
                        </div>

                        <?php if ($pedido['repartidor_nombre']): ?>
                            <div class="repartidor-info">
                                <strong>🚚 Repartidor Asignado:</strong><br>
                                <i class="fas fa-user"></i> <?php echo htmlspecialchars($pedido['repartidor_nombre']); ?>
                                <?php if ($pedido['repartidor_calificacion']): ?>
                                    - ⭐ <?php echo $pedido['repartidor_calificacion']; ?>
                                <?php endif; ?>
                                <br>
                                <?php if ($pedido['repartidor_ubicacion']): ?>
                                    <i class="fas fa-map-marker-alt"></i> <?php echo htmlspecialchars($pedido['repartidor_ubicacion']); ?>
                                <?php endif; ?>
                                <br>
                                <span style="color: #FF9800; font-weight: bold;">⏱️ Tiempo estimado: 20 min</span>
                            </div>
                        <?php endif; ?>

                        <div class="pedido-actions" onclick="event.stopPropagation();">
                            <button class="btn btn-contactar" onclick="contactarCliente('<?php echo $pedido['cliente_telefono']; ?>', '<?php echo htmlspecialchars($pedido['cliente_nombre']); ?>')">
                                <i class="fas fa-phone"></i> Contactar
                            </button>

                            <?php if (!$pedido['repartidor_nombre'] && $pedido['estado'] === 'en_preparacion'): ?>
                                <button class="btn btn-asignar" onclick="mostrarRepartidores(<?php echo $pedido['id']; ?>)">
                                    <i class="fas fa-truck"></i> Asignar Repartidor
                                </button>
                            <?php endif; ?>

                            <?php if ($pedido['estado'] !== 'entregado'): ?>
                                <button class="btn btn-siguiente" onclick="cambiarEstado(<?php echo $pedido['id']; ?>, '<?php echo getSiguienteEstado($pedido['estado']); ?>')">
                                    <i class="fas fa-check"></i> Siguiente
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>

        <!-- Mensaje con corazón -->
        <div class="heart-message">
            <div class="heart-text">
                Hecho con ❤️ para hacer crecer tu negocio
            </div>
        </div>
    </div>

    <!-- Modal para detalle del pedido -->
    <div id="detalleModal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5);">
        <div style="background-color: white; margin: 5% auto; padding: 2rem; border-radius: 15px; width: 90%; max-width: 600px; max-height: 80vh; overflow-y: auto;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.5rem; border-bottom: 1px solid #eee; padding-bottom: 1rem;">
                <h2 id="modalTitle" style="color: #333; margin: 0;">📋 Detalle del Pedido</h2>
                <span onclick="cerrarModal()" style="font-size: 24px; cursor: pointer; color: #666;">&times;</span>
            </div>
            <div id="modalContent">
                <!-- El contenido se carga dinámicamente -->
            </div>
        </div>
    </div>

    <!-- Modal para seleccionar repartidor -->
    <div id="repartidorModal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5);">
        <div style="background-color: white; margin: 10% auto; padding: 2rem; border-radius: 15px; width: 90%; max-width: 500px;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.5rem; border-bottom: 1px solid #eee; padding-bottom: 1rem;">
                <h3 style="color: #333; margin: 0;">🚚 Seleccionar Repartidor</h3>
                <span onclick="cerrarModalRepartidor()" style="font-size: 24px; cursor: pointer; color: #666;">&times;</span>
            </div>
            <div id="repartidoresContent">
                <?php foreach ($repartidores_disponibles as $repartidor): ?>
                    <div style="background: #f8f9fa; padding: 1rem; border-radius: 8px; margin-bottom: 0.5rem; cursor: pointer; border: 1px solid #e2e8f0;"
                         onclick="asignarRepartidor(currentPedidoId, <?php echo $repartidor['id']; ?>, '<?php echo htmlspecialchars($repartidor['nombre']); ?>')">
                        <strong>🚚 <?php echo htmlspecialchars($repartidor['nombre']); ?></strong>
                        <?php if ($repartidor['calificacion']): ?>
                            - ⭐ <?php echo $repartidor['calificacion']; ?>
                        <?php endif; ?>
                        <br>
                        <small style="color: #666;">
                            🚗 <?php echo htmlspecialchars($repartidor['vehiculo'] ?? 'Vehículo no especificado'); ?>
                            <?php if ($repartidor['ubicacion_actual']): ?>
                                <br>📍 <?php echo htmlspecialchars($repartidor['ubicacion_actual']); ?>
                            <?php endif; ?>
                        </small>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>

    <script>
        let currentPedidoId = null;

        function abrirDetallePedido(pedido) {
            document.getElementById('modalTitle').textContent = `📋 Detalle del Pedido #${pedido.id}`;

            const estadoColor = getEstadoColorJS(pedido.estado);
            const estadoTexto = getEstadoTextoJS(pedido.estado);

            let repartidorInfo = '';
            if (pedido.repartidor_nombre) {
                repartidorInfo = `
                    <div style="background: #f0f9ff; padding: 1rem; border-radius: 8px; margin: 1rem 0;">
                        <h4 style="color: #333; margin-bottom: 0.5rem;">🚚 Repartidor Asignado</h4>
                        <p style="margin: 0.2rem 0;"><i class="fas fa-user"></i> ${pedido.repartidor_nombre}</p>
                        <p style="margin: 0.2rem 0;"><i class="fas fa-phone"></i> ${pedido.repartidor_telefono || 'No disponible'}</p>
                        <p style="margin: 0.2rem 0;"><i class="fas fa-car"></i> ${pedido.repartidor_vehiculo || 'Vehículo no especificado'}</p>
                        ${pedido.repartidor_calificacion ? `<p style="margin: 0.2rem 0;">⭐ ${pedido.repartidor_calificacion}/5</p>` : ''}
                        ${pedido.repartidor_ubicacion ? `<p style="margin: 0.2rem 0;"><i class="fas fa-map-marker-alt"></i> ${pedido.repartidor_ubicacion}</p>` : ''}
                        <p style="color: #FF9800; font-weight: bold; margin: 0.5rem 0;">⏱️ Tiempo estimado: 20 min</p>
                    </div>
                `;
            } else {
                repartidorInfo = `
                    <div style="background: #fff3cd; padding: 1rem; border-radius: 8px; margin: 1rem 0; border-left: 4px solid #ffc107;">
                        <p style="margin: 0; color: #856404;"><strong>⚠️ Sin repartidor asignado</strong></p>
                        <p style="margin: 0.5rem 0 0 0; color: #856404; font-size: 0.9rem;">Asigna un repartidor para continuar con la entrega.</p>
                    </div>
                `;
            }

            const content = `
                <div style="margin-bottom: 1.5rem;">
                    <h4 style="color: #333; margin-bottom: 0.5rem;">👤 Cliente</h4>
                    <p style="margin: 0.2rem 0;">${pedido.cliente_tipo === 'taller' ? '🏪' : '👨‍🔧'} ${pedido.cliente_nombre || 'Cliente'}</p>
                    <p style="margin: 0.2rem 0;"><i class="fas fa-phone"></i> ${pedido.cliente_telefono || 'No disponible'}</p>
                    <p style="margin: 0.2rem 0;"><i class="fas fa-map-marker-alt"></i> ${pedido.cliente_direccion || 'Dirección no disponible'}</p>
                </div>

                <div style="margin-bottom: 1.5rem;">
                    <h4 style="color: #333; margin-bottom: 0.5rem;">📊 Estado del Pedido</h4>
                    <div style="background-color: ${estadoColor}; color: white; padding: 0.5rem 1rem; border-radius: 12px; display: inline-block; font-weight: bold; font-size: 0.9rem;">
                        ${estadoTexto}
                    </div>
                </div>

                ${repartidorInfo}

                <div style="margin-bottom: 1.5rem;">
                    <h4 style="color: #333; margin-bottom: 0.5rem;">ℹ️ Información del Pedido</h4>
                    <p style="margin: 0.2rem 0;"><i class="fas fa-clock"></i> Pedido realizado: ${new Date(pedido.fecha_pedido).toLocaleString('es-ES')}</p>
                    <p style="margin: 0.2rem 0;"><i class="fas fa-credit-card"></i> Método de pago: ${pedido.metodo_pago || 'efectivo'}</p>
                    <p style="margin: 0.2rem 0; font-size: 1.2rem; font-weight: bold; color: #4CAF50;"><i class="fas fa-dollar-sign"></i> Total: $${new Intl.NumberFormat('es-ES').format(pedido.total)}</p>
                    ${pedido.notas ? `<p style="margin: 0.2rem 0;"><i class="fas fa-sticky-note"></i> Notas: ${pedido.notas}</p>` : ''}
                </div>

                <div style="display: flex; gap: 0.5rem; margin-top: 1.5rem;">
                    <button onclick="contactarCliente('${pedido.cliente_telefono}', '${pedido.cliente_nombre}')"
                            style="flex: 1; background: #2196F3; color: white; border: none; padding: 0.8rem; border-radius: 8px; cursor: pointer; font-weight: 600;">
                        <i class="fas fa-phone"></i> Contactar Cliente
                    </button>
                    ${pedido.estado !== 'entregado' ? `
                        <button onclick="cambiarEstado(${pedido.id}, '${getSiguienteEstadoJS(pedido.estado)}'); cerrarModal();"
                                style="flex: 1; background: #4CAF50; color: white; border: none; padding: 0.8rem; border-radius: 8px; cursor: pointer; font-weight: 600;">
                            <i class="fas fa-check"></i> Cambiar Estado
                        </button>
                    ` : ''}
                </div>
            `;

            document.getElementById('modalContent').innerHTML = content;
            document.getElementById('detalleModal').style.display = 'block';
        }

        function cerrarModal() {
            document.getElementById('detalleModal').style.display = 'none';
        }

        function mostrarRepartidores(pedidoId) {
            currentPedidoId = pedidoId;
            document.getElementById('repartidorModal').style.display = 'block';
        }

        function cerrarModalRepartidor() {
            document.getElementById('repartidorModal').style.display = 'none';
            currentPedidoId = null;
        }

        function contactarCliente(telefono, nombre) {
            if (telefono && telefono !== 'No disponible') {
                if (confirm(`¿Llamar a ${nombre}?\n\nTeléfono: ${telefono}`)) {
                    window.open(`tel:${telefono}`, '_self');
                }
            } else {
                alert('Número de teléfono no disponible');
            }
        }

        function cambiarEstado(pedidoId, nuevoEstado) {
            if (confirm(`¿Cambiar el estado del pedido #${pedidoId} a "${getEstadoTextoJS(nuevoEstado)}"?`)) {
                const formData = new FormData();
                formData.append('action', 'cambiar_estado');
                formData.append('pedido_id', pedidoId);
                formData.append('nuevo_estado', nuevoEstado);

                fetch('proveedor-pedidos.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('✅ ' + data.message);
                        location.reload();
                    } else {
                        alert('❌ ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('❌ Error al cambiar el estado');
                });
            }
        }

        function asignarRepartidor(pedidoId, repartidorId, repartidorNombre) {
            if (confirm(`¿Asignar a ${repartidorNombre} para el pedido #${pedidoId}?`)) {
                const formData = new FormData();
                formData.append('action', 'asignar_repartidor');
                formData.append('pedido_id', pedidoId);
                formData.append('repartidor_id', repartidorId);

                fetch('proveedor-pedidos.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('✅ ' + data.message);
                        cerrarModalRepartidor();
                        location.reload();
                    } else {
                        alert('❌ ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('❌ Error al asignar repartidor');
                });
            }
        }

        function getEstadoColorJS(estado) {
            switch (estado) {
                case 'nuevo': return '#2196F3';
                case 'en_preparacion': return '#FF9800';
                case 'en_camino': return '#9C27B0';
                case 'entregado': return '#4CAF50';
                default: return '#666';
            }
        }

        function getEstadoTextoJS(estado) {
            switch (estado) {
                case 'nuevo': return 'NUEVO';
                case 'en_preparacion': return 'EN PREPARACIÓN';
                case 'en_camino': return 'EN CAMINO';
                case 'entregado': return 'ENTREGADO';
                default: return estado.toUpperCase();
            }
        }

        function getSiguienteEstadoJS(estadoActual) {
            switch (estadoActual) {
                case 'nuevo': return 'en_preparacion';
                case 'en_preparacion': return 'en_camino';
                case 'en_camino': return 'entregado';
                default: return estadoActual;
            }
        }

        // Cerrar modales al hacer clic fuera
        window.onclick = function(event) {
            const detalleModal = document.getElementById('detalleModal');
            const repartidorModal = document.getElementById('repartidorModal');

            if (event.target === detalleModal) {
                cerrarModal();
            }
            if (event.target === repartidorModal) {
                cerrarModalRepartidor();
            }
        }
    </script>
</body>
</html>
