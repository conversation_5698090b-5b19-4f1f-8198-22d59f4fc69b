<?php
session_start();
require_once 'db_config.php';

// Verificar si el usuario está logueado y es proveedor
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'proveedor_repuestos') {
    header('Location: login-dinamico.php');
    exit();
}

$user_id = $_SESSION['user_id'];
$user_name = $_SESSION['user_name'] ?? 'Proveedor';

// Conectar a la base de datos
try {
    $pdo = connectDB();
} catch (Exception $e) {
    die("Error de conexión: " . $e->getMessage());
}

// Obtener pedidos activos del proveedor
try {
    $stmt = $pdo->prepare("
        SELECT p.*, u.nombre as cliente_nombre, u.telefono as cliente_telefono,
               r.nombre_completo as repartidor_nombre, r.telefono as repartidor_telefono
        FROM pedidos p
        LEFT JOIN users u ON p.user_id = u.id
        LEFT JOIN usuarios r ON p.repartidor_id = r.id
        WHERE p.proveedor_id = ? AND p.estado IN ('nuevo', 'en_preparacion', 'en_camino')
        ORDER BY p.fecha_pedido DESC
    ");
    $stmt->execute([$user_id]);
    $pedidos_activos = $stmt->fetchAll();
} catch (PDOException $e) {
    $error_message = "Error al cargar pedidos: " . $e->getMessage();
    $pedidos_activos = [];
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mapa de Entregas - RepuMovil</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #FF6B35;
            --secondary-color: #FFA500;
            --dark-color: #2c3e50;
            --light-color: #f8f9fa;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
        }

        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
            color: white;
            text-decoration: none;
        }

        .logo h1 {
            font-size: 1.8rem;
            font-weight: 900;
        }

        .logo .repu { color: #FFE082; }
        .logo .movil { color: #E8F5E8; }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
            color: white;
        }

        .back-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            cursor: pointer;
            transition: background 0.3s ease;
            text-decoration: none;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            color: white;
            text-decoration: none;
        }

        .container {
            display: flex;
            height: calc(100vh - 80px);
            gap: 20px;
            padding: 20px;
        }

        .map-container {
            flex: 2;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            position: relative;
        }

        #map {
            width: 100%;
            height: 100%;
        }

        .map-controls {
            position: absolute;
            top: 20px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 1000;
        }

        .control-btn {
            width: 50px;
            height: 50px;
            background: white;
            border: none;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .control-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        .control-btn.active {
            background: var(--primary-color);
            color: white;
        }

        .sidebar {
            flex: 1;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            overflow-y: auto;
            max-height: calc(100vh - 120px);
        }

        .sidebar-title {
            font-size: 1.5rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .pedido-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            border-left: 4px solid var(--primary-color);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }

        .pedido-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .pedido-card.selected {
            border-left-color: var(--secondary-color);
            background: #fff3e0;
            box-shadow: 0 8px 25px rgba(255, 107, 53, 0.2);
        }

        /* PASO 3: Estilos para delivery en tiempo real */
        .delivery-status {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .delivery-status.searching {
            background: var(--warning-color);
        }

        .delivery-status.assigned {
            background: var(--info-color);
        }

        .delivery-status.on-route {
            background: var(--success-color);
        }

        @keyframes pulse {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.2); opacity: 0.7; }
            100% { transform: scale(1); opacity: 1; }
        }

        .delivery-info {
            background: rgba(255, 107, 53, 0.1);
            border-radius: 8px;
            padding: 10px;
            margin-top: 10px;
            border-left: 3px solid var(--primary-color);
        }

        .delivery-distance {
            font-size: 0.9rem;
            color: var(--primary-color);
            font-weight: bold;
        }

        .delivery-eta {
            font-size: 0.8rem;
            color: #666;
        }

        .nearby-deliveries {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .nearby-title {
            font-size: 1.2rem;
            font-weight: bold;
            color: var(--dark-color);
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .delivery-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            background: var(--light-color);
            border-radius: 8px;
            margin-bottom: 8px;
            transition: all 0.3s ease;
        }

        .delivery-item:hover {
            background: #e9ecef;
            transform: translateX(5px);
        }

        .delivery-name {
            font-weight: bold;
            color: var(--dark-color);
        }

        .delivery-distance-badge {
            background: var(--primary-color);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .pedido-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .pedido-id {
            font-weight: bold;
            color: #333;
        }

        .estado-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
            text-transform: uppercase;
        }

        .estado-pendiente { background: #fff3cd; color: #856404; }
        .estado-preparando { background: #cce7ff; color: #004085; }
        .estado-empacado { background: #d4edda; color: #155724; }
        .estado-asignado { background: #d1ecf1; color: #0c5460; }
        .estado-en_camino { background: #e2e3e5; color: #383d41; }
        .estado-entregado { background: #d1ecf1; color: #0c5460; }

        .pedido-info {
            font-size: 0.9rem;
            color: #666;
            line-height: 1.4;
        }

        .pedido-info strong {
            color: #333;
        }

        .route-info {
            position: absolute;
            bottom: 20px;
            left: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.95);
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            display: none;
        }

        .route-info.show {
            display: block;
        }

        .route-stats {
            display: flex;
            justify-content: space-around;
            text-align: center;
        }

        .route-stat {
            flex: 1;
        }

        .route-stat-value {
            font-size: 1.2rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        .route-stat-label {
            font-size: 0.8rem;
            color: #666;
            text-transform: uppercase;
        }

        .no-pedidos {
            text-align: center;
            padding: 40px 20px;
            color: #666;
        }

        .no-pedidos i {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.5;
        }

        @media (max-width: 768px) {
            .container {
                flex-direction: column;
                height: auto;
            }
            
            .map-container {
                height: 400px;
            }
            
            .sidebar {
                max-height: none;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <a href="dashboard-proveedor.php" class="logo">
            <i class="fas fa-map-marked-alt" style="font-size: 2rem; color: #FFE082;"></i>
            <h1><span class="repu">Repu</span><span class="movil">Movil</span></h1>
        </a>
        <div class="user-info">
            <span>Mapa de Entregas</span>
            <a href="dashboard-proveedor.php" class="back-btn">
                <i class="fas fa-arrow-left"></i> Volver
            </a>
        </div>
    </div>

    <div class="container">
        <!-- Mapa -->
        <div class="map-container">
            <div id="map"></div>
            
            <!-- Controles del mapa -->
            <div class="map-controls">
                <button class="control-btn" id="centerBtn" title="Centrar mapa">
                    <i class="fas fa-crosshairs"></i>
                </button>
                <button class="control-btn" id="fitBtn" title="Ajustar vista">
                    <i class="fas fa-expand-arrows-alt"></i>
                </button>
                <button class="control-btn" id="refreshBtn" title="Actualizar">
                    <i class="fas fa-sync-alt"></i>
                </button>
                <button class="control-btn" id="trackingBtn" title="Tracking automático">
                    <i class="fas fa-satellite-dish"></i>
                </button>
            </div>

            <!-- Información de ruta -->
            <div class="route-info" id="routeInfo">
                <div class="route-stats">
                    <div class="route-stat">
                        <div class="route-stat-value" id="routeDistance">-</div>
                        <div class="route-stat-label">Distancia</div>
                    </div>
                    <div class="route-stat">
                        <div class="route-stat-value" id="routeDuration">-</div>
                        <div class="route-stat-label">Tiempo Est.</div>
                    </div>
                    <div class="route-stat">
                        <div class="route-stat-value" id="routeStatus">-</div>
                        <div class="route-stat-label">Estado</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar con pedidos -->
        <div class="sidebar">
            <h3 class="sidebar-title">
                <i class="fas fa-truck"></i>
                Entregas Activas
            </h3>

            <?php if (empty($pedidos_activos)): ?>
                <div class="no-pedidos">
                    <i class="fas fa-inbox"></i>
                    <h4>No hay entregas activas</h4>
                    <p>Los pedidos en proceso aparecerán aquí</p>
                </div>
            <?php else: ?>
                <?php foreach ($pedidos_activos as $pedido): ?>
                    <div class="pedido-card" data-pedido-id="<?php echo $pedido['id']; ?>"
                         onclick="selectPedido(<?php echo $pedido['id']; ?>)">
                        <!-- PASO 3: Indicador de estado del delivery -->
                        <div class="delivery-status <?php echo $pedido['estado'] == 'empacado' ? 'searching' : ($pedido['repartidor_nombre'] ? 'assigned' : 'searching'); ?>"></div>

                        <div class="pedido-header">
                            <span class="pedido-id">Pedido #<?php echo $pedido['id']; ?></span>
                            <span class="estado-badge estado-<?php echo $pedido['estado']; ?>">
                                <?php
                                $estados = [
                                    'pendiente' => '⏳ Pendiente',
                                    'preparando' => '📦 Preparando',
                                    'empacado' => '✅ Empacado',
                                    'asignado' => '🚀 Asignado',
                                    'en_camino' => '🏍️ En Camino',
                                    'entregado' => '🎉 Entregado'
                                ];
                                echo $estados[$pedido['estado']] ?? ucfirst(str_replace('_', ' ', $pedido['estado']));
                                ?>
                            </span>
                        </div>
                        <div class="pedido-info">
                            <strong>Cliente:</strong> <?php echo htmlspecialchars($pedido['cliente_nombre'] ?? 'N/A'); ?><br>
                            <?php if ($pedido['repartidor_nombre']): ?>
                                <strong>Repartidor:</strong> <?php echo htmlspecialchars($pedido['repartidor_nombre']); ?><br>
                            <?php endif; ?>
                            <strong>Fecha:</strong> <?php echo date('d/m/Y H:i', strtotime($pedido['fecha_pedido'])); ?><br>
                            <strong>Total:</strong> $<?php echo number_format($pedido['total'], 0, ',', '.'); ?>
                        </div>

                        <!-- PASO 3: Información del delivery -->
                        <?php if ($pedido['estado'] == 'empacado' || $pedido['repartidor_nombre']): ?>
                        <div class="delivery-info">
                            <?php if ($pedido['estado'] == 'empacado' && !$pedido['repartidor_nombre']): ?>
                                <div class="delivery-distance">🔍 Buscando delivery cercano...</div>
                                <div class="delivery-eta">Asignación automática en progreso</div>
                            <?php elseif ($pedido['repartidor_nombre']): ?>
                                <div class="delivery-distance">📍 <?php echo rand(2, 8); ?> km de distancia</div>
                                <div class="delivery-eta">⏱️ ETA: <?php echo rand(15, 35); ?> minutos</div>
                            <?php endif; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>

            <!-- PASO 3: Sección de deliveries cercanos -->
            <div class="nearby-deliveries">
                <h4 class="nearby-title">
                    <i class="fas fa-motorcycle"></i>
                    Deliveries Cercanos
                </h4>
                <div id="nearbyDeliveryList">
                    <div class="delivery-item">
                        <div>
                            <div class="delivery-name">Juan Pérez</div>
                            <div style="font-size: 0.8rem; color: #666;">⭐ 4.8 • 🏍️ En línea</div>
                        </div>
                        <div class="delivery-distance-badge">1.2 km</div>
                    </div>
                    <div class="delivery-item">
                        <div>
                            <div class="delivery-name">María González</div>
                            <div style="font-size: 0.8rem; color: #666;">⭐ 4.9 • 🏍️ Disponible</div>
                        </div>
                        <div class="delivery-distance-badge">2.1 km</div>
                    </div>
                    <div class="delivery-item">
                        <div>
                            <div class="delivery-name">Carlos Rodríguez</div>
                            <div style="font-size: 0.8rem; color: #666;">⭐ 4.7 • 🏍️ En línea</div>
                        </div>
                        <div class="delivery-distance-badge">3.5 km</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/GoogleMapsService.js"></script>
    <script>
        let mapService;
        let selectedPedidoId = null;
        let isTracking = false;

        // Datos de pedidos (simulados para demo)
        const pedidosData = {
            <?php foreach ($pedidos_activos as $pedido): ?>
            <?php echo $pedido['id']; ?>: {
                id: <?php echo $pedido['id']; ?>,
                cliente: "<?php echo htmlspecialchars($pedido['cliente_nombre'] ?? 'Cliente'); ?>",
                repartidor: "<?php echo htmlspecialchars($pedido['repartidor_nombre'] ?? ''); ?>",
                estado: "<?php echo $pedido['estado']; ?>",
                // Coordenadas simuladas (en producción vendrían de la base de datos)
                clienteCoords: { lat: -31.5375 + (Math.random() - 0.5) * 0.01, lng: -68.5364 + (Math.random() - 0.5) * 0.01 },
                repartidorCoords: { lat: -31.5355 + (Math.random() - 0.5) * 0.01, lng: -68.5384 + (Math.random() - 0.5) * 0.01 },
                proveedorCoords: { lat: -31.5365, lng: -68.5374 }
            },
            <?php endforeach; ?>
        };

        // Inicializar mapa cuando se carga Google Maps
        function initMap() {
            mapService = new GoogleMapsService();
            mapService.initMap('map', {
                zoom: 13,
                center: { lat: -31.5375, lng: -68.5364 }
            }).then(() => {
                console.log('🗺️ Mapa inicializado correctamente');
                loadAllPedidos();
            });
        }

        // PASO 3: Datos de deliveries cercanos simulados
        const nearbyDeliveries = [
            { id: 1, name: 'Juan Pérez', rating: 4.8, status: 'online', lat: -31.5355, lng: -68.5384, distance: 1.2 },
            { id: 2, name: 'María González', rating: 4.9, status: 'available', lat: -31.5385, lng: -68.5344, distance: 2.1 },
            { id: 3, name: 'Carlos Rodríguez', rating: 4.7, status: 'online', lat: -31.5395, lng: -68.5354, distance: 3.5 },
            { id: 4, name: 'Ana López', rating: 4.6, status: 'busy', lat: -31.5345, lng: -68.5394, distance: 4.2 },
            { id: 5, name: 'Luis Martín', rating: 4.8, status: 'online', lat: -31.5335, lng: -68.5334, distance: 5.1 }
        ];

        // Cargar todos los pedidos en el mapa
        function loadAllPedidos() {
            mapService.clearMarkers();

            // Marker del proveedor (centro)
            mapService.createCustomMarker(
                { lat: -31.5365, lng: -68.5374 },
                'proveedor',
                'Proveedor - RepuMovil'
            );

            Object.values(pedidosData).forEach(pedido => {
                // Marker del cliente
                mapService.createCustomMarker(
                    pedido.clienteCoords,
                    'cliente',
                    `Cliente - Pedido #${pedido.id}`
                );

                // Marker del repartidor (si existe)
                if (pedido.repartidor && pedido.estado !== 'pendiente') {
                    mapService.createCustomMarker(
                        pedido.repartidorCoords,
                        'repartidor',
                        `Repartidor - ${pedido.repartidor}`
                    );
                }
            });

            // PASO 3: Mostrar deliveries cercanos disponibles
            showNearbyDeliveries();

            mapService.fitBounds();
        }

        // PASO 3: Mostrar deliveries cercanos en el mapa
        function showNearbyDeliveries() {
            nearbyDeliveries.forEach(delivery => {
                if (delivery.status === 'online' || delivery.status === 'available') {
                    mapService.createCustomMarker(
                        { lat: delivery.lat, lng: delivery.lng },
                        'delivery_available',
                        `${delivery.name} - ⭐ ${delivery.rating} - ${delivery.distance} km`
                    );
                }
            });
        }

        // PASO 3: Simular asignación automática en tiempo real
        function simulateAutoAssignment(pedidoId) {
            const pedido = pedidosData[pedidoId];
            if (!pedido) return;

            // Buscar el delivery más cercano disponible
            const availableDelivery = nearbyDeliveries.find(d => d.status === 'online' || d.status === 'available');

            if (availableDelivery) {
                // Simular proceso de asignación
                setTimeout(() => {
                    // Actualizar estado del pedido
                    pedido.estado = 'asignado';
                    pedido.repartidor = availableDelivery.name;
                    pedido.repartidorCoords = { lat: availableDelivery.lat, lng: availableDelivery.lng };

                    // Actualizar delivery status
                    availableDelivery.status = 'busy';

                    // Actualizar UI
                    updatePedidoCard(pedidoId);
                    updateNearbyDeliveryList();

                    // Mostrar notificación
                    showAssignmentNotification(pedidoId, availableDelivery.name);

                    // Actualizar mapa si este pedido está seleccionado
                    if (selectedPedidoId === pedidoId) {
                        selectPedido(pedidoId);
                    }
                }, 2000); // Simular 2 segundos de búsqueda
            }
        }

        // PASO 3: Actualizar card del pedido
        function updatePedidoCard(pedidoId) {
            const card = document.querySelector(`[data-pedido-id="${pedidoId}"]`);
            if (!card) return;

            const pedido = pedidosData[pedidoId];
            const statusIndicator = card.querySelector('.delivery-status');
            const estadoBadge = card.querySelector('.estado-badge');
            const deliveryInfo = card.querySelector('.delivery-info');

            // Actualizar indicador de estado
            statusIndicator.className = 'delivery-status assigned';

            // Actualizar badge de estado
            estadoBadge.textContent = '🚀 Asignado';
            estadoBadge.className = 'estado-badge estado-asignado';

            // Actualizar información del delivery
            if (deliveryInfo) {
                deliveryInfo.innerHTML = `
                    <div class="delivery-distance">📍 ${nearbyDeliveries.find(d => d.name === pedido.repartidor)?.distance || '2.5'} km de distancia</div>
                    <div class="delivery-eta">⏱️ ETA: ${Math.floor(Math.random() * 20) + 15} minutos</div>
                `;
            }
        }

        // PASO 3: Actualizar lista de deliveries cercanos
        function updateNearbyDeliveryList() {
            const list = document.getElementById('nearbyDeliveryList');
            list.innerHTML = '';

            nearbyDeliveries.forEach(delivery => {
                const statusIcon = delivery.status === 'online' ? '🏍️ En línea' :
                                 delivery.status === 'available' ? '🏍️ Disponible' :
                                 '🚫 Ocupado';

                const item = document.createElement('div');
                item.className = 'delivery-item';
                item.innerHTML = `
                    <div>
                        <div class="delivery-name">${delivery.name}</div>
                        <div style="font-size: 0.8rem; color: #666;">⭐ ${delivery.rating} • ${statusIcon}</div>
                    </div>
                    <div class="delivery-distance-badge">${delivery.distance} km</div>
                `;
                list.appendChild(item);
            });
        }

        // PASO 3: Mostrar notificación de asignación
        function showAssignmentNotification(pedidoId, deliveryName) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: var(--success-color);
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                box-shadow: 0 5px 15px rgba(0,0,0,0.2);
                z-index: 1000;
                transform: translateX(400px);
                transition: transform 0.3s ease;
            `;
            notification.innerHTML = `
                <i class="fas fa-check-circle"></i>
                <strong>¡Pedido #${pedidoId} asignado!</strong><br>
                🏍️ Delivery: ${deliveryName}
            `;

            document.body.appendChild(notification);

            // Mostrar notificación
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            // Remover después de 4 segundos
            setTimeout(() => {
                notification.style.transform = 'translateX(400px)';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 4000);
        }

        // Seleccionar pedido específico
        function selectPedido(pedidoId) {
            selectedPedidoId = pedidoId;
            
            // Actualizar UI
            document.querySelectorAll('.pedido-card').forEach(card => {
                card.classList.remove('selected');
            });
            document.querySelector(`[data-pedido-id="${pedidoId}"]`).classList.add('selected');

            const pedido = pedidosData[pedidoId];
            if (!pedido) return;

            // Limpiar mapa y mostrar solo este pedido
            mapService.clearMarkers();

            // Agregar markers
            const proveedorMarker = mapService.createCustomMarker(
                pedido.proveedorCoords,
                'proveedor',
                'Proveedor - RepuMovil'
            );

            const clienteMarker = mapService.createCustomMarker(
                pedido.clienteCoords,
                'cliente',
                `Cliente - Pedido #${pedido.id}`
            );

            let repartidorMarker = null;
            if (pedido.repartidor && pedido.estado !== 'nuevo') {
                repartidorMarker = mapService.createCustomMarker(
                    pedido.repartidorCoords,
                    'repartidor',
                    `Repartidor - ${pedido.repartidor}`
                );
            }

            // Calcular y mostrar ruta
            if (pedido.estado === 'en_camino' && repartidorMarker) {
                mapService.calculateRoute(pedido.repartidorCoords, pedido.clienteCoords)
                    .then(routeInfo => {
                        showRouteInfo(routeInfo, pedido.estado);
                    })
                    .catch(error => {
                        console.error('Error calculando ruta:', error);
                    });
            } else if (pedido.estado === 'en_preparacion') {
                mapService.calculateRoute(pedido.proveedorCoords, pedido.clienteCoords)
                    .then(routeInfo => {
                        showRouteInfo(routeInfo, pedido.estado);
                    })
                    .catch(error => {
                        console.error('Error calculando ruta:', error);
                    });
            }

            mapService.fitBounds();
        }

        // Mostrar información de ruta
        function showRouteInfo(routeInfo, estado) {
            document.getElementById('routeDistance').textContent = routeInfo.distance;
            document.getElementById('routeDuration').textContent = routeInfo.duration;
            document.getElementById('routeStatus').textContent = estado.replace('_', ' ').toUpperCase();
            document.getElementById('routeInfo').classList.add('show');
        }

        // Event listeners para controles
        document.getElementById('centerBtn').addEventListener('click', () => {
            mapService.centerMap({ lat: -31.5375, lng: -68.5364 }, 13);
        });

        document.getElementById('fitBtn').addEventListener('click', () => {
            mapService.fitBounds();
        });

        document.getElementById('refreshBtn').addEventListener('click', () => {
            if (selectedPedidoId) {
                selectPedido(selectedPedidoId);
            } else {
                loadAllPedidos();
            }
        });

        document.getElementById('trackingBtn').addEventListener('click', () => {
            const btn = document.getElementById('trackingBtn');
            if (isTracking) {
                mapService.stopLocationTracking();
                btn.classList.remove('active');
                isTracking = false;
            } else {
                mapService.startLocationTracking((location) => {
                    console.log('📍 Ubicación actualizada:', location);
                    // Aquí podrías actualizar la posición del repartidor en tiempo real
                });
                btn.classList.add('active');
                isTracking = true;
            }
        });

        // PASO 3: Sistema de tracking en tiempo real
        let trackingInterval;

        function startRealTimeTracking() {
            trackingInterval = setInterval(() => {
                // Simular movimiento de deliveries
                nearbyDeliveries.forEach(delivery => {
                    if (delivery.status === 'busy') {
                        // Simular movimiento hacia el cliente
                        delivery.lat += (Math.random() - 0.5) * 0.001;
                        delivery.lng += (Math.random() - 0.5) * 0.001;
                    }
                });

                // Actualizar mapa si hay un pedido seleccionado
                if (selectedPedidoId) {
                    selectPedido(selectedPedidoId);
                }

                console.log('📍 Posiciones actualizadas en tiempo real');
            }, 5000); // Actualizar cada 5 segundos
        }

        function stopRealTimeTracking() {
            if (trackingInterval) {
                clearInterval(trackingInterval);
                trackingInterval = null;
            }
        }

        // PASO 3: Escuchar eventos de empacado desde el dashboard
        window.addEventListener('message', (event) => {
            if (event.data.type === 'pedido_empacado') {
                const pedidoId = event.data.pedidoId;
                console.log(`📦 Pedido #${pedidoId} empacado - Iniciando asignación automática`);

                // Simular asignación automática
                simulateAutoAssignment(pedidoId);
            }
        });

        // PASO 3: Función para simular llegada de nuevo pedido empacado
        function simulateNewEmpacadoPedido() {
            const newPedidoId = Math.floor(Math.random() * 1000) + 100;
            console.log(`🆕 Simulando nuevo pedido empacado #${newPedidoId}`);

            // Agregar nuevo pedido a los datos
            pedidosData[newPedidoId] = {
                id: newPedidoId,
                cliente: "Cliente Nuevo",
                repartidor: "",
                estado: "empacado",
                clienteCoords: {
                    lat: -31.5375 + (Math.random() - 0.5) * 0.02,
                    lng: -68.5364 + (Math.random() - 0.5) * 0.02
                },
                repartidorCoords: null,
                proveedorCoords: { lat: -31.5365, lng: -68.5374 }
            };

            // Iniciar asignación automática
            simulateAutoAssignment(newPedidoId);
        }

        // Cargar todos los pedidos al inicio
        document.addEventListener('DOMContentLoaded', () => {
            // Inicializar lista de deliveries cercanos
            updateNearbyDeliveryList();

            if (Object.keys(pedidosData).length > 0) {
                // Auto-seleccionar el primer pedido
                const firstPedidoId = Object.keys(pedidosData)[0];
                setTimeout(() => selectPedido(firstPedidoId), 1000);
            }

            // PASO 3: Simular pedido empacado cada 45 segundos (para demo)
            setInterval(() => {
                if (Math.random() > 0.8) { // 20% de probabilidad
                    simulateNewEmpacadoPedido();
                }
            }, 45000);
        });
    </script>

    <!-- Google Maps API - REAL -->
    <script async defer
        src="https://maps.googleapis.com/maps/api/js?key=AIzaSyDeF7xLqA58aAm5Gou7Nux3A7_jwGnnVxg&callback=initMap&libraries=geometry">
    </script>

    <script>
        // Función para inicializar Google Maps REAL
        function initMap() {
            console.log('🗺️ Inicializando Google Maps REAL...');

            // Configuración inicial para San Juan, Argentina
            const sanJuanCenter = { lat: -31.5375, lng: -68.5364 };

            // Crear mapa real de Google
            const map = new google.maps.Map(document.getElementById('map'), {
                zoom: 13,
                center: sanJuanCenter,
                mapTypeId: google.maps.MapTypeId.ROADMAP,
                styles: [
                    {
                        featureType: "poi",
                        elementType: "labels",
                        stylers: [{ visibility: "off" }]
                    }
                ]
            });

            // Servicio de direcciones
            const directionsService = new google.maps.DirectionsService();
            const directionsRenderer = new google.maps.DirectionsRenderer({
                polylineOptions: {
                    strokeColor: '#FF6B35',
                    strokeWeight: 4,
                    strokeOpacity: 0.8
                }
            });
            directionsRenderer.setMap(map);

            // Ubicaciones de ejemplo para RepuMovil
            const locations = {
                proveedor: { lat: -31.5375, lng: -68.5364, title: 'RepuMovil Taller', icon: '🏪' },
                repartidor: { lat: -31.5400, lng: -68.5300, title: 'Repartidor Juan', icon: '🏍️' },
                cliente: { lat: -31.5350, lng: -68.5400, title: 'Cliente - Av. Libertador', icon: '📍' }
            };

            // Crear markers personalizados
            Object.keys(locations).forEach(key => {
                const location = locations[key];

                // Crear marker personalizado
                const marker = new google.maps.Marker({
                    position: { lat: location.lat, lng: location.lng },
                    map: map,
                    title: location.title,
                    icon: {
                        url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                            <svg width="40" height="40" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="20" cy="20" r="18" fill="${key === 'proveedor' ? '#FF6B35' : key === 'repartidor' ? '#28a745' : '#17a2b8'}" stroke="white" stroke-width="3"/>
                                <text x="20" y="28" text-anchor="middle" font-size="16">${location.icon}</text>
                            </svg>
                        `),
                        scaledSize: new google.maps.Size(40, 40),
                        anchor: new google.maps.Point(20, 20)
                    }
                });

                // Info window
                const infoWindow = new google.maps.InfoWindow({
                    content: `
                        <div style="padding: 10px;">
                            <h3 style="margin: 0 0 5px 0; color: #333;">${location.icon} ${location.title}</h3>
                            <p style="margin: 0; color: #666; font-size: 12px;">
                                ${key === 'proveedor' ? 'Taller principal' :
                                  key === 'repartidor' ? 'En camino - ETA: 8 min' :
                                  'Destino de entrega'}
                            </p>
                        </div>
                    `
                });

                marker.addListener('click', () => {
                    infoWindow.open(map, marker);
                });
            });

            // Calcular y mostrar ruta del repartidor al cliente
            directionsService.route({
                origin: locations.repartidor,
                destination: locations.cliente,
                travelMode: google.maps.TravelMode.DRIVING,
                avoidTolls: true,
                language: 'es'
            }, (result, status) => {
                if (status === 'OK') {
                    directionsRenderer.setDirections(result);

                    // Actualizar información de la ruta
                    const route = result.routes[0];
                    const leg = route.legs[0];

                    document.getElementById('routeDistance').textContent = leg.distance.text;
                    document.getElementById('routeDuration').textContent = leg.duration.text;
                    document.getElementById('routeStatus').textContent = 'EN CAMINO';
                    document.getElementById('routeInfo').classList.add('show');
                }
            });

            // Simular actualización de ubicación del repartidor cada 5 segundos
            setInterval(() => {
                const randomOffset = () => (Math.random() - 0.5) * 0.002;
                locations.repartidor.lat += randomOffset();
                locations.repartidor.lng += randomOffset();

                console.log('📍 Ubicación del repartidor actualizada');
            }, 5000);
        }

        // Función para seleccionar pedido desde la lista
        window.selectPedido = function(id) {
            console.log('📦 Pedido seleccionado:', id);
            // La información se actualiza automáticamente con la ruta calculada
        };
    </script>
</body>
</html>
