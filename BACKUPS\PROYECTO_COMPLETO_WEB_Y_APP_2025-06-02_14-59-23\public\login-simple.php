<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RepuMovil - Login Simple</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #FF6B35 0%, #FFA500 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
        }

        .login-container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
            max-width: 400px;
            width: 100%;
        }

        .logo {
            color: #FF6B35;
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .subtitle {
            color: #666;
            margin-bottom: 30px;
        }

        .user-option {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .user-option:hover {
            border-color: #FF6B35;
            background: #fff5f0;
        }

        .user-option.selected {
            border-color: #FF6B35;
            background: #fff5f0;
        }

        .user-title {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .user-desc {
            color: #666;
            font-size: 0.9rem;
        }

        .login-btn {
            background: #FF6B35;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            width: 100%;
            margin-top: 20px;
        }

        .login-btn:hover {
            background: #e55a2b;
        }

        .login-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">🔧 RepuMovil</div>
        <p class="subtitle">Login Rápido - Selecciona tu tipo de usuario</p>

        <div class="user-option" onclick="selectUser('taller')">
            <div class="user-title">🔧 Taller Mecánico</div>
            <div class="user-desc">Buscar y comprar repuestos</div>
        </div>

        <div class="user-option" onclick="selectUser('proveedor')">
            <div class="user-title">🏪 Proveedor</div>
            <div class="user-desc">Vender repuestos a talleres</div>
        </div>

        <div class="user-option" onclick="selectUser('admin')">
            <div class="user-title">👨‍💼 Administrador</div>
            <div class="user-desc">Gestionar el sistema</div>
        </div>

        <button class="login-btn" onclick="login()" id="loginBtn" disabled>
            Iniciar Sesión
        </button>
    </div>

    <script>
        let selectedUserType = null;

        function selectUser(type) {
            // Remover selección anterior
            document.querySelectorAll('.user-option').forEach(option => {
                option.classList.remove('selected');
            });

            // Seleccionar nuevo
            event.target.closest('.user-option').classList.add('selected');
            selectedUserType = type;

            // Habilitar botón
            document.getElementById('loginBtn').disabled = false;
        }

        function login() {
            if (!selectedUserType) {
                alert('Selecciona un tipo de usuario');
                return;
            }

            // Crear usuario simulado según el tipo
            let user = {};

            switch(selectedUserType) {
                case 'taller':
                    user = {
                        id: 2,
                        username: 'taller1',
                        email: '<EMAIL>',
                        role_name: 'workshop',
                        role_id: 2
                    };
                    break;
                case 'proveedor':
                    user = {
                        id: 3,
                        username: 'proveedor1',
                        email: '<EMAIL>',
                        role_name: 'supplier',
                        role_id: 3
                    };
                    break;
                case 'admin':
                    user = {
                        id: 1,
                        username: 'admin',
                        email: '<EMAIL>',
                        role_name: 'admin',
                        role_id: 1
                    };
                    break;
            }

            // Guardar en localStorage
            localStorage.setItem('repumovil_user', JSON.stringify(user));

            // Redirigir según el tipo
            switch(selectedUserType) {
                case 'taller':
                    window.location.href = 'dashboard-taller.php';
                    break;
                case 'proveedor':
                    window.location.href = 'dashboard-taller.php'; // Por ahora mismo dashboard
                    break;
                case 'admin':
                    window.location.href = 'admin.php';
                    break;
            }
        }
    </script>
</body>
</html>
