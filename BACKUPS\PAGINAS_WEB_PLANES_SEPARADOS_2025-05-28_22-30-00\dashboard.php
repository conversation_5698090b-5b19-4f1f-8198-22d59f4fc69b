<?php
// Mostrar todos los errores
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Iniciar sesión
session_start();

// Verificar si el usuario está autenticado y es un taller
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'workshop') {
    header('Location: login.php');
    exit;
}

// Configuración de la base de datos
$host = 'localhost';
$dbname = 'autoconnect_bd';
$username = 'root';
$password = '';

// Función para conectar a la base de datos
function connectDB() {
    global $host, $dbname, $username, $password;

    try {
        $conn = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $conn;
    } catch (PDOException $e) {
        die("Error de conexión: " . $e->getMessage());
    }
}

// Obtener información del usuario
function getUserById($userId) {
    $conn = connectDB();

    $query = "SELECT u.*, r.name as role_name
             FROM users u
             JOIN roles r ON u.role_id = r.id
             WHERE u.id = :id";

    $stmt = $conn->prepare($query);
    $stmt->execute(['id' => $userId]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($user && $user['role_name'] === 'workshop') {
        $workshopQuery = "SELECT * FROM workshops WHERE user_id = :user_id";
        $workshopStmt = $conn->prepare($workshopQuery);
        $workshopStmt->execute(['user_id' => $userId]);
        $user['workshop'] = $workshopStmt->fetch(PDO::FETCH_ASSOC);
    }

    return $user;
}

// Obtener información del usuario
$user = getUserById($_SESSION['user_id']);

// Variables para mensajes
$message = '';
$error = '';

// Procesar formularios si se envían
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Procesar solicitud de repuestos
    if (isset($_POST['action']) && $_POST['action'] === 'request_part') {
        $partName = filter_input(INPUT_POST, 'part_name', FILTER_SANITIZE_STRING);
        $partNumber = filter_input(INPUT_POST, 'part_number', FILTER_SANITIZE_STRING);
        $quantity = (int)$_POST['quantity'];
        $urgency = filter_input(INPUT_POST, 'urgency', FILTER_SANITIZE_STRING);

        if (empty($partName) || empty($partNumber) || $quantity <= 0) {
            $error = 'Por favor, complete todos los campos correctamente.';
        } else {
            // En una implementación real, aquí se crearía la solicitud en la base de datos
            // Por ahora, solo mostramos un mensaje de éxito
            $message = 'Solicitud de repuesto enviada correctamente.';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Panel de Control - Taller Mecánico - AutoConnect</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <style>
        .dashboard {
            display: flex;
            min-height: calc(100vh - 200px);
        }

        .sidebar {
            width: 250px;
            background-color: #2c3e50;
            color: white;
            padding: 20px 0;
        }

        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
        }

        .sidebar-menu {
            list-style: none;
        }

        .sidebar-menu li {
            margin-bottom: 5px;
        }

        .sidebar-menu a {
            display: block;
            padding: 10px 20px;
            color: #ecf0f1;
            text-decoration: none;
            transition: all 0.3s;
        }

        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background-color: #34495e;
            color: white;
        }

        .sidebar-menu i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }

        .main-content {
            flex: 1;
            padding: 30px;
            background-color: #f5f5f5;
        }

        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .dashboard-title {
            font-size: 1.8rem;
            color: #2c3e50;
        }

        .user-info {
            display: flex;
            align-items: center;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #0056b3;
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 10px;
            font-weight: bold;
        }

        .dashboard-cards {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .card-title {
            font-size: 1.2rem;
            color: #2c3e50;
            font-weight: bold;
        }

        .card-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            background-color: rgba(0, 86, 179, 0.1);
            color: #0056b3;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 1.2rem;
        }

        .card-value {
            font-size: 2rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .card-description {
            color: #7f8c8d;
            font-size: 0.9rem;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }

        .data-table th,
        .data-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .data-table th {
            background-color: #f9f9f9;
            font-weight: bold;
            color: #2c3e50;
        }

        .data-table tbody tr:hover {
            background-color: #f5f5f5;
        }

        .status {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }

        .status-in-progress {
            background-color: #cce5ff;
            color: #004085;
        }

        .status-completed {
            background-color: #d4edda;
            color: #155724;
        }

        .form-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 25px;
            margin-bottom: 30px;
        }

        .form-title {
            font-size: 1.3rem;
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .form-row {
            display: flex;
            flex-wrap: wrap;
            margin: -10px;
        }

        .form-group {
            flex: 1;
            min-width: 250px;
            padding: 10px;
        }

        .message {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }

        .message-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .btn {
            display: inline-block;
            padding: 8px 15px;
            background-color: #0056b3;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            transition: background-color 0.3s;
        }

        .btn:hover {
            background-color: #003d7a;
        }

        .btn-sm {
            padding: 5px 10px;
            font-size: 0.8rem;
        }

        .btn-primary {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <header>
        <div class="logo-container">
            <i class="fas fa-cogs logo-icon"></i>
            <h1>AutoConnect</h1>
        </div>
    </header>

    <div class="dashboard">
        <div class="sidebar">
            <div class="sidebar-header">
                <h3>Panel de Control</h3>
                <p>Taller Mecánico</p>
            </div>
            <ul class="sidebar-menu">
                <li><a href="#" class="active"><i class="fas fa-tachometer-alt"></i> Inicio</a></li>
                <li><a href="#"><i class="fas fa-search"></i> Buscar Repuestos</a></li>
                <li><a href="#"><i class="fas fa-clipboard-list"></i> Mis Solicitudes</a></li>
                <li><a href="#"><i class="fas fa-handshake"></i> Ofertas Recibidas</a></li>
                <li><a href="#"><i class="fas fa-history"></i> Historial</a></li>
                <li><a href="#"><i class="fas fa-user-cog"></i> Mi Perfil</a></li>
                <li><a href="logout_reemplazo.php"><i class="fas fa-sign-out-alt"></i> Cerrar Sesión</a></li>
            </ul>
        </div>

        <div class="main-content">
            <div class="dashboard-header">
                <h2 class="dashboard-title">Panel de Control</h2>
                <div class="user-info">
                    <div class="user-avatar">
                        <?php echo substr($user['username'], 0, 1); ?>
                    </div>
                    <span><?php echo $_SESSION['workshop_name'] ?? $user['username']; ?></span>
                </div>
            </div>

            <?php if (!empty($message)): ?>
                <div class="message message-success">
                    <?php echo $message; ?>
                </div>
            <?php endif; ?>

            <?php if (!empty($error)): ?>
                <div class="message message-error">
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>

            <div class="dashboard-cards">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Solicitudes Pendientes</h3>
                        <div class="card-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                    </div>
                    <div class="card-value">3</div>
                    <div class="card-description">Solicitudes esperando ofertas</div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Ofertas Recibidas</h3>
                        <div class="card-icon">
                            <i class="fas fa-tags"></i>
                        </div>
                    </div>
                    <div class="card-value">7</div>
                    <div class="card-description">Ofertas de proveedores</div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">En Proceso</h3>
                        <div class="card-icon">
                            <i class="fas fa-shipping-fast"></i>
                        </div>
                    </div>
                    <div class="card-value">2</div>
                    <div class="card-description">Pedidos en camino</div>
                </div>
            </div>

            <div class="form-card">
                <h3 class="form-title">Solicitar Repuesto</h3>
                <form action="dashboard.php" method="post">
                    <input type="hidden" name="action" value="request_part">

                    <div class="form-row">
                        <div class="form-group">
                            <label for="part-name">Nombre del Repuesto</label>
                            <input type="text" id="part-name" name="part_name" required>
                        </div>

                        <div class="form-group">
                            <label for="part-number">Número de Parte</label>
                            <input type="text" id="part-number" name="part_number" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="quantity">Cantidad</label>
                            <input type="number" id="quantity" name="quantity" min="1" value="1" required>
                        </div>

                        <div class="form-group">
                            <label for="urgency">Urgencia</label>
                            <select id="urgency" name="urgency" required>
                                <option value="normal">Normal</option>
                                <option value="urgent">Urgente</option>
                                <option value="critical">Crítica</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="notes">Notas Adicionales</label>
                        <textarea id="notes" name="notes" rows="3"></textarea>
                    </div>

                    <button type="submit" class="btn btn-primary">Enviar Solicitud</button>
                </form>
            </div>

            <h3>Últimas Solicitudes</h3>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Repuesto</th>
                        <th>Cantidad</th>
                        <th>Fecha</th>
                        <th>Estado</th>
                        <th>Ofertas</th>
                        <th>Acciones</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>REQ-001</td>
                        <td>Filtro de aceite</td>
                        <td>5</td>
                        <td>15/05/2023</td>
                        <td><span class="status status-pending">Pendiente</span></td>
                        <td>3</td>
                        <td><a href="#" class="btn btn-sm">Ver Detalles</a></td>
                    </tr>
                    <tr>
                        <td>REQ-002</td>
                        <td>Pastillas de freno</td>
                        <td>2</td>
                        <td>12/05/2023</td>
                        <td><span class="status status-in-progress">En Proceso</span></td>
                        <td>5</td>
                        <td><a href="#" class="btn btn-sm">Ver Detalles</a></td>
                    </tr>
                    <tr>
                        <td>REQ-003</td>
                        <td>Bomba de agua</td>
                        <td>1</td>
                        <td>10/05/2023</td>
                        <td><span class="status status-completed">Completado</span></td>
                        <td>4</td>
                        <td><a href="#" class="btn btn-sm">Ver Detalles</a></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <footer>
        <div class="footer-bottom">
            <p>&copy; 2023 AutoConnect. Todos los derechos reservados.</p>
        </div>
    </footer>
</body>
</html>




