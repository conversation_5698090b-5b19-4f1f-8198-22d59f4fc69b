# Configuración del entorno de desarrollo React Native

## Requisitos previos
1. Node.js (versión 12 o superior)
2. npm o yarn
3. JDK 11 (para Android)
4. Android Studio (para Android) o Xcode (para iOS)

## Pasos de instalación

### 1. Instalar Node.js y npm
Descarga e instala desde: https://nodejs.org/

### 2. Instalar React Native CLI
```bash
npm install -g react-native-cli
```

### 3. Crear un nuevo proyecto
```bash
npx react-native init AutoConnectApp
cd AutoConnectApp
```

### 4. Instalar dependencias adicionales
```bash
npm install @react-navigation/native @react-navigation/bottom-tabs @react-navigation/stack
npm install react-native-reanimated react-native-gesture-handler react-native-screens
npm install react-native-safe-area-context @react-native-community/masked-view
npm install axios react-native-vector-icons @react-native-async-storage/async-storage
```

### 5. Configurar la estructura del proyecto
Crea las siguientes carpetas dentro de la carpeta `AutoConnectApp`:
```bash
mkdir -p src/screens src/components src/navigation src/context src/services src/assets src/utils
```

### 6. Configurar la navegación
Crea el archivo `src/navigation/AppNavigator.js`:
```javascript
import React, { useContext } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Ionicons } from 'react-native-vector-icons';

// Importar contexto de autenticación
import { AuthContext } from '../context/AuthContext';

// Importar pantallas de autenticación
import LoginScreen from '../screens/LoginScreen';
import RegisterScreen from '../screens/RegisterScreen';

// Importar pantallas principales
import HomeScreen from '../screens/HomeScreen';
import SearchScreen from '../screens/SearchScreen';
import RequestDetailScreen from '../screens/RequestDetailScreen';
import NewRequestScreen from '../screens/NewRequestScreen';
import ProfileScreen from '../screens/ProfileScreen';

// Crear navegadores
const Stack = createStackNavigator();
const Tab = createBottomTabNavigator();

// Navegador de autenticación
const AuthNavigator = () => (
  <Stack.Navigator screenOptions={{ headerShown: false }}>
    <Stack.Screen name="Login" component={LoginScreen} />
    <Stack.Screen name="Register" component={RegisterScreen} />
  </Stack.Navigator>
);

// Navegador de inicio
const HomeNavigator = () => (
  <Stack.Navigator>
    <Stack.Screen name="Solicitudes" component={HomeScreen} />
    <Stack.Screen name="RequestDetail" component={RequestDetailScreen} options={{ title: 'Detalle de solicitud' }} />
    <Stack.Screen name="NewRequest" component={NewRequestScreen} options={{ title: 'Nueva solicitud' }} />
  </Stack.Navigator>
);

// Navegador principal con tabs
const MainNavigator = () => (
  <Tab.Navigator
    screenOptions={({ route }) => ({
      tabBarIcon: ({ focused, color, size }) => {
        let iconName;

        if (route.name === 'Home') {
          iconName = focused ? 'home' : 'home-outline';
        } else if (route.name === 'Search') {
          iconName = focused ? 'search' : 'search-outline';
        } else if (route.name === 'Profile') {
          iconName = focused ? 'person' : 'person-outline';
        }

        return <Ionicons name={iconName} size={size} color={color} />;
      },
    })}
    tabBarOptions={{
      activeTintColor: '#007bff',
      inactiveTintColor: 'gray',
    }}
  >
    <Tab.Screen name="Home" component={HomeNavigator} options={{ title: 'Inicio' }} />
    <Tab.Screen name="Search" component={SearchScreen} options={{ title: 'Buscar' }} />
    <Tab.Screen name="Profile" component={ProfileScreen} options={{ title: 'Perfil' }} />
  </Tab.Navigator>
);

// Navegador principal de la aplicación
const AppNavigator = () => {
  const { userToken, isLoading } = useContext(AuthContext);

  if (isLoading) {
    return null; // O un componente de carga
  }

  return (
    <NavigationContainer>
      {userToken ? <MainNavigator /> : <AuthNavigator />}
    </NavigationContainer>
  );
};

export default AppNavigator;
```

### 7. Configurar el contexto de autenticación
Crea el archivo `src/context/AuthContext.js`:
```javascript
import React, { createContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';
import { API_URL } from '../config';

export const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [userToken, setUserToken] = useState(null);
  const [userInfo, setUserInfo] = useState(null);
  const [error, setError] = useState(null);

  // Función para iniciar sesión
  const login = async (username, password) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await axios.post(`${API_URL}/login`, {
        username,
        password
      });
      
      if (response.data.status === 'success') {
        const { token, user } = response.data;
        setUserToken(token);
        setUserInfo(user);
        
        // Guardar en AsyncStorage
        await AsyncStorage.setItem('userToken', token);
        await AsyncStorage.setItem('userInfo', JSON.stringify(user));
      } else {
        setError(response.data.message);
      }
    } catch (error) {
      setError('Error de conexión. Intente nuevamente.');
      console.log('Error de login:', error);
    }
    
    setIsLoading(false);
  };

  // Función para cerrar sesión
  const logout = async () => {
    setIsLoading(true);
    setUserToken(null);
    setUserInfo(null);
    
    // Eliminar de AsyncStorage
    await AsyncStorage.removeItem('userToken');
    await AsyncStorage.removeItem('userInfo');
    
    setIsLoading(false);
  };

  // Verificar si hay una sesión guardada al iniciar la app
  const isLoggedIn = async () => {
    try {
      setIsLoading(true);
      
      const token = await AsyncStorage.getItem('userToken');
      const userInfoString = await AsyncStorage.getItem('userInfo');
      
      if (token) {
        setUserToken(token);
        setUserInfo(JSON.parse(userInfoString));
      }
      
      setIsLoading(false);
    } catch (error) {
      console.log('Error al verificar sesión:', error);
      setIsLoading(false);
    }
  };

  useEffect(() => {
    isLoggedIn();
  }, []);

  return (
    <AuthContext.Provider 
      value={{ 
        login, 
        logout, 
        isLoading, 
        userToken, 
        userInfo,
        error 
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};
```

### 8. Configurar el archivo de configuración
Crea el archivo `src/config.js`:
```javascript
// URL base de la API
export const API_URL = 'http://*************/autoconnect/api'; // Cambia esta URL por la dirección de tu servidor

// Otras configuraciones
export const APP_VERSION = '1.0.0';
```

### 9. Modificar el archivo App.js principal
Edita el archivo `App.js` en la raíz del proyecto:
```javascript
import React from 'react';
import { StatusBar } from 'react-native';
import { AuthProvider } from './src/context/AuthContext';
import AppNavigator from './src/navigation/AppNavigator';

const App = () => {
  return (
    <AuthProvider>
      <StatusBar barStyle="dark-content" backgroundColor="#fff" />
      <AppNavigator />
    </AuthProvider>
  );
};

export default App;
```

### 10. Crear pantallas básicas
Crea el archivo `src/screens/LoginScreen.js`:
```javascript
import React, { useState, useContext } from 'react';
import { 
  View, 
  Text, 
  TextInput, 
  TouchableOpacity, 
  StyleSheet, 
  Image, 
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Alert
} from 'react-native';
import { AuthContext } from '../context/AuthContext';

const LoginScreen = ({ navigation }) => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const { login, isLoading, error } = useContext(AuthContext);

  const handleLogin = () => {
    if (username.trim() === '' || password === '') {
      Alert.alert('Error', 'Por favor, complete todos los campos');
      return;
    }
    
    login(username, password);
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.container}
    >
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.logoContainer}>
          <Text style={styles.title}>AutoConnect</Text>
          <Text style={styles.subtitle}>Conectando talleres y proveedores</Text>
        </View>
        
        <View style={styles.formContainer}>
          {error && (
            <View style={styles.errorContainer}>
              <Text style={styles.errorText}>{error}</Text>
            </View>
          )}
          
          <TextInput
            style={styles.input}
            placeholder="Nombre de usuario"
            value={username}
            onChangeText={setUsername}
            autoCapitalize="none"
          />
          
          <TextInput
            style={styles.input}
            placeholder="Contraseña"
            value={password}
            onChangeText={setPassword}
            secureTextEntry
          />
          
          <TouchableOpacity 
            style={styles.loginButton}
            onPress={handleLogin}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator color="#fff" />
            ) : (
              <Text style={styles.loginButtonText}>Iniciar Sesión</Text>
            )}
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.registerLink}
            onPress={() => navigation.navigate('Register')}
          >
            <Text style={styles.registerLinkText}>
              ¿No tienes una cuenta? Regístrate
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 20,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#007bff',
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
  },
  formContainer: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  errorContainer: {
    backgroundColor: '#ffebee',
    padding: 10,
    borderRadius: 5,
    marginBottom: 15,
  },
  errorText: {
    color: '#d32f2f',
    textAlign: 'center',
  },
  input: {
    height: 50,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 5,
    marginBottom: 15,
    paddingHorizontal: 15,
    fontSize: 16,
  },
  loginButton: {
    backgroundColor: '#007bff',
    height: 50,
    borderRadius: 5,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 10,
  },
  loginButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  registerLink: {
    marginTop: 20,
    alignItems: 'center',
  },
  registerLinkText: {
    color: '#007bff',
    fontSize: 14,
  },
});

export default LoginScreen;
```

Crea el archivo `src/screens/HomeScreen.js`:
```javascript
import React, { useState, useEffect, useContext } from 'react';
import { 
  View, 
  Text, 
  FlatList, 
  TouchableOpacity, 
  StyleSheet, 
  ActivityIndicator,
  RefreshControl,
  Alert
} from 'react-native';
import axios from 'axios';
import { API_URL } from '../config';
import { AuthContext } from '../context/AuthContext';
import { Ionicons } from 'react-native-vector-icons';

const HomeScreen = ({ navigation }) => {
  const { userToken, userInfo } = useContext(AuthContext);
  const [requests, setRequests] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Función para cargar las solicitudes
  const loadRequests = async () => {
    try {
      const response = await axios.get(`${API_URL}/requests`, {
        headers: {
          'Authorization': `Bearer ${userToken}`
        }
      });
      
      if (response.data.status === 'success') {
        setRequests(response.data.requests || []);
      } else {
        Alert.alert('Error', response.data.message);
      }
    } catch (error) {
      console.log('Error al cargar solicitudes:', error);
      Alert.alert('Error', 'No se pudieron cargar las solicitudes');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Cargar solicitudes al montar el componente
  useEffect(() => {
    loadRequests();
  }, []);

  // Función para refrescar la lista
  const onRefresh = () => {
    setRefreshing(true);
    loadRequests();
  };

  // Renderizar cada elemento de la lista
  const renderItem = ({ item }) => (
    <TouchableOpacity 
      style={styles.card}
      onPress={() => navigation.navigate('RequestDetail', { requestId: item.id })}
    >
      <View style={styles.cardHeader}>
        <Text style={styles.cardTitle}>{item.part_name}</Text>
        <View style={[styles.statusBadge, getStatusStyle(item.status)]}>
          <Text style={styles.statusText}>{getStatusText(item.status)}</Text>
        </View>
      </View>
      
      <Text style={styles.cardSubtitle}>
        {item.vehicle_brand} {item.vehicle_model} ({item.vehicle_year})
      </Text>
      
      <View style={styles.cardFooter}>
        <Text style={styles.dateText}>
          {new Date(item.created_at).toLocaleDateString()}
        </Text>
        <View style={styles.statsContainer}>
          <Text style={styles.statsText}>
            <Ionicons name="chatbubble-outline" size={14} /> {item.offers_count || 0}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  // Función para obtener el estilo según el estado
  const getStatusStyle = (status) => {
    switch (status) {
      case 'pending':
        return styles.statusPending;
      case 'in_progress':
        return styles.statusInProgress;
      case 'completed':
        return styles.statusCompleted;
      default:
        return styles.statusPending;
    }
  };

  // Función para obtener el texto según el estado
  const getStatusText = (status) => {
    switch (status) {
      case 'pending':
        return 'Pendiente';
      case 'in_progress':
        return 'En proceso';
      case 'completed':
        return 'Completado';
      default:
        return 'Pendiente';
    }
  };

  // Renderizar el botón flotante para crear nueva solicitud
  const renderFloatingButton = () => (
    <TouchableOpacity 
      style={styles.floatingButton}
      onPress={() => navigation.navigate('NewRequest')}
    >
      <Ionicons name="add" size={24} color="#fff" />
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007bff" />
        </View>
      ) : (
        <>
          <FlatList
            data={requests}
            renderItem={renderItem}
            keyExtractor={item => item.id.toString()}
            contentContainerStyle={styles.listContainer}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                colors={['#007bff']}
              />
            }
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Text style={styles.emptyText}>No hay solicitudes disponibles</Text>
                <TouchableOpacity 
                  style={styles.createButton}
                  onPress={() => navigation.navigate('NewRequest')}
                >
                  <Text style={styles.createButtonText}>Crear nueva solicitud</Text>
                </TouchableOpacity>
              </View>
            }
          />
          {renderFloatingButton()}
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContainer: {
    padding: 15,
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusPending: {
    backgroundColor: '#fff3cd',
  },
  statusInProgress: {
    backgroundColor: '#cce5ff',
  },
  statusCompleted: {
    backgroundColor: '#d4edda',
  },
  statusText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  cardSubtitle: {
    fontSize: 14,
    color: '#666',
  },
  cardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dateText: {
    fontSize: 12,
    color: '#888',
  },
  statsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statsText: {
    fontSize: 12,
    color: '#666',
    marginRight: 10,
  },
  floatingButton: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#007bff',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 5,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    color: '#888',
    marginBottom: 10,
  },
  createButton: {
    backgroundColor: '#007bff',
    padding: 10,
    borderRadius: 5,
  },
  createButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default HomeScreen;
```

### 11. Crear pantallas adicionales
Crea el archivo `src/screens/RegisterScreen.js`:
```javascript
import React, { useState } from 'react';
import { 
  View, 
  Text, 
  TextInput, 
  TouchableOpacity, 
  StyleSheet, 
  KeyboardAvoidingView,
  Platform,
  Alert
} from 'react-native';
import { AuthContext } from '../context/AuthContext';

const RegisterScreen = ({ navigation }) => {
  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const { register, isLoading, error } = useContext(AuthContext);

  const handleRegister = () => {
    if (username.trim() === '' || email === '' || password === '' || confirmPassword === '') {
      Alert.alert('Error', 'Por favor, complete todos los campos');
      return;
    }
    
    if (password !== confirmPassword) {
      Alert.alert('Error', 'Las contraseñas no coinciden');
      return;
    }
    
    register(username, email, password);
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.container}
    >
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.logoContainer}>
          <Text style={styles.title}>AutoConnect</Text>
          <Text style={styles.subtitle}>Conectando talleres y proveedores</Text>
        </View>
        
        <View style={styles.formContainer}>
          {error && (
            <View style={styles.errorContainer}>
              <Text style={styles.errorText}>{error}</Text>
            </View>
          )}
          
          <TextInput
            style={styles.input}
            placeholder="Nombre de usuario"
            value={username}
            onChangeText={setUsername}
            autoCapitalize="none"
          />
          
          <TextInput
            style={styles.input}
            placeholder="Correo electrónico"
            value={email}
            onChangeText={setEmail}
            keyboardType="email-address"
            autoCapitalize="none"
          />
          
          <TextInput
            style={styles.input}
            placeholder="Contraseña"
            value={password}
            onChangeText={setPassword}
            secureTextEntry
          />
          
          <TextInput
            style={styles.input}
            placeholder="Confirmar contraseña"
            value={confirmPassword}
            onChangeText={setConfirmPassword}
            secureTextEntry
          />
          
          <TouchableOpacity 
            style={styles.registerButton}
            onPress={handleRegister}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator color="#fff" />
            ) : (
              <Text style={styles.registerButtonText}>Registrarse</Text>
            )}
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.loginLink}
            onPress={() => navigation.navigate('Login')}
          >
            <Text style={styles.loginLinkText}>
              ¿Ya tienes una cuenta? Inicia sesión
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 20,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#007bff',
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
  },
  formContainer: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  errorContainer: {
    backgroundColor: '#ffebee',
    padding: 10,
    borderRadius: 5,
    marginBottom: 15,
  },
  errorText: {
    color: '#d32f2f',
    textAlign: 'center',
  },
  input: {
    height: 50,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 5,
    marginBottom: 15,
    paddingHorizontal: 15,
    fontSize: 16,
  },
  registerButton: {
    backgroundColor: '#007bff',
    height: 50,
    borderRadius: 5,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 10,
  },
  registerButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  loginLink: {
    marginTop: 20,
    alignItems: 'center',
  },
  loginLinkText: {
    color: '#007bff',
    fontSize: 14,
  },
});

export default RegisterScreen;
```

Crea el archivo `src/screens/RequestDetailScreen.js`:
```javascript
import React, { useState, useEffect, useContext } from 'react';
import { 
  View, 
  Text, 
  ScrollView, 
  StyleSheet, 
  ActivityIndicator,
  Alert
} from 'react-native';
import axios from 'axios';
import { API_URL } from '../config';
import { AuthContext } from '../context/AuthContext';
import { Ionicons } from 'react-native-vector-icons';

const RequestDetailScreen = ({ route, navigation }) => {
  const { requestId } = route.params;
  const { userToken, userInfo } = useContext(AuthContext);
  const [request, setRequest] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Función para cargar los detalles de la solicitud
  const loadRequestDetails = async () => {
    try {
      const response = await axios.get(`${API_URL}/requests/${requestId}`, {
        headers: {
          'Authorization': `Bearer ${userToken}`
        }
      });
      
      if (response.data.status === 'success') {
        setRequest(response.data.request);
      } else {
        setError(response.data.message);
      }
    } catch (error) {
      console.log('Error al cargar detalles de la solicitud:', error);
      setError('No se pudieron cargar los detalles de la solicitud');
    } finally {
      setLoading(false);
    }
  };

  // Cargar detalles de la solicitud al montar el componente
  useEffect(() => {
    loadRequestDetails();
  }, []);

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007bff" />
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{error}</Text>
      </View>
    );
  }

  if (!request) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>No se encontró la solicitud</Text>
      </View>
    );
  }

  return (
    <ScrollView contentContainerStyle={styles.container}>
      <View style={styles.card}>
        <View style={styles.cardHeader}>
          <Text style={styles.cardTitle}>{request.part_name}</Text>
          <View style={[styles.statusBadge, getStatusStyle(request.status)]}>
            <Text style={styles.statusText}>{getStatusText(request.status)}</Text>
          </View>
        </View>
        
        <Text style={styles.cardSubtitle}>
          {request.vehicle_brand} {request.vehicle_model} ({request.vehicle_year})
        </Text>
        
        <View style={styles.cardFooter}>
          <Text style={styles.dateText}>
            {new Date(request.created_at).toLocaleDateString()}
          </Text>
          <View style={styles.statsContainer}>
            <Text style={styles.statsText}>
              <Ionicons name="chatbubble-outline" size={14} /> {request.offers_count || 0}
            </Text>
          </View>
        </View>
      </View>
      <View style={styles.detailsContainer}>
        <Text style={styles.detailTitle}>Detalles de la solicitud</Text>
        <Text style={styles.detailText}>
          <Text style={styles.detailLabel}>Nombre del vehículo: </Text>
          {request.vehicle_name}
        </Text>
        <Text style={styles.detailText}>
          <Text style={styles.detailLabel}>Modelo: </Text>
          {request.vehicle_model}
        </Text>
        <Text style={styles.detailText}>
          <Text style={styles.detailLabel}>Año: </Text>
          {request.vehicle_year}
        </Text>
        <Text style={styles.detailText}>
          <Text style={styles.detailLabel}>Placa: </Text>
          {request.vehicle_plate}
        </Text>
        <Text style={styles.detailText}>
          <Text style={styles.detailLabel}>Descripción: </Text>
          {request.description}
        </Text>
        <Text style={styles.detailText}>
          <Text style={styles.detailLabel}>Ubicación: </Text>
          {request.location}
        </Text>
        <Text style={styles.detailText}>
          <Text style={styles.detailLabel}>Teléfono de contacto: </Text>
          {request.contact_phone}
        </Text>
        <Text style={styles.detailText}>
          <Text style={styles.detailLabel}>Correo electrónico: </Text>
          {request.contact_email}
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#d32f2f',
    textAlign: 'center',
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusPending: {
    backgroundColor: '#fff3cd',
  },
  statusInProgress: {
    backgroundColor: '#cce5ff',
  },
  statusCompleted: {
    backgroundColor: '#d4edda',
  },
  statusText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  cardSubtitle: {
    fontSize: 14,
    color: '#666',
  },
  cardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dateText: {
    fontSize: 12,
    color: '#888',
  },
  statsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statsText: {
    fontSize: 12,
    color: '#666',
    marginRight: 10,
  },
  detailsContainer: {
    padding: 15,
    backgroundColor: '#fff',
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    marginBottom: 15,
  },
  detailTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  detailText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  detailLabel: {
    fontWeight: 'bold',
    color: '#333',
  },
});

export default RequestDetailScreen;
```

Crea el archivo `src/screens/NewRequestScreen.js`:
```javascript
import React, { useState } from 'react';
import { 
  View, 
  Text, 
  TextInput, 
  TouchableOpacity, 
  StyleSheet, 
  KeyboardAvoidingView,
  Platform,
  Alert
} from 'react-native';
import { AuthContext } from '../context/AuthContext';

const NewRequestScreen = ({ navigation }) => {
  const [partName, setPartName] = useState('');
  const [vehicleBrand, setVehicleBrand] = useState('');
  const [vehicleModel, setVehicleModel] = useState('');
  const [vehicleYear, setVehicleYear] = useState('');
  const [vehiclePlate, setVehiclePlate] = useState('');
  const [description, setDescription] = useState('');
  const [location, setLocation] = useState('');
  const [contactPhone, setContactPhone] = useState('');
  const [contactEmail, setContactEmail] = useState('');
  const { createRequest, isLoading, error } = useContext(AuthContext);

  const handleCreateRequest = () => {
    if (partName.trim() === '' || vehicleBrand === '' || vehicleModel === '' || vehicleYear === '' || vehiclePlate === '' || description === '' || location === '' || contactPhone === '' || contactEmail === '') {
      Alert.alert('Error', 'Por favor, complete todos los campos');
      return;
    }
    
    createRequest({
      part_name: partName,
      vehicle_brand: vehicleBrand,
      vehicle_model: vehicleModel,
      vehicle_year: vehicleYear,
      vehicle_plate: vehiclePlate,
      description,
      location,
      contact_phone: contactPhone,
      contact_email: contactEmail
    });
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.container}
    >
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.logoContainer}>
          <Text style={styles.title}>AutoConnect</Text>
          <Text style={styles.subtitle}>Conectando talleres y proveedores</Text>
        </View>
        
        <View style={styles.formContainer}>
          {error && (
            <View style={styles.errorContainer}>
              <Text style={styles.errorText}>{error}</Text>
            </View>
          )}
          
          <TextInput
            style={styles.input}
            placeholder="Nombre del repuesto"
            value={partName}
            onChangeText={setPartName}
          />
          
          <TextInput
            style={styles.input}
            placeholder="Marca del vehículo"
            value={vehicleBrand}
            onChangeText={setVehicleBrand}
          />
          
          <TextInput
            style={styles.input}
            placeholder="Modelo del vehículo"
            value={vehicleModel}
            onChangeText={setVehicleModel}
          />
          
          <TextInput
            style={styles.input}
            placeholder="Año del vehículo"
            value={vehicleYear}
            onChangeText={setVehicleYear}
            keyboardType="numeric"
          />
          
          <TextInput
            style={styles.input}
            placeholder="Placa del vehículo"
            value={vehiclePlate}
            onChangeText={setVehiclePlate}
          />
          
          <TextInput
            style={styles.input}
            placeholder="Descripción del problema"
            value={description}
            onChangeText={setDescription}
            multiline
            numberOfLines={4}
          />
          
          <TextInput
            style={styles.input}
            placeholder="Ubicación"
            value={location}
            onChangeText={setLocation}
          />
          
          <TextInput
            style={styles.input}
            placeholder="Teléfono de contacto"
            value={contactPhone}
            onChangeText={setContactPhone}
            keyboardType="phone-pad"
          />
          
          <TextInput
            style={styles.input}
            placeholder="Correo electrónico"
            value={contactEmail}
            onChangeText={setContactEmail}
            keyboardType="email-address"
            autoCapitalize="none"
          />
          
          <TouchableOpacity 
            style={styles.createButton}
            onPress={handleCreateRequest}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator color="#fff" />
            ) : (
              <Text style={styles.createButtonText}>Crear Solicitud</Text>
            )}
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.cancelLink}
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.cancelLinkText}>
              Cancelar
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 20,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#007bff',
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
  },
  formContainer: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  errorContainer: {
    backgroundColor: '#ffebee',
    padding: 10,
    borderRadius: 5,
    marginBottom: 15,
  },
  errorText: {
    color: '#d32f2f',
    textAlign: 'center',
  },
  input: {
    height: 50,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 5,
    marginBottom: 15,
    paddingHorizontal: 15,
    fontSize: 16,
  },
  createButton: {
    backgroundColor: '#007bff',
    height: 50,
    borderRadius: 5,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 10,
  },
  createButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  cancelLink: {
    marginTop: 20,
    alignItems: 'center',
  },
  cancelLinkText: {
    color: '#007bff',
    fontSize: 14,
  },
});

export default NewRequestScreen;
```

Crea el archivo `src/screens/ProfileScreen.js`:
```javascript
import React, { useState, useEffect, useContext } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ActivityIndicator,
  Alert
} from 'react-native';
import axios from 'axios';
import { API_URL } from '../config';
import { AuthContext } from '../context/AuthContext';

const ProfileScreen = ({ navigation }) => {
  const { userToken, userInfo } = useContext(AuthContext);
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Función para cargar el perfil del usuario
  const loadUserProfile = async () => {
    try {
      const response = await axios.get(`${API_URL}/users/${userInfo.id}`, {
        headers: {
          'Authorization': `Bearer ${userToken}`
        }
      });
      
      if (response.data.status === 'success') {
        setProfile(response.data.user);
      } else {
        setError(response.data.message);
      }
    } catch (error) {
      console.log('Error al cargar perfil:', error);
      setError('No se pudo cargar el perfil');
    } finally {
      setLoading(false);
    }
  };

  // Cargar perfil al montar el componente
  useEffect(() => {
    loadUserProfile();
  }, []);

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007bff" />
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{error}</Text>
      </View>
    );
  }

  if (!profile) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>No se encontró el perfil</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.card}>
        <Text style={styles.cardTitle}>Perfil de usuario</Text>
        <Text style={styles.cardSubtitle}>{profile.name}</Text>
        <Text style={styles.cardText}>{profile.email}</Text>
        <Text style={styles.cardText}>{profile.phone}</Text>
        <Text style={styles.cardText}>{profile.address}</Text>
      </View>
      <TouchableOpacity 
        style={styles.logoutButton}
        onPress={() => {
          Alert.alert(
            'Cerrar sesión',
            '¿Estás seguro de que quieres cerrar sesión?',
            [
              {
                text: 'Cancelar',
                style: 'cancel',
              },
              {
                text: 'Cerrar sesión',
                style: 'destructive',
                onPress: () => {
                  navigation.navigate('Login');
                },
              },
            ],
            { cancelable: false }
          );
        }}
      >
        <Text style={styles.logoutButtonText}>Cerrar sesión</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#d32f2f',
    textAlign: 'center',
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  cardSubtitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 5,
  },
  cardText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  logoutButton: {
    backgroundColor: '#007bff',
    height: 50,
    borderRadius: 5,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 10,
  },
  logoutButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default ProfileScreen;
```

### 12. Configurar el contexto de autenticación
Edita el archivo `src/context/AuthContext.js`:
```javascript
import React, { createContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';
import { API_URL } from '../config';

export const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [userToken, setUserToken] = useState(null);
  const [userInfo, setUserInfo] = useState(null);
  const [error, setError] = useState(null);

  // Función para iniciar sesión
  const login = async (username, password) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await axios.post(`${API_URL}/login`, {
        username,
        password
      });
      
      if (response.data.status === 'success') {
        const { token, user } = response.data;
        setUserToken(token);
        setUserInfo(user);
        
        // Guardar en AsyncStorage
        await AsyncStorage.setItem('userToken', token);
        await AsyncStorage.setItem('userInfo', JSON.stringify(user));
      } else {
        setError(response.data.message);
      }
    } catch (error) {
      setError('Error de conexión. Intente nuevamente.');
      console.log('Error de login:', error);
    }
    
    setIsLoading(false);
  };

  // Función para cerrar sesión
  const logout = async () => {
    setIsLoading(true);
    setUserToken(null);
    setUserInfo(null);
    
    // Eliminar de AsyncStorage
    await AsyncStorage.removeItem('userToken');
    await AsyncStorage.removeItem('userInfo');
    
    setIsLoading(false);
  };

  // Verificar si hay una sesión guardada al iniciar la app
  const isLoggedIn = async () => {
    try {
      setIsLoading(true);
      
      const token = await AsyncStorage.getItem('userToken');
      const userInfoString = await AsyncStorage.getItem('userInfo');
      
      if (token) {
        setUserToken(token);
        setUserInfo(JSON.parse(userInfoString));
      }
      
      setIsLoading(false);
    } catch (error) {
      console.log('Error al verificar sesión:', error);
      setIsLoading(false);
    }
  };

  useEffect(() => {
    isLoggedIn();
  }, []);

  return (
    <AuthContext.Provider 
      value={{ 
        login, 
        logout, 
        isLoading, 
        userToken, 
        userInfo,
        error 
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};
```

### 13. Configurar el archivo de configuración
Edita el archivo `src/config.js`:
```javascript
// URL base de la API
export const API_URL = 'http://*************/autoconnect/api'; // Cambia esta URL por la dirección de tu servidor

// Otras configuraciones
export const APP_VERSION = '1.0.0';
```

### 14. Modificar el archivo App.js principal
Edita el archivo `App.js` en la raíz del proyecto:
```javascript
import React from 'react';
import { StatusBar } from 'react-native';
import { AuthProvider } from './src/context/AuthContext';
import AppNavigator from './src/navigation/AppNavigator';

const App = () => {
  return (
    <AuthProvider>
      <StatusBar barStyle="dark-content" backgroundColor="#fff" />
      <AppNavigator />
    </AuthProvider>
  );
};

export default App;
```

### 15. Crear pantallas básicas
Edita el archivo `src/screens/LoginScreen.js`:
```javascript
import React, { useState, useContext } from 'react';
import { 
  View, 
  Text, 
  TextInput, 
  TouchableOpacity, 
  StyleSheet, 
  Image, 
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Alert
} from 'react-native';
import { AuthContext } from '../context/AuthContext';

const LoginScreen = ({ navigation }) => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const { login, isLoading, error } = useContext(AuthContext);

  const handleLogin = () => {
    if (username.trim() === '' || password === '') {
      Alert.alert('Error', 'Por favor, complete todos los campos');
      return;
    }
    
    login(username, password);
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.container}
    >
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.logoContainer}>
          <Text style={styles.title}>AutoConnect</Text>
          <Text style={styles.subtitle}>Conectando talleres y proveedores</Text>
        </View>
        
        <View style={styles.formContainer}>
          {error && (
            <View style={styles.errorContainer}>
              <Text style={styles.errorText}>{error}</Text>
            </View>
          )}
          
          <TextInput
            style={styles.input}
            placeholder="Nombre de usuario"
            value={username}
            onChangeText={setUsername}
            autoCapitalize="none"
          />
          
          <TextInput
            style={styles.input}
            placeholder="Contraseña"
            value={password}
            onChangeText={setPassword}
            secureTextEntry
          />
          
          <TouchableOpacity 
            style={styles.loginButton}
            onPress={handleLogin}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator color="#fff" />
            ) : (
              <Text style={styles.loginButtonText}>Iniciar Sesión</Text>
            )}
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.registerLink}
            onPress={() => navigation.navigate('Register')}
          >
            <Text style={styles.registerLinkText}>
              ¿No tienes una cuenta? Regístrate
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 20,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#007bff',
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
  },
  formContainer: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  errorContainer: {
    backgroundColor: '#ffebee',
    padding: 10,
    borderRadius: 5,
    marginBottom: 15,
  },
  errorText: {
    color: '#d32f2f',
    textAlign: 'center',
  },
  input: {
    height: 50,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 5,
    marginBottom: 15,
    paddingHorizontal: 15,
    fontSize: 16,
  },
  loginButton: {
    backgroundColor: '#007bff',
    height: 50,
    borderRadius: 5,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 10,
  },
  loginButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  registerLink: {
    marginTop: 20,
    alignItems: 'center',
  },
  registerLinkText: {
    color: '#007bff',
    fontSize: 14,
  },
});

export default LoginScreen;
```

Edita el archivo `src/screens/HomeScreen.js`:
```javascript
import React, { useState, useEffect, useContext } from 'react';
import { 
  View, 
  Text, 
  FlatList, 
  TouchableOpacity, 
  StyleSheet, 
  ActivityIndicator,
  RefreshControl,
  Alert
} from 'react-native';
import axios from 'axios';
import { API_URL } from '../config';
import { AuthContext } from '../context/AuthContext';
import { Ionicons } from 'react-native-vector-icons';

const HomeScreen = ({ navigation }) => {
  const { userToken, userInfo } = useContext(AuthContext);
  const [requests, setRequests] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Función para cargar las solicitudes
  const loadRequests = async () => {
    try {
      const response = await axios.get(`${API_URL}/requests`, {
        headers: {
          'Authorization': `Bearer ${userToken}`
        }
      });
      
      if (response.data.status === 'success') {
        setRequests(response.data.requests || []);
      } else {
        Alert.alert('Error', response.data.message);
      }
    } catch (error) {
      console.log('Error al cargar solicitudes:', error);
      Alert.alert('Error', 'No se pudieron cargar las solicitudes');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Cargar solicitudes al montar el componente
  useEffect(() => {
    loadRequests();
  }, []);

  // Función para refrescar la lista
  const onRefresh = () => {
    setRefreshing(true);
    loadRequests();
  };

  // Renderizar cada elemento de la lista
  const renderItem = ({ item }) => (
    <TouchableOpacity 
      style={styles.card}
      onPress={() => navigation.navigate('RequestDetail', { requestId: item.id })}
    >
      <View style={styles.cardHeader}>
        <Text style={styles.cardTitle}>{item.part_name}</Text>
        <View style={[styles.statusBadge, getStatusStyle(item.status)]}>
          <Text style={styles.statusText}>{getStatusText(item.status)}</Text>
        </View>
      </View>
      
      <Text style={styles.cardSubtitle}>
        {item.vehicle_brand} {item.vehicle_model} ({item.vehicle_year})
      </Text>
      
      <View style={styles.cardFooter}>
        <Text style={styles.dateText}>
          {new Date(item.created_at).toLocaleDateString()}
        </Text>
        <View style={styles.statsContainer}>
          <Text style={styles.statsText}>
            <Ionicons name="chatbubble-outline" size={14} /> {item.offers_count || 0}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  // Función para obtener el estilo según el estado
  const getStatusStyle = (status) => {
    switch (status) {
      case 'pending':
        return styles.statusPending;
      case 'in_progress':
        return styles.statusInProgress;
      case 'completed':
        return styles.statusCompleted;
      default:
        return styles.statusPending;
    }
  };

  // Función para obtener el texto según el estado
  const getStatusText = (status) => {
    switch (status) {
      case 'pending':
        return 'Pendiente';
      case 'in_progress':
        return 'En proceso';
      case 'completed':
        return 'Completado';
      default:
        return 'Pendiente';
    }
  };

  // Renderizar el botón flotante para crear nueva solicitud
  const renderFloatingButton = () => (
    <TouchableOpacity 
      style={styles.floatingButton}
      onPress={() => navigation.navigate('NewRequest')}
    >
      <Ionicons name="add" size={24} color="#fff" />
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007bff" />
        </View>
      ) : (
        <>
          <FlatList
            data={requests}
            renderItem={renderItem}
            keyExtractor={item => item.id.toString()}
            contentContainerStyle={styles.listContainer}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                colors={['#007bff']}
              />
            }
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Text style={styles.emptyText}>No hay solicitudes disponibles</Text>
                <TouchableOpacity 
                  style={styles.createButton}
                  onPress={() => navigation.navigate('NewRequest')}
                >
                  <Text style={styles.createButtonText}>Crear nueva solicitud</Text>
                </TouchableOpacity>
              </View>
            }
          />
          {renderFloatingButton()}
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContainer: {
    padding: 15,
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusPending: {
    backgroundColor: '#fff3cd',
  },
  statusInProgress: {
    backgroundColor: '#cce5ff',
  },
  statusCompleted: {
    backgroundColor: '#d4edda',
  },
  statusText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  cardSubtitle: {
    fontSize: 14,
    color: '#666',
  },
  cardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dateText: {
    fontSize: 12,
    color: '#888',
  },
  statsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statsText: {
    fontSize: 12,
    color: '#666',
    marginRight: 10,
  },
  floatingButton: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#007bff',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 5,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    color: '#888',
    marginBottom: 10,
  },
  createButton: {
    backgroundColor: '#007bff',
    padding: 10,
    borderRadius: 5,
  },
  createButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default HomeScreen;
```

Edita el archivo `src/screens/RegisterScreen.js`:
```javascript
import React, { useState } from 'react';
import { 
  View, 
  Text, 
  TextInput, 
  TouchableOpacity, 
  StyleSheet, 
  KeyboardAvoidingView,
  Platform,
  Alert
} from 'react-native';
import { AuthContext } from '../context/AuthContext';

const RegisterScreen = ({ navigation }) => {
  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const { register, isLoading, error } = useContext(AuthContext);

  const handleRegister = () => {
    if (username.trim() === '' || email === '' || password === '' || confirmPassword === '') {
      Alert.alert('Error', 'Por favor, complete todos los campos');
      return;
    }
    
    if (password !== confirmPassword) {
      Alert.alert('Error', 'Las contraseñas no coinciden');
      return;
    }
    
    register(username, email, password);
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.container}
    >
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.logoContainer}>
          <Text style={styles.title}>AutoConnect</Text>
          <Text style={styles.subtitle}>Conectando talleres y proveedores</Text>
        </View>
        
        <View style={styles.formContainer}>
          {error && (
            <View style={styles.errorContainer}>
              <Text style={styles.errorText}>{error}</Text>
            </View>
          )}
          
          <TextInput
            style={styles.input}
            placeholder="Nombre de usuario"
            value={username}
            onChangeText={setUsername}
            autoCapitalize="none"
          />
          
          <TextInput
            style={styles.input}
            placeholder="Correo electrónico"
            value={email}
            onChangeText={setEmail}
            keyboardType="email-address"
            autoCapitalize="none"
          />
          
          <TextInput
            style={styles.input}
            placeholder="Contraseña"
            value={password}
            onChangeText={setPassword}
            secureTextEntry
          />
          
          <TextInput
            style={styles.input}
            placeholder="Confirmar contraseña"
            value={confirmPassword}
            onChangeText={setConfirmPassword}
            secureTextEntry
          />
          
          <TouchableOpacity 
            style={styles.registerButton}
            onPress={handleRegister}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator color="#fff" />
            ) : (
              <Text style={styles.registerButtonText}>Registrarse</Text>
            )}
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.loginLink}
            onPress={() => navigation.navigate('Login')}
          >
            <Text style={styles.loginLinkText}>
              ¿Ya tienes una cuenta? Inicia sesión
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 20,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#007bff',
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
  },
  formContainer: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  errorContainer: {
    backgroundColor: '#ffebee',
    padding: 10,
    borderRadius: 5,
    marginBottom: 15,
  },
  errorText: {
    color: '#d32f2f',
    textAlign: 'center',
  },
  input: {
    height: 50,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 5,
    marginBottom: 15,
    paddingHorizontal: 15,
    fontSize: 16,
  },
  registerButton: {
    backgroundColor: '#007bff',
    height: 50,
    borderRadius: 5,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 10,
  },
  registerButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  loginLink: {
    marginTop: 20,
    alignItems: 'center',
  },
  loginLinkText: {
    color: '#007bff',
    fontSize: 14,
  },
});

export default RegisterScreen;
```

Edita el archivo `src/screens/RequestDetailScreen.js`:
```javascript
import React, { useState, useEffect, useContext } from 'react';
import { 
  View, 
  Text, 
  ScrollView, 
  StyleSheet, 
  ActivityIndicator,
  Alert
} from 'react-native';
import axios from 'axios';
import { API_URL } from '../config';
import { AuthContext } from '../context/AuthContext';
import { Ionicons } from 'react-native-vector-icons';

const RequestDetailScreen = ({ route, navigation }) => {
  const { requestId } = route.params;
  const { userToken, userInfo } = useContext(AuthContext);
  const [request, setRequest] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Función para cargar los detalles de la solicitud
  const loadRequestDetails = async () => {
    try {
      const response = await axios.get(`${API_URL}/requests/${requestId}`, {
        headers: {
          'Authorization': `Bearer ${userToken}`
        }
      });
      
      if (response.data.status === 'success') {
        setRequest(response.data.request);
      } else {
        setError(response.data.message);
      }
    } catch (error) {
      console.log('Error al cargar detalles de la solicitud:', error);
      setError('No se pudieron cargar los detalles de la solicitud');
    } finally {
      setLoading(false);
    }
  };

  // Cargar detalles de la solicitud al montar el componente
  useEffect(() => {
    loadRequestDetails();
  }, []);

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007bff" />
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{error}</Text>
      </View>
    );
  }

  if (!request) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>No se encontró la solicitud</Text>
      </View>
    );
  }

  return (
    <ScrollView contentContainerStyle={styles.container}>
      <View style={styles.card}>
        <View style={styles.cardHeader}>
          <Text style={styles.cardTitle}>{request.part_name}</Text>
          <View style={[styles.statusBadge, getStatusStyle(request.status)]}>
            <Text style={styles.statusText}>{getStatusText(request.status)}</Text>
          </View>
        </View>
        
        <Text style={styles.cardSubtitle}>
          {request.vehicle_brand} {request.vehicle_model} ({request.vehicle_year})
        </Text>
        
        <View style={styles.cardFooter}>
          <Text style={styles.dateText}>
            {new Date(request.created_at).toLocaleDateString()}
          </Text>
          <View style={styles.statsContainer}>
            <Text style={styles.statsText}>
              <Ionicons name="chatbubble-outline" size={14} /> {request.offers_count || 0}
            </Text>
          </View>
        </View>
      </View>
      <View style={styles.detailsContainer}>
        <Text style={styles.detailTitle}>Detalles de la solicitud</Text>
        <Text style={styles.detailText}>
          <Text style={styles.detailLabel}>Nombre del vehículo: </Text>
          {request.vehicle_name}
        </Text>
        <Text style={styles.detailText}>
          <Text style={styles.detailLabel}>Modelo: </Text>
          {request.vehicle_model}
        </Text>
        <Text style={styles.detailText}>
          <Text style={styles.detailLabel}>Año: </Text>
          {request.vehicle_year}
        </Text>
        <Text style={styles.detailText}>
          <Text style={styles.detailLabel}>Placa: </Text>
          {request.vehicle_plate}
        </Text>
        <Text style={styles.detailText}>
          <Text style={styles.detailLabel}>Descripción: </Text>
          {request.description}
        </Text>
        <Text style={styles.detailText}>
          <Text style={styles.detailLabel}>Ubicación: </Text>
          {request.location}
        </Text>
        <Text style={styles.detailText}>
          <Text style={styles.detailLabel}>Teléfono de contacto: </Text>
          {request.contact_phone}
        </Text>
        <Text style={styles.detailText}>
          <Text style={styles.detailLabel}>Correo electrónico: </Text>
          {request.contact_email}
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#d32f2f',
    textAlign: 'center',
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusPending: {
    backgroundColor: '#fff3cd',
  },
  statusInProgress: {
    backgroundColor: '#cce5ff',
  },
  statusCompleted: {
    backgroundColor: '#d4edda',
  },
  statusText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  cardSubtitle: {
    fontSize: 14,
    color: '#666',
  },
  cardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dateText: {
    fontSize: 12,
    color: '#888',
  },
  statsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statsText: {
    fontSize: 12,
    color: '#666',
    marginRight: 10,
  },
  detailsContainer: {
    padding: 15,
    backgroundColor: '#fff',
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    marginBottom: 15,
  },
  detailTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  detailText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  detailLabel: {
    fontWeight: 'bold',
    color: '#333',
  },
});

export default RequestDetailScreen;
```

Edita el archivo `src/screens/NewRequestScreen.js`:
```javascript
import React, { useState } from 'react';
import { 
  View, 
  Text, 
  TextInput, 
  TouchableOpacity, 
  StyleSheet, 
  KeyboardAvoidingView,
  Platform,
  Alert
} from 'react-native';
import { AuthContext } from '../context/AuthContext';

const NewRequestScreen = ({ navigation }) => {
  const [partName, setPartName] = useState('');
  const [vehicleBrand, setVehicleBrand] = useState('');
  const [vehicleModel, setVehicleModel] = useState('');
  const [vehicleYear, setVehicleYear] = useState('');
  const [vehiclePlate, setVehiclePlate] = useState('');
  const [description, setDescription] = useState('');
  const [location, setLocation] = useState('');
  const [contactPhone, setContactPhone] = useState('');
  const [contactEmail, setContactEmail] = useState('');
  const { createRequest, isLoading, error } = useContext(AuthContext);

  const handleCreateRequest = () => {
    if (partName.trim() === '' || vehicleBrand === '' || vehicleModel === '' || vehicleYear === '' || vehiclePlate === '' || description === '' || location === '' || contactPhone === '' || contactEmail === '') {
      Alert.alert('Error', 'Por favor, complete todos los campos');
      return;
    }
    
    createRequest({
      part_name: partName,
      vehicle_brand: vehicleBrand,
      vehicle_model: vehicleModel,
      vehicle_year: vehicleYear,
      vehicle_plate: vehiclePlate,
      description,
      location,
      contact_phone: contactPhone,
      contact_email: contactEmail
    });
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.container}
    >
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.logoContainer}>
          <Text style={styles.title}>AutoConnect</Text>
          <Text style={styles.subtitle}>Conectando talleres y proveedores</Text>
        </View>
        
        <View style={styles.formContainer}>
          {error && (
            <View style={styles.errorContainer}>
              <Text style={styles.errorText}>{error}</Text>
            </View>
          )}
          
          <TextInput
            style={styles.input}
            placeholder="Nombre del repuesto"
            value={partName}
            onChangeText={setPartName}
          />
          
          <TextInput
            style={styles.input}
            placeholder="Marca del vehículo"
            value={vehicleBrand}
            onChangeText={setVehicleBrand}
          />
          
          <TextInput
            style={styles.input}
            placeholder="Modelo del vehículo"
            value={vehicleModel}
            onChangeText={setVehicleModel}
          />
          
          <TextInput
            style={styles.input}
            placeholder="Año del vehículo"
            value={vehicleYear}
            onChangeText={setVehicleYear}
            keyboardType="numeric"
          />
          
          <TextInput
            style={styles.input}
            placeholder="Placa del vehículo"
            value={vehiclePlate}
            onChangeText={setVehiclePlate}
          />
          
          <TextInput
            style={styles.input}
            placeholder="Descripción del problema"
            value={description}
            onChangeText={setDescription}
            multiline
            numberOfLines={4}
          />
          
          <TextInput
            style={styles.input}
            placeholder="Ubicación"
            value={location}
            onChangeText={setLocation}
          />
          
          <TextInput
            style={styles.input}
            placeholder="Teléfono de contacto"
            value={contactPhone}
            onChangeText={setContactPhone}
            keyboardType="phone-pad"
          />
          
          <TextInput
            style={styles.input}
            placeholder="Correo electrónico"
            value={contactEmail}
            onChangeText={setContactEmail}
            keyboardType="email-address"
            autoCapitalize="none
