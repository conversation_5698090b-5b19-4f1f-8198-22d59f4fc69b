<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RepuMovil - Catálogo de Repuestos</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #FF6B35;
            --secondary-color: #FFA500;
            --dark-color: #2c3e50;
            --light-color: #f8f9fa;
            --success-color: #28a745;
            --danger-color: #dc3545;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1.5rem;
            font-weight: bold;
        }

        .nav-buttons {
            display: flex;
            gap: 10px;
        }

        .nav-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .nav-btn:hover {
            background: rgba(255,255,255,0.3);
            color: white;
            text-decoration: none;
        }

        /* Main Content */
        .main-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        /* Search and Filters */
        .search-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .search-title {
            color: var(--dark-color);
            font-size: 1.5rem;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .search-form {
            display: grid;
            grid-template-columns: 1fr auto auto auto;
            gap: 15px;
            align-items: end;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 5px;
            color: var(--dark-color);
            font-weight: 500;
        }

        .form-group input,
        .form-group select {
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .search-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            transition: background 0.3s ease;
        }

        .search-btn:hover {
            background: #e55a2b;
        }

        /* Products Grid */
        .products-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .products-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
        }

        .products-title {
            color: var(--dark-color);
            font-size: 1.5rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .products-count {
            color: #666;
            font-size: 0.9rem;
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }

        .product-card {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 20px;
            transition: all 0.3s ease;
            background: white;
        }

        .product-card:hover {
            border-color: var(--primary-color);
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .product-header {
            display: flex;
            justify-content: space-between;
            align-items: start;
            margin-bottom: 15px;
        }

        .product-name {
            color: var(--dark-color);
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .product-brand {
            color: #666;
            font-size: 0.9rem;
        }

        .product-price {
            color: var(--primary-color);
            font-size: 1.5rem;
            font-weight: bold;
        }

        .product-description {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }

        .product-details {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            font-size: 0.9rem;
        }

        .product-category {
            background: var(--light-color);
            padding: 5px 10px;
            border-radius: 15px;
            color: var(--dark-color);
        }

        .product-stock {
            color: var(--success-color);
            font-weight: 500;
        }

        .product-actions {
            display: flex;
            gap: 10px;
        }

        .add-to-cart-btn {
            flex: 1;
            background: var(--success-color);
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: background 0.3s ease;
        }

        .add-to-cart-btn:hover {
            background: #218838;
        }

        .add-to-cart-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        .quantity-input {
            width: 60px;
            padding: 8px;
            border: 2px solid #e9ecef;
            border-radius: 5px;
            text-align: center;
        }

        /* Loading */
        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }

        .loading i {
            font-size: 3rem;
            color: var(--primary-color);
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .search-form {
                grid-template-columns: 1fr;
            }

            .products-grid {
                grid-template-columns: 1fr;
            }

            .header-content {
                flex-direction: column;
                gap: 15px;
            }

            .nav-buttons {
                flex-wrap: wrap;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-wrench"></i>
                <span>RepuMovil</span>
            </div>
            <div class="nav-buttons">
                <a href="dashboard-taller.php" class="nav-btn">
                    <i class="fas fa-home"></i> Dashboard
                </a>
                <a href="carrito.php" class="nav-btn">
                    <i class="fas fa-shopping-cart"></i> Changuito (<span id="cartCount">0</span>)
                </a>
                <a href="mis-pedidos.php" class="nav-btn">
                    <i class="fas fa-list-alt"></i> Mis Pedidos
                </a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Search Section -->
        <section class="search-section">
            <h2 class="search-title">
                <i class="fas fa-search"></i>
                Buscar Repuestos
            </h2>
            <div class="search-form">
                <div class="form-group">
                    <label for="searchInput">Buscar por nombre o código</label>
                    <input type="text" id="searchInput" placeholder="Ej: filtro aceite, BRK001...">
                </div>
                <div class="form-group">
                    <label for="categoryFilter">Categoría</label>
                    <select id="categoryFilter">
                        <option value="">Todas las categorías</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="brandFilter">Marca</label>
                    <select id="brandFilter">
                        <option value="">Todas las marcas</option>
                    </select>
                </div>
                <button class="search-btn" onclick="searchProducts()">
                    <i class="fas fa-search"></i> Buscar
                </button>
            </div>
        </section>

        <!-- Products Section -->
        <section class="products-section">
            <div class="products-header">
                <h2 class="products-title">
                    <i class="fas fa-cogs"></i>
                    Catálogo de Repuestos
                </h2>
                <div class="products-count" id="productsCount">Cargando...</div>
            </div>
            <div class="products-grid" id="productsGrid">
                <div class="loading">
                    <i class="fas fa-spinner"></i>
                    <p>Cargando repuestos...</p>
                </div>
            </div>
        </section>
    </main>

    <script>
        // Verificar usuario logueado
        const currentUser = JSON.parse(localStorage.getItem('repumovil_user') || 'null');
        if (!currentUser) {
            window.location.href = 'login-dinamico.php';
        }

        let allProducts = [];
        let filteredProducts = [];

        // Cargar datos iniciales
        loadInitialData();

        async function loadInitialData() {
            try {
                // Cargar repuestos
                const response = await fetch('../api/repuestos.php');
                const result = await response.json();

                if (result.success) {
                    allProducts = result.data;
                    filteredProducts = [...allProducts];

                    // Llenar filtros
                    populateFilters(result.filters);

                    // Mostrar productos
                    displayProducts(filteredProducts);

                    // Actualizar contador del carrito
                    updateCartCount();
                } else {
                    showError('Error al cargar repuestos: ' + result.error);
                }
            } catch (error) {
                showError('Error de conexión: ' + error.message);
            }
        }

        function populateFilters(filters) {
            const categorySelect = document.getElementById('categoryFilter');
            const brandSelect = document.getElementById('brandFilter');

            // Llenar categorías
            filters.categorias.forEach(categoria => {
                const option = document.createElement('option');
                option.value = categoria;
                option.textContent = categoria;
                categorySelect.appendChild(option);
            });

            // Llenar marcas
            filters.marcas.forEach(marca => {
                const option = document.createElement('option');
                option.value = marca;
                option.textContent = marca;
                brandSelect.appendChild(option);
            });
        }

        function displayProducts(products) {
            const grid = document.getElementById('productsGrid');
            const count = document.getElementById('productsCount');

            count.textContent = `${products.length} repuestos encontrados`;

            if (products.length === 0) {
                grid.innerHTML = `
                    <div style="grid-column: 1 / -1; text-align: center; padding: 50px; color: #666;">
                        <i class="fas fa-search" style="font-size: 3rem; margin-bottom: 20px;"></i>
                        <p>No se encontraron repuestos con los filtros seleccionados</p>
                    </div>
                `;
                return;
            }

            grid.innerHTML = products.map(product => `
                <div class="product-card">
                    <div class="product-header">
                        <div>
                            <h3 class="product-name">${product.nombre}</h3>
                            <p class="product-brand">${product.marca} - ${product.modelo || 'Universal'}</p>
                        </div>
                        <div class="product-price">$${parseFloat(product.precio).toFixed(2)}</div>
                    </div>

                    <p class="product-description">${product.descripcion}</p>

                    <div class="product-details">
                        <span class="product-category">${product.categoria}</span>
                        <span class="product-stock">Stock: ${product.stock}</span>
                    </div>

                    <div class="product-actions">
                        <input type="number" class="quantity-input" value="1" min="1" max="${product.stock}" id="qty-${product.id}">
                        <button class="add-to-cart-btn" onclick="addToCart(${product.id})" ${product.stock === 0 ? 'disabled' : ''}>
                            <i class="fas fa-cart-plus"></i> ${product.stock === 0 ? 'Sin Stock' : 'Agregar'}
                        </button>
                    </div>
                </div>
            `).join('');
        }

        function searchProducts() {
            const search = document.getElementById('searchInput').value.toLowerCase();
            const category = document.getElementById('categoryFilter').value;
            const brand = document.getElementById('brandFilter').value;

            filteredProducts = allProducts.filter(product => {
                const matchesSearch = !search ||
                    product.nombre.toLowerCase().includes(search) ||
                    product.descripcion.toLowerCase().includes(search) ||
                    product.codigo_producto.toLowerCase().includes(search);

                const matchesCategory = !category || product.categoria === category;
                const matchesBrand = !brand || product.marca === brand;

                return matchesSearch && matchesCategory && matchesBrand;
            });

            displayProducts(filteredProducts);
        }

        async function addToCart(productId) {
            const quantity = parseInt(document.getElementById(`qty-${productId}`).value);

            if (quantity <= 0) {
                alert('La cantidad debe ser mayor a 0');
                return;
            }

            try {
                const response = await fetch('../api/carrito.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        user_id: currentUser.id,
                        repuesto_id: productId,
                        cantidad: quantity
                    })
                });

                const result = await response.json();

                if (result.success) {
                    alert('Producto agregado al changuito');
                    updateCartCount();
                } else {
                    alert('Error: ' + result.error);
                }
            } catch (error) {
                alert('Error de conexión: ' + error.message);
            }
        }

        async function updateCartCount() {
            try {
                const response = await fetch(`../api/carrito.php?user_id=${currentUser.id}`);
                const result = await response.json();

                if (result.success) {
                    const count = result.data.summary.item_count;
                    document.getElementById('cartCount').textContent = count;
                }
            } catch (error) {
                console.error('Error al actualizar contador del changuito:', error);
            }
        }

        function showError(message) {
            document.getElementById('productsGrid').innerHTML = `
                <div style="grid-column: 1 / -1; text-align: center; padding: 50px; color: var(--danger-color);">
                    <i class="fas fa-exclamation-triangle" style="font-size: 3rem; margin-bottom: 20px;"></i>
                    <p>${message}</p>
                </div>
            `;
        }

        // Buscar al presionar Enter
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchProducts();
            }
        });
    </script>
</body>
</html>
