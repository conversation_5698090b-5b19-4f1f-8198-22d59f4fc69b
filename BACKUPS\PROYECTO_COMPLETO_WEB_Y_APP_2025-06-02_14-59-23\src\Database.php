<?php

namespace Src;

/**
 * Clase para gestionar la conexión a la base de datos
 */
class Database {
    private $host = 'localhost';
    private $db_name = 'autoconnect_bd';
    private $username = 'root';
    private $password = '';
    private $conn;
    private static $instance = null;

    /**
     * Constructor privado para implementar Singleton
     */
    private function __construct() {
        try {
            $this->conn = new \PDO(
                "mysql:host={$this->host};dbname={$this->db_name}",
                $this->username,
                $this->password
            );
            $this->conn->setAttribute(\PDO::ATTR_ERRMODE, \PDO::ERRMODE_EXCEPTION);
            $this->conn->setAttribute(\PDO::ATTR_DEFAULT_FETCH_MODE, \PDO::FETCH_ASSOC);
            $this->conn->exec("SET NAMES utf8");
        } catch (\PDOException $e) {
            echo "Error de conexión: " . $e->getMessage();
            die();
        }
    }

    /**
     * Obtiene una instancia de la conexión a la base de datos (Singleton)
     *
     * @return Database
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Obtiene la conexión PDO
     *
     * @return \PDO
     */
    public function getConnection() {
        return $this->conn;
    }

    /**
     * Ejecuta una consulta SQL y devuelve el resultado
     *
     * @param string $query Consulta SQL
     * @param array $params Parámetros para la consulta
     * @return array|bool
     */
    public function query($query, $params = []) {
        try {
            $stmt = $this->conn->prepare($query);
            $stmt->execute($params);
            return $stmt;
        } catch (\PDOException $e) {
            echo "Error en la consulta: " . $e->getMessage();
            return false;
        }
    }

    /**
     * Obtiene un solo registro de la base de datos
     *
     * @param string $query Consulta SQL
     * @param array $params Parámetros para la consulta
     * @return array|bool
     */
    public function single($query, $params = []) {
        $stmt = $this->query($query, $params);
        if ($stmt) {
            return $stmt->fetch();
        }
        return false;
    }

    /**
     * Obtiene todos los registros de la base de datos
     *
     * @param string $query Consulta SQL
     * @param array $params Parámetros para la consulta
     * @return array|bool
     */
    public function resultSet($query, $params = []) {
        $stmt = $this->query($query, $params);
        if ($stmt) {
            return $stmt->fetchAll();
        }
        return false;
    }

    /**
     * Obtiene el número de filas afectadas por la última consulta
     *
     * @return int
     */
    public function rowCount($query, $params = []) {
        $stmt = $this->query($query, $params);
        if ($stmt) {
            return $stmt->rowCount();
        }
        return 0;
    }

    /**
     * Obtiene el último ID insertado
     *
     * @return string
     */
    public function lastInsertId() {
        return $this->conn->lastInsertId();
    }
}
