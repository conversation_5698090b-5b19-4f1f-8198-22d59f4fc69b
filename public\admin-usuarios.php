<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RepuMovil - Gestión de Usuarios 👥</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #FF6B35;
            --secondary-color: #FFA500;
            --dark-color: #2c3e50;
            --light-color: #f8f9fa;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
            color: white;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .title {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .nav-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 30px;
        }

        .nav-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .nav-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .nav-btn.active {
            background: white;
            color: var(--primary-color);
        }

        .stats-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            color: var(--dark-color);
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #666;
        }

        .filters-panel {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            color: var(--dark-color);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
        }

        .filter-label {
            font-weight: bold;
            margin-bottom: 5px;
            color: var(--primary-color);
        }

        .filter-input {
            padding: 10px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 0.9rem;
        }

        .filter-input:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .filter-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .filter-btn:hover {
            background: #e55a2b;
        }

        .users-table {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
        }

        .table-header {
            background: var(--primary-color);
            color: white;
            padding: 20px;
            font-size: 1.2rem;
            font-weight: bold;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .add-user-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .add-user-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        th {
            background: var(--light-color);
            font-weight: bold;
            color: var(--dark-color);
        }

        td {
            color: var(--dark-color);
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--info-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin-right: 10px;
        }

        .user-info {
            display: flex;
            align-items: center;
        }

        .user-details {
            flex: 1;
        }

        .user-name {
            font-weight: bold;
            margin-bottom: 3px;
        }

        .user-email {
            font-size: 0.8rem;
            color: #666;
        }

        .status-badge {
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
            text-transform: uppercase;
        }

        .status-activo {
            background: #d4edda;
            color: #155724;
        }

        .status-inactivo {
            background: #f8d7da;
            color: #721c24;
        }

        .status-suspendido {
            background: #fff3cd;
            color: #856404;
        }

        .role-badge {
            padding: 4px 8px;
            border-radius: 10px;
            font-size: 0.7rem;
            font-weight: bold;
            text-transform: uppercase;
        }

        .role-admin {
            background: #6f42c1;
            color: white;
        }

        .role-delivery {
            background: var(--info-color);
            color: white;
        }

        .role-cliente {
            background: var(--success-color);
            color: white;
        }

        .role-proveedor {
            background: var(--warning-color);
            color: white;
        }

        .actions-cell {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            font-size: 0.8rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-edit {
            background: var(--info-color);
            color: white;
        }

        .btn-edit:hover {
            background: #138496;
        }

        .btn-suspend {
            background: var(--warning-color);
            color: white;
        }

        .btn-suspend:hover {
            background: #e0a800;
        }

        .btn-delete {
            background: var(--danger-color);
            color: white;
        }

        .btn-delete:hover {
            background: #c82333;
        }

        .rating-display {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .rating-stars {
            color: #ffd700;
        }

        .rating-count {
            font-size: 0.8rem;
            color: #666;
        }

        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 20px;
        }

        .page-btn {
            background: white;
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
            padding: 8px 15px;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .page-btn:hover,
        .page-btn.active {
            background: var(--primary-color);
            color: white;
        }

        @media (max-width: 768px) {
            .filters-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-overview {
                grid-template-columns: repeat(2, 1fr);
            }
            
            table {
                font-size: 0.8rem;
            }
            
            th, td {
                padding: 10px 8px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">👥📊</div>
            <h1 class="title">Gestión de Usuarios</h1>
            <p class="subtitle">Administración completa de usuarios del sistema RepuMovil</p>
        </div>

        <!-- Navegación -->
        <div class="nav-buttons">
            <a href="admin-dashboard.php" class="nav-btn">
                <i class="fas fa-chart-line"></i>
                Dashboard
            </a>
            <button class="nav-btn active">
                <i class="fas fa-users"></i>
                Usuarios
            </button>
            <a href="admin-reportes.php" class="nav-btn">
                <i class="fas fa-file-alt"></i>
                Reportes
            </a>
            <a href="admin-configuracion.php" class="nav-btn">
                <i class="fas fa-cog"></i>
                Configuración
            </a>
        </div>

        <!-- Estadísticas de Usuarios -->
        <div class="stats-overview">
            <div class="stat-card">
                <div class="stat-number" id="total-usuarios">195</div>
                <div class="stat-label">Total Usuarios</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="deliveries-count">24</div>
                <div class="stat-label">Deliveries</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="clientes-count">156</div>
                <div class="stat-label">Clientes</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="proveedores-count">12</div>
                <div class="stat-label">Proveedores</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="activos-count">187</div>
                <div class="stat-label">Activos</div>
            </div>
        </div>

        <!-- Panel de Filtros -->
        <div class="filters-panel">
            <div class="filters-grid">
                <div class="filter-group">
                    <label class="filter-label">Buscar Usuario</label>
                    <input type="text" class="filter-input" id="search-input" placeholder="Nombre, email o teléfono...">
                </div>
                <div class="filter-group">
                    <label class="filter-label">Tipo de Usuario</label>
                    <select class="filter-input" id="role-filter">
                        <option value="">Todos los tipos</option>
                        <option value="delivery">Deliveries</option>
                        <option value="cliente">Clientes</option>
                        <option value="proveedor">Proveedores</option>
                        <option value="admin">Administradores</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label class="filter-label">Estado</label>
                    <select class="filter-input" id="status-filter">
                        <option value="">Todos los estados</option>
                        <option value="activo">Activos</option>
                        <option value="inactivo">Inactivos</option>
                        <option value="suspendido">Suspendidos</option>
                    </select>
                </div>
                <div class="filter-group">
                    <button class="filter-btn" onclick="aplicarFiltros()">
                        <i class="fas fa-search"></i>
                        Filtrar
                    </button>
                </div>
            </div>
        </div>

        <!-- Tabla de Usuarios -->
        <div class="users-table">
            <div class="table-header">
                <span>
                    <i class="fas fa-users"></i>
                    Lista de Usuarios (195)
                </span>
                <button class="add-user-btn" onclick="agregarUsuario()">
                    <i class="fas fa-plus"></i>
                    Agregar Usuario
                </button>
            </div>
            
            <table>
                <thead>
                    <tr>
                        <th>Usuario</th>
                        <th>Tipo</th>
                        <th>Estado</th>
                        <th>Registro</th>
                        <th>Calificación</th>
                        <th>Actividad</th>
                        <th>Acciones</th>
                    </tr>
                </thead>
                <tbody id="users-table-body">
                    <!-- Los usuarios se cargarán dinámicamente -->
                </tbody>
            </table>
        </div>

        <!-- Paginación -->
        <div class="pagination">
            <button class="page-btn" onclick="cambiarPagina(1)">1</button>
            <button class="page-btn active" onclick="cambiarPagina(2)">2</button>
            <button class="page-btn" onclick="cambiarPagina(3)">3</button>
            <button class="page-btn" onclick="cambiarPagina(4)">4</button>
            <button class="page-btn" onclick="cambiarPagina(5)">5</button>
        </div>
    </div>

    <script>
        // PASO 10: Datos de ejemplo de usuarios
        const usuarios = [
            {
                id: 1,
                nombre: 'Juan Pérez',
                email: '<EMAIL>',
                telefono: '+54 ************',
                tipo: 'delivery',
                estado: 'activo',
                registro: '2024-01-15',
                calificacion: 4.8,
                total_calificaciones: 156,
                ultima_actividad: '2024-01-20 14:30:00',
                entregas_completadas: 234
            },
            {
                id: 2,
                nombre: 'María González',
                email: '<EMAIL>',
                telefono: '+54 ************',
                tipo: 'delivery',
                estado: 'activo',
                registro: '2024-01-10',
                calificacion: 4.9,
                total_calificaciones: 89,
                ultima_actividad: '2024-01-20 13:45:00',
                entregas_completadas: 145
            },
            {
                id: 3,
                nombre: 'Carlos Mendoza',
                email: '<EMAIL>',
                telefono: '+54 ************',
                tipo: 'admin',
                estado: 'activo',
                registro: '2023-12-01',
                calificacion: null,
                total_calificaciones: 0,
                ultima_actividad: '2024-01-20 15:00:00',
                entregas_completadas: 0
            },
            {
                id: 4,
                nombre: 'Taller Mecánico Central',
                email: '<EMAIL>',
                telefono: '+54 ************',
                tipo: 'cliente',
                estado: 'activo',
                registro: '2024-01-05',
                calificacion: null,
                total_calificaciones: 0,
                ultima_actividad: '2024-01-20 12:15:00',
                pedidos_realizados: 45
            },
            {
                id: 5,
                nombre: 'Repuestos San Juan',
                email: '<EMAIL>',
                telefono: '+54 ************',
                tipo: 'proveedor',
                estado: 'activo',
                registro: '2023-11-20',
                calificacion: 4.7,
                total_calificaciones: 234,
                ultima_actividad: '2024-01-20 11:30:00',
                pedidos_procesados: 567
            }
        ];

        let paginaActual = 1;
        let usuariosPorPagina = 10;
        let usuariosFiltrados = [...usuarios];

        // PASO 10: Renderizar tabla de usuarios
        function renderUsuarios() {
            const tbody = document.getElementById('users-table-body');
            const inicio = (paginaActual - 1) * usuariosPorPagina;
            const fin = inicio + usuariosPorPagina;
            const usuariosPagina = usuariosFiltrados.slice(inicio, fin);

            tbody.innerHTML = usuariosPagina.map(usuario => `
                <tr>
                    <td>
                        <div class="user-info">
                            <div class="user-avatar">${usuario.nombre.charAt(0)}</div>
                            <div class="user-details">
                                <div class="user-name">${usuario.nombre}</div>
                                <div class="user-email">${usuario.email}</div>
                            </div>
                        </div>
                    </td>
                    <td>
                        <span class="role-badge role-${usuario.tipo}">
                            ${getTipoDisplayName(usuario.tipo)}
                        </span>
                    </td>
                    <td>
                        <span class="status-badge status-${usuario.estado}">
                            ${getEstadoDisplayName(usuario.estado)}
                        </span>
                    </td>
                    <td>${formatearFecha(usuario.registro)}</td>
                    <td>
                        ${usuario.calificacion ? `
                            <div class="rating-display">
                                <span class="rating-stars">⭐ ${usuario.calificacion}</span>
                                <span class="rating-count">(${usuario.total_calificaciones})</span>
                            </div>
                        ` : '<span style="color: #999;">N/A</span>'}
                    </td>
                    <td>${calcularTiempoActividad(usuario.ultima_actividad)}</td>
                    <td>
                        <div class="actions-cell">
                            <button class="action-btn btn-edit" onclick="editarUsuario(${usuario.id})">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="action-btn btn-suspend" onclick="suspenderUsuario(${usuario.id})">
                                <i class="fas fa-pause"></i>
                            </button>
                            <button class="action-btn btn-delete" onclick="eliminarUsuario(${usuario.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        // PASO 10: Funciones auxiliares
        function getTipoDisplayName(tipo) {
            const tipos = {
                'delivery': 'Delivery',
                'cliente': 'Cliente',
                'proveedor': 'Proveedor',
                'admin': 'Admin'
            };
            return tipos[tipo] || tipo;
        }

        function getEstadoDisplayName(estado) {
            const estados = {
                'activo': 'Activo',
                'inactivo': 'Inactivo',
                'suspendido': 'Suspendido'
            };
            return estados[estado] || estado;
        }

        function formatearFecha(fechaStr) {
            const fecha = new Date(fechaStr);
            return fecha.toLocaleDateString('es-ES');
        }

        function calcularTiempoActividad(fechaStr) {
            const ahora = new Date();
            const fecha = new Date(fechaStr);
            const diff = Math.floor((ahora - fecha) / (1000 * 60));
            
            if (diff < 60) return `${diff} min`;
            if (diff < 1440) return `${Math.floor(diff / 60)} h`;
            return `${Math.floor(diff / 1440)} días`;
        }

        // PASO 10: Funciones de gestión
        function aplicarFiltros() {
            const busqueda = document.getElementById('search-input').value.toLowerCase();
            const tipoFiltro = document.getElementById('role-filter').value;
            const estadoFiltro = document.getElementById('status-filter').value;

            usuariosFiltrados = usuarios.filter(usuario => {
                const coincideBusqueda = !busqueda || 
                    usuario.nombre.toLowerCase().includes(busqueda) ||
                    usuario.email.toLowerCase().includes(busqueda) ||
                    usuario.telefono.includes(busqueda);
                
                const coincideTipo = !tipoFiltro || usuario.tipo === tipoFiltro;
                const coincideEstado = !estadoFiltro || usuario.estado === estadoFiltro;

                return coincideBusqueda && coincideTipo && coincideEstado;
            });

            paginaActual = 1;
            renderUsuarios();
            actualizarContadores();
        }

        function cambiarPagina(pagina) {
            paginaActual = pagina;
            renderUsuarios();
            
            // Actualizar botones de paginación
            document.querySelectorAll('.page-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
        }

        function agregarUsuario() {
            alert('🆕 Agregar Nuevo Usuario\n\nAbriendo formulario para crear un nuevo usuario...\n\n• Información personal\n• Tipo de usuario\n• Permisos y configuración\n• Verificación de datos');
        }

        function editarUsuario(id) {
            const usuario = usuarios.find(u => u.id === id);
            alert(`✏️ Editar Usuario\n\nEditando: ${usuario.nombre}\n\n• Información personal\n• Estado de la cuenta\n• Permisos y roles\n• Historial de actividad`);
        }

        function suspenderUsuario(id) {
            const usuario = usuarios.find(u => u.id === id);
            if (confirm(`⚠️ ¿Suspender Usuario?\n\n¿Estás seguro de que quieres suspender a ${usuario.nombre}?\n\nEsta acción puede revertirse posteriormente.`)) {
                usuario.estado = 'suspendido';
                renderUsuarios();
                alert('✅ Usuario suspendido exitosamente');
            }
        }

        function eliminarUsuario(id) {
            const usuario = usuarios.find(u => u.id === id);
            if (confirm(`🗑️ ¿Eliminar Usuario?\n\n¿Estás seguro de que quieres eliminar a ${usuario.nombre}?\n\n⚠️ ESTA ACCIÓN NO SE PUEDE DESHACER`)) {
                const index = usuarios.findIndex(u => u.id === id);
                usuarios.splice(index, 1);
                aplicarFiltros();
                alert('✅ Usuario eliminado exitosamente');
            }
        }

        function actualizarContadores() {
            const stats = {
                total: usuarios.length,
                deliveries: usuarios.filter(u => u.tipo === 'delivery').length,
                clientes: usuarios.filter(u => u.tipo === 'cliente').length,
                proveedores: usuarios.filter(u => u.tipo === 'proveedor').length,
                activos: usuarios.filter(u => u.estado === 'activo').length
            };

            document.getElementById('total-usuarios').textContent = stats.total;
            document.getElementById('deliveries-count').textContent = stats.deliveries;
            document.getElementById('clientes-count').textContent = stats.clientes;
            document.getElementById('proveedores-count').textContent = stats.proveedores;
            document.getElementById('activos-count').textContent = stats.activos;
        }

        // PASO 10: Inicializar
        document.addEventListener('DOMContentLoaded', function() {
            renderUsuarios();
            actualizarContadores();
            
            // Event listeners para filtros en tiempo real
            document.getElementById('search-input').addEventListener('input', aplicarFiltros);
            document.getElementById('role-filter').addEventListener('change', aplicarFiltros);
            document.getElementById('status-filter').addEventListener('change', aplicarFiltros);
        });
    </script>
</body>
</html>
