<?php
// Mostrar todos los errores
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Depuración del Sistema</h1>";

// Verificar si los archivos existen
$files = [
    '../src/Database.php',
    '../src/UserDB.php',
    '../src/User.php',
    '../src/UserManager.php',
    '../db/init_db.php',
    'login.php',
    'dashboard-workshop.php'
];

echo "<h2>Verificación de archivos:</h2>";
echo "<ul>";
foreach ($files as $file) {
    if (file_exists($file)) {
        echo "<li>$file: ✅ Existe</li>";
    } else {
        echo "<li>$file: ❌ No existe</li>";
    }
}
echo "</ul>";

// Verificar la conexión a la base de datos
echo "<h2>Verificación de la base de datos:</h2>";
try {
    require_once '../src/Database.php';
    $db = Src\Database::getInstance();
    $conn = $db->getConnection();
    echo "<p>✅ Conexión a la base de datos exitosa</p>";
    
    // Verificar si existen las tablas
    $tables = ['roles', 'users', 'workshops', 'suppliers'];
    $existingTables = [];
    
    foreach ($tables as $table) {
        $query = "SHOW TABLES LIKE '$table'";
        $result = $db->query($query);
        
        if ($result && $result->rowCount() > 0) {
            $existingTables[] = $table;
        }
    }
    
    echo "<p>Tablas en la base de datos:</p>";
    echo "<ul>";
    foreach ($tables as $table) {
        $exists = in_array($table, $existingTables);
        echo "<li>$table: " . ($exists ? "✅ Existe" : "❌ No existe") . "</li>";
    }
    echo "</ul>";
    
    // Si no existen todas las tablas, sugerir inicializar la base de datos
    if (count($existingTables) !== count($tables)) {
        echo "<p>⚠️ No todas las tablas existen. <a href='init_db.php'>Inicializar la base de datos</a></p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error de conexión a la base de datos: " . $e->getMessage() . "</p>";
    echo "<p>⚠️ Es posible que necesites <a href='init_db.php'>inicializar la base de datos</a></p>";
}

// Verificar las clases
echo "<h2>Verificación de clases:</h2>";
try {
    require_once '../src/UserDB.php';
    echo "<p>✅ Clase UserDB cargada correctamente</p>";
} catch (Exception $e) {
    echo "<p>❌ Error al cargar la clase UserDB: " . $e->getMessage() . "</p>";
}

try {
    require_once '../src/User.php';
    echo "<p>✅ Clase User cargada correctamente</p>";
} catch (Exception $e) {
    echo "<p>❌ Error al cargar la clase User: " . $e->getMessage() . "</p>";
}

try {
    require_once '../src/UserManager.php';
    echo "<p>✅ Clase UserManager cargada correctamente</p>";
} catch (Exception $e) {
    echo "<p>❌ Error al cargar la clase UserManager: " . $e->getMessage() . "</p>";
}

// Verificar la sesión
echo "<h2>Verificación de la sesión:</h2>";
session_start();
if (isset($_SESSION['user_id'])) {
    echo "<p>✅ Sesión activa para el usuario ID: " . $_SESSION['user_id'] . "</p>";
    echo "<p>Nombre de usuario: " . ($_SESSION['username'] ?? 'No disponible') . "</p>";
    echo "<p>Rol: " . ($_SESSION['role'] ?? 'No disponible') . "</p>";
} else {
    echo "<p>❌ No hay sesión activa</p>";
}

// Mostrar información de PHP
echo "<h2>Información de PHP:</h2>";
echo "<p>Versión de PHP: " . phpversion() . "</p>";
echo "<p>Extensiones cargadas:</p>";
echo "<ul>";
$extensions = ['pdo', 'pdo_mysql', 'mysqli', 'json', 'session'];
foreach ($extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<li>$ext: ✅ Cargada</li>";
    } else {
        echo "<li>$ext: ❌ No cargada</li>";
    }
}
echo "</ul>";

// Mostrar información del servidor
echo "<h2>Información del servidor:</h2>";
echo "<p>Servidor: " . $_SERVER['SERVER_SOFTWARE'] . "</p>";
echo "<p>Documento raíz: " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p>Ruta del script: " . $_SERVER['SCRIPT_FILENAME'] . "</p>";

// Mostrar enlaces útiles
echo "<h2>Enlaces útiles:</h2>";
echo "<ul>";
echo "<li><a href='init_db.php'>Inicializar la base de datos</a></li>";
echo "<li><a href='test_db.php'>Probar la conexión a la base de datos</a></li>";
echo "<li><a href='login.php'>Página de login</a></li>";
echo "</ul>";
?>
