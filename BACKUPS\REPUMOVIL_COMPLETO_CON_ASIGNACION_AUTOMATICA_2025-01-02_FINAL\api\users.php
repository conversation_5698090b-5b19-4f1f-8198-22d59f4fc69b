<?php
// Configurar encabezados CORS
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header("Content-Type: application/json");

// Si es una solicitud OPTIONS, terminar aquí
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Incluir archivo de configuración de la base de datos
require_once '../src/config.php';

try {
    // Conectar a la base de datos
    $dsn = "mysql:host={$config['db']['host']};dbname={$config['db']['name']};charset={$config['db']['charset']}";
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ];
    
    $pdo = new PDO($dsn, $config['db']['user'], $config['db']['pass'], $options);
    
    // Consulta para obtener usuarios con información básica (sin contraseñas)
    $stmt = $pdo->prepare("
        SELECT u.id, u.username, u.email, u.role_id, r.name as role_name, u.created_at, u.last_login, u.status
        FROM users u
        JOIN roles r ON u.role_id = r.id
        ORDER BY u.created_at DESC
    ");
    $stmt->execute();
    
    // Obtener resultados
    $users = $stmt->fetchAll();
    
    // Formatear los datos para la respuesta
    $formattedUsers = [];
    foreach ($users as $user) {
        // Determinar el display_name basado en el rol
        $displayName = $user['username'];
        if ($user['role_name'] === 'admin') {
            $displayName = 'Administrador';
        } elseif ($user['role_name'] === 'workshop') {
            $displayName = 'Taller';
        }
        
        $formattedUsers[] = [
            'id' => $user['id'],
            'username' => $user['username'],
            'email' => $user['email'],
            'role_id' => $user['role_id'],
            'role_name' => $user['role_name'],
            'display_name' => $displayName,
            'created_at' => $user['created_at'],
            'status' => $user['status']
        ];
    }
    
    // Devolver respuesta exitosa
    echo json_encode([
        'success' => true,
        'users' => $formattedUsers
    ]);
    
} catch (PDOException $e) {
    // En caso de error, devolver mensaje de error
    echo json_encode([
        'success' => false,
        'message' => 'Error al conectar con la base de datos: ' . $e->getMessage()
    ]);
}
?>





