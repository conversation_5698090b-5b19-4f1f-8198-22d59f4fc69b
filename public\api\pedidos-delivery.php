<?php
// PASO 3: API para manejo de pedidos por deliveries
// Sistema completo de aceptación/rechazo de pedidos

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Manejar preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Configuración de base de datos
$host = 'localhost';
$dbname = 'mechanical_workshop';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    sendResponse(false, 'Error de conexión a la base de datos: ' . $e->getMessage());
}

// Función para enviar respuesta JSON
function sendResponse($success, $message, $data = null) {
    echo json_encode([
        'success' => $success,
        'message' => $message,
        'data' => $data,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    exit();
}

// Manejar diferentes métodos HTTP
switch ($_SERVER['REQUEST_METHOD']) {
    case 'GET':
        $action = $_GET['action'] ?? '';
        
        switch ($action) {
            case 'get_pedidos_disponibles':
                obtenerPedidosDisponibles($pdo, $_GET);
                break;
                
            case 'get_pedidos_delivery':
                obtenerPedidosDelivery($pdo, $_GET);
                break;
                
            case 'get_pedido_detalle':
                obtenerDetallePedido($pdo, $_GET);
                break;
                
            default:
                sendResponse(false, 'Acción GET no válida');
        }
        break;
        
    case 'POST':
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input || !isset($input['action'])) {
            sendResponse(false, 'Acción requerida');
        }
        
        switch ($input['action']) {
            case 'aceptar_pedido':
                aceptarPedido($pdo, $input);
                break;
                
            case 'rechazar_pedido':
                rechazarPedido($pdo, $input);
                break;
                
            case 'actualizar_estado_pedido':
                actualizarEstadoPedido($pdo, $input);
                break;
                
            case 'crear_pedido_test':
                crearPedidoTest($pdo, $input);
                break;
                
            default:
                sendResponse(false, 'Acción POST no válida');
        }
        break;
        
    default:
        sendResponse(false, 'Método no permitido');
}

/**
 * PASO 3: Obtener pedidos disponibles para asignar
 */
function obtenerPedidosDisponibles($pdo, $params) {
    try {
        $deliveryId = $params['delivery_id'] ?? null;
        $limite = $params['limite'] ?? 10;
        
        // Obtener pedidos nuevos o sin asignar
        $stmt = $pdo->prepare("
            SELECT 
                p.*,
                c.nombre as cliente_nombre,
                c.telefono as cliente_telefono,
                c.direccion as cliente_direccion
            FROM pedidos p
            LEFT JOIN clientes c ON p.cliente_id = c.id
            WHERE p.estado IN ('nuevo', 'pendiente') 
                AND (p.delivery_id IS NULL OR p.delivery_id = 0)
            ORDER BY 
                CASE 
                    WHEN p.prioridad = 'urgente' THEN 1
                    WHEN p.prioridad = 'alta' THEN 2
                    ELSE 3
                END,
                p.fecha_creacion ASC
            LIMIT ?
        ");
        
        $stmt->execute([$limite]);
        $pedidos = $stmt->fetchAll();
        
        // Enriquecer datos de cada pedido
        foreach ($pedidos as &$pedido) {
            // Calcular distancia y tiempo estimado (simulado)
            $pedido['distancia'] = round(rand(5, 200) / 10, 1); // 0.5 - 20 km
            $pedido['tiempo_estimado'] = round($pedido['distancia'] * 3 + rand(5, 15)); // minutos
            $pedido['ganancia_estimada'] = round($pedido['total'] * 0.15); // 15% comisión
            
            // Obtener items del pedido
            $stmtItems = $pdo->prepare("
                SELECT pi.*, r.nombre as repuesto_nombre
                FROM pedido_items pi
                LEFT JOIN repuestos r ON pi.repuesto_id = r.id
                WHERE pi.pedido_id = ?
            ");
            $stmtItems->execute([$pedido['id']]);
            $items = $stmtItems->fetchAll();
            
            $pedido['items'] = array_map(function($item) {
                return $item['repuesto_nombre'] . ' (x' . $item['cantidad'] . ')';
            }, $items);
        }
        
        sendResponse(true, 'Pedidos disponibles obtenidos', $pedidos);
        
    } catch (Exception $e) {
        sendResponse(false, 'Error obteniendo pedidos: ' . $e->getMessage());
    }
}

/**
 * PASO 3: Aceptar pedido por delivery
 */
function aceptarPedido($pdo, $data) {
    try {
        $pedidoId = $data['pedido_id'];
        $deliveryId = $data['delivery_id'];
        $ubicacionDelivery = $data['ubicacion_delivery'] ?? null;
        
        if (!$pedidoId || !$deliveryId) {
            sendResponse(false, 'pedido_id y delivery_id son requeridos');
        }
        
        // Verificar que el pedido esté disponible
        $stmt = $pdo->prepare("
            SELECT * FROM pedidos 
            WHERE id = ? AND estado IN ('nuevo', 'pendiente') 
                AND (delivery_id IS NULL OR delivery_id = 0)
        ");
        $stmt->execute([$pedidoId]);
        $pedido = $stmt->fetch();
        
        if (!$pedido) {
            sendResponse(false, 'Pedido no disponible o ya asignado');
        }
        
        // Verificar que el delivery esté disponible
        $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$deliveryId]);
        $delivery = $stmt->fetch();
        
        if (!$delivery) {
            sendResponse(false, 'Delivery no encontrado');
        }
        
        // Iniciar transacción
        $pdo->beginTransaction();
        
        try {
            // Asignar pedido al delivery
            $stmt = $pdo->prepare("
                UPDATE pedidos 
                SET delivery_id = ?, estado = 'asignado', fecha_asignacion = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$deliveryId, $pedidoId]);
            
            // Registrar en historial de pedidos
            $stmt = $pdo->prepare("
                INSERT INTO pedido_historial (
                    pedido_id, delivery_id, estado_anterior, estado_nuevo, 
                    fecha_cambio, observaciones
                ) VALUES (?, ?, ?, ?, NOW(), ?)
            ");
            $stmt->execute([
                $pedidoId, 
                $deliveryId, 
                $pedido['estado'], 
                'asignado',
                'Pedido aceptado por delivery'
            ]);
            
            // Actualizar estado del delivery
            $stmt = $pdo->prepare("
                UPDATE delivery_status 
                SET estado = 'ocupado', pedido_actual = ?, ultima_actualizacion = NOW()
                WHERE delivery_id = ?
            ");
            $stmt->execute([$pedidoId, $deliveryId]);
            
            // Si no existe el registro de estado, crearlo
            if ($stmt->rowCount() === 0) {
                $stmt = $pdo->prepare("
                    INSERT INTO delivery_status (delivery_id, estado, pedido_actual, ultima_actualizacion)
                    VALUES (?, 'ocupado', ?, NOW())
                ");
                $stmt->execute([$deliveryId, $pedidoId]);
            }
            
            $pdo->commit();
            
            // Enviar notificación al cliente (opcional)
            enviarNotificacionCliente($pdo, $pedidoId, 'pedido_asignado');
            
            error_log("✅ Pedido $pedidoId aceptado por delivery $deliveryId");
            
            sendResponse(true, 'Pedido aceptado exitosamente', [
                'pedido_id' => $pedidoId,
                'delivery_id' => $deliveryId,
                'nuevo_estado' => 'asignado'
            ]);
            
        } catch (Exception $e) {
            $pdo->rollback();
            throw $e;
        }
        
    } catch (Exception $e) {
        error_log("❌ Error aceptando pedido: " . $e->getMessage());
        sendResponse(false, 'Error aceptando pedido: ' . $e->getMessage());
    }
}

/**
 * PASO 3: Rechazar pedido por delivery
 */
function rechazarPedido($pdo, $data) {
    try {
        $pedidoId = $data['pedido_id'];
        $deliveryId = $data['delivery_id'];
        $motivo = $data['motivo'] ?? 'no_especificado';
        $observaciones = $data['observaciones'] ?? '';
        
        if (!$pedidoId || !$deliveryId) {
            sendResponse(false, 'pedido_id y delivery_id son requeridos');
        }
        
        // Registrar rechazo en historial
        $stmt = $pdo->prepare("
            INSERT INTO pedido_rechazos (
                pedido_id, delivery_id, motivo, observaciones, fecha_rechazo
            ) VALUES (?, ?, ?, ?, NOW())
        ");
        $stmt->execute([$pedidoId, $deliveryId, $motivo, $observaciones]);
        
        // Incrementar contador de rechazos del pedido
        $stmt = $pdo->prepare("
            UPDATE pedidos 
            SET rechazos_count = COALESCE(rechazos_count, 0) + 1,
                ultima_actualizacion = NOW()
            WHERE id = ?
        ");
        $stmt->execute([$pedidoId]);
        
        // Si el pedido ha sido rechazado muchas veces, marcarlo como problemático
        $stmt = $pdo->prepare("SELECT rechazos_count FROM pedidos WHERE id = ?");
        $stmt->execute([$pedidoId]);
        $rechazos = $stmt->fetchColumn();
        
        if ($rechazos >= 3) {
            $stmt = $pdo->prepare("
                UPDATE pedidos 
                SET estado = 'problematico', observaciones = CONCAT(COALESCE(observaciones, ''), ' - Múltiples rechazos')
                WHERE id = ?
            ");
            $stmt->execute([$pedidoId]);
        }
        
        error_log("❌ Pedido $pedidoId rechazado por delivery $deliveryId. Motivo: $motivo");
        
        sendResponse(true, 'Pedido rechazado registrado', [
            'pedido_id' => $pedidoId,
            'delivery_id' => $deliveryId,
            'motivo' => $motivo,
            'total_rechazos' => $rechazos
        ]);
        
    } catch (Exception $e) {
        error_log("❌ Error rechazando pedido: " . $e->getMessage());
        sendResponse(false, 'Error rechazando pedido: ' . $e->getMessage());
    }
}

/**
 * PASO 3: Actualizar estado de pedido
 */
function actualizarEstadoPedido($pdo, $data) {
    try {
        $pedidoId = $data['pedido_id'];
        $deliveryId = $data['delivery_id'];
        $nuevoEstado = $data['estado'];
        $observaciones = $data['observaciones'] ?? '';
        $ubicacion = $data['ubicacion'] ?? null;
        
        if (!$pedidoId || !$deliveryId || !$nuevoEstado) {
            sendResponse(false, 'pedido_id, delivery_id y estado son requeridos');
        }
        
        // Obtener estado actual
        $stmt = $pdo->prepare("SELECT estado FROM pedidos WHERE id = ? AND delivery_id = ?");
        $stmt->execute([$pedidoId, $deliveryId]);
        $estadoActual = $stmt->fetchColumn();
        
        if (!$estadoActual) {
            sendResponse(false, 'Pedido no encontrado o no asignado a este delivery');
        }
        
        // Actualizar estado del pedido
        $stmt = $pdo->prepare("
            UPDATE pedidos 
            SET estado = ?, observaciones = ?, ultima_actualizacion = NOW()
            WHERE id = ? AND delivery_id = ?
        ");
        $stmt->execute([$nuevoEstado, $observaciones, $pedidoId, $deliveryId]);
        
        // Registrar en historial
        $stmt = $pdo->prepare("
            INSERT INTO pedido_historial (
                pedido_id, delivery_id, estado_anterior, estado_nuevo, 
                fecha_cambio, observaciones
            ) VALUES (?, ?, ?, ?, NOW(), ?)
        ");
        $stmt->execute([$pedidoId, $deliveryId, $estadoActual, $nuevoEstado, $observaciones]);
        
        // Si el pedido se completó, liberar al delivery
        if ($nuevoEstado === 'entregado') {
            $stmt = $pdo->prepare("
                UPDATE delivery_status 
                SET estado = 'disponible', pedido_actual = NULL, ultima_actualizacion = NOW()
                WHERE delivery_id = ?
            ");
            $stmt->execute([$deliveryId]);
        }
        
        // Enviar notificación al cliente
        enviarNotificacionCliente($pdo, $pedidoId, $nuevoEstado);
        
        error_log("📦 Pedido $pedidoId actualizado a estado: $nuevoEstado");
        
        sendResponse(true, 'Estado actualizado exitosamente', [
            'pedido_id' => $pedidoId,
            'estado_anterior' => $estadoActual,
            'estado_nuevo' => $nuevoEstado
        ]);
        
    } catch (Exception $e) {
        error_log("❌ Error actualizando estado: " . $e->getMessage());
        sendResponse(false, 'Error actualizando estado: ' . $e->getMessage());
    }
}

/**
 * PASO 3: Crear pedido de prueba
 */
function crearPedidoTest($pdo, $data) {
    try {
        $cliente = $data['cliente'] ?? 'Cliente de Prueba';
        $direccion = $data['direccion'] ?? 'Av. Test 123, Centro';
        $total = $data['total'] ?? rand(500, 3000);
        $prioridad = $data['prioridad'] ?? 'normal';
        
        // Crear pedido de prueba
        $stmt = $pdo->prepare("
            INSERT INTO pedidos (
                cliente_nombre, direccion_entrega, total, estado, prioridad,
                fecha_creacion, observaciones
            ) VALUES (?, ?, ?, 'nuevo', ?, NOW(), 'Pedido de prueba generado automáticamente')
        ");
        
        $stmt->execute([$cliente, $direccion, $total, $prioridad]);
        $pedidoId = $pdo->lastInsertId();
        
        // Agregar items de prueba
        $itemsPrueba = [
            'Filtro de aceite',
            'Pastillas de freno',
            'Bujías NGK',
            'Aceite motor 5W30',
            'Correa de distribución'
        ];
        
        $numItems = rand(1, 3);
        for ($i = 0; $i < $numItems; $i++) {
            $item = $itemsPrueba[array_rand($itemsPrueba)];
            $cantidad = rand(1, 4);
            $precio = rand(100, 800);
            
            $stmt = $pdo->prepare("
                INSERT INTO pedido_items (pedido_id, repuesto_nombre, cantidad, precio_unitario)
                VALUES (?, ?, ?, ?)
            ");
            $stmt->execute([$pedidoId, $item, $cantidad, $precio]);
        }
        
        error_log("🧪 Pedido de prueba creado: ID $pedidoId");
        
        sendResponse(true, 'Pedido de prueba creado', [
            'pedido_id' => $pedidoId,
            'cliente' => $cliente,
            'total' => $total,
            'prioridad' => $prioridad
        ]);
        
    } catch (Exception $e) {
        error_log("❌ Error creando pedido de prueba: " . $e->getMessage());
        sendResponse(false, 'Error creando pedido de prueba: ' . $e->getMessage());
    }
}

/**
 * PASO 3: Enviar notificación al cliente (placeholder)
 */
function enviarNotificacionCliente($pdo, $pedidoId, $evento) {
    // Aquí se integraría con el sistema de notificaciones del cliente
    // Por ahora solo registramos en log
    error_log("📱 Notificación cliente - Pedido $pedidoId: $evento");
}
?>
