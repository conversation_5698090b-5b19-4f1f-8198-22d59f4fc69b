<?php
// Lista de archivos de login a eliminar
$files_to_remove = [
    'login.php',
    'login_basic.php',
    'login_final.php',
    'login_new.php',
    'login_nuevo.php',
    'login_simple.php',
    'login_ultra_simple.php',
    // Mantén login_reemplazo.php
    // Mantén logout.php
];

$success_count = 0;
$error_count = 0;
$errors = [];

echo "<h1>Limpieza de archivos de login</h1>";
echo "<p>Eliminando archivos innecesarios...</p>";
echo "<ul>";

foreach ($files_to_remove as $file) {
    if (file_exists($file)) {
        if (unlink($file)) {
            echo "<li>✅ Archivo eliminado: {$file}</li>";
            $success_count++;
        } else {
            echo "<li>❌ Error al eliminar: {$file}</li>";
            $errors[] = $file;
            $error_count++;
        }
    } else {
        echo "<li>⚠️ Archivo no encontrado: {$file}</li>";
    }
}

echo "</ul>";
echo "<p>Resumen: {$success_count} archivos eliminados, {$error_count} errores.</p>";

if ($error_count > 0) {
    echo "<p>Los siguientes archivos no pudieron ser eliminados:</p>";
    echo "<ul>";
    foreach ($errors as $error_file) {
        echo "<li>{$error_file}</li>";
    }
    echo "</ul>";
    echo "<p>Es posible que necesites eliminarlos manualmente.</p>";
}

// Actualizar .htaccess para redirigir a login_reemplazo.php
$htaccess_content = <<<EOT
# Configuración de carga de archivos
php_value upload_max_filesize 2M
php_value post_max_size 8M

# Configuración de codificación
php_value default_charset UTF-8

# Configuración de zona horaria
php_value date.timezone America/Mexico_City

# Redireccionar a login_reemplazo.php si se intenta acceder a login.php o login_simple.php
RewriteEngine On
RewriteRule ^login\.php$ login_reemplazo.php [L,R=301]
RewriteRule ^login_simple\.php$ login_reemplazo.php [L,R=301]
EOT;

if (file_put_contents('.htaccess', $htaccess_content)) {
    echo "<p>✅ Archivo .htaccess actualizado para redirigir a login_reemplazo.php</p>";
} else {
    echo "<p>❌ Error al actualizar el archivo .htaccess</p>";
}

// Actualizar logout.php para que redirija a login_reemplazo.php
$logout_content = <<<EOT
<?php
// Iniciar sesión
session_start();

// Destruir todas las variables de sesión
\$_SESSION = array();

// Si se desea destruir la sesión completamente, borrar también la cookie de sesión
if (ini_get("session.use_cookies")) {
    \$params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        \$params["path"], \$params["domain"],
        \$params["secure"], \$params["httponly"]
    );
}

// Finalmente, destruir la sesión
session_destroy();

// Redirigir al usuario a la página de login
header('Location: login_reemplazo.php');
exit;
?>
EOT;

if (file_put_contents('logout.php', $logout_content)) {
    echo "<p>✅ Archivo logout.php actualizado para redirigir a login_reemplazo.php</p>";
} else {
    echo "<p>❌ Error al actualizar el archivo logout.php</p>";
}

echo "<p><a href='login_reemplazo.php'>Ir a la página de inicio de sesión</a></p>";
?>
