<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RepuMovil - Monitor GPS en Tiempo Real 📍</title>
    <style>
        :root {
            --primary-color: #FF6B35;
            --secondary-color: #FFA500;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --dark-color: #2c3e50;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
            color: white;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .title {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .stats-bar {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .monitor-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .panel {
            background: white;
            color: var(--dark-color);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .panel h3 {
            color: var(--primary-color);
            margin-bottom: 20px;
            font-size: 1.3rem;
        }

        .delivery-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            border-left: 4px solid var(--primary-color);
        }

        .delivery-name {
            font-weight: bold;
            font-size: 1.1rem;
            margin-bottom: 5px;
        }

        .delivery-status {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 10px;
        }

        .location-data {
            background: rgba(255, 107, 53, 0.1);
            padding: 10px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 0.85rem;
        }

        .log-container {
            background: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 10px;
            font-family: monospace;
            font-size: 0.9rem;
            max-height: 400px;
            overflow-y: auto;
        }

        .log-entry {
            margin-bottom: 8px;
            padding: 5px;
            border-radius: 4px;
        }

        .log-success {
            background: rgba(40, 167, 69, 0.2);
        }

        .log-error {
            background: rgba(220, 53, 69, 0.2);
        }

        .log-info {
            background: rgba(23, 162, 184, 0.2);
        }

        .controls {
            margin-bottom: 20px;
            text-align: center;
        }

        .btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            background: #e55a2b;
            transform: translateY(-2px);
        }

        .btn-success {
            background: var(--success-color);
        }

        .btn-success:hover {
            background: #218838;
        }

        .btn-danger {
            background: var(--danger-color);
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-active { background: var(--success-color); }
        .status-inactive { background: var(--danger-color); }
        .status-warning { background: #ffc107; }

        .auto-refresh {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="auto-refresh" id="refresh-indicator">
        🔄 Auto-refresh: ON
    </div>

    <div class="container">
        <div class="header">
            <h1 class="title">📍 Monitor GPS en Tiempo Real</h1>
            <p class="subtitle">Seguimiento en vivo de deliveries de RepuMovil</p>
        </div>

        <!-- Estadísticas en tiempo real -->
        <div class="stats-bar">
            <div class="stat-card">
                <div class="stat-number" id="total-deliveries">0</div>
                <div class="stat-label">Deliveries Activos</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="total-ubicaciones">0</div>
                <div class="stat-label">Ubicaciones Hoy</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="precision-promedio">0m</div>
                <div class="stat-label">Precisión Promedio</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="ultima-actualizacion">--</div>
                <div class="stat-label">Última Actualización</div>
            </div>
        </div>

        <!-- Controles -->
        <div class="controls">
            <button class="btn btn-success" onclick="toggleAutoRefresh()">🔄 Toggle Auto-Refresh</button>
            <button class="btn" onclick="manualRefresh()">🔃 Refresh Manual</button>
            <button class="btn btn-danger" onclick="clearLogs()">🗑️ Limpiar Logs</button>
        </div>

        <!-- Grid principal -->
        <div class="monitor-grid">
            <!-- Panel de deliveries activos -->
            <div class="panel">
                <h3>🚚 Deliveries en Tiempo Real</h3>
                <div id="deliveries-container">
                    <p>Cargando deliveries...</p>
                </div>
            </div>

            <!-- Panel de logs en tiempo real -->
            <div class="panel">
                <h3>📝 Log de Actividad</h3>
                <div id="log-container" class="log-container">
                    <div class="log-entry log-info">📍 Monitor GPS inicializado...</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let autoRefresh = true;
        let refreshInterval = null;
        let lastUpdateTime = null;

        // PASO 2: Inicializar monitor
        document.addEventListener('DOMContentLoaded', function() {
            addLog('🚀 Monitor GPS iniciado', 'info');
            startAutoRefresh();
            loadData();
        });

        // PASO 2: Auto-refresh cada 3 segundos
        function startAutoRefresh() {
            if (refreshInterval) clearInterval(refreshInterval);
            
            refreshInterval = setInterval(() => {
                if (autoRefresh) {
                    loadData();
                }
            }, 3000);
        }

        // PASO 2: Toggle auto-refresh
        function toggleAutoRefresh() {
            autoRefresh = !autoRefresh;
            const indicator = document.getElementById('refresh-indicator');
            indicator.textContent = autoRefresh ? '🔄 Auto-refresh: ON' : '⏸️ Auto-refresh: OFF';
            indicator.style.background = autoRefresh ? 'rgba(40, 167, 69, 0.8)' : 'rgba(220, 53, 69, 0.8)';
            
            addLog(autoRefresh ? '🔄 Auto-refresh activado' : '⏸️ Auto-refresh pausado', 'info');
        }

        // PASO 2: Refresh manual
        function manualRefresh() {
            addLog('🔃 Refresh manual ejecutado', 'info');
            loadData();
        }

        // PASO 2: Cargar datos principales
        async function loadData() {
            try {
                // Cargar deliveries activos
                const response = await fetch('api/tracking.php?action=get_active_deliveries');
                const result = await response.json();
                
                if (result.success) {
                    updateDeliveries(result.data);
                    updateStats(result.data);
                    
                    const now = new Date().toLocaleTimeString();
                    document.getElementById('ultima-actualizacion').textContent = now;
                    
                    if (lastUpdateTime) {
                        const timeDiff = Date.now() - lastUpdateTime;
                        if (timeDiff > 10000) { // Más de 10 segundos
                            addLog(`⚠️ Datos actualizados después de ${Math.round(timeDiff/1000)}s`, 'warning');
                        }
                    }
                    
                    lastUpdateTime = Date.now();
                } else {
                    addLog(`❌ Error cargando datos: ${result.message}`, 'error');
                }
            } catch (error) {
                addLog(`❌ Error de conexión: ${error.message}`, 'error');
            }
        }

        // PASO 2: Actualizar lista de deliveries
        function updateDeliveries(data) {
            const container = document.getElementById('deliveries-container');
            const { activos, inactivos } = data;
            
            container.innerHTML = '';
            
            // Deliveries activos
            if (activos.length > 0) {
                activos.forEach(delivery => {
                    const item = document.createElement('div');
                    item.className = 'delivery-item';
                    
                    const statusColor = delivery.minutos_inactivo <= 5 ? 'status-active' : 
                                       delivery.minutos_inactivo <= 15 ? 'status-warning' : 'status-inactive';
                    
                    item.innerHTML = `
                        <div class="delivery-name">
                            <span class="status-indicator ${statusColor}"></span>
                            ${delivery.nombre} (ID: ${delivery.id})
                        </div>
                        <div class="delivery-status">
                            📞 ${delivery.telefono || 'N/A'} • 
                            ⏰ Actualizado hace ${delivery.minutos_inactivo || 0} min
                        </div>
                        ${delivery.latitude ? `
                            <div class="location-data">
                                📍 Lat: ${parseFloat(delivery.latitude).toFixed(6)}<br>
                                📍 Lng: ${parseFloat(delivery.longitude).toFixed(6)}<br>
                                🎯 Precisión: ±${delivery.accuracy || 'N/A'}m<br>
                                🚗 Velocidad: ${delivery.speed ? (delivery.speed * 3.6).toFixed(1) + ' km/h' : 'N/A'}<br>
                                🧭 Dirección: ${delivery.heading ? delivery.heading.toFixed(0) + '°' : 'N/A'}
                            </div>
                        ` : '<div class="location-data">❌ Sin ubicación GPS</div>'}
                    `;
                    
                    container.appendChild(item);
                });
                
                // Log de actividad
                const nuevasUbicaciones = activos.filter(d => d.latitude && d.minutos_inactivo <= 1);
                if (nuevasUbicaciones.length > 0) {
                    addLog(`📍 ${nuevasUbicaciones.length} ubicaciones actualizadas`, 'success');
                }
            }
            
            // Deliveries inactivos (solo mostrar si hay)
            if (inactivos.length > 0) {
                const inactivosTitle = document.createElement('h4');
                inactivosTitle.textContent = '😴 Deliveries Inactivos';
                inactivosTitle.style.marginTop = '20px';
                inactivosTitle.style.color = '#666';
                container.appendChild(inactivosTitle);
                
                inactivos.slice(0, 3).forEach(delivery => { // Solo mostrar primeros 3
                    const item = document.createElement('div');
                    item.className = 'delivery-item';
                    item.style.opacity = '0.6';
                    item.innerHTML = `
                        <div class="delivery-name">
                            <span class="status-indicator status-inactive"></span>
                            ${delivery.nombre} (ID: ${delivery.id})
                        </div>
                        <div class="delivery-status">
                            📞 ${delivery.telefono || 'N/A'} • 
                            ${delivery.minutos_inactivo ? `⏰ Inactivo ${delivery.minutos_inactivo} min` : '❌ Sin ubicación'}
                        </div>
                    `;
                    container.appendChild(item);
                });
            }
        }

        // PASO 2: Actualizar estadísticas
        function updateStats(data) {
            const { activos, inactivos } = data;
            
            document.getElementById('total-deliveries').textContent = activos.length;
            
            // Calcular ubicaciones del día
            const ubicacionesHoy = activos.reduce((total, delivery) => {
                return total + (delivery.latitude ? 1 : 0);
            }, 0);
            document.getElementById('total-ubicaciones').textContent = ubicacionesHoy;
            
            // Calcular precisión promedio
            const precisiones = activos
                .filter(d => d.accuracy)
                .map(d => parseFloat(d.accuracy));
            
            if (precisiones.length > 0) {
                const precisionPromedio = precisiones.reduce((a, b) => a + b, 0) / precisiones.length;
                document.getElementById('precision-promedio').textContent = `±${precisionPromedio.toFixed(1)}m`;
            } else {
                document.getElementById('precision-promedio').textContent = 'N/A';
            }
        }

        // PASO 2: Agregar entrada al log
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('log-container');
            const timestamp = new Date().toLocaleTimeString();
            
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            
            // Mantener solo últimas 50 entradas
            const entries = logContainer.children;
            if (entries.length > 50) {
                logContainer.removeChild(entries[0]);
            }
        }

        // PASO 2: Limpiar logs
        function clearLogs() {
            const logContainer = document.getElementById('log-container');
            logContainer.innerHTML = '<div class="log-entry log-info">📍 Logs limpiados...</div>';
            addLog('🗑️ Logs limpiados manualmente', 'info');
        }

        // PASO 2: Manejar errores de conexión
        window.addEventListener('online', () => {
            addLog('🌐 Conexión restaurada', 'success');
        });

        window.addEventListener('offline', () => {
            addLog('❌ Conexión perdida', 'error');
        });
    </script>
</body>
</html>
