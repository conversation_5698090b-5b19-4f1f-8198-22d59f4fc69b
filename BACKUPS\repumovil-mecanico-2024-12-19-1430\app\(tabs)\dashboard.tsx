import React from 'react';
import {
  SafeAreaView,
  StatusBar,
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Alert,
  ScrollView,
  Dimensions,
} from 'react-native';

const { width } = Dimensions.get('window');

export default function DashboardScreen() {
  // Funciones del Dashboard
  const buscarTalleres = () => {
    Alert.alert(
      '🔍 Buscar Talleres Cercanos',
      'Esta función abrirá un mapa con talleres cercanos a tu ubicación, mostrando:\n• Distancia\n• Calificaciones\n• Especialidades\n• Precios estimados'
    );
  };

  const verSolicitudes = () => {
    Alert.alert(
      '📱 Mis Solicitudes de Servicio',
      'Aquí podrás ver:\n• Solicitudes pendientes\n• Estado de reparaciones\n• Historial de servicios\n• Comunicación con talleres'
    );
  };

  const verFavoritos = () => {
    Alert.alert(
      '⭐ Talleres Favoritos',
      'Tus talleres de confianza:\n• Acceso rápido\n• Contacto directo\n• Historial de servicios\n• Promociones especiales'
    );
  };

  const contactarMecanicos = () => {
    Alert.alert(
      '📞 Contactar Mecánicos',
      'Conecta con mecánicos independientes:\n• Servicios a domicilio\n• Especialidades específicas\n• Presupuestos inmediatos\n• Chat en tiempo real'
    );
  };

  const verHistorial = () => {
    Alert.alert(
      '📊 Historial de Servicios',
      'Tu historial completo:\n• Servicios realizados\n• Gastos por período\n• Mantenimientos programados\n• Reportes detallados'
    );
  };

  const comprarRepuestos = () => {
    Alert.alert(
      '🔧 Compra tu Repuesto',
      '¡La tienda de repuestos más completa!\n• Repuestos originales\n• Alternativas de calidad\n• Entrega a domicilio\n• Garantía incluida\n\n¡NEEÑO, aquí encontrás todo!'
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#FF6B35" />
      
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.logoContainer}>
          <Text style={styles.logoIcon}>🔧</Text>
          <Text style={styles.logoText}>
            <Text style={styles.logoRepu}>Repu</Text>
            <Text style={styles.logoMovil}>Movil</Text>
          </Text>
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Mensaje de Bienvenida */}
        <View style={styles.welcomeSection}>
          <Text style={styles.welcomeTitle}>¡Bienvenido a RepuMovil!</Text>
          <Text style={styles.welcomeSubtitle}>
            DONDE ENCONTRÁS TODOS LO QUE NECESITÁS PARA TU AUTO O MOTO{' '}
            <Text style={styles.neeñoText}>NEEÑO</Text>
          </Text>
        </View>

        {/* Estadísticas Rápidas */}
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>0</Text>
            <Text style={styles.statLabel}>Servicios Solicitados</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>0</Text>
            <Text style={styles.statLabel}>Talleres Favoritos</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>0</Text>
            <Text style={styles.statLabel}>Compras Realizadas</Text>
          </View>
        </View>

        {/* Dashboard Cards */}
        <View style={styles.cardsContainer}>
          {/* Buscar Talleres */}
          <TouchableOpacity style={styles.card} onPress={buscarTalleres}>
            <Text style={styles.cardIcon}>🔍</Text>
            <Text style={styles.cardTitle}>Buscar Talleres Cercanos</Text>
            <Text style={styles.cardDescription}>
              Encuentra talleres mecánicos cerca de tu ubicación con las mejores calificaciones.
            </Text>
          </TouchableOpacity>

          {/* Mis Solicitudes */}
          <TouchableOpacity style={styles.card} onPress={verSolicitudes}>
            <Text style={styles.cardIcon}>📱</Text>
            <Text style={styles.cardTitle}>Mis Solicitudes de Servicio</Text>
            <Text style={styles.cardDescription}>
              Revisa el estado de tus solicitudes de reparación y mantenimiento.
            </Text>
          </TouchableOpacity>

          {/* Talleres Favoritos */}
          <TouchableOpacity style={styles.card} onPress={verFavoritos}>
            <Text style={styles.cardIcon}>⭐</Text>
            <Text style={styles.cardTitle}>Talleres Favoritos</Text>
            <Text style={styles.cardDescription}>
              Accede rápidamente a tus talleres de confianza guardados.
            </Text>
          </TouchableOpacity>

          {/* Contactar Mecánicos */}
          <TouchableOpacity style={styles.card} onPress={contactarMecanicos}>
            <Text style={styles.cardIcon}>📞</Text>
            <Text style={styles.cardTitle}>Contactar Mecánicos</Text>
            <Text style={styles.cardDescription}>
              Conecta directamente con mecánicos independientes disponibles.
            </Text>
          </TouchableOpacity>

          {/* Historial */}
          <TouchableOpacity style={styles.card} onPress={verHistorial}>
            <Text style={styles.cardIcon}>📊</Text>
            <Text style={styles.cardTitle}>Historial de Servicios</Text>
            <Text style={styles.cardDescription}>
              Consulta el historial completo de todos tus servicios realizados.
            </Text>
          </TouchableOpacity>

          {/* Comprar Repuestos - Card Especial */}
          <TouchableOpacity style={[styles.card, styles.specialCard]} onPress={comprarRepuestos}>
            <Text style={styles.cardIcon}>🔧</Text>
            <Text style={[styles.cardTitle, styles.specialCardText]}>Compra tu Repuesto</Text>
            <Text style={[styles.cardDescription, styles.specialCardText]}>
              Encuentra y compra repuestos originales y alternativos para tu vehículo.
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f7fa',
  },
  header: {
    backgroundColor: '#FF6B35',
    paddingVertical: 20,
    paddingHorizontal: 20,
    borderBottomLeftRadius: 25,
    borderBottomRightRadius: 25,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoIcon: {
    fontSize: 28,
    marginRight: 10,
  },
  logoText: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  logoRepu: {
    color: 'white',
  },
  logoMovil: {
    color: '#FFE4B5',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  welcomeSection: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 25,
    marginVertical: 20,
    alignItems: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
  },
  welcomeTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
    textAlign: 'center',
  },
  welcomeSubtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
  },
  neeñoText: {
    color: '#FF6B35',
    fontWeight: '900',
    fontSize: 18,
    textTransform: 'uppercase',
    letterSpacing: 1,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 20,
  },
  statItem: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 15,
    alignItems: 'center',
    flex: 1,
    marginHorizontal: 5,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FF6B35',
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
    marginTop: 5,
  },
  cardsContainer: {
    paddingBottom: 30,
  },
  card: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    marginBottom: 15,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    borderLeftWidth: 4,
    borderLeftColor: '#FF6B35',
  },
  specialCard: {
    backgroundColor: '#FF6B35',
    borderLeftColor: 'white',
  },
  cardIcon: {
    fontSize: 40,
    marginBottom: 10,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  cardDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  specialCardText: {
    color: 'white',
  },
});
