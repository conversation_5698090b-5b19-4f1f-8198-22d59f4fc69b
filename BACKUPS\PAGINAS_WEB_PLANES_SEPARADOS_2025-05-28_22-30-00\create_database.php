<?php
// Configuración de la conexión
$host = 'localhost';
$username = 'root';
$password = '';
$dbname = 'autoconnect_db';

try {
    // Conectar a MySQL sin seleccionar una base de datos
    $conn = new PDO("mysql:host=$host", $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h1>Creación de la base de datos</h1>";
    
    // Crear la base de datos si no existe
    $sql = "CREATE DATABASE IF NOT EXISTS $dbname";
    $conn->exec($sql);
    echo "<p>✅ Base de datos '$dbname' creada o ya existente.</p>";
    
    // Seleccionar la base de datos
    $conn = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    
    // Crear tabla de roles
    $sql = "CREATE TABLE IF NOT EXISTS roles (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(50) NOT NULL,
        description VARCHAR(255)
    )";
    $conn->exec($sql);
    echo "<p>✅ Tabla 'roles' creada correctamente.</p>";
    
    // Crear tabla de usuarios
    $sql = "CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) NOT NULL UNIQUE,
        email VARCHAR(100) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        role_id INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        last_login TIMESTAMP NULL,
        status ENUM('active', 'root', 'suspended') DEFAULT 'active',
        FOREIGN KEY (role_id) REFERENCES roles(id)
    )";
    $conn->exec($sql);
    echo "<p>✅ Tabla 'users' creada correctamente.</p>";
    
    // Crear tabla de talleres
    $sql = "CREATE TABLE IF NOT EXISTS workshops (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        name VARCHAR(100) NOT NULL,
        address VARCHAR(255) NOT NULL,
        phone VARCHAR(20) NOT NULL,
        email VARCHAR(100) NOT NULL,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id)
    )";
    $conn->exec($sql);
    echo "<p>✅ Tabla 'workshops' creada correctamente.</p>";
    
    // Crear tabla de proveedores
    $sql = "CREATE TABLE IF NOT EXISTS suppliers (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        company_name VARCHAR(100) NOT NULL,
        contact_name VARCHAR(100) NOT NULL,
        address VARCHAR(255) NOT NULL,
        phone VARCHAR(20) NOT NULL,
        email VARCHAR(100) NOT NULL,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id)
    )";
    $conn->exec($sql);
    echo "<p>✅ Tabla 'suppliers' creada correctamente.</p>";
    
    echo "<p>Base de datos inicializada correctamente.</p>";
    echo "<p><a href='index.php'>Volver al inicio</a></p>";
    
} catch(PDOException $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}
?>