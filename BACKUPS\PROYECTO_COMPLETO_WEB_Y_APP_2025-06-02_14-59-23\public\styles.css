/* Estilos para AutoConnect - Plataforma de conexión entre talleres y proveedores */

:root {
    --primary-color: #0056b3;
    --secondary-color: #ff6b00;
    --dark-color: #333;
    --light-color: #f4f4f4;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
    background-color: var(--light-color);
}

a {
    text-decoration: none;
    color: var(--primary-color);
    transition: var(--transition);
}

a:hover {
    color: var(--secondary-color);
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Botones */
.btn {
    display: inline-block;
    padding: 12px 25px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
    transition: var(--transition);
    text-align: center;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: #003d7a;
    color: white;
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: white;
}

.btn-secondary:hover {
    background-color: #e05e00;
    color: white;
}

/* Header */
header {
    background-color: white;
    box-shadow: var(--box-shadow);
    padding: 15px 30px;
    position: sticky;
    top: 0;
    z-index: 100;
}

.logo-container {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.logo-icon {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-right: 15px;
}

header h1 {
    color: var(--primary-color);
    font-size: 2.2rem;
}

header p {
    color: var(--dark-color);
    font-size: 1.1rem;
    margin-bottom: 15px;
}

nav ul {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
}

nav ul li {
    margin: 0 15px;
}

nav ul li a {
    color: var(--dark-color);
    font-weight: 500;
    padding: 5px 0;
    position: relative;
}

nav ul li a:hover {
    color: var(--primary-color);
}

nav ul li a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    bottom: 0;
    left: 0;
    transition: var(--transition);
}

nav ul li a:hover::after {
    width: 100%;
}

.login-btn a, .register-btn a {
    padding: 8px 15px;
    border-radius: 5px;
}

.login-btn a {
    border: 1px solid var(--primary-color);
}

.register-btn a {
    background-color: var(--primary-color);
    color: white;
}

.register-btn a:hover {
    background-color: #003d7a;
    color: white;
}

/* Hero Section */
.hero {
    background: linear-gradient(rgba(0, 86, 179, 0.8), rgba(0, 86, 179, 0.9)), url('https://source.unsplash.com/random/1600x900/?auto-parts') no-repeat center center/cover;
    color: white;
    padding: 80px 20px;
    text-align: center;
}

.hero-content {
    max-width: 800px;
    margin: 0 auto;
}

.hero h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
}

.hero p {
    font-size: 1.2rem;
    margin-bottom: 30px;
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
}

/* Secciones principales */
main {
    padding: 40px 20px;
}

section {
    margin-bottom: 60px;
}

section h2 {
    text-align: center;
    font-size: 2rem;
    margin-bottom: 40px;
    color: var(--primary-color);
    position: relative;
    padding-bottom: 10px;
}

section h2::after {
    content: '';
    position: absolute;
    width: 80px;
    height: 3px;
    background-color: var(--secondary-color);
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
}

/* Cómo funciona */
.steps {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 30px;
}

.step {
    flex: 1;
    min-width: 200px;
    max-width: 250px;
    text-align: center;
    padding: 20px;
    background-color: white;
    border-radius: 10px;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.step:hover {
    transform: translateY(-10px);
}

.step-icon {
    width: 80px;
    height: 80px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto 20px;
}

.step-icon i {
    font-size: 2rem;
}

.step h3 {
    margin-bottom: 15px;
    color: var(--primary-color);
}

/* Beneficios */
.benefits {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 30px;
}

.benefit {
    flex: 1;
    min-width: 250px;
    max-width: 350px;
    text-align: center;
    padding: 30px;
    background-color: white;
    border-radius: 10px;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.benefit:hover {
    transform: translateY(-5px);
}

.benefit i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.benefit h3 {
    margin-bottom: 15px;
    color: var(--primary-color);
}

/* Sección de contacto */
.contact {
    text-align: center;
}

.contact p {
    max-width: 600px;
    margin: 0 auto 30px;
}

.contact-info {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 30px;
    margin-bottom: 40px;
}

.contact-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.contact-item i {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 10px;
}

.contact-form {
    max-width: 600px;
    margin: 0 auto;
    background-color: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: var(--box-shadow);
}

.form-group {
    margin-bottom: 20px;
    text-align: left;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-family: inherit;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

/* Footer */
footer {
    background-color: var(--dark-color);
    color: white;
    padding: 40px 20px 20px;
}

.footer-content {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: 30px;
    margin-bottom: 30px;
}

.footer-logo {
    display: flex;
    align-items: center;
}

.footer-logo .logo-icon {
    color: white;
    margin-right: 10px;
}

.footer-logo h3 {
    font-size: 1.5rem;
}

.footer-links h4,
.footer-social h4 {
    margin-bottom: 15px;
    color: var(--secondary-color);
}

.footer-links ul li {
    margin-bottom: 10px;
}

.footer-links ul li a {
    color: #ddd;
}

.footer-links ul li a:hover {
    color: white;
}

.social-icons {
    display: flex;
    gap: 15px;
}

.social-icons a {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    color: white;
    transition: var(--transition);
}

.social-icons a:hover {
    background-color: var(--primary-color);
    transform: translateY(-5px);
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Responsive */
@media (max-width: 768px) {
    header {
        padding: 15px;
    }

    nav ul {
        flex-direction: column;
        align-items: center;
    }

    nav ul li {
        margin: 5px 0;
    }

    .hero {
        padding: 60px 15px;
    }

    .hero h2 {
        font-size: 2rem;
    }

    .cta-buttons {
        flex-direction: column;
        gap: 15px;
    }

    .step, .benefit {
        min-width: 100%;
    }

    .footer-content {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }
}
