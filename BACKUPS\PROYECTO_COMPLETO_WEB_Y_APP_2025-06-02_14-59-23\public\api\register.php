<?php
// Configuración de CORS para permitir solicitudes desde la aplicación móvil
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header("Content-Type: application/json; charset=UTF-8");

// Responder a las solicitudes OPTIONS (preflight)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Verificar que sea una solicitud POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Método no permitido']);
    exit;
}

// Incluir archivo de configuración de la base de datos
require_once '../db_config.php';

// Obtener datos del cuerpo de la solicitud
$data = json_decode(file_get_contents("php://input"), true);

// Verificar que se recibieron los datos necesarios
if (!isset($data['username']) || !isset($data['email']) || !isset($data['password']) || !isset($data['confirm_password']) || !isset($data['role_id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Faltan datos requeridos']);
    exit;
}

// Extraer y sanitizar datos
$username = filter_var($data['username'], FILTER_SANITIZE_STRING);
$email = filter_var($data['email'], FILTER_SANITIZE_EMAIL);
$password = $data['password'];
$confirm_password = $data['confirm_password'];
$role_id = (int)$data['role_id'];

// Campos para talleres y proveedores
$business_name = filter_var($data['business_name'] ?? '', FILTER_SANITIZE_STRING);
$address = filter_var($data['address'] ?? '', FILTER_SANITIZE_STRING);
$phone = filter_var($data['phone'] ?? '', FILTER_SANITIZE_STRING);
$description = filter_var($data['description'] ?? '', FILTER_SANITIZE_STRING);
$maps_url = filter_var($data['maps_url'] ?? '', FILTER_SANITIZE_URL);

// Campos para mecánicos
$mechanic_name = filter_var($data['mechanic_name'] ?? '', FILTER_SANITIZE_STRING);
$specialties = filter_var($data['specialties'] ?? '', FILTER_SANITIZE_STRING);
$experience = filter_var($data['experience'] ?? '', FILTER_SANITIZE_STRING);

// Campos para personas
$first_name = filter_var($data['first_name'] ?? '',
