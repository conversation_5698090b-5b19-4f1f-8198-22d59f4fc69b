<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RepuMovil - Tracking en Tiempo Real 🗺️</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
        :root {
            --primary-color: #FF6B35;
            --secondary-color: #FFA500;
            --dark-color: #2c3e50;
            --light-color: #f8f9fa;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
            color: white;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .title {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .pedido-info {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            color: var(--dark-color);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .pedido-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .pedido-numero {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        .estado-badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 0.9rem;
            text-transform: uppercase;
        }

        .estado-en-camino {
            background: var(--info-color);
            color: white;
        }

        .estado-entregado {
            background: var(--success-color);
            color: white;
        }

        .tracking-grid {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 20px;
            margin-bottom: 20px;
        }

        @media (max-width: 768px) {
            .tracking-grid {
                grid-template-columns: 1fr;
            }
        }

        .map-container {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            position: relative;
        }

        #map {
            height: 500px;
            width: 100%;
        }

        .map-controls {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .map-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 10px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }

        .map-btn:hover {
            background: #e55a2b;
            transform: translateY(-2px);
        }

        .delivery-info {
            background: white;
            border-radius: 15px;
            padding: 20px;
            color: var(--dark-color);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .delivery-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            margin: 0 auto 15px;
        }

        .delivery-name {
            text-align: center;
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 10px;
            color: var(--primary-color);
        }

        .delivery-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 10px;
            background: var(--light-color);
            border-radius: 8px;
        }

        .stat-value {
            font-size: 1.2rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        .stat-label {
            font-size: 0.8rem;
            color: #666;
        }

        .eta-container {
            background: linear-gradient(135deg, var(--success-color), #20c997);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            margin-bottom: 20px;
        }

        .eta-time {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .eta-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .progress-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            color: var(--dark-color);
        }

        .progress-title {
            font-size: 1.2rem;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 15px;
            text-align: center;
        }

        .progress-steps {
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            margin-bottom: 20px;
        }

        .progress-line {
            position: absolute;
            top: 20px;
            left: 0;
            right: 0;
            height: 4px;
            background: #e9ecef;
            z-index: 1;
        }

        .progress-line-active {
            position: absolute;
            top: 20px;
            left: 0;
            height: 4px;
            background: var(--success-color);
            z-index: 2;
            transition: width 0.5s ease;
        }

        .progress-step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            font-weight: bold;
            position: relative;
            z-index: 3;
            transition: all 0.3s ease;
        }

        .progress-step.completed {
            background: var(--success-color);
            color: white;
        }

        .progress-step.active {
            background: var(--primary-color);
            color: white;
            animation: pulse 2s infinite;
        }

        .progress-step.pending {
            background: #e9ecef;
            color: #6c757d;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .step-labels {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
        }

        .step-label {
            font-size: 0.8rem;
            text-align: center;
            color: #666;
            flex: 1;
        }

        .step-label.active {
            color: var(--primary-color);
            font-weight: bold;
        }

        .contact-delivery {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .contact-btn {
            padding: 12px;
            border: none;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .call-btn {
            background: var(--success-color);
            color: white;
        }

        .call-btn:hover {
            background: #218838;
        }

        .message-btn {
            background: var(--info-color);
            color: white;
        }

        .message-btn:hover {
            background: #138496;
        }

        .live-updates {
            background: white;
            border-radius: 15px;
            padding: 20px;
            color: var(--dark-color);
        }

        .updates-title {
            font-size: 1.2rem;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .update-item {
            display: flex;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }

        .update-item:last-child {
            border-bottom: none;
        }

        .update-time {
            font-size: 0.8rem;
            color: #666;
            min-width: 60px;
        }

        .update-icon {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 15px;
            font-size: 0.9rem;
            color: white;
        }

        .update-icon.pickup {
            background: var(--warning-color);
        }

        .update-icon.route {
            background: var(--info-color);
        }

        .update-icon.delivery {
            background: var(--success-color);
        }

        .update-text {
            flex: 1;
            font-size: 0.9rem;
        }

        .refresh-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            margin: 20px auto;
            display: block;
            transition: all 0.3s ease;
        }

        .refresh-btn:hover {
            background: #e55a2b;
            transform: translateY(-2px);
        }

        /* PASO 8: Estilos para estado actual */
        .estado-actual {
            background: var(--light-color);
            border-radius: 12px;
            padding: 20px;
            margin-top: 20px;
            display: flex;
            align-items: center;
            border-left: 4px solid var(--primary-color);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .estado-icon {
            font-size: 2.5rem;
            margin-right: 20px;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        .estado-info {
            flex: 1;
        }

        .estado-titulo {
            font-size: 1.2rem;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 8px;
        }

        .estado-descripcion {
            color: #666;
            margin-bottom: 8px;
            line-height: 1.4;
        }

        .estado-timestamp {
            font-size: 0.8rem;
            color: #999;
            font-style: italic;
        }

        /* Animaciones para cambios de estado */
        .progress-step.updating {
            animation: pulse-update 1s ease-in-out;
        }

        @keyframes pulse-update {
            0% { transform: scale(1); background-color: var(--warning-color); }
            50% { transform: scale(1.2); background-color: var(--primary-color); }
            100% { transform: scale(1); }
        }

        .estado-actual.updating {
            animation: slide-update 0.5s ease-in-out;
        }

        @keyframes slide-update {
            0% { transform: translateX(-20px); opacity: 0.7; }
            100% { transform: translateX(0); opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">🗺️📦</div>
            <h1 class="title">Tracking en Tiempo Real</h1>
            <p>Seguí tu pedido RepuMovil paso a paso</p>
        </div>

        <!-- Info del Pedido -->
        <div class="pedido-info">
            <div class="pedido-header">
                <div class="pedido-numero">Pedido #1024</div>
                <div class="estado-badge estado-en-camino">En Camino</div>
            </div>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                <div>
                    <strong>Cliente:</strong> Taller Mecánico Central<br>
                    <strong>Dirección:</strong> Av. Libertador 1234, Centro
                </div>
                <div>
                    <strong>Proveedor:</strong> Repuestos San Juan<br>
                    <strong>Total:</strong> $2,450
                </div>
            </div>
        </div>

        <!-- Grid Principal -->
        <div class="tracking-grid">
            <!-- Mapa -->
            <div class="map-container">
                <div class="map-controls">
                    <button class="map-btn" onclick="centerOnDelivery()" title="Centrar en Delivery">
                        <i class="fas fa-motorcycle"></i>
                    </button>
                    <button class="map-btn" onclick="showRoute()" title="Mostrar Ruta">
                        <i class="fas fa-route"></i>
                    </button>
                    <button class="map-btn" onclick="toggleTraffic()" title="Ver Tráfico">
                        <i class="fas fa-traffic-light"></i>
                    </button>
                </div>
                <div id="map"></div>
            </div>

            <!-- Info del Delivery -->
            <div class="delivery-info">
                <div class="delivery-avatar">🏍️</div>
                <div class="delivery-name">Juan Pérez</div>
                
                <div class="delivery-stats">
                    <div class="stat-item">
                        <div class="stat-value">4.8</div>
                        <div class="stat-label">Calificación</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">156</div>
                        <div class="stat-label">Entregas</div>
                    </div>
                </div>

                <div class="eta-container">
                    <div class="eta-time" id="eta-time">12 min</div>
                    <div class="eta-label">Tiempo estimado de llegada</div>
                </div>

                <div class="contact-delivery">
                    <button class="contact-btn call-btn" onclick="callDelivery()">
                        <i class="fas fa-phone"></i> Llamar
                    </button>
                    <button class="contact-btn message-btn" onclick="messageDelivery()">
                        <i class="fas fa-comment"></i> Mensaje
                    </button>
                </div>
            </div>
        </div>

        <!-- Progreso del Pedido -->
        <div class="progress-container">
            <div class="progress-title">
                <i class="fas fa-clipboard-check"></i>
                Estado del Pedido
            </div>

            <div class="progress-steps">
                <div class="progress-line"></div>
                <div class="progress-line-active" id="progress-line" style="width: 75%"></div>

                <div class="progress-step completed" id="step-asignado">✓</div>
                <div class="progress-step completed" id="step-recogido">📦</div>
                <div class="progress-step active" id="step-en-camino">🏍️</div>
                <div class="progress-step pending" id="step-entregado">🏠</div>
            </div>

            <div class="step-labels">
                <div class="step-label" id="label-asignado">Asignado</div>
                <div class="step-label" id="label-recogido">Recogido</div>
                <div class="step-label active" id="label-en-camino">En Camino</div>
                <div class="step-label" id="label-entregado">Entregado</div>
            </div>

            <!-- PASO 8: Información detallada del estado actual -->
            <div class="estado-actual" id="estado-actual">
                <div class="estado-icon">🚚</div>
                <div class="estado-info">
                    <div class="estado-titulo">En Camino hacia tu dirección</div>
                    <div class="estado-descripcion">Juan está conduciendo hacia tu ubicación. Puedes seguir su progreso en el mapa.</div>
                    <div class="estado-timestamp">Actualizado: <span id="estado-timestamp">hace 2 minutos</span></div>
                </div>
            </div>
        </div>

        <!-- Actualizaciones en Vivo -->
        <div class="live-updates">
            <div class="updates-title">
                <i class="fas fa-broadcast-tower"></i>
                Actualizaciones en Tiempo Real
                <div style="margin-left: auto; font-size: 0.8rem; color: #666;">
                    Última actualización: <span id="last-update">hace 30s</span>
                </div>
            </div>
            
            <div class="update-item">
                <div class="update-time">14:32</div>
                <div class="update-icon route">🏍️</div>
                <div class="update-text">
                    <strong>En camino hacia tu dirección</strong><br>
                    Juan está a 2.3 km de distancia, llegará en 12 minutos
                </div>
            </div>

            <div class="update-item">
                <div class="update-time">14:18</div>
                <div class="update-icon pickup">📦</div>
                <div class="update-text">
                    <strong>Pedido recogido del proveedor</strong><br>
                    Juan retiró tu pedido de Repuestos San Juan
                </div>
            </div>

            <div class="update-item">
                <div class="update-time">14:15</div>
                <div class="update-icon pickup">🚀</div>
                <div class="update-text">
                    <strong>Delivery asignado</strong><br>
                    Juan Pérez aceptó tu pedido y se dirige al proveedor
                </div>
            </div>
        </div>

        <button class="refresh-btn" onclick="refreshTracking()">
            <i class="fas fa-sync-alt"></i>
            Actualizar Tracking
        </button>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // PASO 7: Variables globales para tracking
        let map;
        let deliveryMarker;
        let customerMarker;
        let routePolyline;
        let currentPosition = { lat: -31.5375, lng: -68.5364 }; // San Juan Centro
        let customerPosition = { lat: -31.5420, lng: -68.5290 }; // Destino
        let eta = 12; // minutos

        // PASO 7: Inicializar mapa
        function initMap() {
            // Crear mapa centrado en San Juan
            map = L.map('map').setView([currentPosition.lat, currentPosition.lng], 14);

            // Agregar tiles de OpenStreetMap
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors'
            }).addTo(map);

            // Marcador del delivery (moto)
            deliveryMarker = L.marker([currentPosition.lat, currentPosition.lng], {
                icon: L.divIcon({
                    html: '<div style="background: #FF6B35; color: white; width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 20px; border: 3px solid white; box-shadow: 0 2px 10px rgba(0,0,0,0.3);">🏍️</div>',
                    className: 'delivery-marker',
                    iconSize: [40, 40],
                    iconAnchor: [20, 20]
                })
            }).addTo(map);

            // Marcador del cliente (casa)
            customerMarker = L.marker([customerPosition.lat, customerPosition.lng], {
                icon: L.divIcon({
                    html: '<div style="background: #28a745; color: white; width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 20px; border: 3px solid white; box-shadow: 0 2px 10px rgba(0,0,0,0.3);">🏠</div>',
                    className: 'customer-marker',
                    iconSize: [40, 40],
                    iconAnchor: [20, 20]
                })
            }).addTo(map);

            // Popup para delivery
            deliveryMarker.bindPopup(`
                <div style="text-align: center;">
                    <strong>🏍️ Juan Pérez</strong><br>
                    Delivery en camino<br>
                    <small>ETA: ${eta} minutos</small>
                </div>
            `);

            // Popup para cliente
            customerMarker.bindPopup(`
                <div style="text-align: center;">
                    <strong>🏠 Tu Dirección</strong><br>
                    Av. Libertador 1234<br>
                    <small>Destino final</small>
                </div>
            `);

            // Mostrar ruta inicial
            showRoute();
        }

        // PASO 7: Mostrar ruta entre delivery y cliente
        function showRoute() {
            if (routePolyline) {
                map.removeLayer(routePolyline);
            }

            // Crear ruta simulada (en producción usar Google Directions API)
            const routePoints = [
                [currentPosition.lat, currentPosition.lng],
                [currentPosition.lat + 0.002, currentPosition.lng + 0.001],
                [currentPosition.lat + 0.004, currentPosition.lng + 0.003],
                [customerPosition.lat, customerPosition.lng]
            ];

            routePolyline = L.polyline(routePoints, {
                color: '#FF6B35',
                weight: 5,
                opacity: 0.8,
                dashArray: '10, 10'
            }).addTo(map);

            // Ajustar vista para mostrar toda la ruta
            const group = new L.featureGroup([deliveryMarker, customerMarker, routePolyline]);
            map.fitBounds(group.getBounds().pad(0.1));
        }

        // PASO 7: Centrar mapa en delivery
        function centerOnDelivery() {
            map.setView([currentPosition.lat, currentPosition.lng], 16);
            deliveryMarker.openPopup();
        }

        // PASO 7: Toggle vista de tráfico (simulado)
        let trafficVisible = false;
        function toggleTraffic() {
            trafficVisible = !trafficVisible;
            const btn = event.target.closest('.map-btn');
            
            if (trafficVisible) {
                btn.style.background = '#dc3545';
                btn.innerHTML = '<i class="fas fa-traffic-light"></i>';
                // En producción aquí se agregaría capa de tráfico real
                console.log('🚦 Vista de tráfico activada');
            } else {
                btn.style.background = 'var(--primary-color)';
                btn.innerHTML = '<i class="fas fa-traffic-light"></i>';
                console.log('🚦 Vista de tráfico desactivada');
            }
        }

        // PASO 7: Simular movimiento del delivery
        function simulateDeliveryMovement() {
            // Mover delivery hacia el cliente gradualmente
            const deltaLat = (customerPosition.lat - currentPosition.lat) * 0.02;
            const deltaLng = (customerPosition.lng - currentPosition.lng) * 0.02;

            currentPosition.lat += deltaLat;
            currentPosition.lng += deltaLng;

            // Actualizar marcador
            deliveryMarker.setLatLng([currentPosition.lat, currentPosition.lng]);

            // Actualizar ETA
            const distance = calculateDistance(currentPosition, customerPosition);
            eta = Math.max(1, Math.round(distance * 20)); // Aproximación
            document.getElementById('eta-time').textContent = eta + ' min';

            // Actualizar popup
            deliveryMarker.setPopupContent(`
                <div style="text-align: center;">
                    <strong>🏍️ Juan Pérez</strong><br>
                    Delivery en camino<br>
                    <small>ETA: ${eta} minutos</small>
                </div>
            `);

            // Actualizar ruta
            showRoute();

            // PASO 8: Simular cambios de estado ocasionalmente
            if (Math.random() > 0.85) { // 15% de probabilidad
                simulateStatusChange();
            }

            // Agregar actualización al timeline
            addLiveUpdate();
        }

        // PASO 8: Simular cambios de estado del pedido
        function simulateStatusChange() {
            const estados = ['recogido', 'en_camino', 'entregado'];
            const estadoActual = getCurrentStatus();
            const siguienteEstado = getNextStatus(estadoActual);

            if (siguienteEstado) {
                updatePedidoStatus(siguienteEstado);
            }
        }

        // PASO 8: Obtener estado actual
        function getCurrentStatus() {
            const activeStep = document.querySelector('.progress-step.active');
            if (activeStep) {
                const stepId = activeStep.id;
                return stepId.replace('step-', '');
            }
            return 'asignado';
        }

        // PASO 8: Obtener siguiente estado
        function getNextStatus(currentStatus) {
            const sequence = ['asignado', 'recogido', 'en_camino', 'entregado'];
            const currentIndex = sequence.indexOf(currentStatus);
            return currentIndex < sequence.length - 1 ? sequence[currentIndex + 1] : null;
        }

        // PASO 8: Actualizar estado del pedido
        function updatePedidoStatus(nuevoEstado) {
            const estadosInfo = {
                asignado: {
                    icon: '📋',
                    titulo: 'Pedido Asignado',
                    descripcion: 'Juan ha aceptado tu pedido y se dirige al proveedor.',
                    progress: 25
                },
                recogido: {
                    icon: '📦',
                    titulo: 'Pedido Recogido',
                    descripcion: 'Juan ha recogido tu pedido del proveedor y está preparándose para salir.',
                    progress: 50
                },
                en_camino: {
                    icon: '🚚',
                    titulo: 'En Camino hacia tu dirección',
                    descripcion: 'Juan está conduciendo hacia tu ubicación. Puedes seguir su progreso en el mapa.',
                    progress: 75
                },
                entregado: {
                    icon: '✅',
                    titulo: '¡Pedido Entregado!',
                    descripcion: '¡Tu pedido ha sido entregado exitosamente! Esperamos que disfrutes tus repuestos.',
                    progress: 100
                }
            };

            const info = estadosInfo[nuevoEstado];
            if (!info) return;

            // Actualizar barra de progreso con animación
            const progressLine = document.getElementById('progress-line');
            progressLine.style.width = info.progress + '%';

            // Actualizar pasos
            updateProgressSteps(nuevoEstado);

            // Actualizar información del estado actual
            updateEstadoActual(info);

            // PASO 9: Si está entregado, mostrar botón de calificación
            if (nuevoEstado === 'entregado') {
                mostrarBotonCalificacion();
            }

            // Agregar al timeline
            addStatusUpdateToTimeline(nuevoEstado, info);

            console.log(`📦 Estado actualizado: ${nuevoEstado}`);
        }

        // PASO 9: Mostrar botón de calificación
        function mostrarBotonCalificacion() {
            setTimeout(() => {
                const estadoActual = document.getElementById('estado-actual');
                const botonCalificar = document.createElement('div');
                botonCalificar.innerHTML = `
                    <button onclick="irACalificar()" style="
                        background: linear-gradient(135deg, #FF6B35, #FFA500);
                        color: white;
                        border: none;
                        padding: 15px 30px;
                        border-radius: 10px;
                        font-size: 1rem;
                        font-weight: bold;
                        cursor: pointer;
                        margin-top: 15px;
                        width: 100%;
                        transition: all 0.3s ease;
                        box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
                    " onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
                        ⭐ Calificar Delivery
                    </button>
                `;
                estadoActual.appendChild(botonCalificar);
            }, 2000);
        }

        // PASO 9: Ir a página de calificación
        function irACalificar() {
            // En producción, redirigir a la página de calificación
            window.open('calificar-delivery.php?pedido=1024', '_blank');
        }

        // PASO 8: Actualizar pasos del progreso
        function updateProgressSteps(nuevoEstado) {
            const steps = ['asignado', 'recogido', 'en_camino', 'entregado'];
            const currentIndex = steps.indexOf(nuevoEstado);

            steps.forEach((step, index) => {
                const stepElement = document.getElementById(`step-${step}`);
                const labelElement = document.getElementById(`label-${step}`);

                if (index < currentIndex) {
                    // Pasos completados
                    stepElement.className = 'progress-step completed';
                    labelElement.className = 'step-label';
                } else if (index === currentIndex) {
                    // Paso actual
                    stepElement.className = 'progress-step active updating';
                    labelElement.className = 'step-label active';

                    // Remover animación después de 1 segundo
                    setTimeout(() => {
                        stepElement.classList.remove('updating');
                    }, 1000);
                } else {
                    // Pasos pendientes
                    stepElement.className = 'progress-step pending';
                    labelElement.className = 'step-label';
                }
            });
        }

        // PASO 8: Actualizar información del estado actual
        function updateEstadoActual(info) {
            const estadoActual = document.getElementById('estado-actual');
            estadoActual.classList.add('updating');

            setTimeout(() => {
                estadoActual.innerHTML = `
                    <div class="estado-icon">${info.icon}</div>
                    <div class="estado-info">
                        <div class="estado-titulo">${info.titulo}</div>
                        <div class="estado-descripcion">${info.descripcion}</div>
                        <div class="estado-timestamp">Actualizado: <span id="estado-timestamp">ahora</span></div>
                    </div>
                `;

                estadoActual.classList.remove('updating');
            }, 250);
        }

        // PASO 8: Agregar actualización de estado al timeline
        function addStatusUpdateToTimeline(estado, info) {
            const updates = document.querySelector('.live-updates');
            const now = new Date();
            const timeStr = now.getHours().toString().padStart(2, '0') + ':' +
                           now.getMinutes().toString().padStart(2, '0');

            const newUpdate = document.createElement('div');
            newUpdate.className = 'update-item';
            newUpdate.innerHTML = `
                <div class="update-time">${timeStr}</div>
                <div class="update-icon delivery">${info.icon}</div>
                <div class="update-text">
                    <strong>${info.titulo}</strong><br>
                    ${info.descripcion}
                </div>
            `;

            // Insertar al principio
            const firstUpdate = updates.querySelector('.update-item');
            if (firstUpdate) {
                updates.insertBefore(newUpdate, firstUpdate);
            }

            // Mantener solo las últimas 5 actualizaciones
            const allUpdates = updates.querySelectorAll('.update-item');
            if (allUpdates.length > 5) {
                allUpdates[allUpdates.length - 1].remove();
            }
        }

        // PASO 7: Calcular distancia entre dos puntos
        function calculateDistance(pos1, pos2) {
            const R = 6371; // Radio de la Tierra en km
            const dLat = (pos2.lat - pos1.lat) * Math.PI / 180;
            const dLng = (pos2.lng - pos1.lng) * Math.PI / 180;
            const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                      Math.cos(pos1.lat * Math.PI / 180) * Math.cos(pos2.lat * Math.PI / 180) *
                      Math.sin(dLng/2) * Math.sin(dLng/2);
            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
            return R * c;
        }

        // PASO 7: Agregar actualización en vivo
        function addLiveUpdate() {
            const updates = document.querySelector('.live-updates');
            const now = new Date();
            const timeStr = now.getHours().toString().padStart(2, '0') + ':' + 
                           now.getMinutes().toString().padStart(2, '0');
            
            const messages = [
                `Delivery se encuentra a ${(calculateDistance(currentPosition, customerPosition) * 1000).toFixed(0)}m de distancia`,
                `Avanzando por la ruta óptima, ETA actualizado: ${eta} minutos`,
                `Ubicación actualizada - Sin demoras en el tráfico`,
                `Delivery mantiene velocidad constante hacia destino`
            ];
            
            const randomMessage = messages[Math.floor(Math.random() * messages.length)];
            
            const newUpdate = document.createElement('div');
            newUpdate.className = 'update-item';
            newUpdate.innerHTML = `
                <div class="update-time">${timeStr}</div>
                <div class="update-icon route">📍</div>
                <div class="update-text">
                    <strong>Ubicación actualizada</strong><br>
                    ${randomMessage}
                </div>
            `;
            
            // Insertar al principio
            const firstUpdate = updates.querySelector('.update-item');
            if (firstUpdate) {
                updates.insertBefore(newUpdate, firstUpdate);
            }
            
            // Mantener solo las últimas 5 actualizaciones
            const allUpdates = updates.querySelectorAll('.update-item');
            if (allUpdates.length > 5) {
                allUpdates[allUpdates.length - 1].remove();
            }
            
            // Actualizar timestamp
            document.getElementById('last-update').textContent = 'ahora';
        }

        // PASO 7: Funciones de contacto
        function callDelivery() {
            alert('📞 Llamando a Juan Pérez...\n\n+54 ************\n\n¡El delivery será notificado de tu llamada!');
        }

        function messageDelivery() {
            const message = prompt('💬 Enviar mensaje a Juan Pérez:\n\n(El delivery recibirá tu mensaje al instante)');
            if (message) {
                alert(`✅ Mensaje enviado a Juan:\n\n"${message}"\n\n📱 El delivery responderá pronto.`);
                
                // Simular respuesta del delivery
                setTimeout(() => {
                    alert('📱 Respuesta de Juan:\n\n"¡Perfecto! Estoy llegando en unos minutos. ¡Gracias!"');
                }, 3000);
            }
        }

        // PASO 7: Actualizar tracking
        function refreshTracking() {
            const btn = document.querySelector('.refresh-btn');
            const originalText = btn.innerHTML;
            
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Actualizando...';
            btn.disabled = true;
            
            setTimeout(() => {
                simulateDeliveryMovement();
                btn.innerHTML = '<i class="fas fa-check"></i> Actualizado';
                
                setTimeout(() => {
                    btn.innerHTML = originalText;
                    btn.disabled = false;
                }, 1000);
            }, 1500);
        }

        // PASO 7: Inicializar cuando carga la página
        document.addEventListener('DOMContentLoaded', function() {
            initMap();
            
            // Simular movimiento cada 15 segundos
            setInterval(simulateDeliveryMovement, 15000);
            
            // Actualizar "última actualización" cada minuto
            setInterval(() => {
                const lastUpdate = document.getElementById('last-update');
                const current = lastUpdate.textContent;
                if (current === 'ahora') {
                    lastUpdate.textContent = 'hace 1m';
                } else if (current === 'hace 1m') {
                    lastUpdate.textContent = 'hace 2m';
                } else {
                    lastUpdate.textContent = 'hace 30s';
                }
            }, 60000);
        });
    </script>
</body>
</html>
