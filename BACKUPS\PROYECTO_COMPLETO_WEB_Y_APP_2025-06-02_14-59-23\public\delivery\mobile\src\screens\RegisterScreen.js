import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Linking,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { MaterialIcons } from '@expo/vector-icons';

const COLORS = {
  primary: '#FF6B35',
  secondary: '#F7931E',
  red: '#E53E3E',
  white: '#FFFFFF',
  dark: '#2D3748',
  gradient: ['#FF6B35', '#E53E3E', '#F7931E'],
};

const RegisterScreen = ({ navigation }) => {
  const openWebRegistration = () => {
    const url = 'http://localhost/mechanical-workshop/public/delivery/registro.php';
    Linking.openURL(url).catch(err => {
      console.error('Error opening URL:', err);
      alert('No se pudo abrir el formulario de registro');
    });
  };

  return (
    <LinearGradient
      colors={COLORS.gradient}
      style={styles.container}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
    >
      <View style={styles.content}>
        {/* Logo */}
        <View style={styles.logoContainer}>
          <View style={styles.logoCircle}>
            <MaterialIcons name="motorcycle" size={60} color={COLORS.white} />
          </View>
          <Text style={styles.appName}>
            <Text style={styles.repu}>Repu</Text>
            <Text style={styles.movil}>Movil</Text>
          </Text>
          <Text style={styles.delivery}>DELIVERY</Text>
        </View>

        {/* Mensaje */}
        <View style={styles.messageContainer}>
          <MaterialIcons name="web" size={80} color="rgba(255,255,255,0.8)" />
          <Text style={styles.title}>Registro Completo</Text>
          <Text style={styles.subtitle}>
            Para registrarte como repartidor, necesitás completar el formulario web con todos tus documentos
          </Text>
          
          <View style={styles.featuresContainer}>
            <View style={styles.featureItem}>
              <MaterialIcons name="assignment" size={20} color={COLORS.white} />
              <Text style={styles.featureText}>Formulario completo</Text>
            </View>
            <View style={styles.featureItem}>
              <MaterialIcons name="camera-alt" size={20} color={COLORS.white} />
              <Text style={styles.featureText}>Subir documentos</Text>
            </View>
            <View style={styles.featureItem}>
              <MaterialIcons name="verified-user" size={20} color={COLORS.white} />
              <Text style={styles.featureText}>Verificación segura</Text>
            </View>
          </View>
        </View>

        {/* Botones */}
        <View style={styles.buttonsContainer}>
          <TouchableOpacity
            style={styles.registerButton}
            onPress={openWebRegistration}
          >
            <LinearGradient
              colors={['rgba(255, 255, 255, 0.2)', 'rgba(255, 255, 255, 0.1)']}
              style={styles.buttonGradient}
            >
              <MaterialIcons name="open-in-browser" size={24} color={COLORS.white} />
              <Text style={styles.buttonText}>Abrir Formulario Web</Text>
            </LinearGradient>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <MaterialIcons name="arrow-back" size={20} color={COLORS.white} />
            <Text style={styles.backButtonText}>Volver al Login</Text>
          </TouchableOpacity>
        </View>
      </View>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 30,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  logoCircle: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 15,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  appName: {
    fontSize: 28,
    fontWeight: '800',
    marginBottom: 5,
  },
  repu: {
    color: COLORS.white,
  },
  movil: {
    color: '#FFE5D9',
  },
  delivery: {
    fontSize: 16,
    fontWeight: '900',
    color: COLORS.white,
    letterSpacing: 2,
  },
  messageContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: COLORS.white,
    textAlign: 'center',
    marginTop: 20,
    marginBottom: 15,
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 30,
  },
  featuresContainer: {
    alignItems: 'center',
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 25,
    minWidth: 200,
  },
  featureText: {
    color: COLORS.white,
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 10,
  },
  buttonsContainer: {
    width: '100%',
    alignItems: 'center',
  },
  registerButton: {
    borderRadius: 15,
    overflow: 'hidden',
    marginBottom: 20,
    width: '100%',
  },
  buttonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 18,
    paddingHorizontal: 30,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 15,
  },
  buttonText: {
    color: COLORS.white,
    fontSize: 16,
    fontWeight: '700',
    marginLeft: 10,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 15,
    paddingHorizontal: 30,
  },
  backButtonText: {
    color: 'rgba(255, 255, 255, 0.9)',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
  },
});

export default RegisterScreen;
