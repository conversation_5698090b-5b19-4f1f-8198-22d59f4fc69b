<?php
/**
 * Test de debug para verificar conexión y datos
 */

header('Content-Type: application/json');
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🔧 DEBUG REPUMOVIL</h2>";

try {
    // Test 1: Conexión a BD
    echo "<h3>1. Test Conexión BD:</h3>";
    require_once 'db_config.php';
    $pdo = connectDB();
    echo "✅ Conexión exitosa<br>";

    // Test 2: Verificar tabla categorias
    echo "<h3>2. Test Tabla Categorías:</h3>";
    $stmt = $pdo->query("SHOW TABLES LIKE 'categorias'");
    if ($stmt->rowCount() > 0) {
        echo "✅ Tabla categorias existe<br>";

        // Contar categorías
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM categorias");
        $count = $stmt->fetch()['total'];
        echo "📊 Total categorías: $count<br>";

        // Mostrar categorías
        $stmt = $pdo->query("SELECT * FROM categorias ORDER BY orden");
        $categorias = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "<pre>";
        print_r($categorias);
        echo "</pre>";
    } else {
        echo "❌ Tabla categorias NO existe<br>";
    }

    // Test 3: Verificar estructura tabla repuestos
    echo "<h3>3. Test Estructura Tabla Repuestos:</h3>";
    $stmt = $pdo->query("DESCRIBE repuestos");
    $columnas = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<h4>📋 Columnas de la tabla repuestos:</h4>";
    echo "<pre>";
    print_r($columnas);
    echo "</pre>";

    // Test 4: Verificar tabla repuestos
    echo "<h3>4. Test Tabla Repuestos:</h3>";
    $stmt = $pdo->query("SHOW TABLES LIKE 'repuestos'");
    if ($stmt->rowCount() > 0) {
        echo "✅ Tabla repuestos existe<br>";

        // Contar repuestos
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM repuestos");
        $count = $stmt->fetch()['total'];
        echo "📊 Total repuestos: $count<br>";

        // Mostrar algunos repuestos
        $stmt = $pdo->query("SELECT * FROM repuestos LIMIT 3");
        $repuestos = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "<pre>";
        print_r($repuestos);
        echo "</pre>";
    } else {
        echo "❌ Tabla repuestos NO existe<br>";
    }

    // Test 4: Búsqueda simple
    echo "<h3>4. Test Búsqueda Simple:</h3>";
    $stmt = $pdo->prepare("SELECT * FROM repuestos WHERE nombre LIKE ? LIMIT 3");
    $stmt->execute(['%buj%']);
    $resultados = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "🔍 Búsqueda 'buj': " . count($resultados) . " resultados<br>";
    echo "<pre>";
    print_r($resultados);
    echo "</pre>";

} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "<br>";
    echo "📍 Archivo: " . $e->getFile() . "<br>";
    echo "📍 Línea: " . $e->getLine() . "<br>";
}
?>
