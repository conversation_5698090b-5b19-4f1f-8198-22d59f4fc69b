<?php
// PASO 2: Simulador de tracking GPS desde app móvil
// Simula el comportamiento real de la app React Native

echo "📱 SIMULADOR DE TRACKING GPS MÓVIL\n";
echo "==================================\n\n";

$baseUrl = 'http://localhost/mechanical-workshop/public/api/tracking.php';

// Configuración de la simulación
$deliveryId = 1;
$pedidoId = 1;
$duracionMinutos = 5; // Duración de la simulación
$intervalSegundos = 3; // Intervalo entre envíos (como la app real)

// Punto de inicio (San Juan, Argentina)
$latInicio = -31.5375;
$lngInicio = -68.5364;

// Punto de destino (simular entrega)
$latDestino = -31.5400;
$lngDestino = -68.5400;

echo "🎯 CONFIGURACIÓN DE SIMULACIÓN:\n";
echo "   📱 Delivery ID: $deliveryId\n";
echo "   📦 Pedido ID: $pedidoId\n";
echo "   ⏱️ Duración: $duracionMinutos minutos\n";
echo "   🔄 Intervalo: $intervalSegundos segundos\n";
echo "   📍 Inicio: $latInicio, $lngInicio\n";
echo "   🎯 Destino: $latDestino, $lngDestino\n\n";

/**
 * Función para enviar ubicación a la API
 */
function enviarUbicacion($url, $data) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'success' => !$error && $httpCode === 200,
        'response' => $response,
        'error' => $error,
        'http_code' => $httpCode
    ];
}

/**
 * Calcular punto intermedio en la ruta
 */
function calcularPuntoRuta($latInicio, $lngInicio, $latDestino, $lngDestino, $progreso) {
    $lat = $latInicio + ($latDestino - $latInicio) * $progreso;
    $lng = $lngInicio + ($lngDestino - $lngInicio) * $progreso;
    
    // Agregar variación aleatoria para simular movimiento real
    $variacion = 0.0001; // ~10 metros
    $lat += (rand(-100, 100) / 1000000) * $variacion;
    $lng += (rand(-100, 100) / 1000000) * $variacion;
    
    return [$lat, $lng];
}

/**
 * Simular datos de GPS realistas
 */
function simularDatosGPS($lat, $lng, $velocidad) {
    return [
        'latitude' => $lat,
        'longitude' => $lng,
        'accuracy' => rand(3, 8) + (rand(0, 100) / 100), // 3-8 metros
        'speed' => $velocidad + rand(-5, 5), // Variación de velocidad
        'heading' => rand(0, 360), // Dirección aleatoria
        'timestamp' => time() * 1000 // Timestamp en milisegundos
    ];
}

// INICIO DE LA SIMULACIÓN
echo "🚀 INICIANDO SIMULACIÓN DE TRACKING GPS...\n";
echo "==========================================\n\n";

$totalPuntos = ($duracionMinutos * 60) / $intervalSegundos;
$puntosEnviados = 0;
$errores = 0;

for ($i = 0; $i < $totalPuntos; $i++) {
    $progreso = $i / $totalPuntos;
    $tiempoTranscurrido = $i * $intervalSegundos;
    
    // Calcular posición actual en la ruta
    list($lat, $lng) = calcularPuntoRuta($latInicio, $lngInicio, $latDestino, $lngDestino, $progreso);
    
    // Simular velocidad variable (0-50 km/h)
    $velocidadBase = 25; // km/h promedio
    if ($progreso < 0.1) {
        $velocidadBase = 10; // Inicio lento
    } elseif ($progreso > 0.9) {
        $velocidadBase = 5; // Llegada lenta
    }
    
    // Convertir km/h a m/s para la API
    $velocidadMs = ($velocidadBase * 1000) / 3600;
    
    // Generar datos GPS realistas
    $datosGPS = simularDatosGPS($lat, $lng, $velocidadMs);
    
    // Preparar payload como lo haría la app React Native
    $payload = [
        'action' => 'update_location',
        'delivery_id' => $deliveryId,
        'pedido_id' => $pedidoId,
        'latitude' => $datosGPS['latitude'],
        'longitude' => $datosGPS['longitude'],
        'accuracy' => $datosGPS['accuracy'],
        'speed' => $datosGPS['speed'],
        'heading' => $datosGPS['heading'],
        'timestamp' => $datosGPS['timestamp']
    ];
    
    // Enviar a la API
    $resultado = enviarUbicacion($baseUrl, $payload);
    
    if ($resultado['success']) {
        $puntosEnviados++;
        $porcentaje = round($progreso * 100, 1);
        
        echo sprintf(
            "📍 Punto %d/%d (%s%%) - Lat: %s, Lng: %s, Vel: %s km/h, Prec: ±%sm ✅\n",
            $i + 1,
            $totalPuntos,
            $porcentaje,
            number_format($datosGPS['latitude'], 6),
            number_format($datosGPS['longitude'], 6),
            number_format($velocidadBase, 1),
            number_format($datosGPS['accuracy'], 1)
        );
        
        // Mostrar hitos importantes
        if ($progreso >= 0.25 && $progreso < 0.26) {
            echo "   🎯 25% del recorrido completado\n";
        } elseif ($progreso >= 0.50 && $progreso < 0.51) {
            echo "   🎯 50% del recorrido completado\n";
        } elseif ($progreso >= 0.75 && $progreso < 0.76) {
            echo "   🎯 75% del recorrido completado\n";
        } elseif ($progreso >= 0.95) {
            echo "   🎯 Llegando al destino...\n";
        }
        
    } else {
        $errores++;
        echo sprintf(
            "❌ Punto %d/%d - Error: %s (HTTP %d)\n",
            $i + 1,
            $totalPuntos,
            $resultado['error'] ?: 'Error desconocido',
            $resultado['http_code']
        );
    }
    
    // Pausa entre envíos (simular intervalo real)
    if ($i < $totalPuntos - 1) {
        sleep($intervalSegundos);
    }
}

// RESUMEN FINAL
echo "\n🏁 SIMULACIÓN COMPLETADA\n";
echo "========================\n";
echo "✅ Puntos enviados exitosamente: $puntosEnviados/$totalPuntos\n";
echo "❌ Errores: $errores\n";
echo "📊 Tasa de éxito: " . round(($puntosEnviados / $totalPuntos) * 100, 1) . "%\n";
echo "⏱️ Tiempo total: " . ($totalPuntos * $intervalSegundos) . " segundos\n";
echo "🛣️ Distancia simulada: " . round(calculateDistance($latInicio, $lngInicio, $latDestino, $lngDestino), 2) . " km\n\n";

if ($puntosEnviados > 0) {
    echo "🎉 ¡SIMULACIÓN EXITOSA! Los datos están siendo enviados correctamente.\n";
    echo "📱 Verificar en el monitor: http://localhost/mechanical-workshop/public/monitor-tracking.php\n";
    echo "🗺️ Verificar en el panel: http://localhost/mechanical-workshop/public/test-tracking.php\n\n";
} else {
    echo "⚠️ No se pudieron enviar datos. Verificar:\n";
    echo "   - XAMPP está ejecutándose\n";
    echo "   - API tracking.php está funcionando\n";
    echo "   - Base de datos está conectada\n\n";
}

echo "🚀 PRÓXIMO PASO: Probar en la app React Native real\n";
echo "📱 Comando: cd RepuMovilExpo && npx expo start\n";

/**
 * Calcular distancia entre dos puntos
 */
function calculateDistance($lat1, $lng1, $lat2, $lng2) {
    $radioTierra = 6371; // km
    
    $dLat = deg2rad($lat2 - $lat1);
    $dLng = deg2rad($lng2 - $lng1);
    
    $a = sin($dLat/2) * sin($dLat/2) +
         cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
         sin($dLng/2) * sin($dLng/2);
    
    $c = 2 * atan2(sqrt($a), sqrt(1-$a));
    
    return $radioTierra * $c;
}
?>
