<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RepuMovil - Calificar Delivery ⭐</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #FF6B35;
            --secondary-color: #FFA500;
            --dark-color: #2c3e50;
            --light-color: #f8f9fa;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            max-width: 500px;
            width: 100%;
        }

        .calificacion-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            color: var(--dark-color);
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            text-align: center;
            animation: slideUp 0.5s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .success-icon {
            font-size: 4rem;
            color: var(--success-color);
            margin-bottom: 20px;
            animation: bounce 1s ease-in-out;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-20px); }
            60% { transform: translateY(-10px); }
        }

        .title {
            font-size: 1.8rem;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 10px;
        }

        .subtitle {
            color: #666;
            margin-bottom: 30px;
            line-height: 1.5;
        }

        .pedido-info {
            background: var(--light-color);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: left;
        }

        .pedido-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .pedido-numero {
            font-size: 1.2rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        .pedido-total {
            font-size: 1.1rem;
            font-weight: bold;
            color: var(--success-color);
        }

        .delivery-info {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }

        .delivery-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .delivery-details h3 {
            color: var(--primary-color);
            margin-bottom: 5px;
        }

        .delivery-stats {
            font-size: 0.9rem;
            color: #666;
        }

        .rating-section {
            margin-bottom: 30px;
        }

        .rating-title {
            font-size: 1.2rem;
            font-weight: bold;
            color: var(--dark-color);
            margin-bottom: 15px;
        }

        .stars-container {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 20px;
        }

        .star {
            font-size: 2.5rem;
            color: #ddd;
            cursor: pointer;
            transition: all 0.3s ease;
            user-select: none;
        }

        .star:hover,
        .star.active {
            color: #ffd700;
            transform: scale(1.1);
        }

        .star.active {
            animation: starPulse 0.3s ease-in-out;
        }

        @keyframes starPulse {
            0% { transform: scale(1.1); }
            50% { transform: scale(1.3); }
            100% { transform: scale(1.1); }
        }

        .rating-text {
            font-size: 1.1rem;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 10px;
        }

        .rating-description {
            font-size: 0.9rem;
            color: #666;
        }

        .feedback-section {
            margin-bottom: 30px;
            text-align: left;
        }

        .feedback-title {
            font-size: 1.1rem;
            font-weight: bold;
            color: var(--dark-color);
            margin-bottom: 10px;
        }

        .feedback-textarea {
            width: 100%;
            min-height: 100px;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-family: inherit;
            font-size: 0.9rem;
            resize: vertical;
            transition: border-color 0.3s ease;
        }

        .feedback-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .feedback-suggestions {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 10px;
        }

        .suggestion-tag {
            background: var(--light-color);
            border: 1px solid #ddd;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .suggestion-tag:hover {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .buttons-container {
            display: flex;
            gap: 15px;
        }

        .btn {
            flex: 1;
            padding: 15px;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: #e55a2b;
            transform: translateY(-2px);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .thank-you-message {
            display: none;
            text-align: center;
            animation: fadeIn 0.5s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .thank-you-icon {
            font-size: 3rem;
            color: var(--success-color);
            margin-bottom: 20px;
        }

        .thank-you-title {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--success-color);
            margin-bottom: 10px;
        }

        .thank-you-text {
            color: #666;
            margin-bottom: 20px;
        }

        .rating-summary {
            background: var(--light-color);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }

        .summary-label {
            color: #666;
        }

        .summary-value {
            font-weight: bold;
            color: var(--primary-color);
        }

        @media (max-width: 480px) {
            .calificacion-card {
                padding: 20px;
            }
            
            .stars-container {
                gap: 5px;
            }
            
            .star {
                font-size: 2rem;
            }
            
            .buttons-container {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="calificacion-card">
            <!-- Contenido Principal -->
            <div id="main-content">
                <div class="success-icon">✅</div>
                <h1 class="title">¡Pedido Entregado!</h1>
                <p class="subtitle">Tu pedido ha sido entregado exitosamente. Ayúdanos a mejorar calificando tu experiencia.</p>

                <!-- Info del Pedido -->
                <div class="pedido-info">
                    <div class="pedido-header">
                        <div class="pedido-numero">Pedido #1024</div>
                        <div class="pedido-total">$2,450</div>
                    </div>
                    
                    <div class="delivery-info">
                        <div class="delivery-avatar">🏍️</div>
                        <div class="delivery-details">
                            <h3>Juan Pérez</h3>
                            <div class="delivery-stats">
                                ⭐ 4.8 • 156 entregas • Tiempo: 18 min
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sección de Calificación -->
                <div class="rating-section">
                    <div class="rating-title">¿Cómo fue tu experiencia?</div>
                    
                    <div class="stars-container">
                        <span class="star" data-rating="1">⭐</span>
                        <span class="star" data-rating="2">⭐</span>
                        <span class="star" data-rating="3">⭐</span>
                        <span class="star" data-rating="4">⭐</span>
                        <span class="star" data-rating="5">⭐</span>
                    </div>
                    
                    <div class="rating-text" id="rating-text">Selecciona una calificación</div>
                    <div class="rating-description" id="rating-description">Tu opinión nos ayuda a mejorar el servicio</div>
                </div>

                <!-- Sección de Feedback -->
                <div class="feedback-section">
                    <div class="feedback-title">Cuéntanos más sobre tu experiencia (opcional)</div>
                    <textarea 
                        class="feedback-textarea" 
                        id="feedback-text"
                        placeholder="¿Qué te gustó? ¿Qué podríamos mejorar? Tu feedback es muy valioso para nosotros..."
                        maxlength="500"
                    ></textarea>
                    
                    <div class="feedback-suggestions">
                        <span class="suggestion-tag" onclick="addSuggestion('Entrega rápida')">Entrega rápida</span>
                        <span class="suggestion-tag" onclick="addSuggestion('Delivery amable')">Delivery amable</span>
                        <span class="suggestion-tag" onclick="addSuggestion('Pedido en perfecto estado')">Pedido perfecto</span>
                        <span class="suggestion-tag" onclick="addSuggestion('Comunicación excelente')">Buena comunicación</span>
                        <span class="suggestion-tag" onclick="addSuggestion('Muy profesional')">Muy profesional</span>
                    </div>
                </div>

                <!-- Botones -->
                <div class="buttons-container">
                    <button class="btn btn-secondary" onclick="omitirCalificacion()">
                        <i class="fas fa-times"></i>
                        Omitir
                    </button>
                    <button class="btn btn-primary" id="submit-btn" onclick="enviarCalificacion()" disabled>
                        <i class="fas fa-paper-plane"></i>
                        Enviar Calificación
                    </button>
                </div>
            </div>

            <!-- Mensaje de Agradecimiento -->
            <div id="thank-you-message" class="thank-you-message">
                <div class="thank-you-icon">🎉</div>
                <h2 class="thank-you-title">¡Gracias por tu feedback!</h2>
                <p class="thank-you-text">Tu calificación nos ayuda a mantener la calidad del servicio RepuMovil.</p>
                
                <div class="rating-summary">
                    <div class="summary-row">
                        <span class="summary-label">Tu calificación:</span>
                        <span class="summary-value" id="final-rating">⭐⭐⭐⭐⭐</span>
                    </div>
                    <div class="summary-row">
                        <span class="summary-label">Delivery:</span>
                        <span class="summary-value">Juan Pérez</span>
                    </div>
                    <div class="summary-row">
                        <span class="summary-label">Nueva calificación promedio:</span>
                        <span class="summary-value" id="new-avg-rating">4.8 ⭐</span>
                    </div>
                </div>

                <button class="btn btn-primary" onclick="volverAlInicio()">
                    <i class="fas fa-home"></i>
                    Volver al Inicio
                </button>
            </div>
        </div>
    </div>

    <script>
        // PASO 9: Variables globales
        let selectedRating = 0;
        let isSubmitting = false;

        // PASO 9: Configurar eventos de estrellas
        document.addEventListener('DOMContentLoaded', function() {
            const stars = document.querySelectorAll('.star');
            
            stars.forEach(star => {
                star.addEventListener('click', function() {
                    const rating = parseInt(this.dataset.rating);
                    setRating(rating);
                });
                
                star.addEventListener('mouseenter', function() {
                    const rating = parseInt(this.dataset.rating);
                    highlightStars(rating);
                });
            });
            
            document.querySelector('.stars-container').addEventListener('mouseleave', function() {
                highlightStars(selectedRating);
            });
        });

        // PASO 9: Establecer calificación
        function setRating(rating) {
            selectedRating = rating;
            highlightStars(rating);
            updateRatingText(rating);
            
            // Habilitar botón de envío
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = false;
        }

        // PASO 9: Resaltar estrellas
        function highlightStars(rating) {
            const stars = document.querySelectorAll('.star');
            stars.forEach((star, index) => {
                if (index < rating) {
                    star.classList.add('active');
                } else {
                    star.classList.remove('active');
                }
            });
        }

        // PASO 9: Actualizar texto de calificación
        function updateRatingText(rating) {
            const ratingTexts = {
                1: { text: 'Muy malo', description: 'El servicio no cumplió las expectativas' },
                2: { text: 'Malo', description: 'El servicio tuvo varios problemas' },
                3: { text: 'Regular', description: 'El servicio fue aceptable' },
                4: { text: 'Bueno', description: 'El servicio fue satisfactorio' },
                5: { text: 'Excelente', description: '¡El servicio superó las expectativas!' }
            };
            
            const ratingInfo = ratingTexts[rating];
            document.getElementById('rating-text').textContent = ratingInfo.text;
            document.getElementById('rating-description').textContent = ratingInfo.description;
        }

        // PASO 9: Agregar sugerencia al textarea
        function addSuggestion(suggestion) {
            const textarea = document.getElementById('feedback-text');
            const currentText = textarea.value.trim();
            
            if (currentText === '') {
                textarea.value = suggestion;
            } else if (!currentText.includes(suggestion)) {
                textarea.value = currentText + '. ' + suggestion;
            }
            
            textarea.focus();
        }

        // PASO 9: Enviar calificación
        async function enviarCalificacion() {
            if (selectedRating === 0 || isSubmitting) return;
            
            isSubmitting = true;
            const submitBtn = document.getElementById('submit-btn');
            const originalText = submitBtn.innerHTML;
            
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Enviando...';
            submitBtn.disabled = true;
            
            try {
                // Simular llamada a API
                const response = await fetch('api/calificaciones.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'crear_calificacion',
                        pedido_id: '1024',
                        delivery_id: 1,
                        cliente_id: 1,
                        calificacion: selectedRating,
                        comentario: document.getElementById('feedback-text').value.trim(),
                        timestamp: new Date().toISOString()
                    })
                });
                
                // Simular respuesta exitosa
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                // Mostrar mensaje de agradecimiento
                mostrarAgradecimiento();
                
            } catch (error) {
                console.error('Error enviando calificación:', error);
                alert('Error al enviar la calificación. Por favor, intenta de nuevo.');
                
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
                isSubmitting = false;
            }
        }

        // PASO 9: Mostrar mensaje de agradecimiento
        function mostrarAgradecimiento() {
            document.getElementById('main-content').style.display = 'none';
            document.getElementById('thank-you-message').style.display = 'block';
            
            // Actualizar resumen
            const stars = '⭐'.repeat(selectedRating);
            document.getElementById('final-rating').textContent = stars;
            
            // Simular nueva calificación promedio
            const newAvg = (4.8 * 156 + selectedRating) / 157;
            document.getElementById('new-avg-rating').textContent = newAvg.toFixed(1) + ' ⭐';
        }

        // PASO 9: Omitir calificación
        function omitirCalificacion() {
            if (confirm('¿Estás seguro de que quieres omitir la calificación? Tu feedback nos ayuda a mejorar.')) {
                volverAlInicio();
            }
        }

        // PASO 9: Volver al inicio
        function volverAlInicio() {
            // En producción, redirigir a la página principal
            alert('¡Gracias por usar RepuMovil! Redirigiendo al inicio...');
            // window.location.href = '/';
        }
    </script>
</body>
</html>
