<?php

namespace Src;

require_once 'Database.php';

/**
 * Clase para gestionar usuarios en la base de datos
 */
class UserDB {
    private $db;

    /**
     * Constructor de la clase UserDB
     */
    public function __construct() {
        $this->db = Database::getInstance();
    }

    /**
     * Autentica a un usuario
     * 
     * @param string $username Nombre de usuario
     * @param string $password Contraseña
     * @return array|bool Datos del usuario o false si falla
     */
    public function authenticateUser($username, $password) {
        $query = "SELECT u.*, r.name as role_name 
                 FROM users u 
                 JOIN roles r ON u.role_id = r.id 
                 WHERE u.username = :username AND u.status = 'active'";
        
        $user = $this->db->single($query, ['username' => $username]);
        
        if ($user && password_verify($password, $user['password'])) {
            // Actualizar último inicio de sesión
            $this->updateLastLogin($user['id']);
            
            // Obtener datos adicionales según el rol
            if ($user['role_name'] === 'workshop') {
                $user['workshop'] = $this->getWorkshopByUserId($user['id']);
            } elseif ($user['role_name'] === 'supplier') {
                $user['supplier'] = $this->getSupplierByUserId($user['id']);
            }
            
            return $user;
        }
        
        return false;
    }

    /**
     * Actualiza la fecha del último inicio de sesión
     * 
     * @param int $userId ID del usuario
     * @return bool
     */
    private function updateLastLogin($userId) {
        $query = "UPDATE users SET last_login = NOW() WHERE id = :id";
        return $this->db->query($query, ['id' => $userId]) ? true : false;
    }

    /**
     * Obtiene los datos de un taller por ID de usuario
     * 
     * @param int $userId ID del usuario
     * @return array|bool
     */
    public function getWorkshopByUserId($userId) {
        $query = "SELECT * FROM workshops WHERE user_id = :user_id";
        return $this->db->single($query, ['user_id' => $userId]);
    }

    /**
     * Obtiene los datos de un proveedor por ID de usuario
     * 
     * @param int $userId ID del usuario
     * @return array|bool
     */
    public function getSupplierByUserId($userId) {
        $query = "SELECT * FROM suppliers WHERE user_id = :user_id";
        return $this->db->single($query, ['user_id' => $userId]);
    }

    /**
     * Verifica si un nombre de usuario ya existe
     * 
     * @param string $username Nombre de usuario
     * @return bool
     */
    public function usernameExists($username) {
        $query = "SELECT COUNT(*) as count FROM users WHERE username = :username";
        $result = $this->db->single($query, ['username' => $username]);
        return $result['count'] > 0;
    }

    /**
     * Verifica si un correo electrónico ya existe
     * 
     * @param string $email Correo electrónico
     * @return bool
     */
    public function emailExists($email) {
        $query = "SELECT COUNT(*) as count FROM users WHERE email = :email";
        $result = $this->db->single($query, ['email' => $email]);
        return $result['count'] > 0;
    }

    /**
     * Crea un nuevo usuario de taller
     * 
     * @param string $username Nombre de usuario
     * @param string $email Correo electrónico
     * @param string $password Contraseña
     * @param string $workshopName Nombre del taller
     * @param array $workshopData Datos adicionales del taller
     * @return int|bool ID del usuario creado o false si falla
     */
    public function createWorkshopUser($username, $email, $password, $workshopName, $workshopData = []) {
        // Iniciar transacción
        $conn = $this->db->getConnection();
        $conn->beginTransaction();
        
        try {
            // Crear usuario
            $userId = $this->createUser($username, $email, $password, 2); // 2 = workshop role
            
            if (!$userId) {
                $conn->rollBack();
                return false;
            }
            
            // Crear taller
            $query = "INSERT INTO workshops (user_id, name, location, phone, description) 
                     VALUES (:user_id, :name, :location, :phone, :description)";
            
            $params = [
                'user_id' => $userId,
                'name' => $workshopName,
                'location' => $workshopData['location'] ?? '',
                'phone' => $workshopData['phone'] ?? '',
                'description' => $workshopData['description'] ?? ''
            ];
            
            $result = $this->db->query($query, $params);
            
            if (!$result) {
                $conn->rollBack();
                return false;
            }
            
            $conn->commit();
            return $userId;
            
        } catch (\Exception $e) {
            $conn->rollBack();
            return false;
        }
    }

    /**
     * Crea un nuevo usuario
     * 
     * @param string $username Nombre de usuario
     * @param string $email Correo electrónico
     * @param string $password Contraseña
     * @param int $roleId ID del rol
     * @return int|bool ID del usuario creado o false si falla
     */
    private function createUser($username, $email, $password, $roleId) {
        $query = "INSERT INTO users (username, email, password, role_id) 
                 VALUES (:username, :email, :password, :role_id)";
        
        $params = [
            'username' => $username,
            'email' => $email,
            'password' => password_hash($password, PASSWORD_DEFAULT),
            'role_id' => $roleId
        ];
        
        $result = $this->db->query($query, $params);
        
        if ($result) {
            return $this->db->lastInsertId();
        }
        
        return false;
    }

    /**
     * Obtiene todos los usuarios
     * 
     * @return array|bool
     */
    public function getAllUsers() {
        $query = "SELECT u.*, r.name as role_name 
                 FROM users u 
                 JOIN roles r ON u.role_id = r.id 
                 ORDER BY u.id";
        
        return $this->db->resultSet($query);
    }

    /**
     * Obtiene un usuario por su ID
     * 
     * @param int $userId
     * @return array|bool
     */
    public function getUserById($userId) {
        $query = "SELECT u.*, r.name as role_name 
                 FROM users u 
                 JOIN roles r ON u.role_id = r.id 
                 WHERE u.id = :id";
        
        return $this->db->single($query, ['id' => $userId]);
    }

    /**
     * Actualiza el estado de un usuario
     * 
     * @param int $userId
     * @param string $status
     * @return bool
     */
    public function updateUserStatus($userId, $status) {
        $query = "UPDATE users SET status = :status WHERE id = :id";
        return $this->db->query($query, ['id' => $userId, 'status' => $status]) ? true : false;
    }

    /**
     * Elimina un usuario
     * 
     * @param int $userId
     * @return bool
     */
    public function deleteUser($userId) {
        $query = "DELETE FROM users WHERE id = :id";
        return $this->db->query($query, ['id' => $userId]) ? true : false;
    }

    /**
     * Verifica si un usuario es administrador
     * 
     * @param int $userId
     * @return bool
     */
    public function isAdmin($userId) {
        $query = "SELECT r.name FROM users u 
                 JOIN roles r ON u.role_id = r.id 
                 WHERE u.id = :id";
        
        $result = $this->db->single($query, ['id' => $userId]);
        return $result && $result['name'] === 'admin';
    }
}
