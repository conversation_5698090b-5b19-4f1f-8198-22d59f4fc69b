<?php
// Mostrar todos los errores
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Iniciar sesión
session_start();

// Verificar si el usuario está autenticado y es un proveedor
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'supplier') {
    header('Location: login.php');
    exit;
}

// Configuración de la base de datos
$host = 'localhost';
$dbname = 'autoconnect_db';
$username = 'root';
$password = '';

// Función para conectar a la base de datos
function connectDB() {
    global $host, $dbname, $username, $password;
    
    try {
        $conn = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $conn;
    } catch (PDOException $e) {
        die("Error de conexión: " . $e->getMessage());
    }
}

// Obtener información del usuario
function getUserById($userId) {
    $conn = connectDB();
    
    $query = "SELECT u.*, r.name as role_name 
             FROM users u 
             JOIN roles r ON u.role_id = r.id 
             WHERE u.id = :id";
    
    $stmt = $conn->prepare($query);
    $stmt->execute(['id' => $userId]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user && $user['role_name'] === 'supplier') {
        $supplierQuery = "SELECT * FROM suppliers WHERE user_id = :user_id";
        $supplierStmt = $conn->prepare($supplierQuery);
        $supplierStmt->execute(['user_id' => $userId]);
        $user['supplier'] = $supplierStmt->fetch(PDO::FETCH_ASSOC);
    }
    
    return $user;
}

// Obtener información del usuario
$user = getUserById($_SESSION['user_id']);

// Variables para mensajes
$message = '';
$error = '';
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Panel de Control - Proveedor - AutoConnect</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <style>
        .dashboard {
            display: flex;
            min-height: calc(100vh - 200px);
        }
        
        .sidebar {
            width: 250px;
            background-color: #2c3e50;
            color: white;
            padding: 20px 0;
        }
        
        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
        }
        
        .sidebar-menu {
            list-style: none;
        }
        
        .sidebar-menu li {
            margin-bottom: 5px;
        }
        
        .sidebar-menu a {
            display: block;
            padding: 10px 20px;
            color: #ecf0f1;
            text-decoration: none;
            transition: all 0.3s;
        }
        
        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background-color: #34495e;
            color: white;
        }
        
        .sidebar-menu i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }
        
        .main-content {
            flex: 1;
            padding: 30px;
            background-color: #f5f5f5;
        }
        
        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }
        
        .dashboard-title {
            font-size: 1.8rem;
            color: #2c3e50;
        }
        
        .user-info {
            display: flex;
            align-items: center;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #ff6b00;
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 10px;
            font-weight: bold;
        }
        
        .dashboard-cards {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .card-title {
            font-size: 1.2rem;
            color: #2c3e50;
            font-weight: bold;
        }
        
        .card-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            background-color: rgba(255, 107, 0, 0.1);
            color: #ff6b00;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 1.2rem;
        }
        
        .card-value {
            font-size: 2rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .card-description {
            color: #7f8c8d;
            font-size: 0.9rem;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        
        .data-table th,
        .data-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .data-table th {
            background-color: #f9f9f9;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .data-table tbody tr:hover {
            background-color: #f5f5f5;
        }
        
        .status {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }
        
        .status-in-progress {
            background-color: #cce5ff;
            color: #004085;
        }
        
        .status-completed {
            background-color: #d4edda;
            color: #155724;
        }
        
        .btn {
            display: inline-block;
            padding: 8px 15px;
            background-color: #ff6b00;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            transition: background-color 0.3s;
        }
        
        .btn:hover {
            background-color: #e05e00;
        }
        
        .btn-sm {
            padding: 5px 10px;
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <header>
        <div class="logo-container">
            <i class="fas fa-cogs logo-icon"></i>
            <h1>AutoConnect</h1>
        </div>
    </header>

    <div class="dashboard">
        <div class="sidebar">
            <div class="sidebar-header">
                <h3>Panel de Control</h3>
                <p>Proveedor de Repuestos</p>
            </div>
            <ul class="sidebar-menu">
                <li><a href="#" class="active"><i class="fas fa-tachometer-alt"></i> Inicio</a></li>
                <li><a href="#"><i class="fas fa-box"></i> Mi Inventario</a></li>
                <li><a href="#"><i class="fas fa-clipboard-list"></i> Solicitudes de Talleres</a></li>
                <li><a href="#"><i class="fas fa-handshake"></i> Mis Ofertas</a></li>
                <li><a href="#"><i class="fas fa-history"></i> Historial de Ventas</a></li>
                <li><a href="#"><i class="fas fa-user-cog"></i> Mi Perfil</a></li>
                <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i> Cerrar Sesión</a></li>
            </ul>
        </div>
        
        <div class="main-content">
            <div class="dashboard-header">
                <h2 class="dashboard-title">Panel de Control</h2>
                <div class="user-info">
                    <div class="user-avatar">
                        <?php echo substr($user['username'], 0, 1); ?>
                    </div>
                    <span><?php echo $_SESSION['supplier_name'] ?? $user['username']; ?></span>
                </div>
            </div>
            
            <?php if (!empty($message)): ?>
                <div class="message message-success">
                    <?php echo $message; ?>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($error)): ?>
                <div class="message message-error">
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>
            
            <div class="dashboard-cards">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Solicitudes Nuevas</h3>
                        <div class="card-icon">
                            <i class="fas fa-bell"></i>
                        </div>
                    </div>
                    <div class="card-value">12</div>
                    <div class="card-description">Solicitudes de talleres sin ofertar</div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Ofertas Enviadas</h3>
                        <div class="card-icon">
                            <i class="fas fa-paper-plane"></i>
                        </div>
                    </div>
                    <div class="card-value">8</div>
                    <div class="card-description">Ofertas pendientes de respuesta</div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Ventas del Mes</h3>
                        <div class="card-icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                    </div>
                    <div class="card-value">$4,250</div>
                    <div class="card-description">Total de ventas realizadas</div>
                </div>
            </div>
            
            <h3>Solicitudes Recientes de Talleres</h3>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Taller</th>
                        <th>Repuesto</th>
                        <th>Cantidad</th>
                        <th>Fecha</th>
                        <th>Urgencia</th>
                        <th>Acciones</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>SOL-001</td>
                        <td>Taller Mecánico Ejemplo</td>
                        <td>Filtro de aceite</td>
                        <td>5</td>
                        <td>15/05/2023</td>
                        <td><span class="status status-pending">Normal</span></td>
                        <td><a href="#" class="btn btn-sm">Ofertar</a></td>
                    </tr>
                    <tr>
                        <td>SOL-002</td>
                        <td>Taller Automotriz XYZ</td>
                        <td>Pastillas de freno</td>
                        <td>2</td>
                        <td>14/05/2023</td>
                        <td><span class="status status-in-progress">Urgente</span></td>
                        <td><a href="#" class="btn btn-sm">Ofertar</a></td>
                    </tr>
                    <tr>
                        <td>SOL-003</td>
                        <td>Servicio Técnico ABC</td>
                        <td>Bomba de agua</td>
                        <td>1</td>
                        <td>13/05/2023</td>
                        <td><span class="status status-completed">Crítica</span></td>
                        <td><a href="#" class="btn btn-sm">Ofertar</a></td>
                    </tr>
                </tbody>
            </table>
            
            <h3>Mis Ofertas Recientes</h3>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Taller</th>
                        <th>Repuesto</th>
                        <th>Precio</th>
                        <th>Fecha</th>
                        <th>Estado</th>
                        <th>Acciones</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>OF-001</td>
                        <td>Taller Mecánico Ejemplo</td>
                        <td>Filtro de aceite (5)</td>
                        <td>$125.00</td>
                        <td>12/05/2023</td>
                        <td><span class="status status-pending">Pendiente</span></td>
                        <td><a href="#" class="btn btn-sm">Ver Detalles</a></td>
                    </tr>
                    <tr>
                        <td>OF-002</td>
                        <td>Taller Automotriz XYZ</td>
                        <td>Pastillas de freno (2)</td>
                        <td>$350.00</td>
                        <td>11/05/2023</td>
                        <td><span class="status status-in-progress">Aceptada</span></td>
                        <td><a href="#" class="btn btn-sm">Ver Detalles</a></td>
                    </tr>
                    <tr>
                        <td>OF-003</td>
                        <td>Servicio Técnico ABC</td>
                        <td>Bomba de agua (1)</td>
                        <td>$780.00</td>
                        <td>10/05/2023</td>
                        <td><span class="status status-completed">Completada</span></td>
                        <td><a href="#" class="btn btn-sm">Ver Detalles</a></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <footer>
        <div class="footer-bottom">
            <p>&copy; 2023 AutoConnect. Todos los derechos reservados.</p>
        </div>
    </footer>
</body>
</html>
