<?php

namespace Src;

/**
 * Clase principal que gestiona la conexión entre talleres y proveedores
 */
class AutoConnect {
    private $workshops = [];
    private $suppliers = [];
    private $partRequests = [];
    private $offers = [];
    private $transactions = [];

    /**
     * Registra un nuevo taller en la plataforma
     * 
     * @param Workshop $workshop
     * @return bool
     */
    public function registerWorkshop(Workshop $workshop) {
        $this->workshops[$workshop->getId()] = $workshop;
        return true;
    }

    /**
     * Registra un nuevo proveedor en la plataforma
     * 
     * @param Supplier $supplier
     * @return bool
     */
    public function registerSupplier(Supplier $supplier) {
        $this->suppliers[$supplier->getId()] = $supplier;
        return true;
    }

    /**
     * Obtiene todos los talleres registrados
     * 
     * @return array
     */
    public function getWorkshops() {
        return $this->workshops;
    }

    /**
     * Obtiene un taller específico por su ID
     * 
     * @param string $workshopId
     * @return Workshop|null
     */
    public function getWorkshop($workshopId) {
        return isset($this->workshops[$workshopId]) ? $this->workshops[$workshopId] : null;
    }

    /**
     * Obtiene todos los proveedores registrados
     * 
     * @return array
     */
    public function getSuppliers() {
        return $this->suppliers;
    }

    /**
     * Obtiene un proveedor específico por su ID
     * 
     * @param string $supplierId
     * @return Supplier|null
     */
    public function getSupplier($supplierId) {
        return isset($this->suppliers[$supplierId]) ? $this->suppliers[$supplierId] : null;
    }

    /**
     * Crea una nueva solicitud de repuestos
     * 
     * @param string $workshopId ID del taller
     * @param string $partName Nombre del repuesto
     * @param string $partNumber Número de parte
     * @param int $quantity Cantidad requerida
     * @param string $urgency Nivel de urgencia
     * @return string|bool ID de la solicitud o false si falla
     */
    public function createPartRequest($workshopId, $partName, $partNumber, $quantity, $urgency = 'normal') {
        if (!isset($this->workshops[$workshopId])) {
            return false;
        }
        
        $workshop = $this->workshops[$workshopId];
        $requestId = $workshop->createPartRequest($partName, $partNumber, $quantity, $urgency);
        
        // Almacenar la solicitud en el sistema central también
        $this->partRequests[$requestId] = [
            'id' => $requestId,
            'workshopId' => $workshopId,
            'partName' => $partName,
            'partNumber' => $partNumber,
            'quantity' => $quantity,
            'urgency' => $urgency,
            'status' => 'pending',
            'createdAt' => date('Y-m-d H:i:s'),
            'offers' => []
        ];
        
        // Notificar a los proveedores sobre la nueva solicitud
        $this->notifySuppliers($requestId);
        
        return $requestId;
    }

    /**
     * Notifica a los proveedores sobre una nueva solicitud de repuestos
     * 
     * @param string $requestId
     * @return void
     */
    private function notifySuppliers($requestId) {
        // En una implementación real, aquí se enviarían notificaciones
        // a los proveedores que puedan tener el repuesto solicitado
        // Por ahora, simplemente marcamos la solicitud como notificada
        $this->partRequests[$requestId]['notified'] = true;
    }

    /**
     * Obtiene todas las solicitudes de repuestos
     * 
     * @return array
     */
    public function getAllPartRequests() {
        return $this->partRequests;
    }

    /**
     * Obtiene las solicitudes de repuestos pendientes
     * 
     * @return array
     */
    public function getPendingPartRequests() {
        return array_filter($this->partRequests, function($request) {
            return $request['status'] === 'pending';
        });
    }

    /**
     * Crea una oferta para una solicitud de repuestos
     * 
     * @param string $supplierId ID del proveedor
     * @param string $requestId ID de la solicitud
     * @param string $partId ID del repuesto
     * @param float $price Precio ofrecido
     * @param int $quantity Cantidad ofrecida
     * @param int $deliveryTime Tiempo de entrega en horas
     * @return string|bool ID de la oferta o false si falla
     */
    public function createOffer($supplierId, $requestId, $partId, $price, $quantity, $deliveryTime) {
        if (!isset($this->suppliers[$supplierId]) || !isset($this->partRequests[$requestId])) {
            return false;
        }
        
        $supplier = $this->suppliers[$supplierId];
        $workshopId = $this->partRequests[$requestId]['workshopId'];
        
        $offerId = $supplier->createOffer($requestId, $workshopId, $partId, $price, $quantity, $deliveryTime);
        
        if ($offerId) {
            // Almacenar la oferta en el sistema central
            $this->offers[$offerId] = [
                'id' => $offerId,
                'supplierId' => $supplierId,
                'requestId' => $requestId,
                'workshopId' => $workshopId,
                'partId' => $partId,
                'price' => $price,
                'quantity' => $quantity,
                'deliveryTime' => $deliveryTime,
                'status' => 'pending',
                'createdAt' => date('Y-m-d H:i:s')
            ];
            
            // Agregar la oferta a la solicitud
            $this->partRequests[$requestId]['offers'][$offerId] = $this->offers[$offerId];
            
            // Notificar al taller sobre la nueva oferta
            $this->notifyWorkshop($workshopId, $offerId);
            
            return $offerId;
        }
        
        return false;
    }

    /**
     * Notifica a un taller sobre una nueva oferta
     * 
     * @param string $workshopId
     * @param string $offerId
     * @return void
     */
    private function notifyWorkshop($workshopId, $offerId) {
        // En una implementación real, aquí se enviarían notificaciones al taller
        // Por ahora, simplemente marcamos la oferta como notificada
        $this->offers[$offerId]['workshopNotified'] = true;
    }

    /**
     * Acepta una oferta para una solicitud de repuestos
     * 
     * @param string $workshopId ID del taller
     * @param string $requestId ID de la solicitud
     * @param string $offerId ID de la oferta
     * @return bool
     */
    public function acceptOffer($workshopId, $requestId, $offerId) {
        if (!isset($this->workshops[$workshopId]) || 
            !isset($this->partRequests[$requestId]) || 
            !isset($this->offers[$offerId])) {
            return false;
        }
        
        // Verificar que la oferta corresponde a la solicitud y al taller
        if ($this->offers[$offerId]['requestId'] !== $requestId || 
            $this->offers[$offerId]['workshopId'] !== $workshopId) {
            return false;
        }
        
        // Actualizar el estado de la oferta
        $this->offers[$offerId]['status'] = 'accepted';
        $this->partRequests[$requestId]['status'] = 'in_progress';
        $this->partRequests[$requestId]['acceptedOfferId'] = $offerId;
        
        // Actualizar el estado en el taller
        $workshop = $this->workshops[$workshopId];
        $workshop->acceptOffer($requestId, $offerId);
        
        // Actualizar el estado en el proveedor
        $supplierId = $this->offers[$offerId]['supplierId'];
        $supplier = $this->suppliers[$supplierId];
        $supplier->updateOfferStatus($offerId, 'accepted');
        
        // Crear una transacción
        $transactionId = $this->createTransaction($requestId, $offerId);
        
        return true;
    }

    /**
     * Crea una transacción entre un taller y un proveedor
     * 
     * @param string $requestId
     * @param string $offerId
     * @return string ID de la transacción
     */
    private function createTransaction($requestId, $offerId) {
        $transactionId = uniqid('transaction_');
        
        $this->transactions[$transactionId] = [
            'id' => $transactionId,
            'requestId' => $requestId,
            'offerId' => $offerId,
            'workshopId' => $this->offers[$offerId]['workshopId'],
            'supplierId' => $this->offers[$offerId]['supplierId'],
            'amount' => $this->offers[$offerId]['price'] * $this->offers[$offerId]['quantity'],
            'status' => 'pending',
            'createdAt' => date('Y-m-d H:i:s')
        ];
        
        return $transactionId;
    }

    /**
     * Completa una transacción
     * 
     * @param string $transactionId
     * @return bool
     */
    public function completeTransaction($transactionId) {
        if (!isset($this->transactions[$transactionId])) {
            return false;
        }
        
        $this->transactions[$transactionId]['status'] = 'completed';
        $this->transactions[$transactionId]['completedAt'] = date('Y-m-d H:i:s');
        
        $requestId = $this->transactions[$transactionId]['requestId'];
        $this->partRequests[$requestId]['status'] = 'completed';
        
        return true;
    }

    /**
     * Busca proveedores por ubicación
     * 
     * @param string $location
     * @return array
     */
    public function findSuppliersByLocation($location) {
        return array_filter($this->suppliers, function($supplier) use ($location) {
            return stripos($supplier->getLocation(), $location) !== false;
        });
    }

    /**
     * Busca talleres por ubicación
     * 
     * @param string $location
     * @return array
     */
    public function findWorkshopsByLocation($location) {
        return array_filter($this->workshops, function($workshop) use ($location) {
            return stripos($workshop->getLocation(), $location) !== false;
        });
    }

    /**
     * Busca repuestos disponibles entre todos los proveedores
     * 
     * @param string $query Término de búsqueda
     * @return array
     */
    public function searchParts($query) {
        $results = [];
        
        foreach ($this->suppliers as $supplierId => $supplier) {
            $parts = $supplier->searchParts($query);
            
            foreach ($parts as $part) {
                $results[] = [
                    'supplierId' => $supplierId,
                    'supplierName' => $supplier->getName(),
                    'part' => $part
                ];
            }
        }
        
        return $results;
    }
}
