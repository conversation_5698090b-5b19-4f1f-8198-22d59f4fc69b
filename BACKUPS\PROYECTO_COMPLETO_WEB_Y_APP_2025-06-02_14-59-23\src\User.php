<?php

namespace Src;

/**
 * Clase User compatible con el sistema antiguo
 * Esta clase ha sido adaptada para funcionar con el sistema basado en MySQL
 */
class User {
    private $id;
    private $username;
    private $email;
    private $userType;
    private $createdAt;
    private $lastLogin;
    private $status;

    public function __construct($id = 0, $username = '', $email = '', $userType = '') {
        $this->id = $id;
        $this->username = $username;
        $this->email = $email;
        $this->userType = $userType;
        $this->status = 'active';
    }

    public function setCreatedAt($createdAt = null) {
        $this->createdAt = $createdAt ?? date('Y-m-d H:i:s');
    }

    public function setLastLogin($lastLogin = null) {
        $this->lastLogin = $lastLogin ?? date('Y-m-d H:i:s');
    }

    public function setStatus($status) {
        $this->status = $status;
    }

    public function getId() {
        return $this->id;
    }

    public function getName() {
        return $this->username;
    }

    public function getEmail() {
        return $this->email;
    }

    public function getUserType() {
        return $this->userType;
    }

    public function getCreatedAt() {
        return $this->createdAt;
    }

    public function getLastLogin() {
        return $this->lastLogin;
    }

    public function getStatus() {
        return $this->status;
    }
}
