<?php
// Mostrar todos los errores
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Iniciar sesión
session_start();

// Configuración de la base de datos
$host = 'localhost';
$dbname = 'autoconnect_db';
$username = 'root';
$password = '';

// Variables para mensajes
$error = '';
$success = '';

// Intentar crear la base de datos directamente
try {
    // Conectar sin seleccionar base de datos
    $conn = new PDO("mysql:host=$host", $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Crear la base de datos si no existe
    $conn->exec("CREATE DATABASE IF NOT EXISTS $dbname");
    $success = "Base de datos creada o verificada correctamente.";
} catch (PDOException $e) {
    $error = "Error al crear la base de datos: " . $e->getMessage();
}

// Procesar el formulario cuando se envía
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verificar si es login o registro
    if (isset($_POST['action'])) {
        
        // Proceso de login
        if ($_POST['action'] === 'login') {
            $username = $_POST['username'] ?? '';
            $password = $_POST['password'] ?? '';
            
            // Validar campos
            if (empty($username) || empty($password)) {
                $error = 'Por favor, complete todos los campos.';
            } else {
                // Autenticación directa para admin
                if ($username === 'admin' && $password === 'admin123') {
                    // Guardar información del usuario en la sesión
                    $_SESSION['user_id'] = 1;
                    $_SESSION['username'] = 'admin';
                    $_SESSION['user_email'] = '<EMAIL>';
                    $_SESSION['role'] = 'admin';
                    
                    // Redirigir al panel de administración
                    header('Location: admin.php');
                    exit;
                } else {
                    $error = 'Credenciales incorrectas. Por favor, intente nuevamente.';
                }
            }
        }
        
        // El registro solo está disponible para administradores
        if ($_POST['action'] === 'register') {
            $error = 'El registro de nuevos usuarios solo puede ser realizado por un administrador. Por favor, contacte al administrador del sistema.';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Iniciar Sesión - Repumóvil</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            padding-top: 50px;
        }
        .auth-container {
            max-width: 900px;
            margin: 0 auto;
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .row {
            margin: 0;
        }
        .auth-sidebar {
            background-color: #343a40;
            color: white;
            padding: 40px 20px;
            height: 100%;
        }
        .auth-content {
            padding: 40px;
        }
        .logo h1 {
            font-size: 2rem;
            margin-bottom: 5px;
        }
        .logo p {
            font-size: 1rem;
            opacity: 0.8;
        }
        .auth-sidebar ul {
            padding-left: 20px;
            margin-top: 20px;
        }
        .auth-sidebar li {
            margin-bottom: 10px;
        }
        .form-title {
            font-size: 1.8rem;
            margin-bottom: 30px;
            color: #343a40;
        }
        .form-group {
            margin-bottom: 25px;
        }
        .form-control {
            height: 45px;
            border-radius: 4px;
        }
        .btn-primary {
            background-color: #343a40;
            border-color: #343a40;
            padding: 10px 20px;
            font-weight: 500;
        }
        .btn-primary:hover {
            background-color: #23272b;
            border-color: #23272b;
        }
        .alert {
            border-radius: 4px;
        }
        .auth-footer {
            margin-top: 30px;
            text-align: center;
            color: #6c757d;
        }
        .auth-footer a {
            color: #343a40;
            text-decoration: none;
        }
        .auth-footer a:hover {
            text-decoration: underline;
        }
        .error-message {
            background-color: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .success-message {
            background-color: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .auth-tabs {
            display: flex;
        }
        .auth-tab {
            flex: 1;
            text-align: center;
            padding: 15px;
            background-color: #f4f4f4;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .auth-tab.active {
            background-color: #343a40;
            color: white;
        }
        .auth-form {
            display: none;
        }
        .auth-form.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="auth-container">
            <div class="row">
                <div class="col-md-4 d-none d-md-block">
                    <div class="auth-sidebar">
                        <div class="logo">
                            <img src="repumovil.png" alt="Repumóvil Logo" style="height: 80px; width: auto; margin-bottom: 15px;">
                        </div>
                        <p><strong>Bienvenido a Repumóvil</strong></p>
                        <p>La plataforma que conecta talleres mecánicos con proveedores de repuestos.</p>
                        <p><strong>Características:</strong></p>
                        <ul>
                            <li>Gestión de inventario</li>
                            <li>Solicitud de repuestos</li>
                            <li>Seguimiento de órdenes</li>
                            <li>Comunicación directa</li>
                        </ul>
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="auth-content">
                        <div class="logo d-block d-md-none">
                            <h1 style="color: #343a40;">Repumóvil</h1>
                            <p class="text-muted">Sistema de Gestión de Talleres</p>
                        </div>
                        
                        <div class="auth-tabs">
                            <div class="auth-tab active" id="login-tab">Iniciar Sesión</div>
                            <div class="auth-tab" id="register-tab">Registrarse</div>
                        </div>
                        
                        <div class="mt-4">
                            <?php if (!empty($error)): ?>
                                <div class="error-message"><?php echo $error; ?></div>
                            <?php endif; ?>
                            
                            <?php if (!empty($success)): ?>
                                <div class="success-message"><?php echo $success; ?></div>
                            <?php endif; ?>
                            
                            <!-- Formulario de Login -->
                            <form class="auth-form active" id="login-form" method="post" action="login_reemplazo.php">
                                <input type="hidden" name="action" value="login">
                                
                                <div class="form-group">
                                    <label for="login-username">Nombre de Usuario</label>
                                    <input type="text" class="form-control" id="login-username" name="username" required>
                                </div>
                                
                                <div class="form-group">
                                    <label for="login-password">Contraseña</label>
                                    <input type="password" class="form-control" id="login-password" name="password" required>
                                </div>
                                
                                <button type="submit" class="btn btn-primary btn-block">Iniciar Sesión</button>
                                
                                <div class="mt-3 text-center">
                                    <strong>Usuario de prueba:</strong> admin / admin123
                                </div>
                            </form>
                            
                            <!-- Formulario de Registro -->
                            <div class="auth-form" id="register-form">
                                <div class="alert alert-info">
                                    <h4>Registro de Usuarios</h4>
                                    <p>El registro de nuevos usuarios solo puede ser realizado por un administrador del sistema.</p>
                                    <p>Por favor, contacte al administrador para solicitar una cuenta nueva.</p>
                                    <p>Si ya tiene una cuenta, puede iniciar sesión con sus credenciales.</p>
                                    <p>Datos de contacto del administrador:</p>
                                    <ul>
                                        <li>Email: <EMAIL></li>
                                        <li>Teléfono: (*************</li>
                                    </ul>
                                </div>
                            </div>
                            
                            <div class="auth-footer">
                                <p>Al iniciar sesión, acepta nuestros <a href="#">Términos y Condiciones</a> y <a href="#">Política de Privacidad</a>.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script>
        // Cambiar entre formularios de login y registro
        document.getElementById('login-tab').addEventListener('click', function() {
            document.getElementById('login-tab').classList.add('active');
            document.getElementById('register-tab').classList.remove('active');
            document.getElementById('login-form').classList.add('active');
            document.getElementById('register-form').classList.remove('active');
        });
        
        document.getElementById('register-tab').addEventListener('click', function() {
            document.getElementById('register-tab').classList.add('active');
            document.getElementById('login-tab').classList.remove('active');
            document.getElementById('register-form').classList.add('active');
            document.getElementById('login-form').classList.remove('active');
        });
    </script>
</body>
</html>



