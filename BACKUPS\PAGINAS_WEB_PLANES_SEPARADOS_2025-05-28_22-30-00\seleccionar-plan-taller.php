<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Elige tu Plan RepuMovil</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container-plans {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header-section {
            text-align: center;
            margin-bottom: 50px;
            color: white;
        }
        
        .main-title {
            font-size: 48px;
            font-weight: 900;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .logo-repu {
            color: #FF6B35;
        }
        
        .logo-movil {
            color: #FFE4B5;
        }
        
        .subtitle {
            font-size: 24px;
            margin-bottom: 10px;
            font-weight: 600;
        }
        
        .description {
            font-size: 18px;
            opacity: 0.9;
        }
        
        .plans-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-top: 40px;
        }
        
        .plan-card {
            background: white;
            border-radius: 25px;
            padding: 40px 30px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .plan-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 30px 60px rgba(0,0,0,0.2);
        }
        
        .plan-card.premium {
            border: 4px solid #FFD700;
            background: linear-gradient(135deg, #ffffff 0%, #fff9e6 100%);
        }
        
        .plan-card.premium::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 215, 0, 0.1), transparent);
            animation: shine 3s infinite;
        }
        
        @keyframes shine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }
        
        .plan-icon {
            font-size: 80px;
            margin-bottom: 20px;
            display: block;
        }
        
        .plan-title {
            font-size: 32px;
            font-weight: 900;
            margin-bottom: 15px;
            position: relative;
            z-index: 1;
        }
        
        .plan-title.common {
            color: #FF6B35;
        }
        
        .plan-title.premium {
            background: linear-gradient(135deg, #FF6B35, #FFA500, #FFD700);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .plan-price {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
        }
        
        .plan-price.free {
            color: #4CAF50;
        }
        
        .plan-price.premium {
            color: #FF6B35;
        }
        
        .plan-description {
            font-size: 16px;
            color: #666;
            margin-bottom: 30px;
            line-height: 1.6;
            position: relative;
            z-index: 1;
        }
        
        .features-list {
            text-align: left;
            margin-bottom: 30px;
            position: relative;
            z-index: 1;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            font-size: 14px;
        }
        
        .feature-icon {
            color: #4CAF50;
            margin-right: 10px;
            font-size: 16px;
        }
        
        .feature-icon.premium {
            color: #FFD700;
        }
        
        .btn-plan {
            background: linear-gradient(135deg, #FF6B35 0%, #FFA500 100%);
            border: none;
            border-radius: 15px;
            padding: 15px 30px;
            color: white;
            font-weight: 700;
            font-size: 18px;
            width: 100%;
            transition: all 0.3s ease;
            position: relative;
            z-index: 1;
        }
        
        .btn-plan:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(255, 107, 53, 0.4);
            color: white;
        }
        
        .btn-plan.premium {
            background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
            color: #333;
            font-size: 20px;
            padding: 18px 35px;
        }
        
        .btn-plan.premium:hover {
            color: #333;
            box-shadow: 0 10px 25px rgba(255, 215, 0, 0.4);
        }
        
        .crown-icon {
            position: absolute;
            top: -15px;
            right: 20px;
            font-size: 40px;
            color: #FFD700;
            transform: rotate(15deg);
        }
        
        .back-btn {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid white;
            color: white;
            padding: 10px 20px;
            border-radius: 10px;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .back-btn:hover {
            background: white;
            color: #667eea;
        }
        
        @media (max-width: 768px) {
            .plans-container {
                grid-template-columns: 1fr;
                gap: 30px;
            }
            
            .main-title {
                font-size: 36px;
            }
            
            .subtitle {
                font-size: 20px;
            }
            
            .plan-card {
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <a href="registro-dinamico.php" class="back-btn">
        <i class="fas fa-arrow-left me-2"></i>Volver
    </a>

    <div class="container-plans">
        <!-- Header -->
        <div class="header-section">
            <h1 class="main-title">
                <i class="fas fa-wrench me-3"></i>
                <span class="logo-repu">Repu</span><span class="logo-movil">Movil</span>
            </h1>
            <h2 class="subtitle">Elige tu Plan para Talleres</h2>
            <p class="description">Selecciona el plan que mejor se adapte a las necesidades de tu taller</p>
        </div>

        <!-- Plans -->
        <div class="plans-container">
            <!-- Plan Común -->
            <div class="plan-card">
                <i class="fas fa-wrench plan-icon" style="color: #FF6B35;"></i>
                <h3 class="plan-title common">RepuMovil</h3>
                <div class="plan-price free">GRATIS</div>
                <p class="plan-description">
                    Repuestos que llegan a tu taller, cuando los necesitas.
                </p>
                
                <div class="features-list">
                    <div class="feature-item">
                        <i class="fas fa-check feature-icon"></i>
                        <span>📋 Pedido de repuestos</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check feature-icon"></i>
                        <span>🛒 Sistema changuito</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check feature-icon"></i>
                        <span>⭐ Calificaciones de clientes</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check feature-icon"></i>
                        <span>📱 Notificaciones básicas</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check feature-icon"></i>
                        <span>🔍 Búsqueda de repuestos</span>
                    </div>
                </div>
                
                <button class="btn btn-plan" onclick="registrarComun()">
                    <i class="fas fa-rocket me-2"></i>Empezar Gratis
                </button>
            </div>

            <!-- Plan Plus -->
            <div class="plan-card premium">
                <i class="fas fa-crown crown-icon"></i>
                <i class="fas fa-tools plan-icon" style="color: #FFD700;"></i>
                <h3 class="plan-title premium">RepuMovil Plus</h3>
                <div class="plan-price premium">PREMIUM</div>
                <p class="plan-description">
                    La solución completa para tu taller, desde repuestos hasta administración.
                </p>
                
                <div class="features-list">
                    <div class="feature-item">
                        <i class="fas fa-star feature-icon premium"></i>
                        <span><strong>Todo lo de RepuMovil +</strong></span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-star feature-icon premium"></i>
                        <span>👥 Gestión completa de clientes</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-star feature-icon premium"></i>
                        <span>📅 Calendario de turnos</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-star feature-icon premium"></i>
                        <span>📋 Órdenes de trabajo</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-star feature-icon premium"></i>
                        <span>💰 Reportes y estadísticas</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-star feature-icon premium"></i>
                        <span>🔧 Inventario de herramientas</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-star feature-icon premium"></i>
                        <span>📊 Dashboard avanzado</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-star feature-icon premium"></i>
                        <span>📱 WhatsApp integrado</span>
                    </div>
                </div>
                
                <button class="btn btn-plan premium" onclick="registrarPlus()">
                    <i class="fas fa-crown me-2"></i>Elegir Premium
                </button>
            </div>
        </div>
    </div>

    <script>
        function registrarComun() {
            window.location.href = 'registro-taller-comun.php';
        }

        function registrarPlus() {
            window.location.href = 'registro-taller-plus.php';
        }

        // Animaciones al cargar
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.plan-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(50px)';
                
                setTimeout(() => {
                    card.style.transition = 'all 0.8s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 300);
            });
        });
    </script>
</body>
</html>
