<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RepuMovil - Estadísticas de Reasignaciones 📊</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #FF6B35;
            --secondary-color: #FFA500;
            --dark-color: #2c3e50;
            --light-color: #f8f9fa;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
            color: white;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .logo {
            font-size: 3rem;
            margin-bottom: 10px;
        }

        .title {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            color: var(--dark-color);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .stat-title {
            font-size: 1.1rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        .stat-icon {
            font-size: 2rem;
            color: var(--primary-color);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #666;
        }

        .stat-trend {
            font-size: 0.8rem;
            margin-top: 10px;
        }

        .trend-up {
            color: var(--success-color);
        }

        .trend-down {
            color: var(--danger-color);
        }

        .chart-container {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            color: var(--dark-color);
        }

        .chart-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 20px;
            text-align: center;
        }

        .motivos-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .motivo-item {
            background: var(--light-color);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            border-left: 4px solid var(--primary-color);
        }

        .motivo-percentage {
            font-size: 1.8rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        .motivo-label {
            font-size: 0.9rem;
            color: #666;
            margin-top: 5px;
        }

        .motivo-count {
            font-size: 0.8rem;
            color: #999;
        }

        .timeline {
            background: white;
            border-radius: 15px;
            padding: 25px;
            color: var(--dark-color);
        }

        .timeline-item {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #eee;
        }

        .timeline-item:last-child {
            border-bottom: none;
        }

        .timeline-time {
            font-size: 0.8rem;
            color: #666;
            min-width: 80px;
        }

        .timeline-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 15px;
            font-size: 1.2rem;
            color: white;
        }

        .timeline-icon.rejected {
            background: var(--danger-color);
        }

        .timeline-icon.reassigned {
            background: var(--success-color);
        }

        .timeline-content {
            flex: 1;
        }

        .timeline-title {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .timeline-description {
            font-size: 0.9rem;
            color: #666;
        }

        .performance-indicators {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .indicator {
            background: white;
            border-radius: 15px;
            padding: 20px;
            color: var(--dark-color);
            text-align: center;
        }

        .indicator-value {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .indicator-excellent {
            color: var(--success-color);
        }

        .indicator-good {
            color: var(--info-color);
        }

        .indicator-warning {
            color: var(--warning-color);
        }

        .indicator-poor {
            color: var(--danger-color);
        }

        .refresh-btn {
            background: var(--success-color);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            margin: 20px auto;
            display: block;
            transition: all 0.3s ease;
        }

        .refresh-btn:hover {
            background: #218838;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">📊🔄</div>
            <h1 class="title">Estadísticas de Reasignaciones</h1>
            <p class="subtitle">RepuMovil - Análisis de Rendimiento del Sistema</p>
        </div>

        <!-- Indicadores de Rendimiento -->
        <div class="performance-indicators">
            <div class="indicator">
                <div class="indicator-value indicator-excellent" id="successRate">85.7%</div>
                <div>Tasa de Éxito en Reasignaciones</div>
            </div>
            <div class="indicator">
                <div class="indicator-value indicator-good" id="avgTime">4.2s</div>
                <div>Tiempo Promedio de Reasignación</div>
            </div>
            <div class="indicator">
                <div class="indicator-value indicator-warning" id="rejectionRate">12.3%</div>
                <div>Tasa de Rechazo General</div>
            </div>
            <div class="indicator">
                <div class="indicator-value indicator-excellent" id="deliverySatisfaction">94.2%</div>
                <div>Satisfacción de Deliveries</div>
            </div>
        </div>

        <!-- Estadísticas Principales -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-title">Total Rechazos Hoy</div>
                    <div class="stat-icon">❌</div>
                </div>
                <div class="stat-number" id="totalRejections">8</div>
                <div class="stat-label">Pedidos rechazados por deliveries</div>
                <div class="stat-trend trend-down">
                    <i class="fas fa-arrow-down"></i> -15% vs ayer
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-title">Reasignaciones Exitosas</div>
                    <div class="stat-icon">✅</div>
                </div>
                <div class="stat-number" id="successfulReassignments">6</div>
                <div class="stat-label">De 7 intentos de reasignación</div>
                <div class="stat-trend trend-up">
                    <i class="fas fa-arrow-up"></i> +8% vs ayer
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-title">Tiempo Promedio</div>
                    <div class="stat-icon">⏱️</div>
                </div>
                <div class="stat-number" id="avgReassignmentTime">4.2s</div>
                <div class="stat-label">Para encontrar nuevo delivery</div>
                <div class="stat-trend trend-up">
                    <i class="fas fa-arrow-up"></i> Mejorando
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-title">Deliveries Activos</div>
                    <div class="stat-icon">🏍️</div>
                </div>
                <div class="stat-number" id="activeDeliveries">12</div>
                <div class="stat-label">Disponibles para reasignación</div>
                <div class="stat-trend trend-up">
                    <i class="fas fa-arrow-up"></i> +3 vs ayer
                </div>
            </div>
        </div>

        <!-- Motivos de Rechazo -->
        <div class="chart-container">
            <div class="chart-title">
                <i class="fas fa-chart-pie"></i>
                Motivos de Rechazo - Análisis Detallado
            </div>
            <div class="motivos-grid">
                <div class="motivo-item">
                    <div class="motivo-percentage">37.5%</div>
                    <div class="motivo-label">Muy Lejos</div>
                    <div class="motivo-count">3 de 8 rechazos</div>
                </div>
                <div class="motivo-item">
                    <div class="motivo-percentage">25%</div>
                    <div class="motivo-label">No Disponible</div>
                    <div class="motivo-count">2 de 8 rechazos</div>
                </div>
                <div class="motivo-item">
                    <div class="motivo-percentage">12.5%</div>
                    <div class="motivo-label">Problema Vehículo</div>
                    <div class="motivo-count">1 de 8 rechazos</div>
                </div>
                <div class="motivo-item">
                    <div class="motivo-percentage">25%</div>
                    <div class="motivo-label">Otros Motivos</div>
                    <div class="motivo-count">2 de 8 rechazos</div>
                </div>
            </div>
        </div>

        <!-- Timeline de Reasignaciones -->
        <div class="timeline">
            <div class="chart-title">
                <i class="fas fa-clock"></i>
                Timeline de Reasignaciones Recientes
            </div>
            
            <div class="timeline-item">
                <div class="timeline-time">14:32</div>
                <div class="timeline-icon rejected">❌</div>
                <div class="timeline-content">
                    <div class="timeline-title">Pedido #1015 rechazado por Juan Pérez</div>
                    <div class="timeline-description">Motivo: Muy lejos (6.2 km) - Reasignado a María González en 3.1s</div>
                </div>
            </div>

            <div class="timeline-item">
                <div class="timeline-time">14:18</div>
                <div class="timeline-icon reassigned">✅</div>
                <div class="timeline-content">
                    <div class="timeline-title">Reasignación exitosa - Pedido #1014</div>
                    <div class="timeline-description">Carlos Rodríguez no disponible → Ana López aceptó en 2.8s</div>
                </div>
            </div>

            <div class="timeline-item">
                <div class="timeline-time">13:45</div>
                <div class="timeline-icon rejected">❌</div>
                <div class="timeline-content">
                    <div class="timeline-title">Pedido #1013 rechazado por Luis Martín</div>
                    <div class="timeline-description">Motivo: Problema con vehículo - Reasignado a Diego Fernández en 5.2s</div>
                </div>
            </div>

            <div class="timeline-item">
                <div class="timeline-time">13:22</div>
                <div class="timeline-icon reassigned">✅</div>
                <div class="timeline-content">
                    <div class="timeline-title">Reasignación automática por timeout</div>
                    <div class="timeline-description">Pedido #1012 - Sin respuesta en 60s → Reasignado automáticamente</div>
                </div>
            </div>
        </div>

        <button class="refresh-btn" onclick="refreshStats()">
            <i class="fas fa-sync-alt"></i>
            Actualizar Estadísticas
        </button>
    </div>

    <script>
        // PASO 6: Funciones para actualizar estadísticas en tiempo real
        function refreshStats() {
            // Simular actualización de datos
            const stats = {
                totalRejections: Math.floor(Math.random() * 5) + 6,
                successfulReassignments: Math.floor(Math.random() * 3) + 5,
                avgTime: (Math.random() * 2 + 3).toFixed(1),
                activeDeliveries: Math.floor(Math.random() * 5) + 10,
                successRate: (Math.random() * 10 + 80).toFixed(1),
                rejectionRate: (Math.random() * 5 + 10).toFixed(1)
            };

            // Actualizar elementos
            document.getElementById('totalRejections').textContent = stats.totalRejections;
            document.getElementById('successfulReassignments').textContent = stats.successfulReassignments;
            document.getElementById('avgReassignmentTime').textContent = stats.avgTime + 's';
            document.getElementById('activeDeliveries').textContent = stats.activeDeliveries;
            document.getElementById('successRate').textContent = stats.successRate + '%';
            document.getElementById('rejectionRate').textContent = stats.rejectionRate + '%';
            document.getElementById('avgTime').textContent = stats.avgTime + 's';

            // Mostrar confirmación
            const btn = document.querySelector('.refresh-btn');
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-check"></i> Actualizado';
            btn.style.background = 'var(--success-color)';

            setTimeout(() => {
                btn.innerHTML = originalText;
                btn.style.background = 'var(--success-color)';
            }, 2000);

            console.log('📊 Estadísticas actualizadas:', stats);
        }

        // Actualizar automáticamente cada 30 segundos
        setInterval(refreshStats, 30000);

        // Simular datos en tiempo real
        setInterval(() => {
            const deliveries = document.getElementById('activeDeliveries');
            const current = parseInt(deliveries.textContent);
            const change = Math.random() > 0.5 ? 1 : -1;
            const newValue = Math.max(8, Math.min(15, current + change));
            deliveries.textContent = newValue;
        }, 10000);
    </script>
</body>
</html>
