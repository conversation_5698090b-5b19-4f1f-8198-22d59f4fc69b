<?php
// Mostrar todos los errores
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Información básica de PHP
echo "<h1>Prueba de PHP</h1>";
echo "<p>PHP está funcionando correctamente.</p>";
echo "<p>Versión de PHP: " . phpversion() . "</p>";

// Verificar la estructura de directorios
echo "<h2>Estructura de directorios:</h2>";
$directories = [
    '.' => 'Directorio actual',
    './public' => 'Directorio public',
    './src' => 'Directorio src'
];

echo "<ul>";
foreach ($directories as $dir => $description) {
    if (file_exists($dir)) {
        echo "<li>$description ($dir): ✅ Existe";
        if (is_writable($dir)) {
            echo " (Escritura: ✅)";
        } else {
            echo " (Escritura: ❌)";
        }
        if (is_readable($dir)) {
            echo " (Lectura: ✅)";
        } else {
            echo " (Lectura: ❌)";
        }
        echo "</li>";
        
        // Listar archivos en el directorio
        echo "<ul>";
        $files = scandir($dir);
        foreach ($files as $file) {
            if ($file != "." && $file != "..") {
                echo "<li>$file";
                if (is_dir("$dir/$file")) {
                    echo " (Directorio)";
                } else {
                    echo " (Archivo)";
                }
                echo "</li>";
            }
        }
        echo "</ul>";
    } else {
        echo "<li>$description ($dir): ❌ No existe</li>";
    }
}
echo "</ul>";

// Mostrar información del servidor
echo "<h2>Información del servidor:</h2>";
echo "<p>Servidor: " . $_SERVER['SERVER_SOFTWARE'] . "</p>";
echo "<p>Documento raíz: " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p>Ruta del script: " . $_SERVER['SCRIPT_FILENAME'] . "</p>";
echo "<p>URI del script: " . $_SERVER['SCRIPT_NAME'] . "</p>";
echo "<p>Método de solicitud: " . $_SERVER['REQUEST_METHOD'] . "</p>";
echo "<p>Dirección IP del cliente: " . $_SERVER['REMOTE_ADDR'] . "</p>";

// Mostrar enlaces útiles
echo "<h2>Enlaces útiles:</h2>";
echo "<ul>";
echo "<li><a href='login.php'>Página de login</a></li>";
echo "<li><a href='dashboard.php'>Dashboard</a></li>";
echo "<li><a href='logout.php'>Cerrar sesión</a></li>";
echo "</ul>";
?>
