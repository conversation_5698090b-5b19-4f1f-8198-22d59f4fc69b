<?php
session_start();
require_once 'db_config.php';

// Verificar si el usuario está logueado
if (!isset($_SESSION['user_id'])) {
    header('Location: login-dinamico.php');
    exit();
}

$user_id = $_SESSION['user_id'];
$user_name = $_SESSION['user_name'] ?? 'Usuario';
$user_type = $_SESSION['user_type'] ?? 'usuario_regular';

try {
    $pdo = connectDB();
    
    // Obtener pedidos del usuario
    $stmt = $pdo->prepare("
        SELECT p.*, pr.nombre as proveedor_nombre, pr.telefono as proveedor_telefono
        FROM pedidos p
        LEFT JOIN users pr ON p.proveedor_id = pr.id
        WHERE p.usuario_id = ?
        ORDER BY p.fecha_pedido DESC
    ");
    $stmt->execute([$user_id]);
    $pedidos = $stmt->fetchAll();
    
} catch (PDOException $e) {
    $error_message = "Error al cargar pedidos: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mis Pedidos - RepuMovil</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
            min-height: 100vh;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .logo h1 {
            color: #FF6B35;
            font-size: 1.8rem;
            font-weight: 900;
        }

        .logo .repu { color: #FF6B35; }
        .logo .movil { color: #F7931E; }

        .nav-links {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .nav-link {
            color: #333;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .nav-link:hover {
            background: #FF6B35;
            color: white;
            transform: translateY(-2px);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .page-header {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .page-title {
            font-size: 2.5rem;
            color: #333;
            margin-bottom: 0.5rem;
            font-weight: 900;
        }

        .page-subtitle {
            color: #666;
            font-size: 1.1rem;
        }

        .pedidos-grid {
            display: grid;
            gap: 1.5rem;
        }

        .pedido-card {
            background: white;
            border-radius: 20px;
            padding: 1.5rem;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border-left: 6px solid #FF6B35;
            position: relative;
            overflow: hidden;
        }

        .pedido-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: linear-gradient(45deg, #FF6B35, #F7931E);
            border-radius: 0 0 0 100px;
            opacity: 0.1;
        }

        .pedido-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        .pedido-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .pedido-numero {
            font-size: 1.3rem;
            font-weight: bold;
            color: #333;
        }

        .pedido-fecha {
            color: #666;
            font-size: 0.9rem;
        }

        .estado-badge {
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-size: 0.8rem;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .estado-pendiente {
            background: linear-gradient(45deg, #FFA726, #FFB74D);
            color: white;
        }

        .estado-confirmado {
            background: linear-gradient(45deg, #42A5F5, #64B5F6);
            color: white;
        }

        .estado-en-camino {
            background: linear-gradient(45deg, #AB47BC, #BA68C8);
            color: white;
        }

        .estado-entregado {
            background: linear-gradient(45deg, #66BB6A, #81C784);
            color: white;
        }

        .estado-cancelado {
            background: linear-gradient(45deg, #EF5350, #E57373);
            color: white;
        }

        .pedido-info {
            margin: 1rem 0;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .info-row:last-child {
            border-bottom: none;
        }

        .info-label {
            font-weight: 600;
            color: #555;
        }

        .info-value {
            color: #333;
        }

        .pedido-total {
            font-size: 1.5rem;
            font-weight: bold;
            color: #FF6B35;
            text-align: center;
            margin: 1rem 0;
            padding: 1rem;
            background: linear-gradient(45deg, #FFF3E0, #FFE0B2);
            border-radius: 15px;
        }

        .pedido-actions {
            display: flex;
            gap: 1rem;
            margin-top: 1rem;
        }

        .btn {
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            flex: 1;
            justify-content: center;
        }

        .btn-primary {
            background: linear-gradient(45deg, #FF6B35, #F7931E);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(45deg, #78909C, #90A4AE);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .empty-state i {
            font-size: 5rem;
            color: #FF6B35;
            margin-bottom: 1rem;
            opacity: 0.7;
        }

        .empty-state h3 {
            color: #333;
            margin-bottom: 1rem;
            font-size: 1.5rem;
        }

        .empty-state p {
            color: #666;
            margin-bottom: 2rem;
        }

        .floating-btn {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            width: 60px;
            height: 60px;
            background: linear-gradient(45deg, #FF6B35, #F7931E);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            text-decoration: none;
            box-shadow: 0 8px 25px rgba(255, 107, 53, 0.4);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .floating-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 12px 35px rgba(255, 107, 53, 0.6);
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .page-header {
                padding: 1.5rem;
            }
            
            .page-title {
                font-size: 2rem;
            }
            
            .pedido-header {
                flex-direction: column;
                gap: 1rem;
            }
            
            .pedido-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="logo">
            <i class="fas fa-shopping-bag" style="font-size: 2rem; color: #FF6B35;"></i>
            <h1><span class="repu">Repu</span><span class="movil">Movil</span></h1>
        </div>
        <div class="nav-links">
            <a href="dashboard-usuario.php" class="nav-link">
                <i class="fas fa-home"></i> Dashboard
            </a>
            <a href="buscar-repuestos.php" class="nav-link">
                <i class="fas fa-search"></i> Buscar
            </a>
            <a href="logout.php" class="nav-link">
                <i class="fas fa-sign-out-alt"></i> Salir
            </a>
        </div>
    </div>

    <div class="container">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-clipboard-list"></i>
                Mis Pedidos
            </h1>
            <p class="page-subtitle">Seguí el estado de todos tus pedidos en tiempo real</p>
        </div>

        <!-- Pedidos Grid -->
        <?php if (empty($pedidos)): ?>
            <div class="empty-state">
                <i class="fas fa-shopping-cart"></i>
                <h3>No tenés pedidos aún</h3>
                <p>¡Empezá a buscar repuestos y hacé tu primer pedido!</p>
                <a href="buscar-repuestos.php" class="btn btn-primary">
                    <i class="fas fa-search"></i> Buscar Repuestos
                </a>
            </div>
        <?php else: ?>
            <div class="pedidos-grid">
                <?php foreach ($pedidos as $pedido): ?>
                    <div class="pedido-card">
                        <div class="pedido-header">
                            <div>
                                <div class="pedido-numero">Pedido #<?php echo str_pad($pedido['id'], 6, '0', STR_PAD_LEFT); ?></div>
                                <div class="pedido-fecha"><?php echo date('d/m/Y H:i', strtotime($pedido['fecha_pedido'])); ?></div>
                            </div>
                            <div class="estado-badge estado-<?php echo strtolower(str_replace(' ', '-', $pedido['estado'])); ?>">
                                <?php echo ucfirst($pedido['estado']); ?>
                            </div>
                        </div>
                        
                        <div class="pedido-info">
                            <div class="info-row">
                                <span class="info-label">
                                    <i class="fas fa-store"></i> Proveedor:
                                </span>
                                <span class="info-value"><?php echo htmlspecialchars($pedido['proveedor_nombre'] ?? 'No asignado'); ?></span>
                            </div>
                            
                            <div class="info-row">
                                <span class="info-label">
                                    <i class="fas fa-map-marker-alt"></i> Dirección:
                                </span>
                                <span class="info-value"><?php echo htmlspecialchars($pedido['direccion_entrega']); ?></span>
                            </div>
                            
                            <div class="info-row">
                                <span class="info-label">
                                    <i class="fas fa-credit-card"></i> Pago:
                                </span>
                                <span class="info-value"><?php echo ucfirst($pedido['metodo_pago']); ?></span>
                            </div>
                            
                            <?php if ($pedido['notas']): ?>
                            <div class="info-row">
                                <span class="info-label">
                                    <i class="fas fa-sticky-note"></i> Notas:
                                </span>
                                <span class="info-value"><?php echo htmlspecialchars($pedido['notas']); ?></span>
                            </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="pedido-total">
                            Total: $<?php echo number_format($pedido['total'], 0, ',', '.'); ?>
                        </div>
                        
                        <div class="pedido-actions">
                            <a href="detalle-pedido.php?id=<?php echo $pedido['id']; ?>" class="btn btn-primary">
                                <i class="fas fa-eye"></i> Ver Detalle
                            </a>
                            
                            <?php if ($pedido['estado'] === 'pendiente'): ?>
                                <a href="cancelar-pedido.php?id=<?php echo $pedido['id']; ?>" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Cancelar
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>

    <!-- Floating Action Button -->
    <a href="buscar-repuestos.php" class="floating-btn" title="Nuevo Pedido">
        <i class="fas fa-plus"></i>
    </a>
</body>
</html>
