// PASO 3: Modal de notificación de nuevo pedido
// Componente para mostrar pedidos y permitir aceptar/rechazar

import React, { useState, useEffect } from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Dimensions,
  Alert,
  Vibration,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Audio } from 'expo-av';

const { width, height } = Dimensions.get('window');

export interface PedidoData {
  id: number;
  cliente: string;
  direccion: string;
  total: number;
  distancia: number;
  tiempo_estimado: number;
  items: string[];
  telefono?: string;
  observaciones?: string;
  prioridad: 'normal' | 'alta' | 'urgente';
  ganancia_estimada: number;
  fecha_limite?: string;
}

interface Props {
  visible: boolean;
  pedido: PedidoData | null;
  onAccept: (pedidoId: number) => void;
  onReject: (pedidoId: number, motivo?: string) => void;
  onClose: () => void;
  timeoutSeconds?: number;
}

export default function PedidoNotificationModal({
  visible,
  pedido,
  onAccept,
  onReject,
  onClose,
  timeoutSeconds = 30
}: Props) {
  const [timeLeft, setTimeLeft] = useState(timeoutSeconds);
  const [sound, setSound] = useState<Audio.Sound | null>(null);
  const [scaleAnim] = useState(new Animated.Value(0));
  const [pulseAnim] = useState(new Animated.Value(1));

  // PASO 3: Efecto para countdown y timeout automático
  useEffect(() => {
    if (visible && pedido) {
      setTimeLeft(timeoutSeconds);
      startAnimations();
      playNotificationSound();
      
      const interval = setInterval(() => {
        setTimeLeft(prev => {
          if (prev <= 1) {
            clearInterval(interval);
            handleTimeout();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => {
        clearInterval(interval);
        stopSound();
      };
    }
  }, [visible, pedido]);

  // PASO 3: Animaciones de entrada
  const startAnimations = () => {
    // Animación de escala de entrada
    Animated.spring(scaleAnim, {
      toValue: 1,
      tension: 100,
      friction: 8,
      useNativeDriver: true,
    }).start();

    // Animación de pulso continuo
    const pulseAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.05,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    );
    pulseAnimation.start();
  };

  // PASO 3: Reproducir sonido de notificación
  const playNotificationSound = async () => {
    try {
      const { sound: newSound } = await Audio.Sound.createAsync(
        require('../assets/sounds/notification.mp3'), // Agregar archivo de sonido
        { shouldPlay: true, isLooping: false, volume: 1.0 }
      );
      setSound(newSound);
      
      // Vibración para llamar la atención
      Vibration.vibrate([0, 500, 200, 500]);
    } catch (error) {
      console.log('Error reproduciendo sonido:', error);
      // Vibración como fallback
      Vibration.vibrate([0, 1000, 500, 1000]);
    }
  };

  // PASO 3: Detener sonido
  const stopSound = async () => {
    if (sound) {
      await sound.unloadAsync();
      setSound(null);
    }
  };

  // PASO 3: Manejar timeout automático
  const handleTimeout = () => {
    Alert.alert(
      '⏰ Tiempo Agotado',
      'El pedido fue rechazado automáticamente por falta de respuesta.',
      [{ text: 'OK', onPress: () => {
        onReject(pedido?.id || 0, 'timeout');
        onClose();
      }}]
    );
  };

  // PASO 3: Aceptar pedido
  const handleAccept = () => {
    if (!pedido) return;
    
    Vibration.vibrate(200); // Vibración de confirmación
    onAccept(pedido.id);
    onClose();
  };

  // PASO 3: Rechazar pedido con motivo
  const handleReject = () => {
    if (!pedido) return;

    Alert.alert(
      '❌ Rechazar Pedido',
      '¿Por qué rechazas este pedido?',
      [
        { text: 'Cancelar', style: 'cancel' },
        { 
          text: 'Muy lejos', 
          onPress: () => {
            onReject(pedido.id, 'distancia');
            onClose();
          }
        },
        { 
          text: 'No disponible', 
          onPress: () => {
            onReject(pedido.id, 'no_disponible');
            onClose();
          }
        },
        { 
          text: 'Otro motivo', 
          onPress: () => {
            onReject(pedido.id, 'otro');
            onClose();
          }
        },
      ]
    );
  };

  // PASO 3: Obtener color según prioridad
  const getPriorityColor = () => {
    switch (pedido?.prioridad) {
      case 'urgente': return ['#dc3545', '#c82333'];
      case 'alta': return ['#fd7e14', '#e55a2b'];
      default: return ['#FF6B35', '#FFA500'];
    }
  };

  // PASO 3: Obtener icono según prioridad
  const getPriorityIcon = () => {
    switch (pedido?.prioridad) {
      case 'urgente': return '🚨';
      case 'alta': return '⚡';
      default: return '📦';
    }
  };

  if (!visible || !pedido) return null;

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <Animated.View 
          style={[
            styles.container,
            {
              transform: [
                { scale: scaleAnim },
                { scale: pulseAnim }
              ]
            }
          ]}
        >
          <LinearGradient
            colors={getPriorityColor()}
            style={styles.gradient}
          >
            {/* Header con countdown */}
            <View style={styles.header}>
              <Text style={styles.priorityIcon}>{getPriorityIcon()}</Text>
              <Text style={styles.title}>¡NUEVO PEDIDO!</Text>
              <View style={styles.countdown}>
                <Text style={styles.countdownText}>{timeLeft}s</Text>
              </View>
            </View>

            {/* Información del cliente */}
            <View style={styles.clientInfo}>
              <Text style={styles.clientName}>👤 {pedido.cliente}</Text>
              <Text style={styles.clientPhone}>📞 {pedido.telefono || 'No disponible'}</Text>
            </View>

            {/* Información del pedido */}
            <View style={styles.orderInfo}>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>📍 Dirección:</Text>
                <Text style={styles.infoValue}>{pedido.direccion}</Text>
              </View>
              
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>💰 Total:</Text>
                <Text style={styles.infoValue}>${pedido.total.toLocaleString()}</Text>
              </View>
              
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>🛣️ Distancia:</Text>
                <Text style={styles.infoValue}>{pedido.distancia} km</Text>
              </View>
              
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>⏱️ Tiempo estimado:</Text>
                <Text style={styles.infoValue}>{pedido.tiempo_estimado} min</Text>
              </View>
              
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>💵 Tu ganancia:</Text>
                <Text style={[styles.infoValue, styles.ganancia]}>
                  ${pedido.ganancia_estimada.toLocaleString()}
                </Text>
              </View>
            </View>

            {/* Items del pedido */}
            {pedido.items && pedido.items.length > 0 && (
              <View style={styles.itemsContainer}>
                <Text style={styles.itemsTitle}>📋 Items:</Text>
                {pedido.items.slice(0, 3).map((item, index) => (
                  <Text key={index} style={styles.itemText}>• {item}</Text>
                ))}
                {pedido.items.length > 3 && (
                  <Text style={styles.itemText}>... y {pedido.items.length - 3} más</Text>
                )}
              </View>
            )}

            {/* Observaciones */}
            {pedido.observaciones && (
              <View style={styles.observaciones}>
                <Text style={styles.observacionesTitle}>💬 Observaciones:</Text>
                <Text style={styles.observacionesText}>{pedido.observaciones}</Text>
              </View>
            )}

            {/* Botones de acción */}
            <View style={styles.actions}>
              <TouchableOpacity 
                style={[styles.button, styles.rejectButton]} 
                onPress={handleReject}
              >
                <Text style={styles.buttonText}>❌ RECHAZAR</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={[styles.button, styles.acceptButton]} 
                onPress={handleAccept}
              >
                <Text style={styles.buttonText}>✅ ACEPTAR</Text>
              </TouchableOpacity>
            </View>

            {/* Barra de progreso del tiempo */}
            <View style={styles.progressContainer}>
              <View 
                style={[
                  styles.progressBar, 
                  { width: `${(timeLeft / timeoutSeconds) * 100}%` }
                ]} 
              />
            </View>
          </LinearGradient>
        </Animated.View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  container: {
    width: width * 0.9,
    maxWidth: 400,
    borderRadius: 20,
    overflow: 'hidden',
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
  },
  gradient: {
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  priorityIcon: {
    fontSize: 24,
  },
  title: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
    flex: 1,
    textAlign: 'center',
  },
  countdown: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 15,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  countdownText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  clientInfo: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
  },
  clientName: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  clientPhone: {
    color: 'white',
    fontSize: 14,
    opacity: 0.9,
  },
  orderInfo: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  infoLabel: {
    color: 'white',
    fontSize: 14,
    opacity: 0.9,
  },
  infoValue: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  ganancia: {
    color: '#90EE90',
    fontSize: 16,
  },
  itemsContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
  },
  itemsTitle: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  itemText: {
    color: 'white',
    fontSize: 12,
    opacity: 0.9,
    marginBottom: 3,
  },
  observaciones: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 10,
    padding: 15,
    marginBottom: 20,
  },
  observacionesTitle: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  observacionesText: {
    color: 'white',
    fontSize: 12,
    opacity: 0.9,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15,
  },
  button: {
    flex: 1,
    paddingVertical: 15,
    borderRadius: 10,
    marginHorizontal: 5,
  },
  acceptButton: {
    backgroundColor: '#28a745',
  },
  rejectButton: {
    backgroundColor: '#dc3545',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  progressContainer: {
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 2,
  },
  progressBar: {
    height: '100%',
    backgroundColor: 'white',
    borderRadius: 2,
  },
});
