<?php
/**
 * Script para inicializar la base de datos
 *
 * Este script crea la base de datos, las tablas y los usuarios iniciales
 * para la plataforma AutoConnect.
 */

// Mostrar todos los errores
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Configuración de la base de datos
$host = 'localhost';
$username = 'root';
$password = '';

try {
    // Conectar a MySQL sin seleccionar una base de datos
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "Conectado a MySQL correctamente.<br>";

    // Crear la base de datos si no existe
    $sql = "CREATE DATABASE IF NOT EXISTS autoconnect_db";
    $pdo->exec($sql);
    echo "Base de datos 'autoconnect_db' creada o ya existente.<br>";

    // Seleccionar la base de datos
    $pdo->exec("USE autoconnect_db");

    // Crear tabla de roles
    $sql = "CREATE TABLE IF NOT EXISTS roles (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(50) NOT NULL,
        description VARCHAR(255)
    )";
    $pdo->exec($sql);
    echo "Tabla 'roles' creada correctamente.<br>";

    // Crear tabla de usuarios
    $sql = "CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) NOT NULL UNIQUE,
        email VARCHAR(100) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        role_id INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        last_login TIMESTAMP NULL,
        status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
        FOREIGN KEY (role_id) REFERENCES roles(id)
    )";
    $pdo->exec($sql);
    echo "Tabla 'users' creada correctamente.<br>";

    // Crear tabla de talleres
    $sql = "CREATE TABLE IF NOT EXISTS workshops (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        name VARCHAR(100) NOT NULL,
        location VARCHAR(255),
        phone VARCHAR(20),
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )";
    $pdo->exec($sql);
    echo "Tabla 'workshops' creada correctamente.<br>";

    // Crear tabla de proveedores
    $sql = "CREATE TABLE IF NOT EXISTS suppliers (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        name VARCHAR(100) NOT NULL,
        location VARCHAR(255),
        phone VARCHAR(20),
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )";
    $pdo->exec($sql);
    echo "Tabla 'suppliers' creada correctamente.<br>";

    // Verificar si ya existen roles
    $stmt = $pdo->query("SELECT COUNT(*) FROM roles");
    $roleCount = $stmt->fetchColumn();

    if ($roleCount == 0) {
        // Insertar roles
        $sql = "INSERT INTO roles (name, description) VALUES
            ('admin', 'Administrador del sistema'),
            ('workshop', 'Usuario de taller mecánico'),
            ('supplier', 'Proveedor de repuestos')";
        $pdo->exec($sql);
        echo "Roles insertados correctamente.<br>";
    } else {
        echo "Los roles ya existen en la base de datos.<br>";
    }

    // Verificar si ya existe el usuario administrador
    $stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE username = 'admin'");
    $adminCount = $stmt->fetchColumn();

    if ($adminCount == 0) {
        // Insertar usuario administrador
        // Contraseña: admin123
        $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $sql = "INSERT INTO users (username, email, password, role_id) VALUES
            ('admin', '<EMAIL>', :password, 1)";
        $stmt = $pdo->prepare($sql);
        $stmt->execute(['password' => $adminPassword]);
        echo "Usuario administrador creado correctamente.<br>";
        echo "Usuario: admin<br>";
        echo "Contraseña: admin123<br>";
    } else {
        echo "El usuario administrador ya existe en la base de datos.<br>";
    }

    // Verificar si ya existe el usuario de taller de ejemplo
    $stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE username = 'taller1'");
    $workshopCount = $stmt->fetchColumn();

    if ($workshopCount == 0) {
        // Insertar usuario de taller de ejemplo
        // Contraseña: taller123
        $workshopPassword = password_hash('taller123', PASSWORD_DEFAULT);

        // Iniciar transacción
        $pdo->beginTransaction();

        try {
            // Insertar usuario
            $sql = "INSERT INTO users (username, email, password, role_id) VALUES
                ('taller1', '<EMAIL>', :password, 2)";
            $stmt = $pdo->prepare($sql);
            $stmt->execute(['password' => $workshopPassword]);
            $workshopUserId = $pdo->lastInsertId();

            // Insertar datos del taller
            $sql = "INSERT INTO workshops (user_id, name, location, phone, description) VALUES
                (:user_id, 'Taller Mecánico Ejemplo', 'Calle Ejemplo 123, Ciudad', '************',
                'Taller especializado en reparación de motores y sistemas de frenos')";
            $stmt = $pdo->prepare($sql);
            $stmt->execute(['user_id' => $workshopUserId]);

            $pdo->commit();
            echo "Usuario de taller de ejemplo creado correctamente.<br>";
            echo "Usuario: taller1<br>";
            echo "Contraseña: taller123<br>";
        } catch (Exception $e) {
            $pdo->rollBack();
            echo "Error al crear el usuario de taller de ejemplo: " . $e->getMessage() . "<br>";
        }
    } else {
        echo "El usuario de taller de ejemplo ya existe en la base de datos.<br>";
    }

    echo "<br>Inicialización de la base de datos completada correctamente.";

} catch (PDOException $e) {
    echo "Error: " . $e->getMessage();
}
?>
