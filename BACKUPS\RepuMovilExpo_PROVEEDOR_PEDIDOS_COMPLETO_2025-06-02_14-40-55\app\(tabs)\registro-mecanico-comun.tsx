import React, { useState } from 'react';
import {
  SafeAreaView,
  StatusBar,
  StyleSheet,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  Dimensions,
} from 'react-native';
import { useRouter } from 'expo-router';

const { width } = Dimensions.get('window');

export default function RegistroMecanicoComun() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    nombre: '',
    email: '',
    telefono: '',
    password: '',
    especialidades: '',
    experiencia_anos: '',
    descripcion: '',
    zona_trabajo: '',
    precio_hora: '',
  });

  const [loading, setLoading] = useState(false);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const validarFormulario = () => {
    if (!formData.nombre || !formData.email || !formData.password || !formData.especialidades) {
      Alert.alert('⚠️ Campos requeridos', 'Completa todos los campos obligatorios');
      return false;
    }

    if (formData.password.length < 6) {
      Alert.alert('⚠️ Contraseña', 'La contraseña debe tener al menos 6 caracteres');
      return false;
    }

    if (!formData.email.includes('@')) {
      Alert.alert('⚠️ Email', 'Ingresa un email válido');
      return false;
    }

    return true;
  };

  const registrarMecanico = async () => {
    if (!validarFormulario()) return;

    setLoading(true);
    
    try {
      // Preparar datos para enviar a la API
      const datosRegistro = {
        nombre: formData.nombre,
        email: formData.email,
        telefono: formData.telefono,
        password: formData.password,
        especialidades: formData.especialidades,
        experiencia_anos: parseInt(formData.experiencia_anos) || 0,
        descripcion: formData.descripcion,
        zona_trabajo: formData.zona_trabajo,
        precio_hora: parseFloat(formData.precio_hora) || null,
        user_type: 'mecanico_independiente',
        plan_type: 'comun'
      };

      console.log('📤 Enviando datos mecánico a la API:', datosRegistro);

      // Llamar a la API PHP
      const response = await fetch('http://localhost/mechanical-workshop/public/api/registro-dinamico.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify(datosRegistro),
      });

      console.log('📥 Respuesta del servidor mecánico:', response.status);

      const resultado = await response.json();
      console.log('📋 Datos de respuesta mecánico:', resultado);

      if (resultado.success) {
        Alert.alert(
          '🎉 ¡Registro Exitoso!',
          `¡Bienvenido a RepuMovil!\n\nTu perfil de mecánico independiente "${formData.nombre}" ha sido registrado exitosamente en la base de datos.\n\n✅ Incluye:\n• Pedido de repuestos\n• Sistema changuito\n• Calificaciones\n• Gestión móvil\n\n🆔 ID de usuario: ${resultado.user_id}`,
          [
            {
              text: 'Ir al Dashboard',
              onPress: () => router.push('/dashboard-mecanico')
            }
          ]
        );
      } else {
        Alert.alert('❌ Error de Registro', resultado.message || 'Error desconocido');
      }
    } catch (error) {
      console.error('💥 Error al registrar mecánico:', error);
      Alert.alert(
        '❌ Error de Conexión', 
        'No se pudo conectar con el servidor. Verifica:\n\n• Que XAMPP esté ejecutándose\n• Que la API esté disponible\n• Tu conexión a internet\n\nError: ' + error.message
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#2C3E50" />
      
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.logoText}>
            🔧 <Text style={styles.logoRepu}>Repu</Text><Text style={styles.logoMovil}>Movil</Text>
          </Text>
          <Text style={styles.subtitle}>TU TALLER VA DONDE VOS VAS, NEEEÑO</Text>
        </View>

        {/* Características del Plan */}
        <View style={styles.featureHighlight}>
          <Text style={styles.featureTitle}>✨ Tu plan incluye:</Text>
          <View style={styles.featureList}>
            <Text style={styles.featureItem}>✅ Pedido de repuestos móvil</Text>
            <Text style={styles.featureItem}>✅ Calificaciones de clientes</Text>
            <Text style={styles.featureItem}>✅ Gestión de trabajos</Text>
            <Text style={styles.featureItem}>✅ Contacto directo WhatsApp</Text>
          </View>
        </View>

        {/* Formulario */}
        <View style={styles.formContainer}>
          <Text style={styles.sectionTitle}>📝 Datos Básicos</Text>
          
          <TextInput
            style={styles.input}
            placeholder="Nombre Completo *"
            value={formData.nombre}
            onChangeText={(text) => handleInputChange('nombre', text)}
            placeholderTextColor="#999"
          />

          <TextInput
            style={styles.input}
            placeholder="Email *"
            value={formData.email}
            onChangeText={(text) => handleInputChange('email', text)}
            keyboardType="email-address"
            autoCapitalize="none"
            placeholderTextColor="#999"
          />

          <TextInput
            style={styles.input}
            placeholder="Teléfono *"
            value={formData.telefono}
            onChangeText={(text) => handleInputChange('telefono', text)}
            keyboardType="phone-pad"
            placeholderTextColor="#999"
          />

          <TextInput
            style={styles.input}
            placeholder="Contraseña *"
            value={formData.password}
            onChangeText={(text) => handleInputChange('password', text)}
            secureTextEntry
            placeholderTextColor="#999"
          />

          <Text style={styles.sectionTitle}>🔧 Datos Profesionales</Text>

          <TextInput
            style={styles.input}
            placeholder="Especialidades *"
            value={formData.especialidades}
            onChangeText={(text) => handleInputChange('especialidades', text)}
            placeholderTextColor="#999"
          />

          <TextInput
            style={styles.input}
            placeholder="Años de Experiencia"
            value={formData.experiencia_anos}
            onChangeText={(text) => handleInputChange('experiencia_anos', text)}
            keyboardType="numeric"
            placeholderTextColor="#999"
          />

          <TextInput
            style={styles.input}
            placeholder="Descripción de Servicios"
            value={formData.descripcion}
            onChangeText={(text) => handleInputChange('descripcion', text)}
            multiline
            numberOfLines={3}
            placeholderTextColor="#999"
          />

          <TextInput
            style={styles.input}
            placeholder="Zona de Trabajo"
            value={formData.zona_trabajo}
            onChangeText={(text) => handleInputChange('zona_trabajo', text)}
            placeholderTextColor="#999"
          />

          <TextInput
            style={styles.input}
            placeholder="Precio por Hora (opcional)"
            value={formData.precio_hora}
            onChangeText={(text) => handleInputChange('precio_hora', text)}
            keyboardType="numeric"
            placeholderTextColor="#999"
          />

          <TouchableOpacity 
            style={[styles.btnRegistrar, loading && styles.btnDisabled]} 
            onPress={registrarMecanico}
            disabled={loading}
          >
            <Text style={styles.btnRegistrarText}>
              {loading ? '🔄 Creando cuenta...' : '🚀 Crear mi Cuenta RepuMovil'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={styles.btnVolver}
            onPress={() => router.push('/seleccionar-plan')}
          >
            <Text style={styles.btnVolverText}>← Volver a Planes</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#2C3E50',
  },
  content: {
    flex: 1,
  },
  header: {
    alignItems: 'center',
    paddingVertical: 30,
    paddingHorizontal: 20,
  },
  logoText: {
    fontSize: 32,
    fontWeight: '900',
    color: 'white',
    marginBottom: 10,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 2, height: 2 },
    textShadowRadius: 4,
  },
  logoRepu: {
    color: '#3498DB',
  },
  logoMovil: {
    color: '#E8F4FD',
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    lineHeight: 22,
    fontWeight: '700',
  },
  featureHighlight: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    marginHorizontal: 20,
    borderRadius: 15,
    padding: 20,
    marginBottom: 20,
    borderLeftWidth: 4,
    borderLeftColor: '#3498DB',
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: '#333',
    marginBottom: 10,
  },
  featureList: {
    gap: 5,
  },
  featureItem: {
    fontSize: 14,
    color: '#3498DB',
    fontWeight: '600',
  },
  formContainer: {
    backgroundColor: 'white',
    marginHorizontal: 20,
    borderRadius: 25,
    padding: 25,
    marginBottom: 30,
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 5 },
    shadowOpacity: 0.3,
    shadowRadius: 10,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#333',
    marginBottom: 15,
    marginTop: 10,
  },
  input: {
    borderWidth: 2,
    borderColor: '#e9ecef',
    borderRadius: 12,
    padding: 15,
    fontSize: 16,
    marginBottom: 15,
    backgroundColor: '#fff',
  },
  btnRegistrar: {
    backgroundColor: '#3498DB',
    borderRadius: 12,
    padding: 18,
    alignItems: 'center',
    marginTop: 20,
    elevation: 3,
    shadowColor: '#3498DB',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  btnDisabled: {
    backgroundColor: '#ccc',
    elevation: 0,
    shadowOpacity: 0,
  },
  btnRegistrarText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '700',
  },
  btnVolver: {
    alignItems: 'center',
    marginTop: 15,
    padding: 10,
  },
  btnVolverText: {
    color: '#666',
    fontSize: 16,
    fontWeight: '600',
  },
});
