<?php
// Configuración de base de datos
$host = 'localhost';
$dbname = 'mechanical_workshop';
$username = 'root';
$password = '';

try {
    // Conectar a la base de datos
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Verificar si ya existe el proveedor de prueba
    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    
    if ($stmt->fetch()) {
        echo "✅ El proveedor de prueba ya existe!<br>";
        echo "<strong>Email:</strong> <EMAIL><br>";
        echo "<strong>Contraseña:</strong> 123456<br>";
        echo "<strong>Tipo:</strong> proveedor_repuestos<br><br>";
        echo "<a href='login-dinamico.php'>🔐 Ir al <PERSON>gin</a>";
    } else {
        // Crear proveedor de prueba
        $password_hash = password_hash('123456', PASSWORD_DEFAULT);
        
        $stmt = $pdo->prepare("
            INSERT INTO users (nombre, email, password, telefono, user_type)
            VALUES (?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            'Proveedor Test',
            '<EMAIL>', 
            $password_hash,
            '+54 9 ************',
            'proveedor_repuestos'
        ]);
        
        echo "🎉 ¡Proveedor de prueba creado exitosamente!<br><br>";
        echo "<strong>📧 Email:</strong> <EMAIL><br>";
        echo "<strong>🔑 Contraseña:</strong> 123456<br>";
        echo "<strong>👤 Tipo:</strong> proveedor_repuestos<br>";
        echo "<strong>📱 Teléfono:</strong> +54 9 ************<br><br>";
        echo "<a href='login-dinamico.php' style='background: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔐 Ir al Login</a>";
    }
    
} catch (PDOException $e) {
    echo "❌ Error: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crear Proveedor Test - RepuMovil</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        h1 {
            color: #4CAF50;
            margin-bottom: 20px;
        }
        
        a {
            display: inline-block;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏪 Crear Proveedor de Prueba</h1>
        <p>Esta página crea un usuario proveedor para testing.</p>
        <hr style="margin: 20px 0;">
    </div>
</body>
</html>
