<?php
/**
 * API de Carrito para RepuMovil
 * FASE 2: Backend/API - Paso 3
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Manejar preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Incluir configuración de base de datos
require_once 'db_config.php';

try {
    $pdo = connectDB();
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            handleGetCarrito($pdo);
            break;
            
        case 'POST':
            handlePostCarrito($pdo);
            break;
            
        case 'PUT':
            handlePutCarrito($pdo);
            break;
            
        case 'DELETE':
            handleDeleteCarrito($pdo);
            break;
            
        default:
            throw new Exception('Método no permitido');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

/**
 * Obtener carrito del usuario
 */
function handleGetCarrito($pdo) {
    $userId = $_GET['user_id'] ?? '';
    
    if (empty($userId)) {
        throw new Exception('ID de usuario requerido');
    }
    
    $stmt = $pdo->prepare("
        SELECT c.*, r.nombre, r.descripcion, r.marca, r.modelo, r.imagen_url, r.stock,
               s.name as supplier_name, s.phone as supplier_phone,
               (c.cantidad * c.precio_unitario) as subtotal
        FROM carrito c
        JOIN repuestos r ON c.repuesto_id = r.id
        JOIN suppliers s ON r.supplier_id = s.id
        WHERE c.user_id = ?
        ORDER BY c.created_at DESC
    ");
    $stmt->execute([$userId]);
    $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Calcular totales
    $total = 0;
    $totalItems = 0;
    
    foreach ($items as $item) {
        $total += $item['subtotal'];
        $totalItems += $item['cantidad'];
    }
    
    echo json_encode([
        'success' => true,
        'data' => [
            'items' => $items,
            'summary' => [
                'total_items' => $totalItems,
                'total_amount' => $total,
                'item_count' => count($items)
            ]
        ]
    ]);
}

/**
 * Agregar producto al carrito
 */
function handlePostCarrito($pdo) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Datos JSON inválidos');
    }
    
    $userId = $input['user_id'] ?? '';
    $repuestoId = $input['repuesto_id'] ?? '';
    $cantidad = (int)($input['cantidad'] ?? 1);
    
    if (empty($userId) || empty($repuestoId) || $cantidad <= 0) {
        throw new Exception('Datos inválidos');
    }
    
    // Verificar que el repuesto existe y está disponible
    $stmt = $pdo->prepare("SELECT precio, stock FROM repuestos WHERE id = ? AND estado = 'disponible'");
    $stmt->execute([$repuestoId]);
    $repuesto = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$repuesto) {
        throw new Exception('Repuesto no encontrado o no disponible');
    }
    
    if ($cantidad > $repuesto['stock']) {
        throw new Exception('Cantidad solicitada excede el stock disponible');
    }
    
    // Verificar si ya existe en el carrito
    $stmt = $pdo->prepare("SELECT id, cantidad FROM carrito WHERE user_id = ? AND repuesto_id = ?");
    $stmt->execute([$userId, $repuestoId]);
    $existing = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($existing) {
        // Actualizar cantidad existente
        $nuevaCantidad = $existing['cantidad'] + $cantidad;
        
        if ($nuevaCantidad > $repuesto['stock']) {
            throw new Exception('La cantidad total excedería el stock disponible');
        }
        
        $stmt = $pdo->prepare("UPDATE carrito SET cantidad = ? WHERE id = ?");
        $stmt->execute([$nuevaCantidad, $existing['id']]);
        
        $message = 'Cantidad actualizada en el carrito';
    } else {
        // Agregar nuevo item
        $stmt = $pdo->prepare("
            INSERT INTO carrito (user_id, repuesto_id, cantidad, precio_unitario) 
            VALUES (?, ?, ?, ?)
        ");
        $stmt->execute([$userId, $repuestoId, $cantidad, $repuesto['precio']]);
        
        $message = 'Producto agregado al carrito';
    }
    
    echo json_encode([
        'success' => true,
        'message' => $message
    ]);
}

/**
 * Actualizar cantidad en el carrito
 */
function handlePutCarrito($pdo) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Datos JSON inválidos');
    }
    
    $carritoId = $input['carrito_id'] ?? '';
    $cantidad = (int)($input['cantidad'] ?? 0);
    
    if (empty($carritoId) || $cantidad <= 0) {
        throw new Exception('Datos inválidos');
    }
    
    // Verificar que el item existe y obtener info del repuesto
    $stmt = $pdo->prepare("
        SELECT c.*, r.stock 
        FROM carrito c 
        JOIN repuestos r ON c.repuesto_id = r.id 
        WHERE c.id = ?
    ");
    $stmt->execute([$carritoId]);
    $item = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$item) {
        throw new Exception('Item del carrito no encontrado');
    }
    
    if ($cantidad > $item['stock']) {
        throw new Exception('Cantidad excede el stock disponible');
    }
    
    // Actualizar cantidad
    $stmt = $pdo->prepare("UPDATE carrito SET cantidad = ? WHERE id = ?");
    $stmt->execute([$cantidad, $carritoId]);
    
    echo json_encode([
        'success' => true,
        'message' => 'Cantidad actualizada'
    ]);
}

/**
 * Eliminar producto del carrito
 */
function handleDeleteCarrito($pdo) {
    $carritoId = $_GET['carrito_id'] ?? '';
    
    if (empty($carritoId)) {
        throw new Exception('ID del carrito requerido');
    }
    
    $stmt = $pdo->prepare("DELETE FROM carrito WHERE id = ?");
    $result = $stmt->execute([$carritoId]);
    
    if ($stmt->rowCount() === 0) {
        throw new Exception('Item del carrito no encontrado');
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Producto eliminado del carrito'
    ]);
}
?>
