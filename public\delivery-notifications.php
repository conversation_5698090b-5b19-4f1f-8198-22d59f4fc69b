<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RepuMovil - Notificaciones Delivery 📱</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #FF6B35;
            --secondary-color: #FFA500;
            --dark-color: #2c3e50;
            --light-color: #f8f9fa;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
            color: white;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .logo {
            font-size: 3rem;
            margin-bottom: 10px;
        }

        .title {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .notification-demo {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            color: var(--dark-color);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .demo-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 20px;
            color: var(--primary-color);
        }

        .demo-button {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
        }

        .demo-button:hover {
            background: #e55a2b;
            transform: translateY(-2px);
        }

        .demo-button.success {
            background: var(--success-color);
        }

        .demo-button.warning {
            background: var(--warning-color);
        }

        .demo-button.info {
            background: var(--info-color);
        }

        /* PASO 4: Estilos para notificación push web */
        .push-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 350px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            z-index: 1000;
            transform: translateX(400px);
            transition: transform 0.3s ease;
            animation: pulse 2s infinite;
        }

        .push-notification.show {
            transform: translateX(0);
        }

        @keyframes pulse {
            0% { transform: translateX(0) scale(1); }
            50% { transform: translateX(0) scale(1.02); }
            100% { transform: translateX(0) scale(1); }
        }

        .notification-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .notification-title {
            color: white;
            font-size: 1.1rem;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .notification-close {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            cursor: pointer;
            font-weight: bold;
        }

        .notification-content {
            color: white;
            margin-bottom: 15px;
        }

        .notification-cliente {
            font-size: 1rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .notification-direccion {
            font-size: 0.9rem;
            opacity: 0.9;
            margin-bottom: 10px;
        }

        .notification-details {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
        }

        .notification-detail {
            background: rgba(255,255,255,0.2);
            padding: 5px 8px;
            border-radius: 8px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        /* PASO 5: Timer y botones mejorados */
        .notification-timer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .timer-display {
            background: rgba(255,255,255,0.3);
            padding: 8px 15px;
            border-radius: 20px;
            border: 2px solid rgba(255,255,255,0.5);
            font-weight: bold;
            font-size: 1rem;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .timer-progress {
            width: 100%;
            height: 6px;
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
            margin-bottom: 15px;
            overflow: hidden;
        }

        .timer-progress-bar {
            height: 100%;
            border-radius: 3px;
            transition: width 1s ease-in-out;
        }

        .timer-green { background: var(--success-color); }
        .timer-yellow { background: var(--warning-color); }
        .timer-red { background: var(--danger-color); }

        .notification-actions {
            display: flex;
            gap: 15px;
            margin-bottom: 10px;
        }

        .notification-btn {
            flex: 1;
            padding: 16px 20px;
            border: none;
            border-radius: 12px;
            font-weight: 800;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            border: 2px solid rgba(255,255,255,0.4);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .notification-btn.reject {
            background: rgba(220, 53, 69, 0.9);
            color: white;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .notification-btn.accept {
            background: var(--success-color);
            color: white;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .notification-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .notification-btn:active {
            transform: translateY(-1px);
        }

        .notification-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .btn-icon {
            font-size: 1.2rem;
        }

        .urgency-message {
            background: rgba(255,255,255,0.2);
            padding: 10px 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid rgba(255,255,255,0.3);
            font-weight: bold;
            font-size: 0.9rem;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .responding {
            opacity: 0.8;
            pointer-events: none;
        }

        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255,255,255,0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .delivery-status {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            color: var(--dark-color);
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .status-card {
            background: var(--light-color);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            border-left: 4px solid var(--primary-color);
        }

        .status-number {
            font-size: 2rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        .status-label {
            font-size: 0.9rem;
            color: #666;
            margin-top: 5px;
        }

        .log-section {
            background: white;
            border-radius: 15px;
            padding: 20px;
            color: var(--dark-color);
        }

        .log-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .log-time {
            font-size: 0.8rem;
            color: #666;
        }

        .log-message {
            font-weight: bold;
        }

        .log-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: bold;
            text-transform: uppercase;
        }

        .log-status.sent {
            background: #d4edda;
            color: #155724;
        }

        .log-status.delivered {
            background: #cce7ff;
            color: #004085;
        }

        .log-status.accepted {
            background: #d1ecf1;
            color: #0c5460;
        }

        .log-status.rejected {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">🚨📱</div>
            <h1 class="title">Sistema de Notificaciones Push</h1>
            <p class="subtitle">RepuMovil Delivery - Tiempo Real</p>
        </div>

        <!-- Demo de Notificaciones -->
        <div class="notification-demo">
            <h2 class="demo-title">
                <i class="fas fa-bell"></i>
                Simulador de Notificaciones Push
            </h2>
            <p style="margin-bottom: 20px;">Probá diferentes tipos de notificaciones que reciben los deliveries:</p>
            
            <button class="demo-button" onclick="sendNotification('nuevo_pedido')">
                <i class="fas fa-plus-circle"></i>
                Nuevo Pedido Disponible
            </button>
            
            <button class="demo-button success" onclick="sendNotification('pedido_asignado')">
                <i class="fas fa-check-circle"></i>
                Pedido Asignado
            </button>
            
            <button class="demo-button warning" onclick="sendNotification('pedido_urgente')">
                <i class="fas fa-exclamation-triangle"></i>
                Pedido Urgente
            </button>
            
            <button class="demo-button info" onclick="sendNotification('bonus_disponible')">
                <i class="fas fa-gift"></i>
                Bonus Disponible
            </button>
        </div>

        <!-- Estado de Deliveries -->
        <div class="delivery-status">
            <h3>
                <i class="fas fa-motorcycle"></i>
                Estado de Deliveries en Tiempo Real
            </h3>
            <div class="status-grid">
                <div class="status-card">
                    <div class="status-number" id="deliveriesOnline">5</div>
                    <div class="status-label">En Línea</div>
                </div>
                <div class="status-card">
                    <div class="status-number" id="notificationsSent">23</div>
                    <div class="status-label">Notificaciones Enviadas</div>
                </div>
                <div class="status-card">
                    <div class="status-number" id="acceptanceRate">87%</div>
                    <div class="status-label">Tasa de Aceptación</div>
                </div>
                <div class="status-card">
                    <div class="status-number" id="avgResponseTime">12s</div>
                    <div class="status-label">Tiempo Respuesta</div>
                </div>
            </div>
        </div>

        <!-- Log de Notificaciones -->
        <div class="log-section">
            <h3>
                <i class="fas fa-list"></i>
                Log de Notificaciones Push
            </h3>
            <div id="notificationLog">
                <div class="log-item">
                    <div>
                        <div class="log-message">Notificación enviada a Juan Pérez</div>
                        <div class="log-time">Hace 2 minutos</div>
                    </div>
                    <div class="log-status sent">Enviada</div>
                </div>
                <div class="log-item">
                    <div>
                        <div class="log-message">Pedido #1001 aceptado por María González</div>
                        <div class="log-time">Hace 5 minutos</div>
                    </div>
                    <div class="log-status accepted">Aceptada</div>
                </div>
                <div class="log-item">
                    <div>
                        <div class="log-message">Notificación entregada a Carlos Rodríguez</div>
                        <div class="log-time">Hace 8 minutos</div>
                    </div>
                    <div class="log-status delivered">Entregada</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // PASO 4: Sistema de notificaciones push web
        let notificationCount = 23;
        let acceptanceCount = 20;

        function sendNotification(type) {
            const notifications = {
                nuevo_pedido: {
                    title: '🚨 ¡NUEVO PEDIDO!',
                    cliente: 'Taller Mecánico Central',
                    direccion: 'Av. Libertador 1234, Centro',
                    ganancia: '$280',
                    distancia: '2.3 km',
                    tiempo: '15 min'
                },
                pedido_asignado: {
                    title: '✅ ¡PEDIDO ASIGNADO!',
                    cliente: 'AutoService Plus',
                    direccion: 'Ruta 40 Km 15, Zona Industrial',
                    ganancia: '$450',
                    distancia: '4.1 km',
                    tiempo: '25 min'
                },
                pedido_urgente: {
                    title: '⚡ ¡PEDIDO URGENTE!',
                    cliente: 'Mecánico López',
                    direccion: 'Calle Rivadavia 567, Barrio Norte',
                    ganancia: '$350',
                    distancia: '1.8 km',
                    tiempo: '12 min'
                },
                bonus_disponible: {
                    title: '🎁 ¡BONUS DISPONIBLE!',
                    cliente: 'Zona Premium',
                    direccion: 'Centro Comercial San Juan',
                    ganancia: '+$100',
                    distancia: '3.2 km',
                    tiempo: '20 min'
                }
            };

            const notification = notifications[type];
            showPushNotification(notification);
            
            // Actualizar estadísticas
            notificationCount++;
            document.getElementById('notificationsSent').textContent = notificationCount;
            
            // Agregar al log
            addToLog(`Notificación "${notification.title}" enviada`, 'sent');
            
            // Simular respuesta después de 3-8 segundos
            setTimeout(() => {
                const accepted = Math.random() > 0.3; // 70% de aceptación
                if (accepted) {
                    acceptanceCount++;
                    addToLog(`Pedido aceptado por delivery`, 'accepted');
                } else {
                    addToLog(`Pedido rechazado por delivery`, 'rejected');
                }
                
                // Actualizar tasa de aceptación
                const rate = Math.round((acceptanceCount / notificationCount) * 100);
                document.getElementById('acceptanceRate').textContent = rate + '%';
            }, Math.random() * 5000 + 3000);
        }

        // PASO 5: Variables globales para timer
        let currentTimer = null;
        let timerSeconds = 60;
        let responding = false;

        function showPushNotification(data) {
            // Crear notificación con timer
            const notification = document.createElement('div');
            notification.className = 'push-notification';
            notification.innerHTML = `
                <div class="notification-header">
                    <div class="notification-title">${data.title}</div>
                    <div class="notification-timer">
                        <div class="timer-display">⏱️ <span id="timer-seconds">60</span>s</div>
                    </div>
                </div>
                <div class="timer-progress">
                    <div class="timer-progress-bar timer-green" id="timer-bar" style="width: 100%"></div>
                </div>
                <div class="notification-content">
                    <div class="notification-cliente">${data.cliente}</div>
                    <div class="notification-direccion">📍 ${data.direccion}</div>
                    <div class="notification-details">
                        <div class="notification-detail">💰 ${data.ganancia}</div>
                        <div class="notification-detail">📏 ${data.distancia}</div>
                        <div class="notification-detail">⏱️ ${data.tiempo}</div>
                    </div>
                </div>
                <div class="notification-actions">
                    <button class="notification-btn reject" onclick="rejectNotification(this)" id="reject-btn">
                        <span class="btn-icon">❌</span> RECHAZAR
                    </button>
                    <button class="notification-btn accept" onclick="acceptNotification(this)" id="accept-btn">
                        <span class="btn-icon">✅</span> ACEPTAR
                    </button>
                </div>
                <div class="urgency-message" id="urgency-msg">
                    ⚡ ¡Responde rápido para no perder el pedido!
                </div>
            `;

            document.body.appendChild(notification);

            // Mostrar con animación
            setTimeout(() => {
                notification.classList.add('show');
            }, 100);

            // Reproducir sonido (simulado)
            console.log('🔊 Sonido de notificación reproducido');

            // PASO 5: Iniciar timer de 60 segundos
            startNotificationTimer(notification);
        }

        // PASO 5: Función para iniciar timer
        function startNotificationTimer(notification) {
            timerSeconds = 60;
            responding = false;

            const timerDisplay = notification.querySelector('#timer-seconds');
            const timerBar = notification.querySelector('#timer-bar');
            const urgencyMsg = notification.querySelector('#urgency-msg');

            currentTimer = setInterval(() => {
                timerSeconds--;
                timerDisplay.textContent = timerSeconds;

                // Actualizar barra de progreso
                const percentage = (timerSeconds / 60) * 100;
                timerBar.style.width = percentage + '%';

                // Cambiar colores según tiempo restante
                if (timerSeconds > 20) {
                    timerBar.className = 'timer-progress-bar timer-green';
                    urgencyMsg.innerHTML = '⚡ ¡Responde rápido para no perder el pedido!';
                } else if (timerSeconds > 10) {
                    timerBar.className = 'timer-progress-bar timer-yellow';
                    urgencyMsg.innerHTML = '⚠️ ¡Tiempo limitado! Decide ahora.';
                } else if (timerSeconds > 0) {
                    timerBar.className = 'timer-progress-bar timer-red';
                    urgencyMsg.innerHTML = '🚨 ¡ÚLTIMOS SEGUNDOS!';
                } else {
                    // Tiempo agotado
                    handleTimeout(notification);
                    return;
                }
            }, 1000);
        }

        // PASO 5: Función para manejar timeout
        function handleTimeout(notification) {
            if (currentTimer) {
                clearInterval(currentTimer);
                currentTimer = null;
            }

            if (!responding) {
                alert('⏰ Tiempo Agotado\n\nEl pedido fue reasignado automáticamente por falta de respuesta.');
                addToLog('Pedido reasignado por timeout', 'timeout');
                hideNotification(notification.querySelector('.notification-close') || notification);
            }
        }

        // PASO 5: Función para detener timer
        function stopTimer() {
            if (currentTimer) {
                clearInterval(currentTimer);
                currentTimer = null;
            }
        }

        function hideNotification(button) {
            const notification = button.closest('.push-notification');
            notification.classList.remove('show');
            setTimeout(() => {
                if (document.body.contains(notification)) {
                    document.body.removeChild(notification);
                }
            }, 300);
        }

        // PASO 5: Función para aceptar pedido mejorada
        function acceptNotification(button) {
            if (responding) return;

            responding = true;
            stopTimer();

            // Mostrar estado de carga
            const notification = button.closest('.push-notification');
            notification.classList.add('responding');

            button.disabled = true;
            button.innerHTML = '<div class="spinner"></div> ACEPTANDO...';

            // Simular procesamiento
            setTimeout(() => {
                addToLog('Pedido aceptado desde notificación', 'accepted');
                alert('✅ ¡Pedido Aceptado!\n\nPedido confirmado exitosamente.\n📍 Dirígete al proveedor para recoger el pedido.\n💰 Ganancia confirmada.');
                hideNotification(button);
            }, 2000);
        }

        // PASO 5: Función para rechazar pedido mejorada
        function rejectNotification(button) {
            if (responding) return;

            responding = true;
            stopTimer();

            // Mostrar opciones de rechazo
            const motivos = [
                'Muy lejos',
                'No disponible',
                'Problema con vehículo',
                'Otro motivo'
            ];

            const motivoSeleccionado = prompt(
                '❌ ¿Por qué rechazas este pedido?\n\n' +
                motivos.map((m, i) => `${i + 1}. ${m}`).join('\n') +
                '\n\nEscribe el número (1-4):'
            );

            const motivoIndex = parseInt(motivoSeleccionado) - 1;
            const motivo = motivos[motivoIndex] || 'Otro motivo';

            if (motivoSeleccionado) {
                // Mostrar estado de carga
                const notification = button.closest('.push-notification');
                notification.classList.add('responding');

                button.disabled = true;
                button.innerHTML = '<div class="spinner"></div> RECHAZANDO...';

                // PASO 6: Procesar rechazo con reasignación automática
                procesarRechazoConReasignacion(motivo, button, notification);
            } else {
                // Cancelar rechazo, reiniciar timer
                responding = false;
                startNotificationTimer(button.closest('.push-notification'));
            }
        }

        // PASO 6: Función para procesar rechazo con reasignación automática
        async function procesarRechazoConReasignacion(motivo, button, notification) {
            try {
                // Simular llamada a API de reasignación
                const response = await fetch('api/asignacion-automatica.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'reasignar_por_rechazo',
                        pedido_id: Math.floor(Math.random() * 1000) + 1000, // ID simulado
                        repartidor_rechazado: 1, // ID del delivery actual
                        motivo_rechazo: motivo,
                        timestamp_rechazo: new Date().toISOString()
                    })
                });

                // Simular respuesta exitosa (en desarrollo real sería la respuesta real)
                const result = {
                    success: Math.random() > 0.2, // 80% de éxito
                    nuevo_repartidor: {
                        id: Math.floor(Math.random() * 10) + 2,
                        nombre: ['María González', 'Carlos Rodríguez', 'Ana López', 'Luis Martín'][Math.floor(Math.random() * 4)],
                        telefono: '+54 ************',
                        calificacion: (Math.random() * 1.5 + 3.5).toFixed(1)
                    },
                    tiempo_reasignacion: Math.floor(Math.random() * 3) + 2, // 2-4 segundos
                    deliveries_contactados: Math.floor(Math.random() * 3) + 1
                };

                // Simular delay de procesamiento
                await new Promise(resolve => setTimeout(resolve, 2000));

                if (result.success) {
                    // Reasignación exitosa
                    button.innerHTML = '<span class="btn-icon">✅</span> REASIGNADO';
                    button.style.background = 'var(--success-color)';

                    addToLog(`Pedido rechazado: ${motivo}`, 'rejected');
                    addToLog(`Reasignado automáticamente a ${result.nuevo_repartidor.nombre}`, 'reassigned');

                    // Mostrar resultado exitoso
                    setTimeout(() => {
                        alert(`✅ ¡Reasignación Exitosa!\n\n🔄 Pedido reasignado automáticamente\n🏍️ Nuevo delivery: ${result.nuevo_repartidor.nombre}\n⭐ Calificación: ${result.nuevo_repartidor.calificacion}\n📱 Notificación enviada al instante\n⏱️ Tiempo de reasignación: ${result.tiempo_reasignacion}s\n\n¡Gracias por tu honestidad!`);
                        hideNotification(button);
                    }, 1000);

                    // Actualizar estadísticas
                    updateReasignmentStats(true);

                } else {
                    // Error en reasignación
                    button.innerHTML = '<span class="btn-icon">⚠️</span> ERROR';
                    button.style.background = 'var(--warning-color)';

                    addToLog(`Pedido rechazado: ${motivo}`, 'rejected');
                    addToLog('Error en reasignación automática', 'error');

                    setTimeout(() => {
                        alert(`⚠️ Problema en Reasignación\n\nNo se pudo reasignar automáticamente.\nMotivo: No hay deliveries disponibles en la zona.\n\n📞 El proveedor será notificado para asignación manual.`);
                        hideNotification(button);
                    }, 1000);

                    // Actualizar estadísticas
                    updateReasignmentStats(false);
                }

            } catch (error) {
                // Error de conexión
                button.innerHTML = '<span class="btn-icon">❌</span> ERROR';
                button.style.background = 'var(--danger-color)';

                setTimeout(() => {
                    alert(`❌ Error de Conexión\n\nNo se pudo procesar el rechazo.\n¿Deseas intentar de nuevo?`);

                    if (confirm('¿Reintentar reasignación automática?')) {
                        // Reiniciar proceso
                        button.disabled = false;
                        button.innerHTML = '<span class="btn-icon">❌</span> RECHAZAR';
                        button.style.background = 'rgba(220, 53, 69, 0.9)';
                        notification.classList.remove('responding');
                        responding = false;
                        startNotificationTimer(notification);
                    } else {
                        hideNotification(button);
                    }
                }, 1000);

                console.error('Error en reasignación:', error);
            }
        }

        // PASO 6: Función para actualizar estadísticas de reasignación
        function updateReasignmentStats(success) {
            // Actualizar contador de reasignaciones
            const currentReassignments = parseInt(localStorage.getItem('reassignments_today') || '0');
            const successfulReassignments = parseInt(localStorage.getItem('successful_reassignments_today') || '0');

            localStorage.setItem('reassignments_today', (currentReassignments + 1).toString());

            if (success) {
                localStorage.setItem('successful_reassignments_today', (successfulReassignments + 1).toString());
            }

            // Actualizar tasa de éxito de reasignaciones
            const successRate = Math.round(((successfulReassignments + (success ? 1 : 0)) / (currentReassignments + 1)) * 100);

            // Simular actualización en dashboard (en producción sería una llamada a API)
            console.log(`📊 Estadísticas de reasignación actualizadas:`);
            console.log(`   Total reasignaciones hoy: ${currentReassignments + 1}`);
            console.log(`   Reasignaciones exitosas: ${successfulReassignments + (success ? 1 : 0)}`);
            console.log(`   Tasa de éxito: ${successRate}%`);
        }

        function addToLog(message, status) {
            const log = document.getElementById('notificationLog');
            const logItem = document.createElement('div');
            logItem.className = 'log-item';
            logItem.innerHTML = `
                <div>
                    <div class="log-message">${message}</div>
                    <div class="log-time">Ahora</div>
                </div>
                <div class="log-status ${status}">${status.charAt(0).toUpperCase() + status.slice(1)}</div>
            `;
            
            log.insertBefore(logItem, log.firstChild);
            
            // Mantener solo los últimos 10 logs
            while (log.children.length > 10) {
                log.removeChild(log.lastChild);
            }
        }

        // Actualizar tiempo de respuesta cada 5 segundos
        setInterval(() => {
            const responseTime = Math.floor(Math.random() * 20) + 5;
            document.getElementById('avgResponseTime').textContent = responseTime + 's';
        }, 5000);

        // Simular deliveries conectándose/desconectándose
        setInterval(() => {
            const online = Math.floor(Math.random() * 3) + 4; // 4-6 deliveries online
            document.getElementById('deliveriesOnline').textContent = online;
        }, 15000);
    </script>
</body>
</html>
