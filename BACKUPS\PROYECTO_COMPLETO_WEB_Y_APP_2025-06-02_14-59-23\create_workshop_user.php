<?php
// Configuración de la conexión
$host = 'localhost';
$username = 'root';
$password = '';
$dbname = 'autoconnect_db';

// Datos del nuevo usuario de taller
$workshopUsername = 'taller2';
$workshopEmail = '<EMAIL>';
$workshopPassword = 'taller123'; // Contraseña sin encriptar
$workshopName = 'Taller Mecánico Profesional';
$workshopAddress = 'Av. Principal 456, Ciudad';
$workshopPhone = '************';
$workshopDescription = 'Taller especializado en sistemas eléctricos y diagnóstico computarizado';

try {
    // Conectar a la base de datos
    $conn = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h1>Creación de Usuario de Taller</h1>";
    
    // Verificar si el nombre de usuario ya existe
    $stmt = $conn->prepare("SELECT COUNT(*) FROM users WHERE username = :username");
    $stmt->execute(['username' => $workshopUsername]);
    if ($stmt->fetchColumn() > 0) {
        echo "<p>❌ El nombre de usuario '$workshopUsername' ya existe.</p>";
        exit;
    }
    
    // Verificar si el correo ya existe
    $stmt = $conn->prepare("SELECT COUNT(*) FROM users WHERE email = :email");
    $stmt->execute(['email' => $workshopEmail]);
    if ($stmt->fetchColumn() > 0) {
        echo "<p>❌ El correo electrónico '$workshopEmail' ya existe.</p>";
        exit;
    }
    
    // Obtener el ID del rol de taller
    $stmt = $conn->prepare("SELECT id FROM roles WHERE name = 'workshop'");
    $stmt->execute();
    $roleId = $stmt->fetchColumn();
    
    if (!$roleId) {
        echo "<p>❌ No se encontró el rol 'workshop'. Asegúrate de que la tabla de roles esté inicializada.</p>";
        exit;
    }
    
    // Iniciar transacción
    $conn->beginTransaction();
    
    try {
        // Crear usuario
        $hashedPassword = password_hash($workshopPassword, PASSWORD_DEFAULT);
        $stmt = $conn->prepare("INSERT INTO users (username, email, password, role_id) VALUES (:username, :email, :password, :role_id)");
        $stmt->execute([
            'username' => $workshopUsername,
            'email' => $workshopEmail,
            'password' => $hashedPassword,
            'role_id' => $roleId
        ]);
        
        $userId = $conn->lastInsertId();
        
        // Crear taller
        $stmt = $conn->prepare("INSERT INTO workshops (user_id, name, address, phone, email, description) VALUES (:user_id, :name, :address, :phone, :email, :description)");
        $stmt->execute([
            'user_id' => $userId,
            'name' => $workshopName,
            'address' => $workshopAddress,
            'phone' => $workshopPhone,
            'email' => $workshopEmail,
            'description' => $workshopDescription
        ]);
        
        $conn->commit();
        
        echo "<div style='background-color: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin-bottom: 20px;'>";
        echo "<h2>✅ Usuario de taller creado correctamente</h2>";
        echo "<p><strong>Nombre de usuario:</strong> $workshopUsername</p>";
        echo "<p><strong>Contraseña:</strong> $workshopPassword</p>";
        echo "<p><strong>Correo electrónico:</strong> $workshopEmail</p>";
        echo "<p><strong>Nombre del taller:</strong> $workshopName</p>";
        echo "</div>";
        
        echo "<p>Puedes iniciar sesión con estas credenciales en la <a href='login_reemplazo.php'>página de login</a>.</p>";
        
    } catch (Exception $e) {
        $conn->rollBack();
        echo "<p>❌ Error al crear el usuario: " . $e->getMessage() . "</p>";
    }
    
} catch(PDOException $e) {
    echo "<p>❌ Error de conexión: " . $e->getMessage() . "</p>";
}
?>