/* Estilos para el panel de administración */

/* Sidebar */
.sidebar {
    min-height: 100vh;
    background-color: #343a40;
    color: white;
    padding-top: 20px;
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.75);
    padding: 10px 15px;
    margin-bottom: 5px;
}

.sidebar .nav-link:hover {
    color: rgba(255, 255, 255, 1);
    background-color: rgba(255, 255, 255, 0.1);
}

.sidebar .nav-link.active {
    color: white;
    font-weight: bold;
    background-color: rgba(255, 255, 255, 0.2);
    border-left: 4px solid #17a2b8;
}

/* Main content */
.main-content {
    padding: 20px;
    background-color: #f8f9fa;
}

/* Cards */
.card {
    margin-bottom: 20px;
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    font-weight: 600;
}

/* Dashboard stats */
.stat-card {
    border-radius: 4px;
    color: white;
    padding: 20px;
    margin-bottom: 20px;
    transition: transform 0.3s;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-card .stat-number {
    font-size: 2.5rem;
    font-weight: bold;
}

.stat-card .stat-label {
    font-size: 1rem;
    opacity: 0.8;
}

/* Colores para las tarjetas de estadísticas */
.bg-users {
    background-color: #17a2b8;
}

.bg-workshops {
    background-color: #28a745;
}

.bg-suppliers {
    background-color: #ffc107;
}

/* Tablas */
.table th {
    font-weight: 600;
    background-color: #f8f9fa;
}

/* Botones de acción */
.btn-action {
    margin-right: 5px;
}

/* Formularios */
.form-group label {
    font-weight: 500;
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        min-height: auto;
    }
}