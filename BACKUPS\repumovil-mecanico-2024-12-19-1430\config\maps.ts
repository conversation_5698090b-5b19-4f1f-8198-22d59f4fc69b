// Configuración para Google Maps API
export const GOOGLE_MAPS_CONFIG = {
  // Reemplaza con tu API Key real de Google Maps
  API_KEY: 'AIzaSyBOti4mM-6x9WDnZIjIeyEU21OpBXqWBgw', // API Key de ejemplo
  
  // URLs de la API
  GEOCODING_URL: 'https://maps.googleapis.com/maps/api/geocode/json',
  PLACES_URL: 'https://maps.googleapis.com/maps/api/place/autocomplete/json',
  
  // Configuración por defecto
  DEFAULT_LOCATION: {
    lat: -34.6037, // Buenos Aires
    lng: -58.3816,
  },
  
  // Configuración de búsqueda
  SEARCH_CONFIG: {
    language: 'es',
    region: 'ar',
    components: 'country:ar', // Restringir a Argentina
  }
};

// Función para geocodificar una dirección
export const geocodeAddress = async (address: string) => {
  try {
    const url = `${GOOGLE_MAPS_CONFIG.GEOCODING_URL}?address=${encodeURIComponent(address)}&key=${GOOGLE_MAPS_CONFIG.API_KEY}&language=${GOOGLE_MAPS_CONFIG.SEARCH_CONFIG.language}&region=${GOOGLE_MAPS_CONFIG.SEARCH_CONFIG.region}`;
    
    const response = await fetch(url);
    const data = await response.json();
    
    if (data.status === 'OK' && data.results.length > 0) {
      return {
        success: true,
        address: data.results[0].formatted_address,
        coordinates: {
          lat: data.results[0].geometry.location.lat,
          lng: data.results[0].geometry.location.lng,
        },
        placeId: data.results[0].place_id,
      };
    } else {
      return {
        success: false,
        error: 'No se encontró la dirección',
      };
    }
  } catch (error) {
    return {
      success: false,
      error: 'Error al buscar la dirección',
    };
  }
};

// Función para obtener sugerencias de direcciones
export const getAddressSuggestions = async (input: string) => {
  try {
    const url = `${GOOGLE_MAPS_CONFIG.PLACES_URL}?input=${encodeURIComponent(input)}&key=${GOOGLE_MAPS_CONFIG.API_KEY}&language=${GOOGLE_MAPS_CONFIG.SEARCH_CONFIG.language}&components=${GOOGLE_MAPS_CONFIG.SEARCH_CONFIG.components}`;
    
    const response = await fetch(url);
    const data = await response.json();
    
    if (data.status === 'OK') {
      return {
        success: true,
        suggestions: data.predictions.map((prediction: any) => ({
          description: prediction.description,
          placeId: prediction.place_id,
        })),
      };
    } else {
      return {
        success: false,
        error: 'No se encontraron sugerencias',
      };
    }
  } catch (error) {
    return {
      success: false,
      error: 'Error al obtener sugerencias',
    };
  }
};

// Función para generar URL de Google Maps
export const generateMapsUrl = (address: string) => {
  const encodedAddress = encodeURIComponent(address);
  return `https://www.google.com/maps/search/?api=1&query=${encodedAddress}`;
};

// Función para abrir Google Maps en el navegador/app
export const openInGoogleMaps = (address: string) => {
  const url = generateMapsUrl(address);
  // En React Native, usarías Linking.openURL(url)
  console.log('Abrir en Google Maps:', url);
  return url;
};
