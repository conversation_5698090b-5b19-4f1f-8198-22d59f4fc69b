<?php
/**
 * Script para ejecutar la base de datos completa de RepuMovil
 * FASE 1: Completar Base de Datos - Paso a Paso Mostaza Merlo
 */

// Mostrar errores para debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>🚀 RepuMovil - Ejecutar Base de Datos Completa</h1>";
echo "<h2>📋 FASE 1: Completar Base de Datos</h2>";

// Configuración de la base de datos
$host = 'localhost';
$dbname = 'autoconnect_db';
$username = 'root';
$password = '';

try {
    // Conectar a MySQL (sin especificar base de datos)
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "<p>✅ Conexión a MySQL exitosa</p>";

    // Leer el archivo SQL
    $sqlFile = '../db/autoconnect_db.sql';

    if (!file_exists($sqlFile)) {
        throw new Exception("No se encontró el archivo SQL: $sqlFile");
    }

    $sql = file_get_contents($sqlFile);

    if ($sql === false) {
        throw new Exception("No se pudo leer el archivo SQL");
    }

    echo "<p>✅ Archivo SQL leído correctamente</p>";

    // Limpiar el SQL y dividir en statements
    $sql = preg_replace('/--.*$/m', '', $sql); // Remover comentarios
    $sql = preg_replace('/\/\*.*?\*\//s', '', $sql); // Remover comentarios multilinea

    // Dividir por punto y coma, pero mantener statements completos
    $statements = [];
    $current_statement = '';
    $lines = explode("\n", $sql);

    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line)) continue;

        $current_statement .= $line . "\n";

        // Si la línea termina con ; y no está dentro de comillas
        if (substr($line, -1) === ';') {
            $stmt = trim($current_statement);
            if (!empty($stmt)) {
                $statements[] = $stmt;
            }
            $current_statement = '';
        }
    }

    echo "<p>📊 Se encontraron " . count($statements) . " statements SQL</p>";

    // Ejecutar cada statement
    $executed = 0;
    $errors = 0;

    foreach ($statements as $index => $statement) {
        try {
            echo "<p style='color: blue;'>🔄 Ejecutando statement " . ($index + 1) . "...</p>";

            $result = $pdo->exec($statement);
            $executed++;

            // Mostrar progreso para statements importantes
            if (stripos($statement, 'CREATE DATABASE') !== false) {
                echo "<p style='color: green;'>✅ Base de datos creada/verificada</p>";
            } elseif (stripos($statement, 'USE ') !== false) {
                echo "<p style='color: green;'>✅ Base de datos seleccionada</p>";
            } elseif (stripos($statement, 'CREATE TABLE') !== false) {
                preg_match('/CREATE TABLE.*?(\w+)/i', $statement, $matches);
                $tableName = isset($matches[1]) ? $matches[1] : 'desconocida';
                echo "<p style='color: green;'>✅ Tabla '$tableName' creada/verificada</p>";
            } elseif (stripos($statement, 'INSERT INTO') !== false) {
                preg_match('/INSERT INTO\s+(\w+)/i', $statement, $matches);
                $tableName = isset($matches[1]) ? $matches[1] : 'desconocida';
                echo "<p style='color: green;'>✅ Datos insertados en '$tableName' ($result filas afectadas)</p>";
            }

        } catch (PDOException $e) {
            $errors++;
            echo "<p style='color: red;'>❌ Error en statement " . ($index + 1) . ": " . $e->getMessage() . "</p>";
            echo "<p style='color: orange;'>📝 Statement que falló: " . substr($statement, 0, 200) . "...</p>";
            // Continuar con el siguiente statement
        }
    }

    echo "<hr>";
    echo "<h3>📊 Resumen de Ejecución:</h3>";
    echo "<p>✅ Statements ejecutados exitosamente: $executed</p>";
    echo "<p>❌ Errores encontrados: $errors</p>";

    if ($errors == 0) {
        echo "<p style='color: green; font-weight: bold;'>🎉 ¡Base de datos creada completamente!</p>";
    } else {
        echo "<p style='color: orange; font-weight: bold;'>⚠️ Base de datos creada con algunos errores menores</p>";
    }

    // Verificar las tablas creadas
    echo "<hr>";
    echo "<h3>🔍 Verificación de Tablas:</h3>";

    $pdo->exec("USE $dbname");

    $tables = ['roles', 'users', 'workshops', 'suppliers', 'repuestos', 'carrito', 'pedidos', 'pedido_detalles'];

    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            echo "<p>✅ Tabla '$table': {$result['count']} registros</p>";
        } catch (PDOException $e) {
            echo "<p>❌ Error en tabla '$table': " . $e->getMessage() . "</p>";
        }
    }

    echo "<hr>";
    echo "<h3>🎯 Próximos Pasos:</h3>";
    echo "<ul>";
    echo "<li>✅ FASE 1 COMPLETADA: Base de datos lista</li>";
    echo "<li>🔄 FASE 2: Crear APIs para login y repuestos</li>";
    echo "<li>🔄 FASE 3: Implementar frontend</li>";
    echo "<li>🔄 FASE 4: Generar APK</li>";
    echo "</ul>";

    echo "<p><a href='index.php' style='background: #FF6B35; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🏠 Volver al Inicio</a></p>";

} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    echo "<p>💡 Asegúrate de que XAMPP esté ejecutándose y MySQL esté activo</p>";
}
?>
