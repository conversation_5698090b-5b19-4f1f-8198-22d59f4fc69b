-- Crear la base de datos
CREATE DATABASE IF NOT EXISTS autoconnect_db;

-- Usar la base de datos
USE autoconnect_db;

-- <PERSON>rear tabla de roles
CREATE TABLE IF NOT EXISTS roles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    description VARCHAR(255)
);

-- Crear tabla de usuarios
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    role_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    FOREIGN KEY (role_id) REFERENCES roles(id)
);

-- Crear tabla de talleres
CREATE TABLE IF NOT EXISTS workshops (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    location VARCHAR(255),
    phone VARCHAR(20),
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Crear tabla de proveedores
CREATE TABLE IF NOT EXISTS suppliers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    location VARCHAR(255),
    phone VARCHAR(20),
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Insertar roles
INSERT INTO roles (name, description) VALUES
('admin', 'Administrador del sistema'),
('workshop', 'Usuario de taller mecánico'),
('supplier', 'Proveedor de repuestos');

-- Insertar usuario administrador
-- Contraseña: admin123
INSERT IGNORE INTO users (username, email, password, role_id) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 1);

-- Insertar usuario de taller de ejemplo
-- Contraseña: taller123
INSERT IGNORE INTO users (username, email, password, role_id) VALUES
('taller1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 2);

-- Insertar datos del taller de ejemplo
INSERT INTO workshops (user_id, name, location, phone, description) VALUES
(2, 'Taller Mecánico Ejemplo', 'Calle Ejemplo 123, Ciudad', '************', 'Taller especializado en reparación de motores y sistemas de frenos');

-- Insertar usuario de proveedor de ejemplo
-- Contraseña: proveedor123
INSERT IGNORE INTO users (username, email, password, role_id) VALUES
('proveedor1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 3);

-- Insertar datos del proveedor de ejemplo
INSERT INTO suppliers (user_id, name, location, phone, description) VALUES
(3, 'Repuestos Ejemplo S.A.', 'Avenida Repuestos 456, Ciudad', '************', 'Proveedor de repuestos originales y alternativos para todas las marcas');

-- Crear tabla de repuestos
CREATE TABLE IF NOT EXISTS repuestos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    supplier_id INT NOT NULL,
    nombre VARCHAR(255) NOT NULL,
    descripcion TEXT,
    categoria VARCHAR(100) NOT NULL,
    marca VARCHAR(100),
    modelo VARCHAR(100),
    precio DECIMAL(10,2) NOT NULL,
    stock INT DEFAULT 0,
    imagen_url VARCHAR(500),
    codigo_producto VARCHAR(100),
    estado ENUM('disponible', 'agotado', 'descontinuado') DEFAULT 'disponible',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE CASCADE
);

-- Crear tabla de carrito
CREATE TABLE IF NOT EXISTS carrito (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    repuesto_id INT NOT NULL,
    cantidad INT NOT NULL DEFAULT 1,
    precio_unitario DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (repuesto_id) REFERENCES repuestos(id) ON DELETE CASCADE
);

-- Crear tabla de pedidos
CREATE TABLE IF NOT EXISTS pedidos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    supplier_id INT NOT NULL,
    total DECIMAL(10,2) NOT NULL,
    estado ENUM('pendiente', 'confirmado', 'en_preparacion', 'enviado', 'entregado', 'cancelado') DEFAULT 'pendiente',
    metodo_pago VARCHAR(50) NOT NULL,
    direccion_entrega TEXT NOT NULL,
    telefono_contacto VARCHAR(20),
    notas TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE CASCADE
);

-- Crear tabla de detalle de pedidos
CREATE TABLE IF NOT EXISTS pedido_detalles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    pedido_id INT NOT NULL,
    repuesto_id INT NOT NULL,
    cantidad INT NOT NULL,
    precio_unitario DECIMAL(10,2) NOT NULL,
    subtotal DECIMAL(10,2) NOT NULL,
    FOREIGN KEY (pedido_id) REFERENCES pedidos(id) ON DELETE CASCADE,
    FOREIGN KEY (repuesto_id) REFERENCES repuestos(id) ON DELETE CASCADE
);

-- Crear tabla de categorías
CREATE TABLE IF NOT EXISTS categorias (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nombre VARCHAR(100) NOT NULL,
    descripcion TEXT,
    icono VARCHAR(50),
    orden INT DEFAULT 0,
    activa BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insertar categorías principales (nombres normales)
INSERT IGNORE INTO categorias (nombre, descripcion, icono, orden) VALUES
('Motor y Combustible', 'Repuestos para motor, combustible y sistema de inyección', 'fas fa-engine', 1),
('Frenos y Suspensión', 'Pastillas, discos, amortiguadores y componentes de suspensión', 'fas fa-car-brake', 2),
('Sistema Eléctrico', 'Baterías, alternadores, faros y componentes eléctricos', 'fas fa-bolt', 3),
('Filtros y Aceites', 'Filtros de aceite, aire, combustible y lubricantes', 'fas fa-filter', 4),
('Neumáticos y Llantas', 'Neumáticos, llantas y accesorios de ruedas', 'fas fa-tire', 5),
('Carrocería y Cristales', 'Espejos, parabrisas, manijas y accesorios de carrocería', 'fas fa-car-side', 6),
('Climatización', 'Aire acondicionado, calefacción y ventilación', 'fas fa-temperature-low', 7),
('Transmisión', 'Embragues, cajas de cambio y componentes de transmisión', 'fas fa-cogs', 8);

-- Insertar repuestos categorizados (50 productos - 10 por categoría)

-- CATEGORÍA 1: Motor y Combustible (10 productos)
INSERT INTO repuestos (supplier_id, nombre, descripcion, categoria, marca, modelo, precio, stock, codigo_producto) VALUES
(1, 'Bujías NGK Iridio', 'Set de 4 bujías de encendido iridio para mayor durabilidad', 'Motor y Combustible', 'NGK', 'Iridio IX', 45.00, 25, 'MOT001'),
(1, 'Correa de Distribución Gates', 'Correa de distribución reforzada con tensor incluido', 'Motor y Combustible', 'Gates', 'PowerGrip', 85.00, 18, 'MOT002'),
(1, 'Bomba de Combustible Bosch', 'Bomba eléctrica de combustible para inyección', 'Motor y Combustible', 'Bosch', 'Universal', 320.00, 12, 'MOT003'),
(1, 'Termostato Wahler', 'Termostato de refrigeración 82°C', 'Motor y Combustible', 'Wahler', '82°C', 28.50, 30, 'MOT004'),
(1, 'Radiador Valeo', 'Radiador de aluminio con tanques plásticos', 'Motor y Combustible', 'Valeo', 'Universal', 220.00, 10, 'MOT005'),
(1, 'Bomba de Agua Graf', 'Bomba de agua con junta incluida', 'Motor y Combustible', 'Graf', 'Universal', 95.00, 15, 'MOT006'),
(1, 'Sensor de Oxígeno Bosch', 'Sonda lambda universal pre-catalizador', 'Motor y Combustible', 'Bosch', 'LSU 4.9', 180.00, 8, 'MOT007'),
(1, 'Válvula EGR Pierburg', 'Válvula de recirculación de gases de escape', 'Motor y Combustible', 'Pierburg', 'Universal', 250.00, 6, 'MOT008'),
(1, 'Turbocompresor Garrett', 'Turbo remanufacturado con garantía', 'Motor y Combustible', 'Garrett', 'GT1749V', 850.00, 3, 'MOT009'),
(1, 'Kit de Distribución INA', 'Kit completo con correa, tensor y polea', 'Motor y Combustible', 'INA', 'Complete Kit', 165.00, 12, 'MOT010'),

-- CATEGORÍA 2: Frenos y Suspensión (10 productos)
(1, 'Pastillas Freno Brembo', 'Pastillas cerámicas delanteras alta performance', 'Frenos y Suspensión', 'Brembo', 'Ceramic', 120.00, 25, 'FRE001'),
(1, 'Discos de Freno Zimmermann', 'Discos ventilados delanteros 280mm', 'Frenos y Suspensión', 'Zimmermann', 'Sport', 95.00, 20, 'FRE002'),
(1, 'Amortiguador Monroe', 'Amortiguador trasero gas-oil', 'Frenos y Suspensión', 'Monroe', 'Gas-Matic', 85.00, 30, 'FRE003'),
(1, 'Líquido de Frenos DOT4', 'Líquido de frenos sintético 500ml', 'Frenos y Suspensión', 'Castrol', 'DOT 4', 18.50, 50, 'FRE004'),
(1, 'Pastillas Traseras ATE', 'Pastillas de freno traseras orgánicas', 'Frenos y Suspensión', 'ATE', 'Ceramic', 75.00, 22, 'FRE005'),
(1, 'Resorte Espiral Eibach', 'Resorte delantero progresivo', 'Frenos y Suspensión', 'Eibach', 'Pro-Kit', 65.00, 16, 'FRE006'),
(1, 'Buje de Suspensión Lemförder', 'Buje de brazo inferior poliuretano', 'Frenos y Suspensión', 'Lemförder', 'HD', 32.00, 40, 'FRE007'),
(1, 'Cilindro de Freno TRW', 'Cilindro maestro de frenos', 'Frenos y Suspensión', 'TRW', 'Master', 145.00, 8, 'FRE008'),
(1, 'Estabilizador Febi', 'Barra estabilizadora delantera', 'Frenos y Suspensión', 'Febi', 'Bilstein', 78.00, 12, 'FRE009'),
(1, 'Rótula de Suspensión Moog', 'Rótula inferior con grasa incluida', 'Frenos y Suspensión', 'Moog', 'Problem Solver', 42.00, 35, 'FRE010'),

-- CATEGORÍA 3: Sistema Eléctrico (10 productos)
(1, 'Batería Bosch S4', 'Batería 12V 60Ah libre de mantenimiento', 'Sistema Eléctrico', 'Bosch', 'S4 Silver', 180.00, 15, 'ELE001'),
(1, 'Alternador Valeo', 'Alternador 12V 90A remanufacturado', 'Sistema Eléctrico', 'Valeo', '90A', 280.00, 8, 'ELE002'),
(1, 'Faro Halógeno Hella', 'Faro delantero H4 con regulación', 'Sistema Eléctrico', 'Hella', 'H4', 65.00, 20, 'ELE003'),
(1, 'Motor de Arranque Bosch', 'Burro de arranque remanufacturado', 'Sistema Eléctrico', 'Bosch', '1.4kW', 320.00, 6, 'ELE004'),
(1, 'Lámpara LED Philips', 'Bombilla LED H7 6000K', 'Sistema Eléctrico', 'Philips', 'X-treme Vision', 45.00, 30, 'ELE005'),
(1, 'Fusibles Littelfuse', 'Set de fusibles mini 10-30A', 'Sistema Eléctrico', 'Littelfuse', 'Mini Blade', 12.50, 50, 'ELE006'),
(1, 'Relé Hella', 'Relé de 4 pines 12V 40A', 'Sistema Eléctrico', 'Hella', '4RA003', 18.00, 40, 'ELE007'),
(1, 'Bobina de Encendido NGK', 'Bobina individual con conector', 'Sistema Eléctrico', 'NGK', 'U5015', 85.00, 25, 'ELE008'),
(1, 'Sensor ABS Bosch', 'Sensor de velocidad rueda delantera', 'Sistema Eléctrico', 'Bosch', 'ABS', 95.00, 18, 'ELE009'),
(1, 'Cableado Bujías NGK', 'Juego cables alta tensión silicona', 'Sistema Eléctrico', 'NGK', 'RC-SE', 55.00, 22, 'ELE010'),

-- CATEGORÍA 4: Filtros y Aceites (10 productos)
(1, 'Filtro Aceite Mann', 'Filtro de aceite con junta incluida', 'Filtros y Aceites', 'Mann', 'W712/75', 25.50, 50, 'FIL001'),
(1, 'Aceite Castrol GTX', 'Aceite mineral 20W-50 bidón 4L', 'Filtros y Aceites', 'Castrol', 'GTX 20W-50', 35.00, 30, 'FIL002'),
(1, 'Filtro Aire K&N', 'Filtro de aire deportivo lavable', 'Filtros y Aceites', 'K&N', 'Performance', 85.00, 15, 'FIL003'),
(1, 'Aceite Mobil 1', 'Aceite sintético 5W-30 bidón 4L', 'Filtros y Aceites', 'Mobil', '5W-30 Synthetic', 65.00, 25, 'FIL004'),
(1, 'Filtro Combustible Bosch', 'Filtro de nafta con conexiones', 'Filtros y Aceites', 'Bosch', 'F026403', 28.00, 35, 'FIL005'),
(1, 'Filtro Habitáculo Mann', 'Filtro antipolen con carbón activado', 'Filtros y Aceites', 'Mann', 'CUK2545', 32.00, 40, 'FIL006'),
(1, 'Aceite Caja Shell', 'Aceite transmisión manual 75W-90', 'Filtros y Aceites', 'Shell', 'Spirax S5', 45.00, 20, 'FIL007'),
(1, 'Filtro Hidráulico Mahle', 'Filtro dirección hidráulica', 'Filtros y Aceites', 'Mahle', 'HX163D', 38.50, 25, 'FIL008'),
(1, 'Refrigerante Prestone', 'Anticongelante concentrado 1L', 'Filtros y Aceites', 'Prestone', 'Extended Life', 22.00, 45, 'FIL009'),
(1, 'Aditivo Liqui Moly', 'Limpiador sistema combustible', 'Filtros y Aceites', 'Liqui Moly', 'Jectron', 18.50, 30, 'FIL010'),

-- CATEGORÍA 5: Neumáticos y Llantas (10 productos)
(1, 'Neumático Michelin Energy', 'Neumático 185/65R15 88H', 'Neumáticos y Llantas', 'Michelin', 'Energy Saver', 95.00, 35, 'NEU001'),
(1, 'Neumático Bridgestone Turanza', 'Neumático 195/60R16 89H', 'Neumáticos y Llantas', 'Bridgestone', 'Turanza T001', 110.00, 28, 'NEU002'),
(1, 'Llanta Aleación 15"', 'Llanta aleación 15x6 4x100', 'Neumáticos y Llantas', 'OZ Racing', 'Alleggerita', 180.00, 12, 'NEU003'),
(1, 'Neumático Continental Premium', 'Neumático 205/55R16 91V', 'Neumáticos y Llantas', 'Continental', 'PremiumContact', 125.00, 25, 'NEU004'),
(1, 'Válvula Neumático', 'Válvula tubeless TR413', 'Neumáticos y Llantas', 'Schrader', 'TR413', 3.50, 100, 'NEU005'),
(1, 'Neumático Pirelli P7', 'Neumático 225/45R17 94W', 'Neumáticos y Llantas', 'Pirelli', 'Cinturato P7', 145.00, 20, 'NEU006'),
(1, 'Tapa de Llanta Cromada', 'Tapa centro llanta cromada 60mm', 'Neumáticos y Llantas', 'Universal', 'Chrome', 8.50, 50, 'NEU007'),
(1, 'Neumático Yokohama BluEarth', 'Neumático ecológico 175/70R14', 'Neumáticos y Llantas', 'Yokohama', 'BluEarth-A', 85.00, 30, 'NEU008'),
(1, 'Sensor TPMS', 'Sensor presión neumáticos 433MHz', 'Neumáticos y Llantas', 'Continental', 'VDO REDI', 65.00, 15, 'NEU009'),
(1, 'Neumático Goodyear EfficientGrip', 'Neumático 215/60R16 95H', 'Neumáticos y Llantas', 'Goodyear', 'EfficientGrip', 115.00, 22, 'NEU010'),

-- CATEGORÍA 6: Carrocería y Cristales (10 productos)
(1, 'Espejo Retrovisor Izquierdo', 'Espejo eléctrico con calefacción', 'Carrocería y Cristales', 'TYC', 'Electric Heat', 85.00, 18, 'CAR001'),
(1, 'Parabrisas Laminado', 'Parabrisas con sensor lluvia', 'Carrocería y Cristales', 'Pilkington', 'Rain Sensor', 220.00, 8, 'CAR002'),
(1, 'Manija Puerta Exterior', 'Manija cromada lado conductor', 'Carrocería y Cristales', 'Dorman', 'Chrome', 45.00, 25, 'CAR003'),
(1, 'Faro Trasero LED', 'Grupo óptico trasero LED', 'Carrocería y Cristales', 'Valeo', 'LED Matrix', 125.00, 15, 'CAR004'),
(1, 'Paragolpes Delantero', 'Paragolpes plástico sin pintar', 'Carrocería y Cristales', 'Van Wezel', 'Unpainted', 180.00, 6, 'CAR005'),
(1, 'Cristal Puerta Trasera', 'Cristal templado tintado', 'Carrocería y Cristales', 'Sekurit', 'Tinted', 95.00, 12, 'CAR006'),
(1, 'Moldura Lateral', 'Moldura protección lateral negra', 'Carrocería y Cristales', '3M', 'Protection Film', 35.00, 30, 'CAR007'),
(1, 'Rejilla Parrilla', 'Rejilla delantera cromada', 'Carrocería y Cristales', 'Blic', 'Chrome Grille', 75.00, 10, 'CAR008'),
(1, 'Spoiler Trasero', 'Spoiler aerodinámico fibra', 'Carrocería y Cristales', 'Rieger', 'Carbon Look', 165.00, 8, 'CAR009'),
(1, 'Adhesivo Parabrisas', 'Pegamento poliuretano 310ml', 'Carrocería y Cristales', 'Sikaflex', '221', 28.50, 40, 'CAR010'),

-- CATEGORÍA 7: Climatización (10 productos)
(1, 'Compresor A/C Denso', 'Compresor aire acondicionado remanufacturado', 'Climatización', 'Denso', '10S17C', 380.00, 6, 'CLI001'),
(1, 'Filtro Habitáculo Bosch', 'Filtro cabina con carbón activado', 'Climatización', 'Bosch', 'Active Carbon', 35.00, 40, 'CLI002'),
(1, 'Condensador A/C Valeo', 'Condensador aluminio con secador', 'Climatización', 'Valeo', 'ThermoKing', 185.00, 10, 'CLI003'),
(1, 'Gas Refrigerante R134a', 'Gas refrigerante lata 340g', 'Climatización', 'Honeywell', 'R134a', 25.00, 30, 'CLI004'),
(1, 'Evaporador A/C Nissens', 'Evaporador aluminio multicapa', 'Climatización', 'Nissens', 'MultiLayer', 165.00, 8, 'CLI005'),
(1, 'Ventilador Radiador Febi', 'Electroventilador con resistencia', 'Climatización', 'Febi', 'Bilstein', 125.00, 12, 'CLI006'),
(1, 'Termostato A/C Wahler', 'Termostato climatización 87°C', 'Climatización', 'Wahler', '87°C', 45.00, 25, 'CLI007'),
(1, 'Manguera A/C Gates', 'Manguera alta presión con racores', 'Climatización', 'Gates', 'Green Stripe', 85.00, 15, 'CLI008'),
(1, 'Sensor Temperatura NTC', 'Sensor temperatura exterior', 'Climatización', 'Hella', 'NTC', 32.00, 35, 'CLI009'),
(1, 'Aceite Compresor A/C', 'Aceite PAG 46 botella 250ml', 'Climatización', 'Denso', 'PAG 46', 28.50, 20, 'CLI010'),

-- CATEGORÍA 8: Transmisión (10 productos)
(1, 'Kit Embrague Sachs', 'Kit completo disco, plato y collarín', 'Transmisión', 'Sachs', 'Performance', 285.00, 8, 'TRA001'),
(1, 'Aceite Transmisión Castrol', 'Aceite ATF Dexron III 1L', 'Transmisión', 'Castrol', 'Transmax Z', 18.50, 35, 'TRA002'),
(1, 'Junta Homocinética GKN', 'Junta CV lado rueda con fuelle', 'Transmisión', 'GKN', 'Lobro', 95.00, 20, 'TRA003'),
(1, 'Filtro Transmisión Mann', 'Filtro caja automática con junta', 'Transmisión', 'Mann', 'H50002', 45.00, 25, 'TRA004'),
(1, 'Semieje Completo GSP', 'Semieje con juntas homocinéticas', 'Transmisión', 'GSP', 'Complete', 165.00, 12, 'TRA005'),
(1, 'Cilindro Embrague LuK', 'Cilindro maestro embrague', 'Transmisión', 'LuK', 'Master', 85.00, 15, 'TRA006'),
(1, 'Diferencial Remanufacturado', 'Diferencial trasero reacondicionado', 'Transmisión', 'Eaton', 'Reman', 650.00, 3, 'TRA007'),
(1, 'Cable Embrague Cofle', 'Cable accionamiento embrague', 'Transmisión', 'Cofle', 'Heavy Duty', 35.00, 30, 'TRA008'),
(1, 'Soporte Motor Corteco', 'Soporte motor hidráulico', 'Transmisión', 'Corteco', 'Hydraulic', 75.00, 18, 'TRA009'),
(1, 'Volante Motor Valeo', 'Volante bimasa remanufacturado', 'Transmisión', 'Valeo', 'DMF', 485.00, 4, 'TRA010');
