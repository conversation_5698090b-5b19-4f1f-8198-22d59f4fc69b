<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RepuMovil - Sistema Completo de Delivery 🚚</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #FF6B35;
            --secondary-color: #FFA500;
            --dark-color: #2c3e50;
            --light-color: #f8f9fa;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 50px;
            animation: fadeInDown 1s ease-out;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .logo {
            font-size: 4rem;
            margin-bottom: 20px;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-20px); }
            60% { transform: translateY(-10px); }
        }

        .title {
            font-size: 3.5rem;
            font-weight: bold;
            margin-bottom: 15px;
            text-shadow: 3px 3px 6px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.3rem;
            margin-bottom: 30px;
            opacity: 0.9;
            line-height: 1.6;
        }

        .version-badge {
            display: inline-block;
            background: rgba(255,255,255,0.2);
            padding: 8px 20px;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: bold;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
        }

        .demos-section {
            margin: 60px 0;
            animation: fadeInUp 1s ease-out 0.6s both;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .section-title {
            text-align: center;
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 40px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .demos-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
        }

        .demo-card {
            background: white;
            color: var(--dark-color);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            text-decoration: none;
            display: block;
        }

        .demo-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 25px 50px rgba(0,0,0,0.3);
            color: var(--dark-color);
        }

        .demo-icon {
            font-size: 2.5rem;
            color: var(--primary-color);
            margin-bottom: 20px;
        }

        .demo-title {
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 15px;
            color: var(--primary-color);
        }

        .demo-description {
            line-height: 1.6;
            margin-bottom: 20px;
            color: #666;
        }

        .demo-features {
            list-style: none;
            margin-bottom: 20px;
        }

        .demo-features li {
            padding: 5px 0;
            color: #666;
            font-size: 0.9rem;
        }

        .demo-features li:before {
            content: "✓ ";
            color: var(--success-color);
            font-weight: bold;
        }

        .demo-button {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: bold;
            text-align: center;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            width: 100%;
        }

        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(255, 107, 53, 0.4);
        }

        .stats-section {
            background: rgba(255,255,255,0.1);
            padding: 40px;
            border-radius: 20px;
            margin: 50px 0;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255,255,255,0.2);
            animation: fadeInUp 1s ease-out 0.9s both;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            text-align: center;
        }

        .stat-item {
            padding: 20px;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #ffd700;
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .footer {
            text-align: center;
            margin-top: 60px;
            padding: 30px;
            border-top: 1px solid rgba(255,255,255,0.2);
            animation: fadeIn 1s ease-out 1.2s both;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .footer-text {
            font-size: 1.1rem;
            margin-bottom: 20px;
        }

        .tech-stack {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .tech-item {
            background: rgba(255,255,255,0.1);
            padding: 8px 16px;
            border-radius: 15px;
            font-size: 0.9rem;
            backdrop-filter: blur(10px);
        }

        @media (max-width: 768px) {
            .title {
                font-size: 2.5rem;
            }
            
            .demos-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">🚚📱🔧</div>
            <h1 class="title">RepuMovil</h1>
            <p class="subtitle">
                Sistema Completo de Delivery de Repuestos Automotrices<br>
                <strong>¡Los 10 Pasos Implementados Completamente!</strong>
            </p>
            <div class="version-badge">
                <i class="fas fa-rocket"></i>
                Versión 1.0 - Sistema Completo
            </div>
        </div>

        <!-- Estadísticas del Sistema -->
        <div class="stats-section">
            <h2 class="section-title">📈 Sistema Completamente Implementado</h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">10</div>
                    <div class="stat-label">Pasos Completados</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">15+</div>
                    <div class="stat-label">Páginas Funcionales</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">8</div>
                    <div class="stat-label">APIs Completas</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">100%</div>
                    <div class="stat-label">Responsive Design</div>
                </div>
            </div>
        </div>

        <!-- Demos Disponibles -->
        <div class="demos-section">
            <h2 class="section-title">🎯 Demos Interactivos - Todos los Pasos</h2>
            <div class="demos-grid">
                <!-- Demo Delivery -->
                <a href="delivery-dashboard.html" class="demo-card">
                    <div class="demo-icon">🏍️</div>
                    <h3 class="demo-title">Dashboard Delivery (Pasos 1-8)</h3>
                    <p class="demo-description">
                        Experiencia completa del repartidor con todas las funcionalidades implementadas.
                    </p>
                    <ul class="demo-features">
                        <li>Notificaciones push en tiempo real</li>
                        <li>Tracking GPS automático</li>
                        <li>Gestión de estados de pedidos</li>
                        <li>Sistema de calificaciones</li>
                    </ul>
                    <button class="demo-button">
                        <i class="fas fa-play"></i>
                        Probar Demo Delivery
                    </button>
                </a>

                <!-- Demo Cliente -->
                <a href="tracking-pedido.php" class="demo-card">
                    <div class="demo-icon">📱</div>
                    <h3 class="demo-title">Tracking Cliente (Pasos 6-9)</h3>
                    <p class="demo-description">
                        Seguimiento en vivo del pedido desde la perspectiva del cliente.
                    </p>
                    <ul class="demo-features">
                        <li>Mapa interactivo en tiempo real</li>
                        <li>Progreso visual del pedido</li>
                        <li>ETA dinámico actualizado</li>
                        <li>Sistema de calificación</li>
                    </ul>
                    <button class="demo-button">
                        <i class="fas fa-map-marked-alt"></i>
                        Ver Tracking Cliente
                    </button>
                </a>

                <!-- Demo Proveedor -->
                <a href="proveedor-estados.php" class="demo-card">
                    <div class="demo-icon">🏪</div>
                    <h3 class="demo-title">Panel Proveedor (Paso 8)</h3>
                    <p class="demo-description">
                        Dashboard completo para gestión de pedidos y deliveries.
                    </p>
                    <ul class="demo-features">
                        <li>Vista de todos los pedidos activos</li>
                        <li>Estadísticas en tiempo real</li>
                        <li>Filtros por estado</li>
                        <li>Timeline detallado</li>
                    </ul>
                    <button class="demo-button">
                        <i class="fas fa-store"></i>
                        Abrir Panel Proveedor
                    </button>
                </a>

                <!-- Demo Admin -->
                <a href="admin-dashboard.php" class="demo-card">
                    <div class="demo-icon">📊</div>
                    <h3 class="demo-title">Dashboard Admin (Paso 10)</h3>
                    <p class="demo-description">
                        Centro de control administrativo con métricas avanzadas.
                    </p>
                    <ul class="demo-features">
                        <li>KPIs principales del sistema</li>
                        <li>Gráficos interactivos</li>
                        <li>Gestión de usuarios</li>
                        <li>Reportes detallados</li>
                    </ul>
                    <button class="demo-button">
                        <i class="fas fa-chart-line"></i>
                        Ver Dashboard Admin
                    </button>
                </a>

                <!-- Demo Calificaciones -->
                <a href="calificar-delivery.php" class="demo-card">
                    <div class="demo-icon">⭐</div>
                    <h3 class="demo-title">Sistema Calificaciones (Paso 9)</h3>
                    <p class="demo-description">
                        Interfaz completa de calificación y feedback del cliente.
                    </p>
                    <ul class="demo-features">
                        <li>Sistema de 5 estrellas interactivo</li>
                        <li>Comentarios y sugerencias</li>
                        <li>Tags predefinidos</li>
                        <li>Respuestas de deliveries</li>
                    </ul>
                    <button class="demo-button">
                        <i class="fas fa-star"></i>
                        Calificar Delivery
                    </button>
                </a>

                <!-- Demo Gestión Usuarios -->
                <a href="admin-usuarios.php" class="demo-card">
                    <div class="demo-icon">👥</div>
                    <h3 class="demo-title">Gestión Usuarios (Paso 10)</h3>
                    <p class="demo-description">
                        Panel administrativo para gestión completa de usuarios.
                    </p>
                    <ul class="demo-features">
                        <li>Lista completa de usuarios</li>
                        <li>Filtros avanzados</li>
                        <li>Acciones de administración</li>
                        <li>Estadísticas por tipo</li>
                    </ul>
                    <button class="demo-button">
                        <i class="fas fa-users"></i>
                        Gestionar Usuarios
                    </button>
                </a>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p class="footer-text">
                <strong>🚀 RepuMovil - Sistema Completo Implementado</strong><br>
                Todos los 10 pasos desarrollados con las mejores tecnologías
            </p>
            
            <div class="tech-stack">
                <div class="tech-item">📱 React Native</div>
                <div class="tech-item">🌐 PHP + MySQL</div>
                <div class="tech-item">🗺️ Leaflet Maps</div>
                <div class="tech-item">📊 Chart.js</div>
                <div class="tech-item">🔔 Push Notifications</div>
                <div class="tech-item">📍 GPS Tracking</div>
                <div class="tech-item">⭐ Rating System</div>
                <div class="tech-item">📈 Analytics</div>
            </div>
        </div>
    </div>
</body>
</html>
