<?php
session_start();
require_once 'config.php';

$error_message = '';
$success_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $pdo = connectDB();
        $pdo->beginTransaction();
        
        // Validar que aceptó los términos
        if (!isset($_POST['acepta_terminos']) || $_POST['acepta_terminos'] !== '1') {
            throw new Exception('Debes aceptar los términos y condiciones para continuar');
        }
        
        // Validar datos básicos
        $email = trim($_POST['email']);
        $password = $_POST['password'];
        $confirm_password = $_POST['confirm_password'];
        
        if (empty($email) || empty($password)) {
            throw new Exception('Email y contraseña son obligatorios');
        }
        
        if ($password !== $confirm_password) {
            throw new Exception('Las contraseñas no coinciden');
        }
        
        if (strlen($password) < 6) {
            throw new Exception('La contraseña debe tener al menos 6 caracteres');
        }
        
        // Verificar si el email ya existe
        $stmt = $pdo->prepare("SELECT id FROM usuarios WHERE email = ?");
        $stmt->execute([$email]);
        if ($stmt->fetch()) {
            throw new Exception('Este email ya está registrado');
        }
        
        // Validar datos personales
        $nombre_completo = trim($_POST['nombre_completo']);
        $dni = preg_replace('/[^0-9]/', '', $_POST['dni']);
        $fecha_nacimiento = $_POST['fecha_nacimiento'];
        $telefono = trim($_POST['telefono']);
        $direccion = trim($_POST['direccion']);
        
        if (empty($nombre_completo) || empty($dni) || empty($fecha_nacimiento) || empty($telefono) || empty($direccion)) {
            throw new Exception('Todos los datos personales son obligatorios');
        }
        
        if (!validarDNI($dni)) {
            throw new Exception('DNI inválido');
        }
        
        // Validar edad (mayor de 18)
        $fecha_nac = new DateTime($fecha_nacimiento);
        $hoy = new DateTime();
        $edad = $hoy->diff($fecha_nac)->y;
        if ($edad < 18) {
            throw new Exception('Debes ser mayor de 18 años para registrarte');
        }
        
        // Validar datos de pago
        $cbu_alias = trim($_POST['cbu_alias']);
        $cuil_cuit = preg_replace('/[^0-9]/', '', $_POST['cuil_cuit'] ?? '');
        
        if (empty($cbu_alias)) {
            throw new Exception('CBU o Alias bancario es obligatorio');
        }
        
        // Validar vehículo
        $tipo_vehiculo = $_POST['tipo_vehiculo'];
        $marca = trim($_POST['marca'] ?? '');
        $modelo = trim($_POST['modelo'] ?? '');
        $patente = trim($_POST['patente'] ?? '');
        $licencia = trim($_POST['licencia'] ?? '');
        
        if (empty($tipo_vehiculo)) {
            throw new Exception('Tipo de vehículo es obligatorio');
        }
        
        if (in_array($tipo_vehiculo, ['moto', 'auto']) && empty($licencia)) {
            throw new Exception('Licencia de conducir es obligatoria para moto/auto');
        }
        
        // Crear usuario
        $password_hash = password_hash($password, PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("
            INSERT INTO usuarios (email, password, estado, fecha_registro) 
            VALUES (?, ?, 'pendiente', NOW())
        ");
        $stmt->execute([$email, $password_hash]);
        $usuario_id = $pdo->lastInsertId();
        
        // Insertar datos personales
        $stmt = $pdo->prepare("
            INSERT INTO datos_personales (usuario_id, nombre_completo, dni, fecha_nacimiento, telefono, direccion_completa) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([$usuario_id, $nombre_completo, $dni, $fecha_nacimiento, $telefono, $direccion]);
        
        // Insertar datos de pago
        $stmt = $pdo->prepare("
            INSERT INTO datos_pago (usuario_id, cbu_alias, cuil_cuit) 
            VALUES (?, ?, ?)
        ");
        $stmt->execute([$usuario_id, $cbu_alias, $cuil_cuit]);
        
        // Insertar vehículo
        $stmt = $pdo->prepare("
            INSERT INTO vehiculos (usuario_id, tipo_vehiculo, marca, modelo, patente, licencia_conducir) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([$usuario_id, $tipo_vehiculo, $marca, $modelo, $patente, $licencia]);
        
        // Insertar declaración jurada
        $stmt = $pdo->prepare("
            INSERT INTO declaraciones_juradas (usuario_id, acepta_terminos, acepta_responsabilidad, acepta_datos_personales, ip_registro, user_agent)
            VALUES (?, 1, 1, 1, ?, ?)
        ");
        $stmt->execute([$usuario_id, obtenerIP(), $_SERVER['HTTP_USER_AGENT'] ?? '']);

        // Procesar uploads de documentos
        $documentos_requeridos = [
            'dni_frente' => true,
            'dni_dorso' => true,
            'selfie_dni' => true,
        ];

        // Agregar documentos condicionales según tipo de vehículo
        if (in_array($tipo_vehiculo, ['moto', 'auto'])) {
            $documentos_requeridos['licencia_foto'] = true;
            $documentos_requeridos['seguro_foto'] = true;
        }

        // Documento opcional
        $documentos_opcionales = ['antecedentes'];

        // Procesar cada documento
        foreach ($documentos_requeridos as $doc_type => $required) {
            if (isset($_FILES[$doc_type]) && $_FILES[$doc_type]['error'] === UPLOAD_ERR_OK) {
                try {
                    $file_info = uploadFile($_FILES[$doc_type], $doc_type, $usuario_id);

                    // Guardar en base de datos
                    $stmt = $pdo->prepare("
                        INSERT INTO documentos (usuario_id, tipo_documento, nombre_archivo, ruta_archivo, tamaño_archivo, tipo_mime)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([
                        $usuario_id,
                        $doc_type,
                        $file_info['nombre_archivo'],
                        $file_info['ruta_archivo'],
                        $file_info['tamaño_archivo'],
                        $file_info['tipo_mime']
                    ]);

                } catch (Exception $upload_error) {
                    throw new Exception("Error al subir $doc_type: " . $upload_error->getMessage());
                }
            } elseif ($required) {
                throw new Exception("El documento $doc_type es obligatorio");
            }
        }

        // Procesar documentos opcionales
        foreach ($documentos_opcionales as $doc_type) {
            if (isset($_FILES[$doc_type]) && $_FILES[$doc_type]['error'] === UPLOAD_ERR_OK) {
                try {
                    $file_info = uploadFile($_FILES[$doc_type], $doc_type, $usuario_id);

                    // Guardar en base de datos
                    $stmt = $pdo->prepare("
                        INSERT INTO documentos (usuario_id, tipo_documento, nombre_archivo, ruta_archivo, tamaño_archivo, tipo_mime)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([
                        $usuario_id,
                        $doc_type,
                        $file_info['nombre_archivo'],
                        $file_info['ruta_archivo'],
                        $file_info['tamaño_archivo'],
                        $file_info['tipo_mime']
                    ]);

                } catch (Exception $upload_error) {
                    // Los documentos opcionales no deberían fallar el registro
                    logActivity("Error al subir documento opcional $doc_type: " . $upload_error->getMessage(), $usuario_id);
                }
            }
        }

        $pdo->commit();

        logActivity("Registro exitoso con documentos", $usuario_id);

        $success_message = "¡Registro exitoso! Tu solicitud y documentos están siendo revisados. Te contactaremos pronto.";
        
    } catch (Exception $e) {
        $pdo->rollBack();
        $error_message = $e->getMessage();
        logActivity("Error en registro: " . $e->getMessage());
    }
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registro de Repartidor - RepuMovil Delivery</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700;800&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-orange: #FF6B35;
            --secondary-orange: #F7931E;
            --primary-red: #E53E3E;
            --secondary-red: #FC8181;
            --gradient-main: linear-gradient(135deg, #FF6B35 0%, #E53E3E 50%, #F7931E 100%);
            --white: #ffffff;
            --dark: #2D3748;
            --light-gray: #F7FAFC;
            --box-shadow: 0 10px 30px rgba(229, 62, 62, 0.2);
            --transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 1rem;
        }

        .registro-header {
            background: var(--gradient-main);
            color: white;
            padding: 2rem;
            border-radius: 20px 20px 0 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .registro-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .logo-icon {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 1.5rem;
            backdrop-filter: blur(10px);
            position: relative;
            z-index: 2;
        }

        .registro-title {
            font-size: 1.8rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
            position: relative;
            z-index: 2;
        }

        .registro-subtitle {
            opacity: 0.9;
            position: relative;
            z-index: 2;
            font-size: 1rem;
            line-height: 1.4;
        }

        .form-container {
            background: white;
            border-radius: 0 0 20px 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .form-content {
            padding: 2rem;
        }

        .section-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--dark);
            margin: 2rem 0 1rem 0;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--primary-red);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .section-title:first-child {
            margin-top: 0;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--dark);
        }

        .required {
            color: var(--primary-red);
        }

        .form-input,
        .form-select,
        .form-textarea {
            width: 100%;
            padding: 0.8rem;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 1rem;
            transition: var(--transition);
            background: var(--light-gray);
        }

        .form-input:focus,
        .form-select:focus,
        .form-textarea:focus {
            outline: none;
            border-color: var(--primary-red);
            background: white;
            box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
        }

        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }

        .declaracion-section {
            background: #fff5f5;
            border: 2px solid var(--primary-red);
            border-radius: 15px;
            padding: 1.5rem;
            margin: 2rem 0;
        }

        .declaracion-title {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--primary-red);
            margin-bottom: 1rem;
            text-align: center;
        }

        .declaracion-text {
            font-size: 0.9rem;
            line-height: 1.6;
            color: var(--dark);
            margin-bottom: 1rem;
        }

        .declaracion-list {
            padding-left: 1.5rem;
            margin-bottom: 1rem;
        }

        .declaracion-list li {
            margin-bottom: 0.5rem;
            line-height: 1.5;
        }

        .checkbox-group {
            display: flex;
            align-items: flex-start;
            gap: 0.5rem;
            margin: 1rem 0;
            padding: 1rem;
            background: white;
            border-radius: 10px;
            border: 2px solid var(--primary-red);
        }

        .checkbox-group input[type="checkbox"] {
            margin-top: 0.2rem;
            transform: scale(1.2);
        }

        .checkbox-group label {
            font-weight: 600;
            color: var(--primary-red);
            cursor: pointer;
        }

        /* Documentos Upload Styles */
        .documentos-container {
            background: #fff8f5;
            border: 2px solid var(--secondary-orange);
            border-radius: 15px;
            padding: 1.5rem;
            margin: 1rem 0;
        }

        .documento-info {
            display: flex;
            align-items: flex-start;
            gap: 0.5rem;
            background: white;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1.5rem;
            border-left: 4px solid var(--primary-orange);
        }

        .documento-info i {
            color: var(--primary-orange);
            margin-top: 0.2rem;
        }

        .documento-info p {
            margin: 0;
            color: var(--dark);
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .file-upload-container {
            position: relative;
            margin-bottom: 1rem;
        }

        .file-input {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
            z-index: 2;
        }

        .file-upload-label {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 1rem;
            border: 2px dashed #e2e8f0;
            border-radius: 10px;
            background: var(--light-gray);
            cursor: pointer;
            transition: var(--transition);
            min-height: 60px;
        }

        .file-upload-label:hover {
            border-color: var(--primary-red);
            background: white;
        }

        .file-upload-label i {
            font-size: 1.2rem;
            color: var(--primary-red);
        }

        .file-upload-label span {
            font-weight: 500;
            color: var(--dark);
        }

        .file-preview {
            margin-top: 0.5rem;
            padding: 0.5rem;
            background: white;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            display: none;
        }

        .file-preview.active {
            display: block;
        }

        .file-preview-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem;
            background: #f8f9fa;
            border-radius: 6px;
        }

        .file-preview-item i {
            color: var(--primary-red);
        }

        .file-preview-name {
            flex: 1;
            font-size: 0.9rem;
            color: var(--dark);
            font-weight: 500;
        }

        .file-preview-size {
            font-size: 0.8rem;
            color: #666;
        }

        .file-remove {
            background: none;
            border: none;
            color: #dc3545;
            cursor: pointer;
            padding: 0.2rem;
            border-radius: 3px;
            transition: var(--transition);
        }

        .file-remove:hover {
            background: #dc3545;
            color: white;
        }

        .form-help {
            display: block;
            font-size: 0.8rem;
            color: #666;
            margin-top: 0.5rem;
            font-style: italic;
        }

        .btn-submit {
            width: 100%;
            padding: 1rem;
            background: var(--gradient-main);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            margin-top: 1rem;
        }

        .btn-submit:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow);
        }

        .btn-submit:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .alert {
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
            font-weight: 500;
        }

        .alert-error {
            background: #fed7d7;
            color: #c53030;
            border: 1px solid #feb2b2;
        }

        .alert-success {
            background: #c6f6d5;
            color: #2f855a;
            border: 1px solid #9ae6b4;
        }

        .back-link {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--primary-red);
            text-decoration: none;
            font-weight: 600;
            margin-bottom: 2rem;
            transition: var(--transition);
        }

        .back-link:hover {
            transform: translateX(-5px);
        }

        @media (max-width: 768px) {
            .container {
                padding: 0 0.5rem;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .registro-header,
            .form-content {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="index.php" class="back-link">
            <i class="fas fa-arrow-left"></i> Volver al inicio
        </a>

        <div class="registro-header">
            <div class="logo-icon">
                <i class="fas fa-motorcycle"></i>
            </div>
            <h1 class="registro-title">Registro de Repartidor Independiente</h1>
            <p class="registro-subtitle">
                Plataforma de Delivery<br>
                Bienvenido. Para formar parte de nuestra red de repartidores, por favor completá el siguiente formulario con tus datos reales. Toda la información será tratada con confidencialidad y utilizada exclusivamente para fines operativos y legales.
            </p>
        </div>

        <div class="form-container">
            <div class="form-content">
                <?php if ($error_message): ?>
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-circle"></i> <?php echo htmlspecialchars($error_message); ?>
                    </div>
                <?php endif; ?>

                <?php if ($success_message): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($success_message); ?>
                    </div>
                <?php else: ?>

                <form method="POST" enctype="multipart/form-data" id="registroForm">
                    <!-- Datos de Acceso -->
                    <div class="section-title">
                        <i class="fas fa-key"></i> Datos de Acceso
                    </div>
                    
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="email">Correo Electrónico <span class="required">*</span></label>
                            <input type="email" id="email" name="email" class="form-input" required 
                                   value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>">
                        </div>
                        
                        <div class="form-group">
                            <label for="password">Contraseña <span class="required">*</span></label>
                            <input type="password" id="password" name="password" class="form-input" required minlength="6">
                        </div>
                        
                        <div class="form-group">
                            <label for="confirm_password">Confirmar Contraseña <span class="required">*</span></label>
                            <input type="password" id="confirm_password" name="confirm_password" class="form-input" required>
                        </div>
                    </div>

                    <!-- Datos Personales -->
                    <div class="section-title">
                        <i class="fas fa-user"></i> Datos Personales
                    </div>
                    
                    <div class="form-grid">
                        <div class="form-group full-width">
                            <label for="nombre_completo">Nombre completo <span class="required">*</span></label>
                            <input type="text" id="nombre_completo" name="nombre_completo" class="form-input" required 
                                   value="<?php echo htmlspecialchars($_POST['nombre_completo'] ?? ''); ?>">
                        </div>
                        
                        <div class="form-group">
                            <label for="dni">DNI o Cédula de Identidad <span class="required">*</span></label>
                            <input type="text" id="dni" name="dni" class="form-input" required 
                                   value="<?php echo htmlspecialchars($_POST['dni'] ?? ''); ?>">
                        </div>
                        
                        <div class="form-group">
                            <label for="fecha_nacimiento">Fecha de nacimiento <span class="required">*</span></label>
                            <input type="date" id="fecha_nacimiento" name="fecha_nacimiento" class="form-input" required 
                                   value="<?php echo htmlspecialchars($_POST['fecha_nacimiento'] ?? ''); ?>">
                        </div>
                        
                        <div class="form-group">
                            <label for="telefono">Número de teléfono <span class="required">*</span></label>
                            <input type="tel" id="telefono" name="telefono" class="form-input" required 
                                   value="<?php echo htmlspecialchars($_POST['telefono'] ?? ''); ?>">
                        </div>
                        
                        <div class="form-group full-width">
                            <label for="direccion">Dirección actual completa <span class="required">*</span></label>
                            <textarea id="direccion" name="direccion" class="form-textarea" required><?php echo htmlspecialchars($_POST['direccion'] ?? ''); ?></textarea>
                        </div>
                    </div>

                    <!-- Datos de Pago -->
                    <div class="section-title">
                        <i class="fas fa-credit-card"></i> Datos de Pago
                    </div>
                    
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="cbu_alias">CBU o Alias Bancario (para transferencias) <span class="required">*</span></label>
                            <input type="text" id="cbu_alias" name="cbu_alias" class="form-input" required 
                                   value="<?php echo htmlspecialchars($_POST['cbu_alias'] ?? ''); ?>">
                        </div>
                        
                        <div class="form-group">
                            <label for="cuil_cuit">CUIL / CUIT (opcional al inicio)</label>
                            <input type="text" id="cuil_cuit" name="cuil_cuit" class="form-input" 
                                   value="<?php echo htmlspecialchars($_POST['cuil_cuit'] ?? ''); ?>">
                        </div>
                    </div>

                    <!-- Datos del Vehículo -->
                    <div class="section-title">
                        <i class="fas fa-motorcycle"></i> Datos del Vehículo
                    </div>

                    <div class="form-grid">
                        <div class="form-group">
                            <label for="tipo_vehiculo">Tipo de vehículo <span class="required">*</span></label>
                            <select id="tipo_vehiculo" name="tipo_vehiculo" class="form-select" required>
                                <option value="">Seleccionar...</option>
                                <option value="bicicleta" <?php echo ($_POST['tipo_vehiculo'] ?? '') === 'bicicleta' ? 'selected' : ''; ?>>Bicicleta</option>
                                <option value="moto" <?php echo ($_POST['tipo_vehiculo'] ?? '') === 'moto' ? 'selected' : ''; ?>>Moto</option>
                                <option value="auto" <?php echo ($_POST['tipo_vehiculo'] ?? '') === 'auto' ? 'selected' : ''; ?>>Auto</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="marca">Marca</label>
                            <input type="text" id="marca" name="marca" class="form-input"
                                   value="<?php echo htmlspecialchars($_POST['marca'] ?? ''); ?>">
                        </div>

                        <div class="form-group">
                            <label for="modelo">Modelo</label>
                            <input type="text" id="modelo" name="modelo" class="form-input"
                                   value="<?php echo htmlspecialchars($_POST['modelo'] ?? ''); ?>">
                        </div>

                        <div class="form-group">
                            <label for="patente">Patente</label>
                            <input type="text" id="patente" name="patente" class="form-input"
                                   value="<?php echo htmlspecialchars($_POST['patente'] ?? ''); ?>">
                        </div>

                        <div class="form-group">
                            <label for="licencia">Licencia de conducir (vigente)</label>
                            <input type="text" id="licencia" name="licencia" class="form-input"
                                   value="<?php echo htmlspecialchars($_POST['licencia'] ?? ''); ?>">
                        </div>
                    </div>

                    <!-- Documentación Requerida -->
                    <div class="section-title">
                        <i class="fas fa-camera"></i> Documentación Requerida (adjuntar)
                    </div>

                    <div class="documentos-container">
                        <div class="documento-info">
                            <i class="fas fa-info-circle"></i>
                            <p>Subí fotos claras de los siguientes documentos. Formatos permitidos: JPG, PNG, PDF. Tamaño máximo: 5MB por archivo.</p>
                        </div>

                        <div class="form-grid">
                            <div class="form-group full-width">
                                <label for="dni_frente">Foto clara del DNI (frente) <span class="required">*</span></label>
                                <div class="file-upload-container">
                                    <input type="file" id="dni_frente" name="dni_frente" class="file-input" accept="image/*,.pdf" required>
                                    <label for="dni_frente" class="file-upload-label">
                                        <i class="fas fa-cloud-upload-alt"></i>
                                        <span>Seleccionar archivo</span>
                                    </label>
                                    <div class="file-preview" id="preview_dni_frente"></div>
                                </div>
                            </div>

                            <div class="form-group full-width">
                                <label for="dni_dorso">Foto clara del DNI (dorso) <span class="required">*</span></label>
                                <div class="file-upload-container">
                                    <input type="file" id="dni_dorso" name="dni_dorso" class="file-input" accept="image/*,.pdf" required>
                                    <label for="dni_dorso" class="file-upload-label">
                                        <i class="fas fa-cloud-upload-alt"></i>
                                        <span>Seleccionar archivo</span>
                                    </label>
                                    <div class="file-preview" id="preview_dni_dorso"></div>
                                </div>
                            </div>

                            <div class="form-group full-width" id="licencia_upload_group" style="display: none;">
                                <label for="licencia_foto">Foto de la licencia de conducir <span class="required">*</span></label>
                                <div class="file-upload-container">
                                    <input type="file" id="licencia_foto" name="licencia_foto" class="file-input" accept="image/*,.pdf">
                                    <label for="licencia_foto" class="file-upload-label">
                                        <i class="fas fa-cloud-upload-alt"></i>
                                        <span>Seleccionar archivo</span>
                                    </label>
                                    <div class="file-preview" id="preview_licencia_foto"></div>
                                </div>
                            </div>

                            <div class="form-group full-width">
                                <label for="selfie_dni">Foto tipo carnet o selfie clara con el DNI en mano <span class="required">*</span></label>
                                <div class="file-upload-container">
                                    <input type="file" id="selfie_dni" name="selfie_dni" class="file-input" accept="image/*" required>
                                    <label for="selfie_dni" class="file-upload-label">
                                        <i class="fas fa-cloud-upload-alt"></i>
                                        <span>Seleccionar archivo</span>
                                    </label>
                                    <div class="file-preview" id="preview_selfie_dni"></div>
                                </div>
                            </div>

                            <div class="form-group full-width" id="seguro_upload_group" style="display: none;">
                                <label for="seguro_foto">Foto del seguro del vehículo <span class="required">*</span></label>
                                <div class="file-upload-container">
                                    <input type="file" id="seguro_foto" name="seguro_foto" class="file-input" accept="image/*,.pdf">
                                    <label for="seguro_foto" class="file-upload-label">
                                        <i class="fas fa-cloud-upload-alt"></i>
                                        <span>Seleccionar archivo</span>
                                    </label>
                                    <div class="file-preview" id="preview_seguro_foto"></div>
                                </div>
                            </div>

                            <div class="form-group full-width">
                                <label for="antecedentes">Certificado de antecedentes penales (opcional pero recomendado)</label>
                                <div class="file-upload-container">
                                    <input type="file" id="antecedentes" name="antecedentes" class="file-input" accept="image/*,.pdf">
                                    <label for="antecedentes" class="file-upload-label">
                                        <i class="fas fa-cloud-upload-alt"></i>
                                        <span>Seleccionar archivo</span>
                                    </label>
                                    <div class="file-preview" id="preview_antecedentes"></div>
                                    <small class="form-help">Podés obtenerlo desde Ciudadano Digital o en comisarías</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Declaración Jurada -->
                    <div class="declaracion-section">
                        <div class="declaracion-title">
                            <i class="fas fa-gavel"></i> Declaración Jurada y Exención de Responsabilidad
                        </div>
                        
                        <div class="declaracion-text">
                            <strong>Yo, [nombre completo], DNI [número], declaro que:</strong>
                        </div>
                        
                        <ol class="declaracion-list">
                            <li>Me registro en esta plataforma como trabajador independiente, sin relación de dependencia ni vínculo laboral con la empresa ni con los comercios o clientes que utilizan el sistema.</li>
                            <li>Me comprometo a cumplir con las normas de tránsito, mantener la documentación y el seguro del vehículo al día, y utilizar los elementos de seguridad correspondientes.</li>
                            <li>Soy responsable exclusivo de cualquier incidente, accidente, robo, pérdida, multa, daño o inconveniente ocurrido durante la prestación del servicio.</li>
                            <li>La plataforma actúa únicamente como intermediaria digital entre comercios, clientes y repartidores, sin asumir ninguna responsabilidad civil, penal, laboral ni económica por los actos o decisiones de los repartidores.</li>
                            <li>En caso de reclamos de terceros, accidentes o problemas legales, libero expresamente a la empresa de toda responsabilidad y asumo los costos y consecuencias derivados de mi accionar.</li>
                            <li>Autorizo el uso de mis datos personales con fines operativos y legales conforme a la Ley de Protección de Datos Personales (Ley N° 25.326 en Argentina).</li>
                        </ol>
                        
                        <div class="declaracion-text">
                            <strong>Declaración Jurada Adicional:</strong><br>
                            Declaro que toda la información suministrada en este formulario es verdadera, completa y exacta. Reconozco que cualquier falsedad, omisión o intento de engaño en la documentación presentada o en los datos ingresados podrá generar la baja inmediata de la plataforma, y eventualmente derivar en acciones legales civiles o penales conforme a la normativa vigente en la República Argentina.
                        </div>
                        
                        <div class="checkbox-group">
                            <input type="checkbox" id="acepta_terminos" name="acepta_terminos" value="1" required>
                            <label for="acepta_terminos">
                                <strong>ACEPTACIÓN OBLIGATORIA:</strong> Declaro que he leído y acepto en su totalidad esta declaración jurada, los términos y condiciones de uso de la plataforma, y la política de privacidad.
                            </label>
                        </div>
                    </div>

                    <button type="submit" class="btn-submit">
                        <i class="fas fa-paper-plane"></i> Enviar Solicitud de Registro
                    </button>
                </form>

                <?php endif; ?>
            </div>
        </div>
    </div>

    <script>
        // Validación en tiempo real
        document.getElementById('confirm_password').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;
            
            if (password !== confirmPassword) {
                this.style.borderColor = '#E53E3E';
            } else {
                this.style.borderColor = '#48BB78';
            }
        });

        // Validación de DNI
        document.getElementById('dni').addEventListener('input', function() {
            this.value = this.value.replace(/[^0-9]/g, '');
        });

        // Validación de CUIL
        document.getElementById('cuil_cuit').addEventListener('input', function() {
            this.value = this.value.replace(/[^0-9]/g, '');
        });

        // Mostrar/ocultar campos según tipo de vehículo
        document.getElementById('tipo_vehiculo').addEventListener('change', function() {
            const licenciaGroup = document.getElementById('licencia').closest('.form-group');
            const patenteGroup = document.getElementById('patente').closest('.form-group');
            const licenciaUploadGroup = document.getElementById('licencia_upload_group');
            const seguroUploadGroup = document.getElementById('seguro_upload_group');

            if (this.value === 'bicicleta') {
                licenciaGroup.style.display = 'none';
                patenteGroup.style.display = 'none';
                licenciaUploadGroup.style.display = 'none';
                seguroUploadGroup.style.display = 'none';
                document.getElementById('licencia').required = false;
                document.getElementById('licencia_foto').required = false;
                document.getElementById('seguro_foto').required = false;
            } else {
                licenciaGroup.style.display = 'block';
                patenteGroup.style.display = 'block';
                licenciaUploadGroup.style.display = 'block';
                seguroUploadGroup.style.display = 'block';
                document.getElementById('licencia').required = true;
                document.getElementById('licencia_foto').required = true;
                document.getElementById('seguro_foto').required = true;
            }
        });

        // Manejar uploads de archivos
        function setupFileUpload(inputId, previewId) {
            const input = document.getElementById(inputId);
            const preview = document.getElementById(previewId);

            input.addEventListener('change', function(e) {
                const file = e.target.files[0];

                if (file) {
                    // Validar tamaño (5MB)
                    if (file.size > 5 * 1024 * 1024) {
                        alert('El archivo es demasiado grande. Máximo 5MB.');
                        input.value = '';
                        return;
                    }

                    // Validar tipo
                    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
                    if (!allowedTypes.includes(file.type)) {
                        alert('Tipo de archivo no permitido. Solo JPG, PNG y PDF.');
                        input.value = '';
                        return;
                    }

                    // Mostrar preview
                    showFilePreview(file, preview);
                } else {
                    hideFilePreview(preview);
                }
            });
        }

        function showFilePreview(file, previewElement) {
            const fileSize = (file.size / 1024 / 1024).toFixed(2);
            const fileIcon = file.type.includes('pdf') ? 'fas fa-file-pdf' : 'fas fa-image';

            previewElement.innerHTML = `
                <div class="file-preview-item">
                    <i class="${fileIcon}"></i>
                    <span class="file-preview-name">${file.name}</span>
                    <span class="file-preview-size">${fileSize} MB</span>
                    <button type="button" class="file-remove" onclick="removeFile('${previewElement.id}')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            previewElement.classList.add('active');
        }

        function hideFilePreview(previewElement) {
            previewElement.innerHTML = '';
            previewElement.classList.remove('active');
        }

        function removeFile(previewId) {
            const preview = document.getElementById(previewId);
            const inputId = previewId.replace('preview_', '');
            const input = document.getElementById(inputId);

            input.value = '';
            hideFilePreview(preview);
        }

        // Configurar todos los uploads
        setupFileUpload('dni_frente', 'preview_dni_frente');
        setupFileUpload('dni_dorso', 'preview_dni_dorso');
        setupFileUpload('licencia_foto', 'preview_licencia_foto');
        setupFileUpload('selfie_dni', 'preview_selfie_dni');
        setupFileUpload('seguro_foto', 'preview_seguro_foto');
        setupFileUpload('antecedentes', 'preview_antecedentes');

        // Validación del formulario
        document.getElementById('registroForm').addEventListener('submit', function(e) {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            const aceptaTerminos = document.getElementById('acepta_terminos').checked;
            
            if (password !== confirmPassword) {
                e.preventDefault();
                alert('Las contraseñas no coinciden');
                return;
            }
            
            if (!aceptaTerminos) {
                e.preventDefault();
                alert('Debes aceptar los términos y condiciones para continuar');
                return;
            }
        });
    </script>
</body>
</html>
