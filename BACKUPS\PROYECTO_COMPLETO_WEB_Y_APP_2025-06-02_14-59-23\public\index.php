<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RepuMovil - No te muevas, nosotros llevamos los repuestos a vos</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700;800&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-orange: #FF6B35;
            --secondary-orange: #F7931E;
            --gradient-bg: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            --gradient-orange: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
            --light-color: #f8f9fa;
            --dark-color: #212529;
            --white: #ffffff;
            --box-shadow: 0 10px 30px rgba(255, 107, 53, 0.2);
            --box-shadow-hover: 0 15px 40px rgba(255, 107, 53, 0.3);
            --transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            line-height: 1.6;
            color: var(--dark-color);
            background: var(--gradient-bg);
            overflow-x: hidden;
        }

        a {
            text-decoration: none;
            color: var(--primary-orange);
            transition: var(--transition);
        }

        a:hover {
            color: var(--secondary-orange);
        }

        /* Header Moderno */
        header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
            padding: 15px 0;
            transition: var(--transition);
        }

        .navbar {
            padding: 0;
        }

        .navbar-brand {
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 800;
            font-size: 1.8rem;
            text-decoration: none !important;
        }

        .logo-icon {
            width: 35px;
            height: 60px;
            background: var(--gradient-orange);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            box-shadow: var(--box-shadow);
            transition: var(--transition);
            position: relative;
            overflow: hidden;
            margin-right: 12px;
        }

        .logo-icon::before {
            content: '';
            position: absolute;
            top: 6px;
            left: 50%;
            transform: translateX(-50%);
            width: 18px;
            height: 3px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 2px;
        }

        .logo-icon::after {
            content: '';
            position: absolute;
            bottom: 6px;
            left: 50%;
            transform: translateX(-50%);
            width: 12px;
            height: 12px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
        }

        .logo-phone-screen {
            position: absolute;
            top: 12px;
            left: 4px;
            right: 4px;
            bottom: 22px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: inset 0 0 5px rgba(0,0,0,0.1);
        }

        .logo-wrench {
            color: var(--primary-orange);
            font-size: 14px;
        }

        .logo-icon:hover {
            transform: rotate(5deg) scale(1.05);
            box-shadow: var(--box-shadow-hover);
        }

        .logo-text .repu {
            color: var(--primary-orange);
        }

        .logo-text .movil {
            color: var(--secondary-orange);
        }

        .navbar-nav .nav-link {
            padding: 10px 15px;
            font-weight: 500;
        }

        .btn-login {
            background: transparent;
            border: 2px solid var(--primary-orange);
            color: var(--primary-orange);
            margin-right: 10px;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 600;
            transition: var(--transition);
        }

        .btn-login:hover {
            background: var(--primary-orange);
            color: white;
            transform: translateY(-2px);
            box-shadow: var(--box-shadow);
        }

        .btn-register {
            background: var(--gradient-orange);
            border: 2px solid transparent;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 600;
            transition: var(--transition);
            box-shadow: var(--box-shadow);
        }

        .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-hover);
            color: white;
        }

        /* Hero Section Innovador */
        .hero {
            background: linear-gradient(135deg, var(--primary-orange) 0%, var(--secondary-orange) 50%, #FF8C42 100%);
            color: white;
            padding: 120px 0 80px 0;
            text-align: center;
            position: relative;
            overflow: hidden;
            min-height: 100vh;
            display: flex;
            align-items: center;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .hero-content {
            max-width: 1000px;
            margin: 0 auto;
            position: relative;
            z-index: 2;
        }

        .hero h1 {
            font-size: 4rem;
            font-weight: 800;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            line-height: 1.2;
        }

        .hero-slogan {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 15px;
            text-transform: uppercase;
            letter-spacing: 1px;
            color: #FFE5D9;
        }

        .hero-subtitle {
            font-size: 1.3rem;
            margin-bottom: 40px;
            opacity: 0.95;
            font-weight: 400;
        }

        .mobile-mockup {
            position: absolute;
            right: -50px;
            top: 50%;
            transform: translateY(-50%);
            width: 250px;
            height: 450px;
            background: linear-gradient(145deg, var(--primary-orange), var(--secondary-orange));
            border-radius: 35px;
            box-shadow: 0 25px 80px rgba(255, 107, 53, 0.4);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .mobile-mockup::before {
            content: '';
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            width: 120px;
            height: 6px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 3px;
        }

        .mobile-mockup::after {
            content: '';
            position: absolute;
            bottom: 15px;
            left: 50%;
            transform: translateX(-50%);
            width: 50px;
            height: 50px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
        }

        .mobile-screen {
            width: 200px;
            height: 350px;
            background: #f0f0f0;
            border-radius: 20px;
            margin-top: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            box-shadow: inset 0 0 20px rgba(0,0,0,0.1);
        }

        .mobile-wrench {
            font-size: 80px;
            color: var(--primary-orange);
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .cta-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 30px;
        }

        .cta-buttons .btn {
            padding: 18px 40px;
            font-weight: 700;
            border-radius: 50px;
            font-size: 1.1rem;
            transition: var(--transition);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border: none;
            position: relative;
            overflow: hidden;
        }

        .btn-primary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid white;
            backdrop-filter: blur(10px);
        }

        .btn-primary:hover {
            background: white;
            color: var(--primary-orange);
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(255,255,255,0.3);
        }

        .btn-secondary {
            background: rgba(0, 0, 0, 0.2);
            color: white;
            border: 2px solid rgba(255,255,255,0.3);
            backdrop-filter: blur(10px);
        }

        .btn-secondary:hover {
            background: rgba(0, 0, 0, 0.4);
            border-color: white;
            transform: translateY(-3px);
            color: white;
        }

        .app-download-section {
            margin-top: 50px;
            padding: 30px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }

        .download-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 20px;
        }

        .download-btn {
            background: #000000;
            color: white;
            padding: 12px 20px;
            border-radius: 12px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 12px;
            transition: var(--transition);
            font-weight: 500;
            min-width: 180px;
            border: 1px solid #333;
        }

        .download-btn:hover {
            background: #1a1a1a;
            transform: translateY(-2px);
            color: white;
            text-decoration: none;
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .download-icon {
            font-size: 24px;
            width: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .download-text {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            line-height: 1.2;
        }

        .download-text small {
            font-size: 11px;
            opacity: 0.8;
            font-weight: 400;
        }

        .download-text strong {
            font-size: 16px;
            font-weight: 600;
        }

        .google-play-icon {
            background: linear-gradient(45deg, #ff6b35, #f7931e, #4285f4, #34a853);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .app-store-icon {
            color: #ffffff;
        }

        /* How it works */
        .how-it-works {
            padding: 80px 0;
            background-color: white;
            position: relative;
            overflow: hidden;
        }

        .section-title {
            text-align: center;
            margin-bottom: 50px;
        }

        .section-title h2 {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-orange);
            position: relative;
            display: inline-block;
            padding-bottom: 15px;
        }

        .section-title h2::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 3px;
            background-color: var(--secondary-orange);
        }

        .how-it-works-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            max-width: 1200px;
            margin: 0 auto;
            gap: 50px;
        }

        .steps {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            flex: 1;
            max-width: 600px;
        }

        .step {
            flex: 0 0 250px;
            margin: 20px;
            text-align: center;
            padding: 30px;
            background-color: white;
            border-radius: 10px;
            box-shadow: var(--box-shadow);
            transition: var(--transition);
        }

        .step:hover {
            transform: translateY(-10px);
        }

        .step-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background-color: var(--light-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: var(--primary-orange);
        }

        .step h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: var(--primary-orange);
        }

        /* Mobile App Mockup */
        .app-mockup-container {
            flex: 0 0 350px;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
        }

        .app-mockup {
            width: 280px;
            height: 500px;
            background: linear-gradient(145deg, #2c3e50, #34495e);
            border-radius: 35px;
            box-shadow: 0 25px 80px rgba(0, 0, 0, 0.3);
            position: relative;
            overflow: hidden;
            transform: translateX(100px);
            opacity: 0;
            transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .app-mockup.reveal {
            transform: translateX(0);
            opacity: 1;
        }

        .app-mockup::before {
            content: '';
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            width: 120px;
            height: 6px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 3px;
        }

        .app-screen {
            width: 240px;
            height: 430px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            border-radius: 25px;
            margin: 35px auto 0;
            position: relative;
            overflow: hidden;
            box-shadow: inset 0 0 20px rgba(0,0,0,0.1);
        }

        .app-header {
            text-align: center;
            padding: 40px 20px 20px;
        }

        .app-logo-circle {
            width: 60px;
            height: 60px;
            background: #FF6B35;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
            box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
        }

        .app-logo-icon {
            font-size: 25px;
            color: white;
        }

        .app-title {
            font-size: 24px;
            font-weight: bold;
            margin: 0 0 10px 0;
            color: #333;
        }

        .app-title .logo-repu {
            color: #FF6B35;
        }

        .app-title .logo-movil {
            color: #FFA500;
        }

        .app-subtitle {
            font-size: 16px;
            color: #666;
            margin-bottom: 30px;
        }

        .app-form {
            padding: 0 30px;
        }

        .app-input {
            width: 100%;
            height: 45px;
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            margin-bottom: 15px;
            padding: 0 15px;
            font-size: 14px;
            box-sizing: border-box;
        }

        .app-button {
            width: 100%;
            height: 45px;
            background: linear-gradient(135deg, #FF6B35 0%, #FFA500 100%);
            border: none;
            border-radius: 10px;
            color: white;
            font-size: 16px;
            font-weight: 600;
            margin-top: 10px;
        }

        .app-link {
            text-align: center;
            margin-top: 20px;
            font-size: 12px;
            color: #666;
        }

        /* Features */
        .features {
            padding: 80px 0;
            background-color: var(--light-color);
            position: relative;
            overflow: hidden;
        }

        .features-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            max-width: 1200px;
            margin: 0 auto;
            gap: 50px;
        }

        .features .row {
            flex: 1;
            max-width: 700px;
        }

        /* Talleres App Mockup */
        .talleres-mockup-container {
            flex: 0 0 350px;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            order: -1;
        }

        .talleres-mockup {
            width: 280px;
            height: 500px;
            background: linear-gradient(145deg, #2c3e50, #34495e);
            border-radius: 35px;
            box-shadow: 0 25px 80px rgba(0, 0, 0, 0.3);
            position: relative;
            overflow: hidden;
            transform: translateX(-100px);
            opacity: 0;
            transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .talleres-mockup.reveal {
            transform: translateX(0);
            opacity: 1;
        }

        .talleres-mockup::before {
            content: '';
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            width: 120px;
            height: 6px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 3px;
        }

        .talleres-screen {
            width: 240px;
            height: 430px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            border-radius: 25px;
            margin: 35px auto 0;
            position: relative;
            overflow: hidden;
            box-shadow: inset 0 0 20px rgba(0,0,0,0.1);
        }

        .talleres-header {
            background: #FF6B35;
            color: white;
            padding: 15px 20px;
            text-align: center;
            border-radius: 25px 25px 0 0;
        }

        .talleres-title {
            font-size: 16px;
            font-weight: bold;
            margin: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .talleres-content {
            padding: 20px;
            background: white;
            margin: 0 15px;
            border-radius: 15px;
            margin-top: 15px;
            text-align: center;
        }

        .talleres-app-title {
            font-size: 18px;
            font-weight: bold;
            color: #FF6B35;
            margin-bottom: 5px;
        }

        .talleres-subtitle {
            font-size: 11px;
            color: #666;
            margin-bottom: 15px;
            line-height: 1.3;
        }

        .talleres-stats {
            display: flex;
            justify-content: space-around;
            margin-bottom: 20px;
        }

        .talleres-stat {
            text-align: center;
        }

        .talleres-stat-number {
            font-size: 20px;
            font-weight: bold;
            color: #FF6B35;
            display: block;
        }

        .talleres-stat-label {
            font-size: 9px;
            color: #666;
            line-height: 1.2;
        }

        .talleres-button {
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 10px;
            padding: 12px 20px;
            font-size: 14px;
            font-weight: 600;
            width: 100%;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .talleres-section {
            background: #FFF3E0;
            border-radius: 10px;
            padding: 12px;
            margin-bottom: 10px;
        }

        .talleres-section-title {
            font-size: 12px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .talleres-section-text {
            font-size: 10px;
            color: #666;
            line-height: 1.3;
        }

        .feature-card {
            background-color: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: var(--box-shadow);
            margin-bottom: 30px;
            transition: var(--transition);
        }

        .feature-card:hover {
            transform: translateY(-5px);
        }

        .feature-img {
            height: 200px;
            background-color: var(--primary-orange);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 3rem;
        }

        .feature-content {
            padding: 25px;
        }

        .feature-content h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: var(--primary-orange);
        }

        /* Testimonials */
        .testimonials {
            padding: 80px 0;
            background-color: white;
        }

        .testimonial {
            text-align: center;
            padding: 30px;
            margin: 20px;
            background-color: var(--light-color);
            border-radius: 10px;
            box-shadow: var(--box-shadow);
        }

        .testimonial-img {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin: 0 auto 20px;
            overflow: hidden;
            background-color: var(--primary-orange);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
        }

        .testimonial-text {
            font-style: italic;
            margin-bottom: 20px;
        }

        .testimonial-author {
            font-weight: 700;
            color: var(--primary-orange);
        }

        /* Contact */
        .contact {
            padding: 80px 0;
            background-color: var(--light-color);
        }

        .contact-form {
            background-color: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: var(--box-shadow);
        }

        .form-control {
            height: 50px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        textarea.form-control {
            height: 150px;
        }

        /* Footer */
        footer {
            background-color: var(--primary-orange);
            color: white;
            padding: 60px 0 20px;
        }

        .footer-content {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            margin-bottom: 40px;
        }

        .footer-logo {
            margin-bottom: 20px;
        }

        .footer-logo img {
            height: 40px;
            margin-bottom: 10px;
        }

        .footer-links h4, .footer-social h4 {
            margin-bottom: 20px;
            position: relative;
            padding-bottom: 10px;
        }

        .footer-links h4::after, .footer-social h4::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 50px;
            height: 2px;
            background-color: var(--secondary-orange);
        }

        .footer-links ul {
            list-style: none;
            padding: 0;
        }

        .footer-links li {
            margin-bottom: 10px;
        }

        .footer-links a {
            color: rgba(255, 255, 255, 0.8);
        }

        .footer-links a:hover {
            color: white;
        }

        .social-icons {
            display: flex;
        }

        .social-icons a {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: 10px;
            transition: var(--transition);
        }

        .social-icons a:hover {
            background-color: var(--secondary-orange);
            transform: translateY(-3px);
        }

        .footer-bottom {
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* Responsive */
        @media (max-width: 1200px) {
            .mobile-mockup {
                right: -100px;
                width: 200px;
                height: 360px;
            }

            .mobile-screen {
                width: 160px;
                height: 280px;
            }

            .how-it-works-content {
                gap: 30px;
            }

            .app-mockup-container {
                flex: 0 0 300px;
            }

            .app-mockup {
                width: 250px;
                height: 450px;
            }

            .app-screen {
                width: 210px;
                height: 380px;
            }

            .features-content {
                gap: 30px;
            }

            .talleres-mockup-container {
                flex: 0 0 300px;
            }

            .talleres-mockup {
                width: 250px;
                height: 450px;
            }

            .talleres-screen {
                width: 210px;
                height: 380px;
            }

            .mobile-wrench {
                font-size: 60px;
            }
        }

        @media (max-width: 768px) {
            .hero {
                padding: 100px 0 60px 0;
                min-height: auto;
            }

            .hero h1 {
                font-size: 2.5rem;
            }

            .hero-slogan {
                font-size: 1.2rem;
            }

            .hero-subtitle {
                font-size: 1.1rem;
            }

            .mobile-mockup {
                display: none;
            }

            .step {
                flex: 0 0 100%;
                margin: 10px 0;
            }

            .how-it-works-content {
                flex-direction: column;
                gap: 40px;
            }

            .app-mockup-container {
                flex: none;
                order: -1;
            }

            .app-mockup {
                width: 220px;
                height: 400px;
                transform: translateX(0);
            }

            .app-screen {
                width: 180px;
                height: 320px;
            }

            .app-header {
                padding: 30px 15px 15px;
            }

            .app-logo-circle {
                width: 50px;
                height: 50px;
            }

            .app-logo-icon {
                font-size: 20px;
            }

            .app-title {
                font-size: 20px;
            }

            .app-subtitle {
                font-size: 14px;
            }

            .app-form {
                padding: 0 20px;
            }

            .app-input {
                height: 40px;
                font-size: 12px;
            }

            .app-button {
                height: 40px;
                font-size: 14px;
            }

            .features-content {
                flex-direction: column;
                gap: 40px;
            }

            .talleres-mockup-container {
                flex: none;
                order: -1;
            }

            .talleres-mockup {
                width: 220px;
                height: 400px;
                transform: translateX(0);
            }

            .talleres-screen {
                width: 180px;
                height: 320px;
            }

            .talleres-header {
                padding: 12px 15px;
            }

            .talleres-title {
                font-size: 14px;
            }

            .talleres-content {
                padding: 15px;
                margin: 0 10px;
                margin-top: 10px;
            }

            .talleres-app-title {
                font-size: 16px;
            }

            .talleres-subtitle {
                font-size: 10px;
            }

            .talleres-stats {
                margin-bottom: 15px;
            }

            .talleres-stat-number {
                font-size: 16px;
            }

            .talleres-stat-label {
                font-size: 8px;
            }

            .talleres-button {
                padding: 10px 15px;
                font-size: 12px;
            }

            .talleres-section {
                padding: 10px;
            }

            .talleres-section-title {
                font-size: 11px;
            }

            .talleres-section-text {
                font-size: 9px;
            }

            .footer-content > div {
                flex: 0 0 100%;
                margin-bottom: 30px;
            }

            .download-buttons {
                flex-direction: column;
                align-items: center;
            }

            .download-btn {
                min-width: 200px;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <nav class="navbar navbar-expand-lg navbar-light">
                <a class="navbar-brand" href="index.php">
                    <div class="logo-icon">
                        <div class="logo-phone-screen">
                            <div class="logo-wrench">
                                <i class="fas fa-wrench"></i>
                            </div>
                        </div>
                    </div>
                    <div class="logo-text">
                        <span class="repu">Repu</span><span class="movil">Movil</span>
                    </div>
                </a>
                <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav ml-auto">
                        <li class="nav-item active">
                            <a class="nav-link" href="#">Inicio</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#talleres">Talleres</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#proveedores">Proveedores</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#como-funciona">Cómo Funciona</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#contacto">Contacto</a>
                        </li>
                        <li class="nav-item">
                            <a class="btn btn-login" href="login-dinamico.php">Iniciar Sesión</a>
                        </li>
                        <li class="nav-item">
                            <a class="btn btn-register" href="registro-dinamico.php">Registrarse</a>
                        </li>
                    </ul>
                </div>
            </nav>
        </div>
    </header>

    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <div class="hero-slogan">NO TE MUEVAS, NOSOTROS LLEVAMOS LOS REPUESTOS A VOS</div>
                <h1>RepuMovil</h1>
                <div class="hero-subtitle">CLICK, PEDÍ, RECIBÍ - REPARAR NUNCA FUE TAN FÁCIL</div>
                <p>🌐 <strong>Sistema Web Completo</strong> + 📱 <strong>Aplicación Android/iOS</strong></p>
                <p>Conectamos talleres, mecánicos y usuarios con proveedores de repuestos en tiempo real</p>

                <div class="cta-buttons">
                    <a href="login-dinamico.php" class="btn btn-primary">
                        <i class="fas fa-desktop"></i> Acceder al Sistema Web
                    </a>
                    <a href="#app-download" class="btn btn-secondary">
                        <i class="fas fa-mobile-alt"></i> Descargar App Móvil
                    </a>
                </div>

                <div class="app-download-section">
                    <h3>📱 Descarga RepuMovil App</h3>
                    <p>Disponible para Android y iOS - ¡Pedí repuestos desde cualquier lugar!</p>
                    <div class="download-buttons">
                        <a href="#" class="download-btn">
                            <div class="download-icon">
                                <i class="fab fa-google-play google-play-icon"></i>
                            </div>
                            <div class="download-text">
                                <small>Disponible en</small>
                                <strong>Google Play</strong>
                            </div>
                        </a>
                        <a href="#" class="download-btn">
                            <div class="download-icon">
                                <i class="fab fa-apple app-store-icon"></i>
                            </div>
                            <div class="download-text">
                                <small>Descargar en</small>
                                <strong>App Store</strong>
                            </div>
                        </a>
                    </div>
                </div>
            </div>

        </div>
    </section>

    <main>
        <section id="como-funciona" class="how-it-works">
            <div class="container">
                <div class="section-title">
                    <h2>Cómo Funciona</h2>
                </div>
                <div class="how-it-works-content">
                    <div class="steps">
                        <div class="step">
                            <div class="step-icon"><i class="fas fa-user-plus"></i></div>
                            <h3>Regístrate</h3>
                            <p>Crea tu cuenta como taller mecánico o proveedor de repuestos</p>
                        </div>
                        <div class="step">
                            <div class="step-icon"><i class="fas fa-search"></i></div>
                            <h3>Busca</h3>
                            <p>Talleres: Encuentra proveedores con los repuestos que necesitas</p>
                            <p>Proveedores: Conecta con talleres que requieren tus productos</p>
                        </div>
                        <div class="step">
                            <div class="step-icon"><i class="fas fa-handshake"></i></div>
                            <h3>Conecta</h3>
                            <p>Establece relaciones comerciales directas y eficientes</p>
                        </div>
                        <div class="step">
                            <div class="step-icon"><i class="fas fa-chart-line"></i></div>
                            <h3>Crece</h3>
                            <p>Aumenta tus ventas y optimiza la gestión de tu negocio</p>
                        </div>
                    </div>

                    <!-- Mobile App Mockup -->
                    <div class="app-mockup-container">
                        <div class="app-mockup" id="appMockup">
                            <div class="app-screen">
                                <div class="app-header">
                                    <div class="app-logo-circle">
                                        <i class="fas fa-wrench app-logo-icon"></i>
                                    </div>
                                    <h1 class="app-title">
                                        <span class="logo-repu">Repu</span><span class="logo-movil">Movil</span>
                                    </h1>
                                    <p class="app-subtitle">Iniciar Sesión</p>
                                </div>
                                <div class="app-form">
                                    <input type="text" class="app-input" placeholder="Correo Electrónico" readonly>
                                    <input type="password" class="app-input" placeholder="Contraseña" readonly>
                                    <button class="app-button">Iniciar Sesión</button>
                                    <div class="app-link">¿No tiene una cuenta? Regístrese aquí</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="talleres" class="features">
            <div class="container">
                <div class="section-title">
                    <h2>Para Talleres Mecánicos</h2>
                </div>
                <div class="features-content">
                    <!-- Talleres App Mockup -->
                    <div class="talleres-mockup-container">
                        <div class="talleres-mockup" id="talleresMockup">
                            <div class="talleres-screen">
                                <div class="talleres-header">
                                    <h1 class="talleres-title">
                                        <i class="fas fa-wrench"></i> RepuMovil
                                    </h1>
                                </div>
                                <div class="talleres-content">
                                    <div class="talleres-app-title">RepuMovil</div>
                                    <div class="talleres-subtitle">Repuestos que llegan a tu taller, cuando los necesitas.</div>

                                    <div class="talleres-stats">
                                        <div class="talleres-stat">
                                            <span class="talleres-stat-number">0</span>
                                            <div class="talleres-stat-label">Pedidos<br>Realizados</div>
                                        </div>
                                        <div class="talleres-stat">
                                            <span class="talleres-stat-number">0</span>
                                            <div class="talleres-stat-label">Repuestos en<br>Progreso</div>
                                        </div>
                                        <div class="talleres-stat">
                                            <span class="talleres-stat-number">4.8</span>
                                            <div class="talleres-stat-label">Calificación<br>Promedio</div>
                                        </div>
                                    </div>

                                    <button class="talleres-button">
                                        <i class="fas fa-shopping-cart"></i>
                                        Pedido de Repuestos
                                    </button>

                                    <div class="talleres-section">
                                        <div class="talleres-section-title">
                                            <i class="fas fa-star" style="color: #FFD700;"></i>
                                            Calificaciones Recibidas
                                        </div>
                                        <div class="talleres-section-text">
                                            Revisa las calificaciones y comentarios de
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="feature-card">
                                <div class="feature-img">
                                    <i class="fas fa-search"></i>
                                </div>
                                <div class="feature-content">
                                    <h3>Búsqueda Eficiente</h3>
                                    <p>Encuentra rápidamente los repuestos que necesitas para tus reparaciones.</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="feature-card">
                                <div class="feature-img">
                                    <i class="fas fa-dollar-sign"></i>
                                </div>
                                <div class="feature-content">
                                    <h3>Mejores Precios</h3>
                                    <p>Compara precios entre diferentes proveedores y obtén las mejores ofertas.</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="feature-card">
                                <div class="feature-img">
                                    <i class="fas fa-truck"></i>
                                </div>
                                <div class="feature-content">
                                    <h3>Entrega Rápida</h3>
                                    <p>Recibe los repuestos en tu taller en el menor tiempo posible.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="proveedores" class="features" style="background-color: white;">
            <div class="container">
                <div class="section-title">
                    <h2>Para Proveedores de Repuestos</h2>
                </div>
                <div class="row">
                    <div class="col-md-4">
                        <div class="feature-card">
                            <div class="feature-img">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="feature-content">
                                <h3>Más Clientes</h3>
                                <p>Amplía tu cartera de clientes conectando con talleres mecánicos de toda la región.</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="feature-card">
                            <div class="feature-img">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <div class="feature-content">
                                <h3>Aumenta tus Ventas</h3>
                                <p>Incrementa tus ventas al llegar a más talleres que necesitan tus productos.</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="feature-card">
                            <div class="feature-img">
                                <i class="fas fa-tasks"></i>
                            </div>
                            <div class="feature-content">
                                <h3>Gestión Eficiente</h3>
                                <p>Administra tus inventarios y pedidos de manera más sencilla.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="contacto" class="contact">
            <h2>Contáctanos</h2>
            <p>¿Tienes preguntas sobre nuestra plataforma? Estamos aquí para ayudarte.</p>
            <div class="contact-info">
                <div class="contact-item">
                    <i class="fas fa-envelope"></i>
                    <p><EMAIL></p>
                </div>
                <div class="contact-item">
                    <i class="fas fa-phone"></i>
                    <p>(*************</p>
                </div>
                <div class="contact-item">
                    <i class="fas fa-map-marker-alt"></i>
                    <p>San Juan, Argentina</p>
                </div>
            </div>
            <form class="contact-form">
                <div class="form-group">
                    <label for="name">Nombre</label>
                    <input type="text" id="name" name="name" required>
                </div>
                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" id="email" name="email" required>
                </div>
                <div class="form-group">
                    <label for="message">Mensaje</label>
                    <textarea id="message" name="message" rows="5" required></textarea>
                </div>
                <button type="submit" class="btn btn-primary">Enviar Mensaje</button>
            </form>
        </section>
    </main>

    <footer>
        <div class="footer-content">
            <div class="footer-logo">
                <i class="fas fa-wrench logo-icon"></i>
                <h3>RepuMovil</h3>
            </div>
            <div class="footer-links">
                <h4>Enlaces Rápidos</h4>
                <ul>
                    <li><a href="#talleres">Talleres</a></li>
                    <li><a href="#proveedores">Proveedores</a></li>
                    <li><a href="#como-funciona">Cómo Funciona</a></li>
                    <li><a href="#contacto">Contacto</a></li>
                </ul>
            </div>
            <div class="footer-social">
                <h4>Síguenos</h4>
                <div class="social-icons">
                    <a href="#"><i class="fab fa-facebook"></i></a>
                    <a href="#"><i class="fab fa-twitter"></i></a>
                    <a href="#"><i class="fab fa-instagram"></i></a>
                    <a href="#"><i class="fab fa-linkedin"></i></a>
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; 2024 RepuMovil. Todos los derechos reservados. Hecho con ❤️ en San Juan, Argentina.</p>
        </div>
    </footer>

    <!-- Scroll Reveal JavaScript -->
    <script>
        // Función para detectar cuando un elemento entra en el viewport
        function isElementInViewport(el) {
            const rect = el.getBoundingClientRect();
            return (
                rect.top >= 0 &&
                rect.left >= 0 &&
                rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
                rect.right <= (window.innerWidth || document.documentElement.clientWidth)
            );
        }

        // Función para detectar cuando un elemento está parcialmente visible
        function isElementPartiallyInViewport(el) {
            const rect = el.getBoundingClientRect();
            const windowHeight = window.innerHeight || document.documentElement.clientHeight;
            const windowWidth = window.innerWidth || document.documentElement.clientWidth;

            return (
                rect.bottom >= 0 &&
                rect.right >= 0 &&
                rect.top <= windowHeight &&
                rect.left <= windowWidth
            );
        }

        // Función para manejar el scroll reveal
        function handleScrollReveal() {
            const appMockup = document.getElementById('appMockup');
            const talleresMockup = document.getElementById('talleresMockup');

            if (appMockup && isElementPartiallyInViewport(appMockup)) {
                appMockup.classList.add('reveal');
            }

            if (talleresMockup && isElementPartiallyInViewport(talleresMockup)) {
                talleresMockup.classList.add('reveal');
            }
        }

        // Event listeners
        window.addEventListener('scroll', handleScrollReveal);
        window.addEventListener('load', handleScrollReveal);
        window.addEventListener('resize', handleScrollReveal);

        // Ejecutar al cargar la página
        document.addEventListener('DOMContentLoaded', function() {
            handleScrollReveal();
        });
    </script>
</body>
</html>




