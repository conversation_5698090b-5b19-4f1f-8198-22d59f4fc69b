<?php
require_once 'db_config.php';

echo "<h1>🔍 Debug de Usuarios y Redirecciones</h1>";

try {
    $pdo = connectDB();
    
    // Obtener todos los usuarios
    $stmt = $pdo->prepare("
        SELECT u.id, u.username, u.email, u.role_id, r.name as role_name
        FROM users u
        LEFT JOIN roles r ON u.role_id = r.id
        ORDER BY u.id
    ");
    $stmt->execute();
    $users = $stmt->fetchAll();
    
    echo "<h2>📋 Lista de Usuarios y sus Redirecciones</h2>";
    
    foreach ($users as $user) {
        echo "<div style='background: white; padding: 15px; margin: 10px 0; border-radius: 8px; border-left: 5px solid #4CAF50;'>";
        echo "<h3>👤 {$user['username']} ({$user['email']})</h3>";
        echo "<p><strong>ID:</strong> {$user['id']} | <strong>Role ID:</strong> {$user['role_id']} | <strong>Role Name:</strong> {$user['role_name']}</p>";
        
        // Verificar en qué tabla está
        $user_type = 'usuario_regular';
        $table_found = 'ninguna';
        
        // Verificar workshops
        $stmt = $pdo->prepare("SELECT name FROM workshops WHERE user_id = ?");
        $stmt->execute([$user['id']]);
        $workshop = $stmt->fetch();
        
        if ($workshop) {
            $user_type = 'taller_mecanico';
            $table_found = 'workshops';
            echo "<p>🔧 <strong>Tipo:</strong> Taller Mecánico | <strong>Nombre:</strong> {$workshop['name']}</p>";
        } else {
            // Verificar suppliers
            $stmt = $pdo->prepare("SELECT name FROM suppliers WHERE user_id = ?");
            $stmt->execute([$user['id']]);
            $supplier = $stmt->fetch();
            
            if ($supplier) {
                $user_type = 'proveedor_repuestos';
                $table_found = 'suppliers';
                echo "<p>🏪 <strong>Tipo:</strong> Proveedor de Repuestos | <strong>Nombre:</strong> {$supplier['name']}</p>";
            } else {
                // Verificar mechanics
                $stmt = $pdo->prepare("SELECT name FROM mechanics WHERE user_id = ?");
                $stmt->execute([$user['id']]);
                $mechanic = $stmt->fetch();
                
                if ($mechanic) {
                    $user_type = 'mecanico_independiente';
                    $table_found = 'mechanics';
                    echo "<p>🔧 <strong>Tipo:</strong> Mecánico Independiente | <strong>Nombre:</strong> {$mechanic['name']}</p>";
                } else {
                    echo "<p>👤 <strong>Tipo:</strong> Usuario Regular</p>";
                }
            }
        }
        
        // Mostrar redirección
        $redirect = getDashboardUrl($user_type);
        echo "<p>📍 <strong>Tabla encontrada:</strong> {$table_found}</p>";
        echo "<p>🎯 <strong>User Type:</strong> {$user_type}</p>";
        echo "<p>🔗 <strong>Redirección:</strong> <a href='{$redirect}' target='_blank'>{$redirect}</a></p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #ffebee; padding: 20px; border-radius: 8px; color: #d32f2f;'>";
    echo "<h2>❌ Error</h2>";
    echo "<p>{$e->getMessage()}</p>";
    echo "</div>";
}

// Función copiada del login.php
function getDashboardUrl($user_type, $additional_data = []) {
    switch ($user_type) {
        case 'taller_mecanico':
            $plan_type = $additional_data['plan_type'] ?? 'comun';
            return $plan_type === 'plus' ? 'dashboard-taller-plus.php' : 'dashboard-taller-comun.php';
            
        case 'proveedor_repuestos':
            return 'dashboard-proveedor.php';
            
        case 'mecanico_independiente':
            return 'dashboard-mecanico.php';
            
        case 'usuario_regular':
            return 'dashboard-usuario.php';
            
        default:
            return 'dashboard-usuario.php';
    }
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Usuarios - RepuMovil</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        
        h1 {
            color: #4CAF50;
            text-align: center;
            margin-bottom: 30px;
        }
        
        h2 {
            color: #333;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
        }
        
        a {
            color: #4CAF50;
            text-decoration: none;
        }
        
        a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
</body>
</html>
