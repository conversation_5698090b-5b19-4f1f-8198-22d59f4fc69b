<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RepuMovil - Dashboard Taller</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #FF6B35;
            --secondary-color: #FFA500;
            --dark-color: #2c3e50;
            --light-color: #f8f9fa;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1.5rem;
            font-weight: bold;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Main Content */
        .main-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .welcome-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            text-align: center;
        }

        .welcome-title {
            color: var(--primary-color);
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .welcome-subtitle {
            color: #666;
            font-size: 1.1rem;
            margin-bottom: 20px;
        }

        /* Stats Cards */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: var(--dark-color);
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }

        /* Action Buttons */
        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .action-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            cursor: pointer;
            text-decoration: none;
            color: inherit;
        }

        .action-card:hover {
            transform: translateY(-5px);
            text-decoration: none;
            color: inherit;
        }

        .action-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            margin-bottom: 15px;
        }

        .action-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: var(--dark-color);
            margin-bottom: 10px;
        }

        .action-description {
            color: #666;
            line-height: 1.5;
        }

        /* Recent Orders */
        .recent-orders {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .section-title {
            font-size: 1.5rem;
            color: var(--dark-color);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .orders-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .order-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            background: var(--light-color);
            border-radius: 10px;
            transition: background 0.3s ease;
        }

        .order-item:hover {
            background: #e9ecef;
        }

        .order-info h4 {
            color: var(--dark-color);
            margin-bottom: 5px;
        }

        .order-info p {
            color: #666;
            font-size: 0.9rem;
        }

        .order-status {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
            text-transform: uppercase;
        }

        .status-pendiente {
            background: #fff3cd;
            color: #856404;
        }

        .status-confirmado {
            background: #d4edda;
            color: #155724;
        }

        .status-enviado {
            background: #cce5ff;
            color: #004085;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .actions-grid {
                grid-template-columns: 1fr;
            }

            .welcome-title {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-wrench"></i>
                <span>RepuMovil</span>
            </div>
            <div class="user-info">
                <span id="userName">Cargando...</span>
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <button onclick="logout()" style="background: rgba(255,255,255,0.2); border: none; color: white; padding: 8px 15px; border-radius: 5px; cursor: pointer;">
                    <i class="fas fa-sign-out-alt"></i> Salir
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Welcome Section -->
        <section class="welcome-section">
            <h1 class="welcome-title">¡Bienvenido a RepuMovil!</h1>
            <p class="welcome-subtitle">Gestiona tus repuestos y pedidos desde un solo lugar</p>
            <p style="color: var(--primary-color); font-weight: bold; font-size: 1.2rem;">
                "NO TE MUEVAS, NOSOTROS LLEVAMOS LOS REPUESTOS A VOS"
            </p>
        </section>

        <!-- Stats Cards -->
        <section class="stats-grid">
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon" style="background: var(--success-color);">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                </div>
                <div class="stat-number" id="totalPedidos">0</div>
                <div class="stat-label">Pedidos Realizados</div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon" style="background: var(--info-color);">
                        <i class="fas fa-clock"></i>
                    </div>
                </div>
                <div class="stat-number" id="pedidosPendientes">0</div>
                <div class="stat-label">Pedidos Pendientes</div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon" style="background: var(--warning-color);">
                        <i class="fas fa-star"></i>
                    </div>
                </div>
                <div class="stat-number">4.8</div>
                <div class="stat-label">Calificación Promedio</div>
            </div>
        </section>

        <!-- Action Cards -->
        <section class="actions-grid">
            <a href="catalogo-repuestos.php" class="action-card">
                <div class="action-icon" style="background: var(--primary-color);">
                    <i class="fas fa-search"></i>
                </div>
                <h3 class="action-title">Buscar Repuestos</h3>
                <p class="action-description">Explora nuestro catálogo completo de repuestos y encuentra lo que necesitas para tu taller.</p>
            </a>

            <a href="carrito.php" class="action-card">
                <div class="action-icon" style="background: var(--success-color);">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <h3 class="action-title">Mi Changuito</h3>
                <p class="action-description">Revisa los productos que has agregado y procede con tu pedido.</p>
            </a>

            <a href="mis-pedidos.php" class="action-card">
                <div class="action-icon" style="background: var(--info-color);">
                    <i class="fas fa-list-alt"></i>
                </div>
                <h3 class="action-title">Mis Pedidos</h3>
                <p class="action-description">Consulta el estado de tus pedidos y el historial de compras.</p>
            </a>

            <a href="perfil.php" class="action-card">
                <div class="action-icon" style="background: var(--warning-color);">
                    <i class="fas fa-user-cog"></i>
                </div>
                <h3 class="action-title">Mi Perfil</h3>
                <p class="action-description">Actualiza tu información personal y configuraciones de cuenta.</p>
            </a>
        </section>

        <!-- Recent Orders -->
        <section class="recent-orders">
            <h2 class="section-title">
                <i class="fas fa-history"></i>
                Pedidos Recientes
            </h2>
            <div class="orders-list" id="recentOrders">
                <p style="text-align: center; color: #666; padding: 20px;">Cargando pedidos...</p>
            </div>
        </section>
    </main>

    <script>
        // Verificar si hay usuario logueado
        const currentUser = JSON.parse(localStorage.getItem('repumovil_user') || 'null');

        if (!currentUser) {
            // Simular usuario para testing
            const testUser = {
                id: 2,
                username: 'taller1',
                email: '<EMAIL>',
                role_name: 'workshop'
            };
            localStorage.setItem('repumovil_user', JSON.stringify(testUser));
            window.location.reload();
        }

        // Mostrar información del usuario
        document.getElementById('userName').textContent = currentUser.username || 'Usuario';

        // Cargar estadísticas y pedidos
        loadDashboardData();

        async function loadDashboardData() {
            try {
                // Cargar pedidos del usuario
                const response = await fetch(`../api/pedidos.php?user_id=${currentUser.id}`);
                const result = await response.json();

                if (result.success) {
                    const pedidos = result.data;

                    // Actualizar estadísticas
                    document.getElementById('totalPedidos').textContent = pedidos.length;

                    const pendientes = pedidos.filter(p => p.estado === 'pendiente').length;
                    document.getElementById('pedidosPendientes').textContent = pendientes;

                    // Mostrar pedidos recientes
                    displayRecentOrders(pedidos.slice(0, 5));
                } else {
                    console.error('Error al cargar pedidos:', result.error);
                }
            } catch (error) {
                console.error('Error:', error);
            }
        }

        function displayRecentOrders(pedidos) {
            const container = document.getElementById('recentOrders');

            if (pedidos.length === 0) {
                container.innerHTML = '<p style="text-align: center; color: #666; padding: 20px;">No tienes pedidos recientes</p>';
                return;
            }

            container.innerHTML = pedidos.map(pedido => `
                <div class="order-item">
                    <div class="order-info">
                        <h4>Pedido #${pedido.id}</h4>
                        <p>${pedido.supplier_name} - $${parseFloat(pedido.total).toFixed(2)}</p>
                        <p><small>${new Date(pedido.created_at).toLocaleDateString()}</small></p>
                    </div>
                    <span class="order-status status-${pedido.estado}">${pedido.estado}</span>
                </div>
            `).join('');
        }

        function logout() {
            localStorage.removeItem('repumovil_user');
            window.location.href = 'login-dinamico.php';
        }
    </script>
</body>
</html>
