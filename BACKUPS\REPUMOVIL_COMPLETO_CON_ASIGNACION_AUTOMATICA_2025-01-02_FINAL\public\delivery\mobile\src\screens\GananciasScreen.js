import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';

const COLORS = {
  primary: '#FF6B35',
  red: '#E53E3E',
  white: '#FFFFFF',
  dark: '#2D3748',
  lightGray: '#F7FAFC',
  success: '#48BB78',
};

const GananciasScreen = () => {
  const ganancias = [
    { id: 1, fecha: '2024-01-15', pedidos: 8, monto: 12000, estado: 'pagado' },
    { id: 2, fecha: '2024-01-14', pedidos: 6, monto: 9000, estado: 'pendiente' },
    { id: 3, fecha: '2024-01-13', pedidos: 10, monto: 15000, estado: 'pagado' },
  ];

  const GananciaCard = ({ item }) => (
    <View style={styles.gananciaCard}>
      <View style={styles.gananciaHeader}>
        <Text style={styles.fecha}>{item.fecha}</Text>
        <View style={[styles.estadoBadge, 
          item.estado === 'pagado' ? styles.estadoPagado : styles.estadoPendiente
        ]}>
          <Text style={styles.estadoText}>
            {item.estado === 'pagado' ? 'Pagado' : 'Pendiente'}
          </Text>
        </View>
      </View>
      
      <View style={styles.gananciaInfo}>
        <Text style={styles.pedidosCount}>{item.pedidos} pedidos</Text>
        <Text style={styles.monto}>${item.monto.toLocaleString()}</Text>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Mis Ganancias</Text>
      </View>

      <ScrollView style={styles.content}>
        <View style={styles.resumenContainer}>
          <Text style={styles.sectionTitle}>Resumen del Mes</Text>
          <View style={styles.resumenCard}>
            <View style={styles.resumenItem}>
              <MaterialIcons name="attach-money" size={32} color={COLORS.success} />
              <Text style={styles.resumenValue}>$45,000</Text>
              <Text style={styles.resumenLabel}>Total Ganado</Text>
            </View>
            <View style={styles.resumenItem}>
              <MaterialIcons name="assignment" size={32} color={COLORS.primary} />
              <Text style={styles.resumenValue}>24</Text>
              <Text style={styles.resumenLabel}>Pedidos</Text>
            </View>
            <View style={styles.resumenItem}>
              <MaterialIcons name="trending-up" size={32} color={COLORS.red} />
              <Text style={styles.resumenValue}>$1,875</Text>
              <Text style={styles.resumenLabel}>Promedio</Text>
            </View>
          </View>
        </View>

        <View style={styles.historialContainer}>
          <Text style={styles.sectionTitle}>Historial Diario</Text>
          {ganancias.map((item) => (
            <GananciaCard key={item.id} item={item} />
          ))}
        </View>

        <TouchableOpacity style={styles.retirarButton}>
          <MaterialIcons name="account-balance" size={24} color={COLORS.white} />
          <Text style={styles.retirarButtonText}>Solicitar Retiro</Text>
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.lightGray,
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 20,
    backgroundColor: COLORS.white,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '800',
    color: COLORS.dark,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: COLORS.dark,
    marginBottom: 15,
  },
  resumenContainer: {
    marginBottom: 30,
  },
  resumenCard: {
    backgroundColor: COLORS.white,
    borderRadius: 15,
    padding: 20,
    flexDirection: 'row',
    justifyContent: 'space-around',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  resumenItem: {
    alignItems: 'center',
  },
  resumenValue: {
    fontSize: 20,
    fontWeight: '800',
    color: COLORS.dark,
    marginTop: 8,
  },
  resumenLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  historialContainer: {
    marginBottom: 30,
  },
  gananciaCard: {
    backgroundColor: COLORS.white,
    borderRadius: 15,
    padding: 15,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  gananciaHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  fecha: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  estadoBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
  },
  estadoPagado: {
    backgroundColor: '#C6F6D5',
  },
  estadoPendiente: {
    backgroundColor: '#FEEBC8',
  },
  estadoText: {
    fontSize: 12,
    fontWeight: '600',
    color: COLORS.dark,
  },
  gananciaInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  pedidosCount: {
    fontSize: 14,
    color: '#666',
  },
  monto: {
    fontSize: 18,
    fontWeight: '800',
    color: COLORS.success,
  },
  retirarButton: {
    backgroundColor: COLORS.red,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 15,
    borderRadius: 10,
    marginBottom: 20,
  },
  retirarButtonText: {
    color: COLORS.white,
    fontWeight: '700',
    fontSize: 16,
    marginLeft: 8,
  },
});

export default GananciasScreen;
