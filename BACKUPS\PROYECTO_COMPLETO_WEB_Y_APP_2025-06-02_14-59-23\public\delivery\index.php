<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RepuMovil Delivery - Generá ingresos con nosotros</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700;800&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-orange: #FF6B35;
            --secondary-orange: #F7931E;
            --primary-red: #E53E3E;
            --secondary-red: #FC8181;
            --gradient-main: linear-gradient(135deg, #FF6B35 0%, #E53E3E 50%, #F7931E 100%);
            --gradient-bg: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            --white: #ffffff;
            --dark: #2D3748;
            --light-gray: #F7FAFC;
            --box-shadow: 0 10px 30px rgba(229, 62, 62, 0.2);
            --box-shadow-hover: 0 15px 40px rgba(229, 62, 62, 0.3);
            --transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            line-height: 1.6;
            color: var(--dark);
            background: var(--gradient-bg);
            overflow-x: hidden;
        }

        /* Header */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
            padding: 15px 0;
            transition: var(--transition);
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
            text-decoration: none;
        }

        .logo-icon {
            width: 50px;
            height: 50px;
            background: var(--gradient-main);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            box-shadow: var(--box-shadow);
            transition: var(--transition);
        }

        .logo-icon:hover {
            transform: rotate(5deg) scale(1.05);
        }

        .logo-text {
            font-size: 1.8rem;
            font-weight: 800;
        }

        .logo-text .repu {
            color: var(--primary-orange);
        }

        .logo-text .movil {
            color: var(--primary-red);
        }

        .logo-text .delivery {
            color: var(--secondary-orange);
            font-weight: 900;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .nav-buttons {
            display: flex;
            gap: 1rem;
        }

        .btn {
            padding: 12px 24px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            transition: var(--transition);
            border: 2px solid transparent;
        }

        .btn-login {
            background: transparent;
            border-color: var(--primary-red);
            color: var(--primary-red);
        }

        .btn-login:hover {
            background: var(--primary-red);
            color: white;
            transform: translateY(-2px);
        }

        .btn-register {
            background: var(--gradient-main);
            color: white;
            box-shadow: var(--box-shadow);
        }

        .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-hover);
        }

        /* Hero Section */
        .hero {
            background: var(--gradient-main);
            color: white;
            padding: 120px 0 80px 0;
            text-align: center;
            position: relative;
            overflow: hidden;
            min-height: 100vh;
            display: flex;
            align-items: center;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .hero-content {
            max-width: 1000px;
            margin: 0 auto;
            position: relative;
            z-index: 2;
            padding: 0 2rem;
        }

        .hero h1 {
            font-size: 4rem;
            font-weight: 800;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            line-height: 1.2;
        }

        .hero-slogan {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 15px;
            text-transform: uppercase;
            letter-spacing: 2px;
            color: #FFE5D9;
        }

        .hero-subtitle {
            font-size: 1.4rem;
            margin-bottom: 40px;
            opacity: 0.95;
            font-weight: 400;
        }

        .cta-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 30px;
        }

        .cta-buttons .btn {
            padding: 18px 40px;
            font-weight: 700;
            border-radius: 50px;
            font-size: 1.1rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn-primary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid white;
            backdrop-filter: blur(10px);
        }

        .btn-primary:hover {
            background: white;
            color: var(--primary-red);
            transform: translateY(-3px);
        }

        .btn-secondary {
            background: rgba(0, 0, 0, 0.2);
            color: white;
            border: 2px solid rgba(255,255,255,0.3);
            backdrop-filter: blur(10px);
        }

        .btn-secondary:hover {
            background: rgba(0, 0, 0, 0.4);
            border-color: white;
            transform: translateY(-3px);
        }

        /* Features Section */
        .features {
            padding: 80px 0;
            background: white;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .section-title {
            text-align: center;
            margin-bottom: 60px;
        }

        .section-title h2 {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--dark);
            margin-bottom: 15px;
        }

        .section-title p {
            font-size: 1.2rem;
            color: #666;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .feature-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: var(--transition);
            border-top: 5px solid var(--primary-red);
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            background: var(--gradient-main);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            font-size: 2rem;
            color: white;
        }

        .feature-card h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: var(--dark);
        }

        .feature-card p {
            color: #666;
            line-height: 1.6;
        }

        /* Footer */
        .footer {
            background: var(--dark);
            color: white;
            padding: 40px 0 20px;
            text-align: center;
        }

        .footer p {
            margin-bottom: 10px;
        }

        .footer-links {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-bottom: 20px;
        }

        .footer-links a {
            color: white;
            text-decoration: none;
            transition: var(--transition);
        }

        .footer-links a:hover {
            color: var(--primary-orange);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.5rem;
            }

            .hero-slogan {
                font-size: 1.3rem;
            }

            .nav-container {
                padding: 0 1rem;
            }

            .nav-buttons {
                flex-direction: column;
                gap: 0.5rem;
            }

            .cta-buttons {
                flex-direction: column;
                align-items: center;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }

            .footer-links {
                flex-direction: column;
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="nav-container">
            <a href="index.php" class="logo">
                <div class="logo-icon">
                    <i class="fas fa-motorcycle"></i>
                </div>
                <div class="logo-text">
                    <span class="repu">Repu</span><span class="movil">Movil</span> <span class="delivery">Delivery</span>
                </div>
            </a>
            <div class="nav-buttons">
                <a href="login.php" class="btn btn-login">
                    <i class="fas fa-sign-in-alt"></i> Iniciar Sesión
                </a>
                <a href="registro.php" class="btn btn-register">
                    <i class="fas fa-user-plus"></i> Registrarse
                </a>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-content">
            <div class="hero-slogan">GENERÁ INGRESOS CON NOSOTROS</div>
            <h1>RepuMovil <span style="color: #FFE5D9;">Delivery</span></h1>
            <div class="hero-subtitle">Convertite en repartidor independiente y trabajá cuando quieras, donde quieras</div>
            <p style="font-size: 1.2rem; margin-bottom: 2rem;">🏍️ <strong>Moto</strong> • 🚗 <strong>Auto</strong> • 🚲 <strong>Bicicleta</strong></p>
            
            <div class="cta-buttons">
                <a href="registro.php" class="btn btn-primary">
                    <i class="fas fa-rocket"></i> Empezar Ahora
                </a>
                <a href="#como-funciona" class="btn btn-secondary">
                    <i class="fas fa-play"></i> Cómo Funciona
                </a>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features" id="como-funciona">
        <div class="container">
            <div class="section-title">
                <h2>¿Por qué elegir RepuMovil Delivery?</h2>
                <p>La plataforma que te permite generar ingresos de forma independiente</p>
            </div>
            
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h3>Horarios Flexibles</h3>
                    <p>Trabajá cuando quieras. Vos decidís tus horarios y días de trabajo. Libertad total para organizar tu tiempo.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <h3>Ganancias Inmediatas</h3>
                    <p>Cobrá por cada entrega realizada. Pagos semanales directos a tu cuenta bancaria. Sin demoras ni complicaciones.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-map-marked-alt"></i>
                    </div>
                    <h3>Zona de Trabajo</h3>
                    <p>Elegí tu zona de trabajo preferida. Conocé tu barrio y optimizá tus rutas para maximizar tus ganancias.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3>Seguridad Total</h3>
                    <p>Plataforma segura con seguimiento en tiempo real. Soporte 24/7 para cualquier inconveniente durante tus entregas.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3>App Fácil de Usar</h3>
                    <p>Aplicación intuitiva que te guía paso a paso. Navegación GPS integrada y comunicación directa con clientes.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3>Comunidad</h3>
                    <p>Unite a nuestra comunidad de repartidores. Compartí experiencias, tips y formá parte de un equipo ganador.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-links">
                <a href="#terminos">Términos y Condiciones</a>
                <a href="#privacidad">Política de Privacidad</a>
                <a href="#contacto">Contacto</a>
                <a href="#ayuda">Ayuda</a>
            </div>
            <p>&copy; 2024 RepuMovil Delivery. Todos los derechos reservados.</p>
            <p>Hecho con ❤️ en San Juan, Argentina</p>
        </div>
    </footer>
</body>
</html>
