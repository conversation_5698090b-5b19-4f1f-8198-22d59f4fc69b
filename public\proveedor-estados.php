<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RepuMovil - Estados de Pedidos 📦</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #FF6B35;
            --secondary-color: #FFA500;
            --dark-color: #2c3e50;
            --light-color: #f8f9fa;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
            color: white;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .title {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .stats-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            color: var(--dark-color);
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #666;
        }

        .pedidos-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
        }

        .pedido-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            color: var(--dark-color);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: transform 0.3s ease;
        }

        .pedido-card:hover {
            transform: translateY(-5px);
        }

        .pedido-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .pedido-numero {
            font-size: 1.2rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        .estado-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
            text-transform: uppercase;
        }

        .estado-asignado {
            background: #e3f2fd;
            color: #1976d2;
        }

        .estado-recogido {
            background: #fff3e0;
            color: #f57c00;
        }

        .estado-en-camino {
            background: #e8f5e8;
            color: #388e3c;
        }

        .estado-entregado {
            background: #f3e5f5;
            color: #7b1fa2;
        }

        .pedido-info {
            margin-bottom: 15px;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 0.9rem;
        }

        .info-label {
            color: #666;
        }

        .info-value {
            font-weight: bold;
        }

        .progress-container {
            margin: 15px 0;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            border-radius: 4px;
            transition: width 0.5s ease;
        }

        .progress-steps {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
        }

        .progress-step {
            font-size: 0.7rem;
            color: #666;
            text-align: center;
            flex: 1;
        }

        .progress-step.active {
            color: var(--primary-color);
            font-weight: bold;
        }

        .delivery-info {
            background: var(--light-color);
            border-radius: 8px;
            padding: 12px;
            margin-top: 15px;
        }

        .delivery-name {
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 5px;
        }

        .delivery-details {
            font-size: 0.8rem;
            color: #666;
        }

        .timeline {
            margin-top: 15px;
            border-top: 1px solid #eee;
            padding-top: 15px;
        }

        .timeline-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            font-size: 0.8rem;
        }

        .timeline-time {
            color: #666;
            min-width: 50px;
        }

        .timeline-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-size: 0.7rem;
            color: white;
        }

        .timeline-icon.asignado {
            background: #2196f3;
        }

        .timeline-icon.recogido {
            background: #ff9800;
        }

        .timeline-icon.en-camino {
            background: #4caf50;
        }

        .timeline-icon.entregado {
            background: #9c27b0;
        }

        .timeline-text {
            flex: 1;
            color: #666;
        }

        .refresh-btn {
            background: var(--success-color);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            margin: 20px auto;
            display: block;
            transition: all 0.3s ease;
        }

        .refresh-btn:hover {
            background: #218838;
            transform: translateY(-2px);
        }

        .filter-buttons {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .filter-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: bold;
        }

        .filter-btn.active {
            background: white;
            color: var(--primary-color);
        }

        .filter-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .filter-btn.active:hover {
            background: white;
        }

        @media (max-width: 768px) {
            .pedidos-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-overview {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">📦📊</div>
            <h1 class="title">Estados de Pedidos en Tiempo Real</h1>
            <p class="subtitle">RepuMovil - Dashboard del Proveedor</p>
        </div>

        <!-- Estadísticas Generales -->
        <div class="stats-overview">
            <div class="stat-card">
                <div class="stat-number" id="total-pedidos">8</div>
                <div class="stat-label">Pedidos Activos</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="pedidos-asignados">2</div>
                <div class="stat-label">Asignados</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="pedidos-recogidos">3</div>
                <div class="stat-label">Recogidos</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="pedidos-en-camino">2</div>
                <div class="stat-label">En Camino</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="pedidos-entregados">1</div>
                <div class="stat-label">Entregados Hoy</div>
            </div>
        </div>

        <!-- Filtros -->
        <div class="filter-buttons">
            <button class="filter-btn active" onclick="filterPedidos('todos')">Todos</button>
            <button class="filter-btn" onclick="filterPedidos('asignado')">Asignados</button>
            <button class="filter-btn" onclick="filterPedidos('recogido')">Recogidos</button>
            <button class="filter-btn" onclick="filterPedidos('en_camino')">En Camino</button>
            <button class="filter-btn" onclick="filterPedidos('entregado')">Entregados</button>
        </div>

        <!-- Grid de Pedidos -->
        <div class="pedidos-grid" id="pedidos-container">
            <!-- Los pedidos se cargarán dinámicamente -->
        </div>

        <button class="refresh-btn" onclick="refreshPedidos()">
            <i class="fas fa-sync-alt"></i>
            Actualizar Estados
        </button>
    </div>

    <script>
        // PASO 8: Datos de ejemplo de pedidos
        let pedidos = [
            {
                id: '1024',
                cliente: 'Taller Mecánico Central',
                direccion: 'Av. Libertador 1234',
                total: 2450,
                estado: 'en_camino',
                delivery: 'Juan Pérez',
                delivery_telefono: '+54 ************',
                delivery_calificacion: 4.8,
                eta: 12,
                progreso: 75,
                timeline: [
                    { estado: 'asignado', tiempo: '14:15', descripcion: 'Pedido asignado a Juan Pérez' },
                    { estado: 'recogido', tiempo: '14:30', descripcion: 'Pedido recogido del proveedor' },
                    { estado: 'en_camino', tiempo: '14:45', descripcion: 'En camino hacia el cliente' }
                ]
            },
            {
                id: '1025',
                cliente: 'AutoService Plus',
                direccion: 'Ruta 40 Km 15',
                total: 3200,
                estado: 'recogido',
                delivery: 'María González',
                delivery_telefono: '+54 ************',
                delivery_calificacion: 4.9,
                eta: 25,
                progreso: 50,
                timeline: [
                    { estado: 'asignado', tiempo: '14:20', descripcion: 'Pedido asignado a María González' },
                    { estado: 'recogido', tiempo: '14:35', descripcion: 'Pedido recogido del proveedor' }
                ]
            },
            {
                id: '1026',
                cliente: 'Mecánico López',
                direccion: 'Calle Rivadavia 567',
                total: 1800,
                estado: 'asignado',
                delivery: 'Carlos Rodríguez',
                delivery_telefono: '+54 ************',
                delivery_calificacion: 4.7,
                eta: 35,
                progreso: 25,
                timeline: [
                    { estado: 'asignado', tiempo: '14:50', descripcion: 'Pedido asignado a Carlos Rodríguez' }
                ]
            }
        ];

        let filtroActual = 'todos';

        // PASO 8: Renderizar pedidos
        function renderPedidos() {
            const container = document.getElementById('pedidos-container');
            const pedidosFiltrados = filtroActual === 'todos' ? 
                pedidos : pedidos.filter(p => p.estado === filtroActual);

            container.innerHTML = pedidosFiltrados.map(pedido => `
                <div class="pedido-card" data-estado="${pedido.estado}">
                    <div class="pedido-header">
                        <div class="pedido-numero">Pedido #${pedido.id}</div>
                        <div class="estado-badge estado-${pedido.estado}">
                            ${getEstadoDisplayName(pedido.estado)}
                        </div>
                    </div>
                    
                    <div class="pedido-info">
                        <div class="info-row">
                            <span class="info-label">Cliente:</span>
                            <span class="info-value">${pedido.cliente}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">Dirección:</span>
                            <span class="info-value">${pedido.direccion}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">Total:</span>
                            <span class="info-value">$${pedido.total.toLocaleString()}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">ETA:</span>
                            <span class="info-value">${pedido.eta} minutos</span>
                        </div>
                    </div>

                    <div class="progress-container">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${pedido.progreso}%"></div>
                        </div>
                        <div class="progress-steps">
                            <div class="progress-step ${pedido.progreso >= 25 ? 'active' : ''}">Asignado</div>
                            <div class="progress-step ${pedido.progreso >= 50 ? 'active' : ''}">Recogido</div>
                            <div class="progress-step ${pedido.progreso >= 75 ? 'active' : ''}">En Camino</div>
                            <div class="progress-step ${pedido.progreso >= 100 ? 'active' : ''}">Entregado</div>
                        </div>
                    </div>

                    <div class="delivery-info">
                        <div class="delivery-name">🏍️ ${pedido.delivery}</div>
                        <div class="delivery-details">
                            📞 ${pedido.delivery_telefono} • ⭐ ${pedido.delivery_calificacion}
                        </div>
                    </div>

                    <div class="timeline">
                        ${pedido.timeline.map(item => `
                            <div class="timeline-item">
                                <div class="timeline-time">${item.tiempo}</div>
                                <div class="timeline-icon ${item.estado}">
                                    ${getEstadoIcon(item.estado)}
                                </div>
                                <div class="timeline-text">${item.descripcion}</div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `).join('');
        }

        // PASO 8: Obtener nombre del estado
        function getEstadoDisplayName(estado) {
            const nombres = {
                'asignado': 'Asignado',
                'recogido': 'Recogido',
                'en_camino': 'En Camino',
                'entregado': 'Entregado'
            };
            return nombres[estado] || estado;
        }

        // PASO 8: Obtener icono del estado
        function getEstadoIcon(estado) {
            const iconos = {
                'asignado': '📋',
                'recogido': '📦',
                'en_camino': '🚚',
                'entregado': '✅'
            };
            return iconos[estado] || '❓';
        }

        // PASO 8: Filtrar pedidos
        function filterPedidos(filtro) {
            filtroActual = filtro;
            
            // Actualizar botones
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // Re-renderizar
            renderPedidos();
            updateStats();
        }

        // PASO 8: Actualizar estadísticas
        function updateStats() {
            const stats = {
                total: pedidos.length,
                asignados: pedidos.filter(p => p.estado === 'asignado').length,
                recogidos: pedidos.filter(p => p.estado === 'recogido').length,
                en_camino: pedidos.filter(p => p.estado === 'en_camino').length,
                entregados: pedidos.filter(p => p.estado === 'entregado').length
            };

            document.getElementById('total-pedidos').textContent = stats.total;
            document.getElementById('pedidos-asignados').textContent = stats.asignados;
            document.getElementById('pedidos-recogidos').textContent = stats.recogidos;
            document.getElementById('pedidos-en-camino').textContent = stats.en_camino;
            document.getElementById('pedidos-entregados').textContent = stats.entregados;
        }

        // PASO 8: Simular actualizaciones de estado
        function simulateStatusUpdates() {
            pedidos.forEach(pedido => {
                if (Math.random() > 0.9) { // 10% probabilidad
                    const siguienteEstado = getNextStatus(pedido.estado);
                    if (siguienteEstado) {
                        updatePedidoStatus(pedido, siguienteEstado);
                    }
                }
            });
        }

        // PASO 8: Obtener siguiente estado
        function getNextStatus(currentStatus) {
            const sequence = ['asignado', 'recogido', 'en_camino', 'entregado'];
            const currentIndex = sequence.indexOf(currentStatus);
            return currentIndex < sequence.length - 1 ? sequence[currentIndex + 1] : null;
        }

        // PASO 8: Actualizar estado de pedido
        function updatePedidoStatus(pedido, nuevoEstado) {
            const progressMap = {
                'asignado': 25,
                'recogido': 50,
                'en_camino': 75,
                'entregado': 100
            };

            pedido.estado = nuevoEstado;
            pedido.progreso = progressMap[nuevoEstado];
            
            // Agregar al timeline
            const now = new Date();
            const timeStr = now.getHours().toString().padStart(2, '0') + ':' + 
                           now.getMinutes().toString().padStart(2, '0');
            
            pedido.timeline.push({
                estado: nuevoEstado,
                tiempo: timeStr,
                descripcion: `Estado actualizado a ${getEstadoDisplayName(nuevoEstado)}`
            });

            console.log(`📦 Pedido #${pedido.id} actualizado a: ${nuevoEstado}`);
        }

        // PASO 8: Refrescar pedidos
        function refreshPedidos() {
            const btn = document.querySelector('.refresh-btn');
            const originalText = btn.innerHTML;
            
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Actualizando...';
            btn.disabled = true;
            
            setTimeout(() => {
                simulateStatusUpdates();
                renderPedidos();
                updateStats();
                
                btn.innerHTML = '<i class="fas fa-check"></i> Actualizado';
                
                setTimeout(() => {
                    btn.innerHTML = originalText;
                    btn.disabled = false;
                }, 1000);
            }, 1500);
        }

        // PASO 8: Inicializar
        document.addEventListener('DOMContentLoaded', function() {
            renderPedidos();
            updateStats();
            
            // Actualizar automáticamente cada 30 segundos
            setInterval(() => {
                simulateStatusUpdates();
                renderPedidos();
                updateStats();
            }, 30000);
        });
    </script>
</body>
</html>
