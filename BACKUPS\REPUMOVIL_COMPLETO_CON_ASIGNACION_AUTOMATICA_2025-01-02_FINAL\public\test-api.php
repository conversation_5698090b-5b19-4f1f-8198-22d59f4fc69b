<?php
// Test directo del API de registro
echo "<h1>🧪 Test del API de Registro</h1>";

// Simular método POST
$_SERVER['REQUEST_METHOD'] = 'POST';

// Simular datos POST
$_POST = [
    'user_type' => 'usuario_regular',
    'nombre' => 'Juan <PERSON> Test',
    'email' => 'test' . time() . '@test.com',
    'telefono' => '123456789',
    'password' => '123456',
    'confirm_password' => '123456'
];

echo "<h2>📝 Datos de prueba:</h2>";
echo "<pre>";
print_r($_POST);
echo "</pre>";

echo "<h2>🔄 Ejecutando API...</h2>";

// Incluir el API
ob_start();
include 'api/registro-dinamico.php';
$output = ob_get_clean();

echo "<h2>📤 Respuesta del API:</h2>";
echo "<pre>$output</pre>";

echo "<h2>✅ Test completado</h2>";
?>
