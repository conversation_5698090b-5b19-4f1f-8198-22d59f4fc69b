<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RepuMovil Común - Dashboard Taller</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .navbar-custom {
            background: linear-gradient(135deg, #FF6B35 0%, #FFA500 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-size: 24px;
            font-weight: bold;
            color: white !important;
        }

        .logo-repu {
            color: white;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .logo-movil {
            color: #FFE4B5;
        }

        .welcome-section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .welcome-section::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 107, 53, 0.05), transparent);
            animation: shine 3s infinite;
        }

        @keyframes shine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .welcome-title {
            font-size: 32px;
            color: #333;
            margin-bottom: 15px;
            position: relative;
            z-index: 1;
        }

        .welcome-subtitle {
            font-size: 18px;
            color: #666;
            position: relative;
            z-index: 1;
        }

        .logo-destacado {
            background: linear-gradient(135deg, #FF6B35, #FFA500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 900;
            font-size: 36px;
            text-shadow: 0 0 20px rgba(255, 107, 53, 0.5);
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }

        .dashboard-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: none;
            position: relative;
            overflow: hidden;
        }

        .dashboard-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .dashboard-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(135deg, #FF6B35 0%, #FFA500 100%);
        }

        .card-icon {
            font-size: 60px;
            margin-bottom: 20px;
            display: block;
        }

        .icon-pedidos { color: #4CAF50; }
        .icon-calificaciones { color: #FFD700; }

        .card-title {
            font-size: 24px;
            font-weight: 700;
            color: #333;
            margin-bottom: 15px;
        }

        .card-description {
            color: #666;
            font-size: 16px;
            margin-bottom: 25px;
            line-height: 1.6;
        }

        .btn-card {
            background: linear-gradient(135deg, #FF6B35 0%, #FFA500 100%);
            border: none;
            border-radius: 12px;
            padding: 15px 25px;
            color: white;
            font-weight: 600;
            width: 100%;
            transition: all 0.3s ease;
            font-size: 16px;
        }

        .btn-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(255, 107, 53, 0.4);
            color: white;
        }

        .special-card {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
        }

        .special-card .card-title,
        .special-card .card-description {
            color: white;
        }

        .special-card .btn-card {
            background: white;
            color: #4CAF50;
        }

        .special-card .btn-card:hover {
            background: #f8f9fa;
            color: #4CAF50;
        }

        .stats-row {
            display: flex;
            justify-content: space-around;
            margin: 30px 0;
            flex-wrap: wrap;
            gap: 20px;
        }

        .stat-item {
            text-align: center;
            background: white;
            padding: 25px;
            border-radius: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            flex: 1;
            min-width: 180px;
        }

        .stat-number {
            font-size: 36px;
            font-weight: bold;
            color: #FF6B35;
            display: block;
        }

        .stat-label {
            font-size: 14px;
            color: #666;
            margin-top: 8px;
        }

        .changuito-icon {
            background: linear-gradient(135deg, #FF6B35, #FFA500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 24px;
            margin-right: 8px;
        }

        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }

            .welcome-title {
                font-size: 28px;
            }

            .logo-destacado {
                font-size: 32px;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-wrench me-2"></i>
                <span class="logo-repu">Repu</span><span class="logo-movil">Movil</span>
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="#" onclick="logout()">
                    <i class="fas fa-sign-out-alt me-1"></i>Cerrar Sesión
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Mensaje de Bienvenida -->
        <div class="welcome-section">
            <h1 class="welcome-title">
                <span class="logo-destacado">RepuMovil</span>
            </h1>
            <p class="welcome-subtitle">
                Repuestos que llegan a tu taller, cuando los necesitas.
            </p>
        </div>

        <!-- Estadísticas Rápidas -->
        <div class="stats-row">
            <div class="stat-item">
                <span class="stat-number">0</span>
                <div class="stat-label">Pedidos Realizados</div>
            </div>
            <div class="stat-item">
                <span class="stat-number">0</span>
                <div class="stat-label">Repuestos en Changuito</div>
            </div>
            <div class="stat-item">
                <span class="stat-number">4.8</span>
                <div class="stat-label">Calificación Promedio</div>
            </div>
        </div>

        <!-- Dashboard Principal -->
        <div class="dashboard-grid">
            <!-- Pedido de Repuestos -->
            <div class="dashboard-card special-card">
                <i class="fas fa-shopping-cart card-icon icon-pedidos"></i>
                <h3 class="card-title">📋 Pedido de Repuestos</h3>
                <p class="card-description">
                    Busca y solicita repuestos directamente a proveedores verificados.
                    <span class="changuito-icon">🛒</span>Agrega al changuito y realiza tu pedido.
                </p>
                <button class="btn btn-card" onclick="abrirPedidoRepuestos()">
                    <i class="fas fa-search me-2"></i>Buscar Repuestos
                </button>
            </div>

            <!-- Calificaciones Recibidas -->
            <div class="dashboard-card">
                <i class="fas fa-star card-icon icon-calificaciones"></i>
                <h3 class="card-title">⭐ Calificaciones Recibidas</h3>
                <p class="card-description">
                    Revisa las calificaciones y comentarios de tus clientes.
                    Mantén tu reputación y mejora tu servicio.
                </p>
                <button class="btn btn-card" onclick="verCalificaciones()">
                    <i class="fas fa-eye me-2"></i>Ver Calificaciones
                </button>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Funciones del Dashboard Taller Común
        function abrirPedidoRepuestos() {
            // Crear modal para búsqueda de repuestos
            const modalHTML = `
                <div class="modal fade" id="pedidoModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header" style="background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%); color: white;">
                                <h5 class="modal-title">
                                    <i class="fas fa-shopping-cart me-2"></i>Pedido de Repuestos
                                </h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <!-- Búsqueda -->
                                <div class="mb-4">
                                    <label class="form-label fw-bold">🔍 Buscar Repuesto:</label>
                                    <input type="text" class="form-control form-control-lg"
                                           placeholder="Ej: Bujía para VW Gol 2010"
                                           id="busquedaRepuesto">
                                    <small class="text-muted">Busca por marca, modelo, año o tipo de repuesto</small>
                                </div>

                                <!-- Resultados de búsqueda -->
                                <div id="resultadosBusqueda" class="mb-4">
                                    <h6 class="fw-bold">Resultados encontrados:</h6>
                                    <div class="list-group">
                                        <div class="list-group-item d-flex justify-content-between align-items-center">
                                            <div>
                                                <strong>Bujía NGK VW Gol 2010</strong><br>
                                                <small class="text-muted">Proveedor: AutoPartes Central</small><br>
                                                <span class="badge bg-success">En Stock: 15 unidades</span>
                                            </div>
                                            <div class="text-end">
                                                <div class="fw-bold text-success">$2,500</div>
                                                <button class="btn btn-sm btn-outline-primary" onclick="agregarAlChanguito('Bujía NGK VW Gol 2010', 2500)">
                                                    🛒 Agregar
                                                </button>
                                            </div>
                                        </div>
                                        <div class="list-group-item d-flex justify-content-between align-items-center">
                                            <div>
                                                <strong>Bujía Bosch VW Gol 2010</strong><br>
                                                <small class="text-muted">Proveedor: Repuestos del Norte</small><br>
                                                <span class="badge bg-warning">Stock Bajo: 3 unidades</span>
                                            </div>
                                            <div class="text-end">
                                                <div class="fw-bold text-success">$2,800</div>
                                                <button class="btn btn-sm btn-outline-primary" onclick="agregarAlChanguito('Bujía Bosch VW Gol 2010', 2800)">
                                                    🛒 Agregar
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Changuito -->
                                <div class="border rounded p-3" style="background-color: #f8f9fa;">
                                    <h6 class="fw-bold">🛒 Mi Changuito:</h6>
                                    <div id="itemsChanguito">
                                        <p class="text-muted">No hay items en el changuito</p>
                                    </div>
                                    <hr>
                                    <div class="d-flex justify-content-between">
                                        <strong>Total:</strong>
                                        <strong id="totalChanguito">$0</strong>
                                    </div>
                                </div>

                                <!-- Método de Pago -->
                                <div class="mt-4">
                                    <h6 class="fw-bold">💳 Método de Pago:</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="metodoPago" value="efectivo" checked>
                                                <label class="form-check-label">💵 Efectivo</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="metodoPago" value="transferencia">
                                                <label class="form-check-label">🏦 Transferencia</label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="metodoPago" value="tarjeta">
                                                <label class="form-check-label">💳 Tarjeta</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="metodoPago" value="cuenta_corriente">
                                                <label class="form-check-label">📋 Cuenta Corriente</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                                <button type="button" class="btn btn-success" onclick="realizarPedido()">
                                    <i class="fas fa-check me-2"></i>Realizar Pedido
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Agregar modal al DOM
            document.body.insertAdjacentHTML('beforeend', modalHTML);

            // Mostrar modal
            const modal = new bootstrap.Modal(document.getElementById('pedidoModal'));
            modal.show();

            // Limpiar modal cuando se cierre
            document.getElementById('pedidoModal').addEventListener('hidden.bs.modal', function () {
                this.remove();
            });
        }

        let changuito = [];
        let totalChanguito = 0;

        function agregarAlChanguito(nombre, precio) {
            changuito.push({nombre, precio});
            totalChanguito += precio;
            actualizarChanguito();

            // Mostrar notificación
            alert(`✅ ${nombre} agregado al changuito!`);
        }

        function actualizarChanguito() {
            const container = document.getElementById('itemsChanguito');
            const totalElement = document.getElementById('totalChanguito');

            if (changuito.length === 0) {
                container.innerHTML = '<p class="text-muted">No hay items en el changuito</p>';
            } else {
                container.innerHTML = changuito.map((item, index) => `
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>${item.nombre}</span>
                        <div>
                            <span class="fw-bold">$${item.precio.toLocaleString()}</span>
                            <button class="btn btn-sm btn-outline-danger ms-2" onclick="eliminarDelChanguito(${index})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                `).join('');
            }

            totalElement.textContent = `$${totalChanguito.toLocaleString()}`;
        }

        function eliminarDelChanguito(index) {
            totalChanguito -= changuito[index].precio;
            changuito.splice(index, 1);
            actualizarChanguito();
        }

        function realizarPedido() {
            if (changuito.length === 0) {
                alert('❌ El changuito está vacío');
                return;
            }

            const metodoPago = document.querySelector('input[name="metodoPago"]:checked').value;

            alert(`🎉 ¡Pedido realizado exitosamente!\n\nTotal: $${totalChanguito.toLocaleString()}\nMétodo de pago: ${metodoPago}\nItems: ${changuito.length}\n\nEl proveedor será notificado y te contactará pronto.`);

            // Cerrar modal
            bootstrap.Modal.getInstance(document.getElementById('pedidoModal')).hide();

            // Limpiar changuito
            changuito = [];
            totalChanguito = 0;
        }

        function verCalificaciones() {
            alert('⭐ Función: Calificaciones Recibidas\n\nAquí podrás ver:\n• Calificaciones de clientes\n• Comentarios y reseñas\n• Promedio general\n• Sugerencias de mejora\n\n¡Mantén tu reputación alta para más clientes!');
        }

        function logout() {
            if (confirm('¿Estás seguro que quieres cerrar sesión?')) {
                window.location.href = 'login-dinamico.php';
            }
        }

        // Animaciones al cargar la página
        document.addEventListener('DOMContentLoaded', function() {
            // Animación de entrada para las cards
            const cards = document.querySelectorAll('.dashboard-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });
    </script>
</body>
</html>
