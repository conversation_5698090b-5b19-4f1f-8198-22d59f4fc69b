<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RepuMovil - <PERSON> 🛒</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #FF6B35;
            --secondary-color: #FFA500;
            --dark-color: #2c3e50;
            --light-color: #f8f9fa;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1.5rem;
            font-weight: bold;
        }

        .nav-buttons {
            display: flex;
            gap: 10px;
        }

        .nav-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .nav-btn:hover {
            background: rgba(255,255,255,0.3);
            color: white;
            text-decoration: none;
        }

        /* Main Content */
        .main-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        /* Changuito Header */
        .cart-header {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            text-align: center;
        }

        .cart-title {
            color: var(--dark-color);
            font-size: 2rem;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .cart-subtitle {
            color: #666;
            font-size: 1.1rem;
        }

        /* Changuito Content */
        .cart-content {
            display: grid;
            grid-template-columns: 1fr 350px;
            gap: 30px;
        }

        /* Changuito Items */
        .cart-items {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .items-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid var(--light-color);
        }

        .items-title {
            color: var(--dark-color);
            font-size: 1.5rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .clear-changuito-btn {
            background: var(--danger-color);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9rem;
        }

        .clear-changuito-btn:hover {
            background: #c82333;
        }

        .cart-item {
            display: flex;
            align-items: center;
            padding: 20px;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }

        .cart-item:hover {
            border-color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .item-image {
            width: 80px;
            height: 80px;
            background: var(--light-color);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            color: var(--primary-color);
            font-size: 2rem;
        }

        .item-details {
            flex: 1;
        }

        .item-name {
            color: var(--dark-color);
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .item-brand {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 10px;
        }

        .item-price {
            color: var(--primary-color);
            font-size: 1.1rem;
            font-weight: bold;
        }

        .item-actions {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .quantity-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .qty-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .qty-btn:hover {
            background: #e55a2b;
        }

        .qty-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        .qty-display {
            font-size: 1.1rem;
            font-weight: bold;
            min-width: 30px;
            text-align: center;
        }

        .remove-btn {
            background: var(--danger-color);
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 5px;
            cursor: pointer;
        }

        .remove-btn:hover {
            background: #c82333;
        }

        /* Changuito Summary */
        .cart-summary {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            height: fit-content;
            position: sticky;
            top: 20px;
        }

        .summary-title {
            color: var(--dark-color);
            font-size: 1.5rem;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e9ecef;
        }

        .summary-row:last-child {
            border-bottom: none;
            margin-bottom: 25px;
            font-size: 1.2rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        .checkout-btn {
            width: 100%;
            background: var(--success-color);
            color: white;
            border: none;
            padding: 15px;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .checkout-btn:hover {
            background: #218838;
        }

        .checkout-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        /* Empty Changuito */
        .empty-cart {
            text-align: center;
            padding: 50px;
            color: #666;
        }

        .empty-cart i {
            font-size: 5rem;
            color: var(--primary-color);
            margin-bottom: 20px;
        }

        .empty-cart h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
        }

        .shop-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-top: 20px;
        }

        .shop-btn:hover {
            background: #e55a2b;
            color: white;
            text-decoration: none;
        }

        /* Loading */
        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }

        .loading i {
            font-size: 3rem;
            color: var(--primary-color);
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .cart-content {
                grid-template-columns: 1fr;
            }

            .cart-item {
                flex-direction: column;
                text-align: center;
                gap: 15px;
            }

            .item-image {
                margin-right: 0;
            }

            .item-actions {
                justify-content: center;
            }

            .header-content {
                flex-direction: column;
                gap: 15px;
            }

            .nav-buttons {
                flex-wrap: wrap;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-wrench"></i>
                <span>RepuMovil</span>
            </div>
            <div class="nav-buttons">
                <a href="dashboard-taller.php" class="nav-btn">
                    <i class="fas fa-home"></i> Dashboard
                </a>
                <a href="catalogo-repuestos.php" class="nav-btn">
                    <i class="fas fa-search"></i> Catálogo
                </a>
                <a href="mis-pedidos.php" class="nav-btn">
                    <i class="fas fa-list-alt"></i> Mis Pedidos
                </a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Cart Header -->
        <section class="cart-header">
            <h1 class="cart-title">
                <i class="fas fa-shopping-cart"></i>
                Mi Changuito
                <span style="font-size: 1.5rem;">🛒</span>
            </h1>
            <p class="cart-subtitle">"CLICK, PEDÍ, RECIBÍ - REPARAR NUNCA FUE TAN FÁCIL"</p>
        </section>

        <!-- Cart Content -->
        <div class="cart-content" id="cartContent">
            <div class="loading">
                <i class="fas fa-spinner"></i>
                <p>Cargando tu changuito...</p>
            </div>
        </div>
    </main>

    <script>
        // Verificar usuario logueado
        const currentUser = JSON.parse(localStorage.getItem('repumovil_user') || 'null');
        if (!currentUser) {
            window.location.href = 'login-dinamico.php';
        }

        let cartItems = [];

        // Cargar carrito al iniciar
        loadCart();

        async function loadCart() {
            try {
                const response = await fetch(`../api/carrito.php?user_id=${currentUser.id}`);
                const result = await response.json();

                if (result.success) {
                    cartItems = result.data.items;
                    displayCart(result.data);
                } else {
                    showError('Error al cargar el changuito: ' + result.error);
                }
            } catch (error) {
                showError('Error de conexión: ' + error.message);
            }
        }

        function displayCart(cartData) {
            const container = document.getElementById('cartContent');

            if (cartItems.length === 0) {
                container.innerHTML = `
                    <div class="empty-cart" style="grid-column: 1 / -1;">
                        <i class="fas fa-shopping-cart"></i>
                        <h3>Tu changuito está vacío</h3>
                        <p>¡Agregá algunos repuestos para empezar!</p>
                        <a href="catalogo-repuestos.php" class="shop-btn">
                            <i class="fas fa-search"></i> Explorar Catálogo
                        </a>
                    </div>
                `;
                return;
            }

            container.innerHTML = `
                <!-- Cart Items -->
                <div class="cart-items">
                    <div class="items-header">
                        <h2 class="items-title">
                            <i class="fas fa-list"></i>
                            Productos (${cartData.summary.item_count})
                        </h2>
                        <button class="clear-changuito-btn" onclick="clearCart()">
                            <i class="fas fa-trash"></i> Vaciar Changuito
                        </button>
                    </div>
                    <div class="items-list">
                        ${cartItems.map(item => `
                            <div class="cart-item">
                                <div class="item-image">
                                    <i class="fas fa-cog"></i>
                                </div>
                                <div class="item-details">
                                    <h3 class="item-name">${item.nombre}</h3>
                                    <p class="item-brand">${item.marca} - ${item.modelo || 'Universal'}</p>
                                    <p class="item-price">$${parseFloat(item.precio_unitario).toFixed(2)} c/u</p>
                                </div>
                                <div class="item-actions">
                                    <div class="quantity-controls">
                                        <button class="qty-btn" onclick="updateQuantity(${item.id}, ${item.cantidad - 1})" ${item.cantidad <= 1 ? 'disabled' : ''}>
                                            <i class="fas fa-minus"></i>
                                        </button>
                                        <span class="qty-display">${item.cantidad}</span>
                                        <button class="qty-btn" onclick="updateQuantity(${item.id}, ${item.cantidad + 1})" ${item.cantidad >= item.stock ? 'disabled' : ''}>
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    </div>
                                    <button class="remove-btn" onclick="removeItem(${item.id})">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>

                <!-- Cart Summary -->
                <div class="cart-summary">
                    <h2 class="summary-title">
                        <i class="fas fa-calculator"></i>
                        Resumen
                    </h2>
                    <div class="summary-row">
                        <span>Productos (${cartData.summary.total_items})</span>
                        <span>$${cartData.summary.total_amount.toFixed(2)}</span>
                    </div>
                    <div class="summary-row">
                        <span>Envío</span>
                        <span style="color: var(--success-color);">GRATIS</span>
                    </div>
                    <div class="summary-row">
                        <span><strong>Total</strong></span>
                        <span><strong>$${cartData.summary.total_amount.toFixed(2)}</strong></span>
                    </div>
                    <button class="checkout-btn" onclick="proceedToCheckout()">
                        <i class="fas fa-credit-card"></i> Proceder al Pago
                    </button>
                </div>
            `;
        }

        async function updateQuantity(cartId, newQuantity) {
            if (newQuantity <= 0) return;

            try {
                const response = await fetch('../api/carrito.php', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        carrito_id: cartId,
                        cantidad: newQuantity
                    })
                });

                const result = await response.json();

                if (result.success) {
                    loadCart(); // Recargar carrito
                } else {
                    alert('Error: ' + result.error);
                }
            } catch (error) {
                alert('Error de conexión: ' + error.message);
            }
        }

        async function removeItem(cartId) {
            if (!confirm('¿Estás seguro de que querés eliminar este producto?')) return;

            try {
                const response = await fetch(`../api/carrito.php?carrito_id=${cartId}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (result.success) {
                    loadCart(); // Recargar carrito
                } else {
                    alert('Error: ' + result.error);
                }
            } catch (error) {
                alert('Error de conexión: ' + error.message);
            }
        }

        async function clearCart() {
            if (!confirm('¿Estás seguro de que querés vaciar todo el changuito?')) return;

            try {
                // Eliminar todos los items uno por uno
                for (const item of cartItems) {
                    await fetch(`../api/carrito.php?carrito_id=${item.id}`, {
                        method: 'DELETE'
                    });
                }

                loadCart(); // Recargar carrito
            } catch (error) {
                alert('Error de conexión: ' + error.message);
            }
        }

        function proceedToCheckout() {
            if (cartItems.length === 0) {
                alert('Tu changuito está vacío');
                return;
            }

            window.location.href = 'checkout.php';
        }

        function showError(message) {
            document.getElementById('cartContent').innerHTML = `
                <div style="grid-column: 1 / -1; text-align: center; padding: 50px; color: var(--danger-color);">
                    <i class="fas fa-exclamation-triangle" style="font-size: 3rem; margin-bottom: 20px;"></i>
                    <p>${message}</p>
                    <button onclick="loadCart()" style="background: var(--primary-color); color: white; border: none; padding: 10px 20px; border-radius: 5px; margin-top: 15px; cursor: pointer;">
                        Reintentar
                    </button>
                </div>
            `;
        }
    </script>
</body>
</html>
