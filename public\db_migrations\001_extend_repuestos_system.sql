-- =====================================================
-- MIGRACIÓN 001: EXTENDER SISTEMA DE REPUESTOS
-- Fecha: 2025-01-06
-- Descripción: Agregar funcionalidades avanzadas sin romper lo existente
-- =====================================================

-- PASO 1: AGREGAR CAMPOS NUEVOS A TABLA REPUESTOS EXISTENTE
-- (Solo agregamos, no modificamos ni eliminamos nada)

ALTER TABLE repuestos 
ADD COLUMN IF NOT EXISTS marca_vehiculo VARCHAR(100) COMMENT 'Marca del vehículo compatible (ej: Ford, Volkswagen)',
ADD COLUMN IF NOT EXISTS modelo_vehiculo VARCHAR(100) COMMENT 'Modelo del vehículo compatible (ej: Focus, Gol)',
ADD COLUMN IF NOT EXISTS año_desde INT COMMENT 'Año desde el cual es compatible',
ADD COLUMN IF NOT EXISTS año_hasta INT COMMENT 'Año hasta el cual es compatible',
ADD COLUMN IF NOT EXISTS numero_pieza VARCHAR(100) COMMENT 'Número de pieza específico del fabricante',
ADD COLUMN IF NOT EXISTS calidad ENUM('original', 'primera_linea', 'generico') DEFAULT 'generico' COMMENT 'Calidad del repuesto',
ADD COLUMN IF NOT EXISTS medidas TEXT COMMENT 'Medidas técnicas detalladas',
ADD COLUMN IF NOT EXISTS activo BOOLEAN DEFAULT TRUE COMMENT 'Si el producto está activo para venta',
ADD COLUMN IF NOT EXISTS supplier_name VARCHAR(100) COMMENT 'Nombre del proveedor (para compatibilidad)';

-- PASO 2: CREAR TABLA DE IMÁGENES MÚLTIPLES
CREATE TABLE IF NOT EXISTS repuesto_imagenes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    repuesto_id INT NOT NULL,
    url_imagen VARCHAR(500) NOT NULL,
    nombre_archivo VARCHAR(255),
    orden INT DEFAULT 1 COMMENT 'Orden de visualización (1 = principal)',
    descripcion VARCHAR(255) COMMENT 'Descripción de la imagen',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (repuesto_id) REFERENCES repuestos(id) ON DELETE CASCADE,
    INDEX idx_repuesto_orden (repuesto_id, orden)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- PASO 3: CREAR TABLA DE CATEGORÍAS ESTRUCTURADAS
CREATE TABLE IF NOT EXISTS categorias_repuestos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nombre VARCHAR(100) NOT NULL,
    descripcion TEXT,
    icono VARCHAR(100) COMMENT 'Clase de icono FontAwesome',
    color VARCHAR(7) DEFAULT '#FF6B35' COMMENT 'Color hexadecimal para la categoría',
    orden INT DEFAULT 0 COMMENT 'Orden de visualización',
    activa BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_activa_orden (activa, orden)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- PASO 4: CREAR TABLA DE MARCAS DE VEHÍCULOS
CREATE TABLE IF NOT EXISTS marcas_vehiculos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nombre VARCHAR(100) NOT NULL UNIQUE,
    logo_url VARCHAR(500),
    activa BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_nombre (nombre),
    INDEX idx_activa (activa)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- PASO 5: CREAR TABLA DE MODELOS DE VEHÍCULOS
CREATE TABLE IF NOT EXISTS modelos_vehiculos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    marca_id INT NOT NULL,
    nombre VARCHAR(100) NOT NULL,
    año_desde INT,
    año_hasta INT,
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (marca_id) REFERENCES marcas_vehiculos(id) ON DELETE CASCADE,
    INDEX idx_marca_nombre (marca_id, nombre),
    INDEX idx_activo (activo)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- PASO 6: CREAR TABLA DE HISTORIAL DE CAMBIOS
CREATE TABLE IF NOT EXISTS repuesto_historial (
    id INT AUTO_INCREMENT PRIMARY KEY,
    repuesto_id INT NOT NULL,
    campo_modificado VARCHAR(100) NOT NULL,
    valor_anterior TEXT,
    valor_nuevo TEXT,
    usuario_id INT,
    fecha_cambio TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (repuesto_id) REFERENCES repuestos(id) ON DELETE CASCADE,
    INDEX idx_repuesto_fecha (repuesto_id, fecha_cambio)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- PASO 7: INSERTAR CATEGORÍAS PREDEFINIDAS
INSERT IGNORE INTO categorias_repuestos (nombre, descripcion, icono, color, orden) VALUES
('Motor y Combustible', 'Repuestos relacionados con el motor y sistema de combustible', 'fas fa-cog', '#FF6B35', 1),
('Frenos y Suspensión', 'Sistema de frenos, amortiguadores y suspensión', 'fas fa-car-crash', '#E74C3C', 2),
('Eléctrico y Encendido', 'Sistema eléctrico, baterías, alternadores, encendido', 'fas fa-bolt', '#F39C12', 3),
('Transmisión', 'Caja de cambios, embrague, diferencial', 'fas fa-cogs', '#3498DB', 4),
('Carrocería y Exterior', 'Paragolpes, espejos, luces, carrocería', 'fas fa-car', '#9B59B6', 5),
('Interior y Confort', 'Asientos, tapizados, aire acondicionado', 'fas fa-chair', '#1ABC9C', 6),
('Neumáticos y Llantas', 'Neumáticos, llantas, válvulas', 'fas fa-circle', '#34495E', 7),
('Aceites y Lubricantes', 'Aceites de motor, lubricantes, filtros', 'fas fa-tint', '#E67E22', 8),
('Herramientas', 'Herramientas para mecánicos y talleres', 'fas fa-wrench', '#95A5A6', 9),
('Accesorios', 'Accesorios varios para vehículos', 'fas fa-plus', '#2ECC71', 10);

-- PASO 8: INSERTAR MARCAS DE VEHÍCULOS POPULARES EN ARGENTINA
INSERT IGNORE INTO marcas_vehiculos (nombre) VALUES
('Chevrolet'), ('Ford'), ('Volkswagen'), ('Fiat'), ('Toyota'),
('Renault'), ('Peugeot'), ('Citroën'), ('Nissan'), ('Honda'),
('Hyundai'), ('Kia'), ('Suzuki'), ('Mitsubishi'), ('Mazda'),
('BMW'), ('Mercedes-Benz'), ('Audi'), ('Volvo'), ('Jeep');

-- PASO 9: INSERTAR MODELOS POPULARES (EJEMPLOS)
-- Chevrolet
INSERT IGNORE INTO modelos_vehiculos (marca_id, nombre, año_desde, año_hasta)
SELECT id, 'Onix', 2012, NULL FROM marcas_vehiculos WHERE nombre = 'Chevrolet';
INSERT IGNORE INTO modelos_vehiculos (marca_id, nombre, año_desde, año_hasta)
SELECT id, 'Cruze', 2009, NULL FROM marcas_vehiculos WHERE nombre = 'Chevrolet';
INSERT IGNORE INTO modelos_vehiculos (marca_id, nombre, año_desde, año_hasta)
SELECT id, 'Corsa', 1994, 2016 FROM marcas_vehiculos WHERE nombre = 'Chevrolet';

-- Ford
INSERT IGNORE INTO modelos_vehiculos (marca_id, nombre, año_desde, año_hasta)
SELECT id, 'Focus', 1998, NULL FROM marcas_vehiculos WHERE nombre = 'Ford';
INSERT IGNORE INTO modelos_vehiculos (marca_id, nombre, año_desde, año_hasta)
SELECT id, 'Fiesta', 1976, NULL FROM marcas_vehiculos WHERE nombre = 'Ford';
INSERT IGNORE INTO modelos_vehiculos (marca_id, nombre, año_desde, año_hasta)
SELECT id, 'EcoSport', 2003, NULL FROM marcas_vehiculos WHERE nombre = 'Ford';

-- Volkswagen
INSERT IGNORE INTO modelos_vehiculos (marca_id, nombre, año_desde, año_hasta)
SELECT id, 'Gol', 1980, NULL FROM marcas_vehiculos WHERE nombre = 'Volkswagen';
INSERT IGNORE INTO modelos_vehiculos (marca_id, nombre, año_desde, año_hasta)
SELECT id, 'Polo', 1975, NULL FROM marcas_vehiculos WHERE nombre = 'Volkswagen';
INSERT IGNORE INTO modelos_vehiculos (marca_id, nombre, año_desde, año_hasta)
SELECT id, 'Suran', 2005, NULL FROM marcas_vehiculos WHERE nombre = 'Volkswagen';

-- Fiat
INSERT IGNORE INTO modelos_vehiculos (marca_id, nombre, año_desde, año_hasta)
SELECT id, 'Palio', 1996, NULL FROM marcas_vehiculos WHERE nombre = 'Fiat';
INSERT IGNORE INTO modelos_vehiculos (marca_id, nombre, año_desde, año_hasta)
SELECT id, 'Cronos', 2018, NULL FROM marcas_vehiculos WHERE nombre = 'Fiat';
INSERT IGNORE INTO modelos_vehiculos (marca_id, nombre, año_desde, año_hasta)
SELECT id, 'Uno', 1983, NULL FROM marcas_vehiculos WHERE nombre = 'Fiat';

-- PASO 10: CREAR ÍNDICES PARA OPTIMIZAR BÚSQUEDAS
CREATE INDEX IF NOT EXISTS idx_repuestos_marca_vehiculo ON repuestos(marca_vehiculo);
CREATE INDEX IF NOT EXISTS idx_repuestos_modelo_vehiculo ON repuestos(modelo_vehiculo);
CREATE INDEX IF NOT EXISTS idx_repuestos_año_desde ON repuestos(año_desde);
CREATE INDEX IF NOT EXISTS idx_repuestos_año_hasta ON repuestos(año_hasta);
CREATE INDEX IF NOT EXISTS idx_repuestos_activo ON repuestos(activo);
CREATE INDEX IF NOT EXISTS idx_repuestos_calidad ON repuestos(calidad);
CREATE INDEX IF NOT EXISTS idx_repuestos_numero_pieza ON repuestos(numero_pieza);

-- PASO 11: CREAR VISTA PARA REPUESTOS COMPLETOS
CREATE OR REPLACE VIEW vista_repuestos_completos AS
SELECT
    r.*,
    c.nombre as categoria_nombre,
    c.icono as categoria_icono,
    c.color as categoria_color,
    mv.nombre as marca_vehiculo_nombre,
    GROUP_CONCAT(ri.url_imagen ORDER BY ri.orden SEPARATOR ',') as imagenes_urls,
    COUNT(ri.id) as total_imagenes
FROM repuestos r
LEFT JOIN categorias_repuestos c ON r.categoria = c.nombre
LEFT JOIN marcas_vehiculos mv ON r.marca_vehiculo = mv.nombre
LEFT JOIN repuesto_imagenes ri ON r.id = ri.repuesto_id
WHERE r.activo = TRUE
GROUP BY r.id;

-- PASO 12: COMENTARIOS FINALES
-- =====================================================
-- MIGRACIÓN COMPLETADA EXITOSAMENTE
--
-- NUEVAS FUNCIONALIDADES AGREGADAS:
-- ✅ Campos extendidos en tabla repuestos
-- ✅ Sistema de imágenes múltiples
-- ✅ Categorías estructuradas con iconos
-- ✅ Marcas y modelos de vehículos
-- ✅ Historial de cambios
-- ✅ Índices optimizados
-- ✅ Vista completa para consultas
--
-- COMPATIBILIDAD:
-- ✅ Todo el código existente sigue funcionando
-- ✅ No se modificaron campos existentes
-- ✅ Solo se agregaron funcionalidades nuevas
-- =====================================================
