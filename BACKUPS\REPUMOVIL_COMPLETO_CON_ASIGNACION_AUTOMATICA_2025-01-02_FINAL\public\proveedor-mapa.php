<?php
session_start();
require_once 'db_config.php';

// Verificar si el usuario está logueado y es proveedor
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'proveedor_repuestos') {
    header('Location: login-dinamico.php');
    exit();
}

$user_id = $_SESSION['user_id'];
$user_name = $_SESSION['user_name'] ?? 'Proveedor';

// Conectar a la base de datos
try {
    $pdo = connectDB();
} catch (Exception $e) {
    die("Error de conexión: " . $e->getMessage());
}

// Obtener pedidos activos del proveedor
try {
    $stmt = $pdo->prepare("
        SELECT p.*, u.nombre as cliente_nombre, u.telefono as cliente_telefono,
               r.nombre_completo as repartidor_nombre, r.telefono as repartidor_telefono
        FROM pedidos p
        LEFT JOIN users u ON p.user_id = u.id
        LEFT JOIN usuarios r ON p.repartidor_id = r.id
        WHERE p.proveedor_id = ? AND p.estado IN ('nuevo', 'en_preparacion', 'en_camino')
        ORDER BY p.fecha_pedido DESC
    ");
    $stmt->execute([$user_id]);
    $pedidos_activos = $stmt->fetchAll();
} catch (PDOException $e) {
    $error_message = "Error al cargar pedidos: " . $e->getMessage();
    $pedidos_activos = [];
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mapa de Entregas - RepuMovil</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            min-height: 100vh;
        }

        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
            color: white;
            text-decoration: none;
        }

        .logo h1 {
            font-size: 1.8rem;
            font-weight: 900;
        }

        .logo .repu { color: #FFE082; }
        .logo .movil { color: #E8F5E8; }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
            color: white;
        }

        .back-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            cursor: pointer;
            transition: background 0.3s ease;
            text-decoration: none;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            color: white;
            text-decoration: none;
        }

        .container {
            display: flex;
            height: calc(100vh - 80px);
            gap: 20px;
            padding: 20px;
        }

        .map-container {
            flex: 2;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            position: relative;
        }

        #map {
            width: 100%;
            height: 100%;
        }

        .map-controls {
            position: absolute;
            top: 20px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 1000;
        }

        .control-btn {
            width: 50px;
            height: 50px;
            background: white;
            border: none;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .control-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        .control-btn.active {
            background: #4CAF50;
            color: white;
        }

        .sidebar {
            flex: 1;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            overflow-y: auto;
            max-height: calc(100vh - 120px);
        }

        .sidebar-title {
            font-size: 1.5rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .pedido-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            border-left: 4px solid #4CAF50;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .pedido-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .pedido-card.selected {
            border-left-color: #FF6B35;
            background: #fff3e0;
        }

        .pedido-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .pedido-id {
            font-weight: bold;
            color: #333;
        }

        .estado-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
            text-transform: uppercase;
        }

        .estado-nuevo { background: #E3F2FD; color: #1976D2; }
        .estado-en_preparacion { background: #FFF3E0; color: #F57C00; }
        .estado-en_camino { background: #E8F5E8; color: #388E3C; }

        .pedido-info {
            font-size: 0.9rem;
            color: #666;
            line-height: 1.4;
        }

        .pedido-info strong {
            color: #333;
        }

        .route-info {
            position: absolute;
            bottom: 20px;
            left: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.95);
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            display: none;
        }

        .route-info.show {
            display: block;
        }

        .route-stats {
            display: flex;
            justify-content: space-around;
            text-align: center;
        }

        .route-stat {
            flex: 1;
        }

        .route-stat-value {
            font-size: 1.2rem;
            font-weight: bold;
            color: #4CAF50;
        }

        .route-stat-label {
            font-size: 0.8rem;
            color: #666;
            text-transform: uppercase;
        }

        .no-pedidos {
            text-align: center;
            padding: 40px 20px;
            color: #666;
        }

        .no-pedidos i {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.5;
        }

        @media (max-width: 768px) {
            .container {
                flex-direction: column;
                height: auto;
            }
            
            .map-container {
                height: 400px;
            }
            
            .sidebar {
                max-height: none;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <a href="dashboard-proveedor.php" class="logo">
            <i class="fas fa-map-marked-alt" style="font-size: 2rem; color: #FFE082;"></i>
            <h1><span class="repu">Repu</span><span class="movil">Movil</span></h1>
        </a>
        <div class="user-info">
            <span>Mapa de Entregas</span>
            <a href="dashboard-proveedor.php" class="back-btn">
                <i class="fas fa-arrow-left"></i> Volver
            </a>
        </div>
    </div>

    <div class="container">
        <!-- Mapa -->
        <div class="map-container">
            <div id="map"></div>
            
            <!-- Controles del mapa -->
            <div class="map-controls">
                <button class="control-btn" id="centerBtn" title="Centrar mapa">
                    <i class="fas fa-crosshairs"></i>
                </button>
                <button class="control-btn" id="fitBtn" title="Ajustar vista">
                    <i class="fas fa-expand-arrows-alt"></i>
                </button>
                <button class="control-btn" id="refreshBtn" title="Actualizar">
                    <i class="fas fa-sync-alt"></i>
                </button>
                <button class="control-btn" id="trackingBtn" title="Tracking automático">
                    <i class="fas fa-satellite-dish"></i>
                </button>
            </div>

            <!-- Información de ruta -->
            <div class="route-info" id="routeInfo">
                <div class="route-stats">
                    <div class="route-stat">
                        <div class="route-stat-value" id="routeDistance">-</div>
                        <div class="route-stat-label">Distancia</div>
                    </div>
                    <div class="route-stat">
                        <div class="route-stat-value" id="routeDuration">-</div>
                        <div class="route-stat-label">Tiempo Est.</div>
                    </div>
                    <div class="route-stat">
                        <div class="route-stat-value" id="routeStatus">-</div>
                        <div class="route-stat-label">Estado</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar con pedidos -->
        <div class="sidebar">
            <h3 class="sidebar-title">
                <i class="fas fa-truck"></i>
                Entregas Activas
            </h3>

            <?php if (empty($pedidos_activos)): ?>
                <div class="no-pedidos">
                    <i class="fas fa-inbox"></i>
                    <h4>No hay entregas activas</h4>
                    <p>Los pedidos en proceso aparecerán aquí</p>
                </div>
            <?php else: ?>
                <?php foreach ($pedidos_activos as $pedido): ?>
                    <div class="pedido-card" data-pedido-id="<?php echo $pedido['id']; ?>" 
                         onclick="selectPedido(<?php echo $pedido['id']; ?>)">
                        <div class="pedido-header">
                            <span class="pedido-id">Pedido #<?php echo $pedido['id']; ?></span>
                            <span class="estado-badge estado-<?php echo $pedido['estado']; ?>">
                                <?php echo ucfirst(str_replace('_', ' ', $pedido['estado'])); ?>
                            </span>
                        </div>
                        <div class="pedido-info">
                            <strong>Cliente:</strong> <?php echo htmlspecialchars($pedido['cliente_nombre'] ?? 'N/A'); ?><br>
                            <?php if ($pedido['repartidor_nombre']): ?>
                                <strong>Repartidor:</strong> <?php echo htmlspecialchars($pedido['repartidor_nombre']); ?><br>
                            <?php endif; ?>
                            <strong>Fecha:</strong> <?php echo date('d/m/Y H:i', strtotime($pedido['fecha_pedido'])); ?><br>
                            <strong>Total:</strong> $<?php echo number_format($pedido['total'], 0, ',', '.'); ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/GoogleMapsService.js"></script>
    <script>
        let mapService;
        let selectedPedidoId = null;
        let isTracking = false;

        // Datos de pedidos (simulados para demo)
        const pedidosData = {
            <?php foreach ($pedidos_activos as $pedido): ?>
            <?php echo $pedido['id']; ?>: {
                id: <?php echo $pedido['id']; ?>,
                cliente: "<?php echo htmlspecialchars($pedido['cliente_nombre'] ?? 'Cliente'); ?>",
                repartidor: "<?php echo htmlspecialchars($pedido['repartidor_nombre'] ?? ''); ?>",
                estado: "<?php echo $pedido['estado']; ?>",
                // Coordenadas simuladas (en producción vendrían de la base de datos)
                clienteCoords: { lat: -31.5375 + (Math.random() - 0.5) * 0.01, lng: -68.5364 + (Math.random() - 0.5) * 0.01 },
                repartidorCoords: { lat: -31.5355 + (Math.random() - 0.5) * 0.01, lng: -68.5384 + (Math.random() - 0.5) * 0.01 },
                proveedorCoords: { lat: -31.5365, lng: -68.5374 }
            },
            <?php endforeach; ?>
        };

        // Inicializar mapa cuando se carga Google Maps
        function initMap() {
            mapService = new GoogleMapsService();
            mapService.initMap('map', {
                zoom: 13,
                center: { lat: -31.5375, lng: -68.5364 }
            }).then(() => {
                console.log('🗺️ Mapa inicializado correctamente');
                loadAllPedidos();
            });
        }

        // Cargar todos los pedidos en el mapa
        function loadAllPedidos() {
            mapService.clearMarkers();
            
            Object.values(pedidosData).forEach(pedido => {
                // Marker del proveedor (siempre visible)
                mapService.createCustomMarker(
                    pedido.proveedorCoords,
                    'proveedor',
                    'Proveedor - RepuMovil'
                );

                // Marker del cliente
                mapService.createCustomMarker(
                    pedido.clienteCoords,
                    'cliente',
                    `Cliente - Pedido #${pedido.id}`
                );

                // Marker del repartidor (si existe)
                if (pedido.repartidor && pedido.estado !== 'nuevo') {
                    mapService.createCustomMarker(
                        pedido.repartidorCoords,
                        'repartidor',
                        `Repartidor - ${pedido.repartidor}`
                    );
                }
            });

            mapService.fitBounds();
        }

        // Seleccionar pedido específico
        function selectPedido(pedidoId) {
            selectedPedidoId = pedidoId;
            
            // Actualizar UI
            document.querySelectorAll('.pedido-card').forEach(card => {
                card.classList.remove('selected');
            });
            document.querySelector(`[data-pedido-id="${pedidoId}"]`).classList.add('selected');

            const pedido = pedidosData[pedidoId];
            if (!pedido) return;

            // Limpiar mapa y mostrar solo este pedido
            mapService.clearMarkers();

            // Agregar markers
            const proveedorMarker = mapService.createCustomMarker(
                pedido.proveedorCoords,
                'proveedor',
                'Proveedor - RepuMovil'
            );

            const clienteMarker = mapService.createCustomMarker(
                pedido.clienteCoords,
                'cliente',
                `Cliente - Pedido #${pedido.id}`
            );

            let repartidorMarker = null;
            if (pedido.repartidor && pedido.estado !== 'nuevo') {
                repartidorMarker = mapService.createCustomMarker(
                    pedido.repartidorCoords,
                    'repartidor',
                    `Repartidor - ${pedido.repartidor}`
                );
            }

            // Calcular y mostrar ruta
            if (pedido.estado === 'en_camino' && repartidorMarker) {
                mapService.calculateRoute(pedido.repartidorCoords, pedido.clienteCoords)
                    .then(routeInfo => {
                        showRouteInfo(routeInfo, pedido.estado);
                    })
                    .catch(error => {
                        console.error('Error calculando ruta:', error);
                    });
            } else if (pedido.estado === 'en_preparacion') {
                mapService.calculateRoute(pedido.proveedorCoords, pedido.clienteCoords)
                    .then(routeInfo => {
                        showRouteInfo(routeInfo, pedido.estado);
                    })
                    .catch(error => {
                        console.error('Error calculando ruta:', error);
                    });
            }

            mapService.fitBounds();
        }

        // Mostrar información de ruta
        function showRouteInfo(routeInfo, estado) {
            document.getElementById('routeDistance').textContent = routeInfo.distance;
            document.getElementById('routeDuration').textContent = routeInfo.duration;
            document.getElementById('routeStatus').textContent = estado.replace('_', ' ').toUpperCase();
            document.getElementById('routeInfo').classList.add('show');
        }

        // Event listeners para controles
        document.getElementById('centerBtn').addEventListener('click', () => {
            mapService.centerMap({ lat: -31.5375, lng: -68.5364 }, 13);
        });

        document.getElementById('fitBtn').addEventListener('click', () => {
            mapService.fitBounds();
        });

        document.getElementById('refreshBtn').addEventListener('click', () => {
            if (selectedPedidoId) {
                selectPedido(selectedPedidoId);
            } else {
                loadAllPedidos();
            }
        });

        document.getElementById('trackingBtn').addEventListener('click', () => {
            const btn = document.getElementById('trackingBtn');
            if (isTracking) {
                mapService.stopLocationTracking();
                btn.classList.remove('active');
                isTracking = false;
            } else {
                mapService.startLocationTracking((location) => {
                    console.log('📍 Ubicación actualizada:', location);
                    // Aquí podrías actualizar la posición del repartidor en tiempo real
                });
                btn.classList.add('active');
                isTracking = true;
            }
        });

        // Cargar todos los pedidos al inicio
        document.addEventListener('DOMContentLoaded', () => {
            if (Object.keys(pedidosData).length > 0) {
                // Auto-seleccionar el primer pedido
                const firstPedidoId = Object.keys(pedidosData)[0];
                setTimeout(() => selectPedido(firstPedidoId), 1000);
            }
        });
    </script>

    <!-- Google Maps API - REAL -->
    <script async defer
        src="https://maps.googleapis.com/maps/api/js?key=AIzaSyDeF7xLqA58aAm5Gou7Nux3A7_jwGnnVxg&callback=initMap&libraries=geometry">
    </script>

    <script>
        // Función para inicializar Google Maps REAL
        function initMap() {
            console.log('🗺️ Inicializando Google Maps REAL...');

            // Configuración inicial para San Juan, Argentina
            const sanJuanCenter = { lat: -31.5375, lng: -68.5364 };

            // Crear mapa real de Google
            const map = new google.maps.Map(document.getElementById('map'), {
                zoom: 13,
                center: sanJuanCenter,
                mapTypeId: google.maps.MapTypeId.ROADMAP,
                styles: [
                    {
                        featureType: "poi",
                        elementType: "labels",
                        stylers: [{ visibility: "off" }]
                    }
                ]
            });

            // Servicio de direcciones
            const directionsService = new google.maps.DirectionsService();
            const directionsRenderer = new google.maps.DirectionsRenderer({
                polylineOptions: {
                    strokeColor: '#4CAF50',
                    strokeWeight: 4,
                    strokeOpacity: 0.8
                }
            });
            directionsRenderer.setMap(map);

            // Ubicaciones de ejemplo para RepuMovil
            const locations = {
                proveedor: { lat: -31.5375, lng: -68.5364, title: 'RepuMovil Taller', icon: '🏪' },
                repartidor: { lat: -31.5400, lng: -68.5300, title: 'Repartidor Juan', icon: '🏍️' },
                cliente: { lat: -31.5350, lng: -68.5400, title: 'Cliente - Av. Libertador', icon: '📍' }
            };

            // Crear markers personalizados
            Object.keys(locations).forEach(key => {
                const location = locations[key];

                // Crear marker personalizado
                const marker = new google.maps.Marker({
                    position: { lat: location.lat, lng: location.lng },
                    map: map,
                    title: location.title,
                    icon: {
                        url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                            <svg width="40" height="40" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="20" cy="20" r="18" fill="${key === 'proveedor' ? '#4CAF50' : key === 'repartidor' ? '#FF6B35' : '#9C27B0'}" stroke="white" stroke-width="3"/>
                                <text x="20" y="28" text-anchor="middle" font-size="16">${location.icon}</text>
                            </svg>
                        `),
                        scaledSize: new google.maps.Size(40, 40),
                        anchor: new google.maps.Point(20, 20)
                    }
                });

                // Info window
                const infoWindow = new google.maps.InfoWindow({
                    content: `
                        <div style="padding: 10px;">
                            <h3 style="margin: 0 0 5px 0; color: #333;">${location.icon} ${location.title}</h3>
                            <p style="margin: 0; color: #666; font-size: 12px;">
                                ${key === 'proveedor' ? 'Taller principal' :
                                  key === 'repartidor' ? 'En camino - ETA: 8 min' :
                                  'Destino de entrega'}
                            </p>
                        </div>
                    `
                });

                marker.addListener('click', () => {
                    infoWindow.open(map, marker);
                });
            });

            // Calcular y mostrar ruta del repartidor al cliente
            directionsService.route({
                origin: locations.repartidor,
                destination: locations.cliente,
                travelMode: google.maps.TravelMode.DRIVING,
                avoidTolls: true,
                language: 'es'
            }, (result, status) => {
                if (status === 'OK') {
                    directionsRenderer.setDirections(result);

                    // Actualizar información de la ruta
                    const route = result.routes[0];
                    const leg = route.legs[0];

                    document.getElementById('routeDistance').textContent = leg.distance.text;
                    document.getElementById('routeDuration').textContent = leg.duration.text;
                    document.getElementById('routeStatus').textContent = 'EN CAMINO';
                    document.getElementById('routeInfo').classList.add('show');
                }
            });

            // Simular actualización de ubicación del repartidor cada 5 segundos
            setInterval(() => {
                const randomOffset = () => (Math.random() - 0.5) * 0.002;
                locations.repartidor.lat += randomOffset();
                locations.repartidor.lng += randomOffset();

                console.log('📍 Ubicación del repartidor actualizada');
            }, 5000);
        }

        // Función para seleccionar pedido desde la lista
        window.selectPedido = function(id) {
            console.log('📦 Pedido seleccionado:', id);
            // La información se actualiza automáticamente con la ruta calculada
        };
    </script>
</body>
</html>
