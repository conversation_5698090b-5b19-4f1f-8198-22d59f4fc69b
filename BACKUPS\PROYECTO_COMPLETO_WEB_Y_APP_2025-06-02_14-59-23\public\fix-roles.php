<?php
// Arreglar roles en la base de datos
require_once 'db_config.php';

try {
    $conn = connectDB();

    echo "<h1>🔧 Arreglando Roles...</h1>";

    // Desactivar verificación de claves foráneas temporalmente
    $conn->exec("SET FOREIGN_KEY_CHECKS = 0");
    echo "🔓 Claves foráneas desactivadas<br>";

    // Limpiar tabla de roles
    $conn->exec("DELETE FROM roles");
    echo "✅ Roles limpiados<br>";

    // Insertar roles correctos
    $conn->exec("INSERT INTO roles (id, name, description) VALUES
        (1, 'admin', 'Administrador del sistema'),
        (2, 'taller', 'Taller mecánico'),
        (3, 'mecanico', 'Mecánico independiente'),
        (4, 'usuario', 'Usuario regular'),
        (5, 'proveedor', 'Proveedor de repuestos')
    ");
    echo "✅ Roles insertados correctamente<br>";

    // Reactivar verificación de claves foráneas
    $conn->exec("SET FOREIGN_KEY_CHECKS = 1");
    echo "🔒 Claves foráneas reactivadas<br>";

    // Verificar roles
    $stmt = $conn->query("SELECT * FROM roles ORDER BY id");
    $roles = $stmt->fetchAll();

    echo "<h2>📋 Roles en la base de datos:</h2>";
    echo "<table border='1' style='border-collapse: collapse; padding: 5px;'>";
    echo "<tr><th>ID</th><th>Nombre</th><th>Descripción</th></tr>";
    foreach ($roles as $role) {
        echo "<tr>";
        echo "<td>{$role['id']}</td>";
        echo "<td>{$role['name']}</td>";
        echo "<td>{$role['description']}</td>";
        echo "</tr>";
    }
    echo "</table>";

    echo "<h2>✅ Roles arreglados correctamente!</h2>";
    echo "<p><a href='test-api.php'>🧪 Probar API</a></p>";
    echo "<p><a href='registro-dinamico.php'>📝 Ir al Registro</a></p>";

} catch (Exception $e) {
    echo "<h2>❌ Error:</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
}
?>
