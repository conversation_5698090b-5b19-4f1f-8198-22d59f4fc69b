<?php
/**
 * Configuración centralizada de la base de datos
 * Este archivo debe ser incluido en todos los scripts que necesiten conectarse a la base de datos
 */

// Configuración de la base de datos - MODIFICAR ESTOS VALORES SEGÚN TU ENTORNO
$db_config = [
    'host' => 'localhost',
    'dbname' => 'autoconnect_bd',
    'username' => 'root',  // Usuario por defecto en XAMPP/WAMP
    'password' => '',      // Contraseña vacía por defecto en XAMPP/WAMP
    'charset' => 'utf8mb4',
    'options' => [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ]
];

/**
 * Función para conectar a la base de datos
 * @return PDO Conexión a la base de datos
 */
function connectDB() {
    global $db_config;

    try {
        // Intentar conectar a la base de datos específica
        $dsn = "mysql:host={$db_config['host']};dbname={$db_config['dbname']};charset={$db_config['charset']}";
        $conn = new PDO($dsn, $db_config['username'], $db_config['password'], $db_config['options']);
        return $conn;
    } catch (PDOException $e) {
        // Si no se puede conectar a la base de datos específica, intentar conectar solo al servidor MySQL
        try {
            $conn = new PDO("mysql:host={$db_config['host']}", $db_config['username'], $db_config['password']);
            $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

            // Crear la base de datos si no existe
            $conn->exec("CREATE DATABASE IF NOT EXISTS {$db_config['dbname']} CHARACTER SET {$db_config['charset']} COLLATE {$db_config['charset']}_unicode_ci");

            // Conectar a la base de datos recién creada
            $dsn = "mysql:host={$db_config['host']};dbname={$db_config['dbname']};charset={$db_config['charset']}";
            $conn = new PDO($dsn, $db_config['username'], $db_config['password'], $db_config['options']);

            // Inicializar la base de datos
            initializeDB($conn);

            return $conn;
        } catch (PDOException $innerException) {
            // Error fatal - no se puede conectar ni crear la base de datos
            die("
                <div style='font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; border: 1px solid #e3e3e3; border-radius: 5px; background-color: #f9f9f9;'>
                    <h2 style='color: #d9534f;'>Error de conexión a la base de datos</h2>
                    <p><strong>Mensaje de error:</strong> {$innerException->getMessage()}</p>
                    <hr>
                    <h3>Posibles soluciones:</h3>
                    <ol>
                        <li>Verifica que el servidor MySQL esté en ejecución.</li>
                        <li>Comprueba que las credenciales en el archivo <code>db_config.php</code> sean correctas:
                            <ul>
                                <li>Usuario: <code>{$db_config['username']}</code></li>
                                <li>Contraseña: " . (empty($db_config['password']) ? "<em>vacía</em>" : "<em>configurada</em>") . "</li>
                            </ul>
                        </li>
                        <li>Si usas XAMPP/WAMP, asegúrate de que el servicio MySQL esté iniciado.</li>
                        <li>Intenta crear manualmente la base de datos <code>{$db_config['dbname']}</code> desde phpMyAdmin.</li>
                    </ol>
                    <p>Si el problema persiste, contacta al administrador del sistema.</p>
                </div>
            ");
        }
    }
}

/**
 * Función para inicializar la base de datos con tablas y datos básicos
 * @param PDO $conn Conexión a la base de datos
 */
function initializeDB($conn) {
    try {
        // Verificar si existe la tabla de roles
        $stmt = $conn->query("SHOW TABLES LIKE 'roles'");
        if ($stmt->rowCount() == 0) {
            // Crear tabla de roles
            $conn->exec("CREATE TABLE roles (
                id INT PRIMARY KEY AUTO_INCREMENT,
                name VARCHAR(50) NOT NULL,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )");

            // Insertar roles básicos
            $conn->exec("INSERT INTO roles (id, name, description) VALUES
                (1, 'root', 'Administrador del sistema'),
                (2, 'root', 'Taller mecánico'),
                (3, 'root', 'Proveedor de repuestos')");
        }

        // Verificar si existe la tabla de usuarios
        $stmt = $conn->query("SHOW TABLES LIKE 'users'");
        if ($stmt->rowCount() == 0) {
            // Crear tabla de usuarios
            $conn->exec("CREATE TABLE users (
                id INT PRIMARY KEY AUTO_INCREMENT,
                username VARCHAR(50) NOT NULL UNIQUE,
                email VARCHAR(100) NOT NULL UNIQUE,
                password VARCHAR(255) NOT NULL,
                role_id INT NOT NULL,
                status ENUM('active', 'root', 'suspended') DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP NULL,
                FOREIGN KEY (role_id) REFERENCES roles(id)
            )");
        }

        // Verificar si existe la tabla de talleres
        $stmt = $conn->query("SHOW TABLES LIKE 'workshops'");
        if ($stmt->rowCount() == 0) {
            // Crear tabla de talleres
            $conn->exec("CREATE TABLE workshops (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id INT NOT NULL UNIQUE,
                name VARCHAR(100) NOT NULL,
                location VARCHAR(255),
                phone VARCHAR(20),
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id)
            )");
        }

        // Verificar si existe la tabla de proveedores
        $stmt = $conn->query("SHOW TABLES LIKE 'suppliers'");
        if ($stmt->rowCount() == 0) {
            // Crear tabla de proveedores
            $conn->exec("CREATE TABLE suppliers (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id INT NOT NULL UNIQUE,
                name VARCHAR(100) NOT NULL,
                address VARCHAR(255),
                phone VARCHAR(20),
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id)
            )");
        }

        // Verificar si ya existe el usuario administrador
        $stmt = $conn->query("SELECT COUNT(*) FROM users WHERE username = 'admin'");
        $adminCount = $stmt->fetchColumn();

        if ($adminCount == 0) {
            // Insertar usuario administrador
            // Contraseña: admin123
            $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
            $stmt = $conn->prepare("INSERT INTO users (username, email, password, role_id) VALUES
                ('admin', '<EMAIL>', :password, 1)");
            $stmt->execute(['password' => $adminPassword]);
        }

        // Verificar si ya existe el usuario de taller de ejemplo
        $stmt = $conn->query("SELECT COUNT(*) FROM users WHERE username = 'taller1'");
        $workshopCount = $stmt->fetchColumn();

        if ($workshopCount == 0) {
            // Insertar usuario de taller de ejemplo
            // Contraseña: taller123
            $workshopPassword = password_hash('taller123', PASSWORD_DEFAULT);

            // Iniciar transacción
            $conn->beginTransaction();

            try {
                // Insertar usuario
                $stmt = $conn->prepare("INSERT INTO users (username, email, password, role_id) VALUES
                    ('taller1', '<EMAIL>', :password, 2)");
                $stmt->execute(['password' => $workshopPassword]);
                $workshopUserId = $conn->lastInsertId();

                // Insertar datos del taller
                $stmt = $conn->prepare("INSERT INTO workshops (user_id, name, location, phone, description) VALUES
                    (:user_id, 'Taller Mecánico Ejemplo', 'Calle Ejemplo 123, Ciudad', '************',
                    'Taller especializado en reparación de motores y sistemas de frenos')");
                $stmt->execute(['user_id' => $workshopUserId]);

                $conn->commit();
            } catch (Exception $e) {
                $conn->rollBack();
                throw $e;
            }
        }
    } catch (PDOException $e) {
        die("Error al inicializar la base de datos: " . $e->getMessage());
    }
}
?>
