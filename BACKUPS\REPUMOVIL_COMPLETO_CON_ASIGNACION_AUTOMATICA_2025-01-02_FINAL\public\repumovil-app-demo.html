<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RepuMovil App - Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .phone-container {
            max-width: 375px;
            margin: 0 auto;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            border-radius: 25px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            min-height: 600px;
        }

        .logo-container {
            text-align: center;
            margin: 60px 0 40px 0;
        }

        .logo-circle {
            width: 80px;
            height: 80px;
            border-radius: 40px;
            background: #FF6B35;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 10px auto;
            box-shadow: 0 6px 12px rgba(255, 107, 53, 0.4);
            font-size: 40px;
        }

        .logo-title {
            font-size: 32px;
            font-weight: bold;
        }

        .logo-repu {
            color: #FF6B35;
        }

        .logo-movil {
            color: #FFA500;
        }

        .title {
            font-size: 28px;
            font-weight: 600;
            color: #333;
            text-align: center;
            margin-bottom: 40px;
        }

        .form-container {
            padding: 0 5px;
        }

        .label {
            font-size: 14px;
            color: #666;
            margin-bottom: 6px;
            margin-top: 12px;
            display: block;
        }

        .input {
            width: 100%;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 16px;
            font-size: 16px;
            background: white;
            margin-bottom: 12px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .input:focus {
            outline: none;
            border-color: #FF6B35;
            box-shadow: 0 4px 8px rgba(255, 107, 53, 0.2);
        }

        .forgot-container {
            text-align: right;
            margin-bottom: 30px;
        }

        .forgot-text {
            color: #666;
            font-size: 14px;
            text-decoration: none;
        }

        .login-button {
            width: 100%;
            background: #FF6B35;
            padding: 15px;
            border-radius: 12px;
            border: none;
            color: white;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            box-shadow: 0 4px 8px rgba(255, 107, 53, 0.3);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .login-button:hover {
            background: #e55a2b;
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(255, 107, 53, 0.4);
        }

        .register-container {
            text-align: center;
            margin-top: 10px;
        }

        .register-text {
            font-size: 14px;
            color: #666;
        }

        .register-link {
            color: #FF6B35;
            font-weight: 600;
            text-decoration: none;
        }

        .register-link:hover {
            text-decoration: underline;
        }

        .demo-note {
            background: rgba(255, 107, 53, 0.1);
            border: 2px solid #FF6B35;
            border-radius: 12px;
            padding: 15px;
            margin: 20px 0;
            text-align: center;
        }

        .demo-note h3 {
            color: #FF6B35;
            margin-bottom: 10px;
        }

        .demo-note p {
            color: #666;
            font-size: 14px;
        }

        @media (max-width: 480px) {
            .phone-container {
                margin: 10px;
                padding: 20px;
                border-radius: 15px;
            }
            
            .logo-container {
                margin: 30px 0 20px 0;
            }
            
            .logo-circle {
                width: 60px;
                height: 60px;
                font-size: 30px;
            }
            
            .logo-title {
                font-size: 24px;
            }
            
            .title {
                font-size: 22px;
                margin-bottom: 30px;
            }
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <!-- Logo -->
        <div class="logo-container">
            <div class="logo-circle">
                🔧
            </div>
            <div class="logo-title">
                <span class="logo-repu">Repu</span><span class="logo-movil">Movil</span>
            </div>
        </div>

        <!-- Título -->
        <h1 class="title">Iniciar Sesión</h1>

        <!-- Demo Note -->
        <div class="demo-note">
            <h3>🚀 Demo de la App RepuMovil</h3>
            <p>Esta es una vista previa del diseño de la aplicación móvil con el nuevo estilo moderno aplicado.</p>
        </div>

        <!-- Formulario -->
        <div class="form-container">
            <!-- Email -->
            <label class="label">Correo Electrónico</label>
            <input type="email" class="input" placeholder="<EMAIL>">

            <!-- Contraseña -->
            <label class="label">Contraseña</label>
            <input type="password" class="input" placeholder="Tu contraseña">

            <!-- ¿Olvidó su contraseña? -->
            <div class="forgot-container">
                <a href="#" class="forgot-text">¿Olvidó su contraseña?</a>
            </div>

            <!-- Botón Iniciar Sesión -->
            <button class="login-button" onclick="showAlert()">Iniciar Sesión</button>

            <!-- Registrarse -->
            <div class="register-container">
                <span class="register-text">
                    ¿No tiene una cuenta? <a href="#" class="register-link" onclick="showRegister()">Regístrese aquí</a>
                </span>
            </div>
        </div>
    </div>

    <script>
        function showAlert() {
            alert('🎉 ¡Funcionalidad de login lista!\n\nEste es el diseño moderno de RepuMovil con:\n✅ Gradiente de fondo\n✅ Botones con sombra naranja\n✅ Inputs modernos\n✅ Logo con efecto 3D');
        }

        function showRegister() {
            alert('🔧 ¡Pantalla de registro disponible!\n\nIncluye formularios para:\n• Usuario regular\n• Taller mecánico\n• Mecánico independiente\n• Proveedor de repuestos\n\nCon campos específicos para cada tipo de usuario.');
        }
    </script>
</body>
</html>
