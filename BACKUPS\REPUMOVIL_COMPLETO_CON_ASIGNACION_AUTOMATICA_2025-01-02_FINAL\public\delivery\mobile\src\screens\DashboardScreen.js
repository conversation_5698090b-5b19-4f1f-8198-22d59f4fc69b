import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { MaterialIcons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';

const COLORS = {
  primary: '#FF6B35',
  secondary: '#F7931E',
  red: '#E53E3E',
  white: '#FFFFFF',
  dark: '#2D3748',
  lightGray: '#F7FAFC',
  success: '#48BB78',
  warning: '#ED8936',
  gradient: ['#FF6B35', '#E53E3E', '#F7931E'],
};

const DashboardScreen = ({ navigation }) => {
  const [userData, setUserData] = useState(null);
  const [stats, setStats] = useState({
    total_pedidos: 0,
    pedidos_entregados: 0,
    ganancias_totales: 0,
    calificacion_promedio: 0,
  });
  const [refreshing, setRefreshing] = useState(false);
  const [isOnline, setIsOnline] = useState(false);

  useEffect(() => {
    loadUserData();
    loadStats();
  }, []);

  const loadUserData = async () => {
    try {
      const data = await AsyncStorage.getItem('userData');
      if (data) {
        setUserData(JSON.parse(data));
      }
    } catch (error) {
      console.error('Error loading user data:', error);
    }
  };

  const loadStats = async () => {
    try {
      // Simular carga de estadísticas
      setStats({
        total_pedidos: 15,
        pedidos_entregados: 12,
        ganancias_totales: 45000,
        calificacion_promedio: 4.8,
      });
    } catch (error) {
      console.error('Error loading stats:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadStats();
    setRefreshing(false);
  };

  const toggleOnlineStatus = () => {
    setIsOnline(!isOnline);
    Alert.alert(
      isOnline ? 'Desconectado' : 'Conectado',
      isOnline ? 'Ya no recibirás pedidos nuevos' : 'Ahora podés recibir pedidos'
    );
  };

  const StatCard = ({ icon, value, label, color }) => (
    <View style={styles.statCard}>
      <View style={[styles.statIcon, { backgroundColor: color }]}>
        <MaterialIcons name={icon} size={24} color={COLORS.white} />
      </View>
      <Text style={styles.statValue}>{value}</Text>
      <Text style={styles.statLabel}>{label}</Text>
    </View>
  );

  const QuickAction = ({ icon, title, onPress, color }) => (
    <TouchableOpacity style={styles.actionCard} onPress={onPress}>
      <LinearGradient
        colors={color}
        style={styles.actionGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <MaterialIcons name={icon} size={32} color={COLORS.white} />
        <Text style={styles.actionTitle}>{title}</Text>
      </LinearGradient>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={COLORS.gradient}
        style={styles.header}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={styles.headerContent}>
          <View style={styles.userInfo}>
            <Text style={styles.greeting}>¡Hola!</Text>
            <Text style={styles.userName}>
              {userData?.nombre_completo?.split(' ')[0] || 'Repartidor'}
            </Text>
          </View>
          
          <TouchableOpacity
            style={[styles.statusButton, isOnline && styles.statusButtonOnline]}
            onPress={toggleOnlineStatus}
          >
            <MaterialIcons 
              name={isOnline ? 'radio-button-checked' : 'radio-button-unchecked'} 
              size={20} 
              color={COLORS.white} 
            />
            <Text style={styles.statusText}>
              {isOnline ? 'En línea' : 'Desconectado'}
            </Text>
          </TouchableOpacity>
        </View>
      </LinearGradient>

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Stats Grid */}
        <View style={styles.statsContainer}>
          <Text style={styles.sectionTitle}>Tus Estadísticas</Text>
          <View style={styles.statsGrid}>
            <StatCard
              icon="assignment"
              value={stats.total_pedidos}
              label="Total Pedidos"
              color="#4299E1"
            />
            <StatCard
              icon="check-circle"
              value={stats.pedidos_entregados}
              label="Entregados"
              color={COLORS.success}
            />
            <StatCard
              icon="attach-money"
              value={`$${stats.ganancias_totales.toLocaleString()}`}
              label="Ganancias"
              color={COLORS.warning}
            />
            <StatCard
              icon="star"
              value={stats.calificacion_promedio.toFixed(1)}
              label="Calificación"
              color="#9F7AEA"
            />
          </View>
        </View>

        {/* Quick Actions */}
        <View style={styles.actionsContainer}>
          <Text style={styles.sectionTitle}>Acciones Rápidas</Text>
          <View style={styles.actionsGrid}>
            <QuickAction
              icon="search"
              title="Buscar Pedidos"
              color={['#4299E1', '#63B3ED']}
              onPress={() => navigation.navigate('Pedidos')}
            />
            <QuickAction
              icon="map"
              title="Ver Mapa"
              color={[COLORS.primary, COLORS.secondary]}
              onPress={() => navigation.navigate('Mapa')}
            />
            <QuickAction
              icon="account-balance-wallet"
              title="Mis Ganancias"
              color={[COLORS.success, '#68D391']}
              onPress={() => navigation.navigate('Ganancias')}
            />
            <QuickAction
              icon="person"
              title="Mi Perfil"
              color={['#9F7AEA', '#B794F6']}
              onPress={() => navigation.navigate('Perfil')}
            />
          </View>
        </View>

        {/* Status Message */}
        <View style={styles.statusMessage}>
          {isOnline ? (
            <View style={styles.onlineMessage}>
              <MaterialIcons name="wifi" size={24} color={COLORS.success} />
              <Text style={styles.onlineText}>
                Estás en línea y podés recibir pedidos
              </Text>
            </View>
          ) : (
            <View style={styles.offlineMessage}>
              <MaterialIcons name="wifi-off" size={24} color="#999" />
              <Text style={styles.offlineText}>
                Conectate para empezar a recibir pedidos
              </Text>
            </View>
          )}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.lightGray,
  },
  header: {
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  userInfo: {
    flex: 1,
  },
  greeting: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    fontWeight: '500',
  },
  userName: {
    fontSize: 24,
    color: COLORS.white,
    fontWeight: '800',
  },
  statusButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  statusButtonOnline: {
    backgroundColor: 'rgba(72, 187, 120, 0.3)',
    borderColor: COLORS.success,
  },
  statusText: {
    color: COLORS.white,
    fontWeight: '600',
    marginLeft: 5,
    fontSize: 12,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: COLORS.dark,
    marginBottom: 15,
    marginTop: 20,
  },
  statsContainer: {
    marginTop: 10,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statCard: {
    backgroundColor: COLORS.white,
    borderRadius: 15,
    padding: 15,
    alignItems: 'center',
    width: '48%',
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  statIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
  },
  statValue: {
    fontSize: 20,
    fontWeight: '800',
    color: COLORS.dark,
    marginBottom: 5,
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
    fontWeight: '500',
  },
  actionsContainer: {
    marginTop: 10,
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionCard: {
    width: '48%',
    marginBottom: 15,
    borderRadius: 15,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 5,
  },
  actionGradient: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 100,
  },
  actionTitle: {
    color: COLORS.white,
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
    marginTop: 8,
  },
  statusMessage: {
    marginVertical: 20,
    paddingBottom: 20,
  },
  onlineMessage: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.white,
    padding: 15,
    borderRadius: 10,
    borderLeftWidth: 4,
    borderLeftColor: COLORS.success,
  },
  onlineText: {
    marginLeft: 10,
    color: COLORS.success,
    fontWeight: '600',
    flex: 1,
  },
  offlineMessage: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.white,
    padding: 15,
    borderRadius: 10,
    borderLeftWidth: 4,
    borderLeftColor: '#999',
  },
  offlineText: {
    marginLeft: 10,
    color: '#666',
    fontWeight: '500',
    flex: 1,
  },
});

export default DashboardScreen;
