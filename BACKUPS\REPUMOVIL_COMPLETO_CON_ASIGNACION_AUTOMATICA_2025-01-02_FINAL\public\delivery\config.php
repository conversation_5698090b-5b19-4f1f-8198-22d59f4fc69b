<?php
/**
 * Configuración de RepuMovil Delivery
 */

// Configuración de base de datos
$db_config = [
    'host' => 'localhost',
    'dbname' => 'repumovil_delivery',
    'username' => 'root',
    'password' => '',
    'charset' => 'utf8mb4',
    'options' => [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ]
];

/**
 * Función para conectar a la base de datos
 */
function connectDB() {
    global $db_config;
    
    try {
        $dsn = "mysql:host={$db_config['host']};dbname={$db_config['dbname']};charset={$db_config['charset']}";
        $pdo = new PDO($dsn, $db_config['username'], $db_config['password'], $db_config['options']);
        return $pdo;
    } catch (PDOException $e) {
        // Si no existe la base de datos, intentar crearla
        try {
            $pdo = new PDO("mysql:host={$db_config['host']}", $db_config['username'], $db_config['password']);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // Crear la base de datos
            $pdo->exec("CREATE DATABASE IF NOT EXISTS {$db_config['dbname']} CHARACTER SET {$db_config['charset']} COLLATE {$db_config['charset']}_unicode_ci");
            
            // Conectar a la nueva base de datos
            $dsn = "mysql:host={$db_config['host']};dbname={$db_config['dbname']};charset={$db_config['charset']}";
            $pdo = new PDO($dsn, $db_config['username'], $db_config['password'], $db_config['options']);
            
            // Ejecutar el script de creación de tablas
            initializeDatabase($pdo);
            
            return $pdo;
        } catch (PDOException $innerException) {
            die("Error de conexión: " . $innerException->getMessage());
        }
    }
}

/**
 * Inicializar la base de datos con las tablas necesarias
 */
function initializeDatabase($pdo) {
    $sql = file_get_contents(__DIR__ . '/setup_database.sql');
    
    // Dividir el SQL en statements individuales
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    foreach ($statements as $statement) {
        if (!empty($statement) && !str_starts_with($statement, '--')) {
            try {
                $pdo->exec($statement);
            } catch (PDOException $e) {
                // Ignorar errores de tablas que ya existen
                if (strpos($e->getMessage(), 'already exists') === false) {
                    error_log("Error ejecutando SQL: " . $e->getMessage());
                }
            }
        }
    }
}

/**
 * Configuración de uploads
 */
define('UPLOAD_DIR', __DIR__ . '/uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_EXTENSIONS', ['jpg', 'jpeg', 'png', 'pdf']);

/**
 * Crear directorios de upload si no existen
 */
function createUploadDirectories() {
    $directories = [
        UPLOAD_DIR . 'dni/',
        UPLOAD_DIR . 'licencias/',
        UPLOAD_DIR . 'selfies/',
        UPLOAD_DIR . 'seguros/',
        UPLOAD_DIR . 'antecedentes/'
    ];
    
    foreach ($directories as $dir) {
        if (!file_exists($dir)) {
            mkdir($dir, 0755, true);
        }
    }
}

/**
 * Función para subir archivos
 */
function uploadFile($file, $tipo_documento, $usuario_id) {
    createUploadDirectories();
    
    if ($file['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('Error al subir el archivo');
    }
    
    if ($file['size'] > MAX_FILE_SIZE) {
        throw new Exception('El archivo es demasiado grande (máximo 5MB)');
    }
    
    $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($extension, ALLOWED_EXTENSIONS)) {
        throw new Exception('Tipo de archivo no permitido');
    }
    
    // Determinar directorio según tipo de documento
    $subdirectory = match($tipo_documento) {
        'dni_frente', 'dni_dorso' => 'dni/',
        'licencia' => 'licencias/',
        'selfie_dni' => 'selfies/',
        'seguro' => 'seguros/',
        'antecedentes' => 'antecedentes/',
        default => 'otros/'
    };
    
    $filename = $usuario_id . '_' . $tipo_documento . '_' . time() . '.' . $extension;
    $filepath = UPLOAD_DIR . $subdirectory . $filename;
    
    if (!move_uploaded_file($file['tmp_name'], $filepath)) {
        throw new Exception('Error al guardar el archivo');
    }
    
    return [
        'nombre_archivo' => $filename,
        'ruta_archivo' => $filepath,
        'tamaño_archivo' => $file['size'],
        'tipo_mime' => $file['type']
    ];
}

/**
 * Función para validar DNI argentino
 */
function validarDNI($dni) {
    $dni = preg_replace('/[^0-9]/', '', $dni);
    return strlen($dni) >= 7 && strlen($dni) <= 8 && is_numeric($dni);
}

/**
 * Función para validar CUIL/CUIT
 */
function validarCUIL($cuil) {
    $cuil = preg_replace('/[^0-9]/', '', $cuil);
    if (strlen($cuil) != 11) return false;
    
    $multiplicadores = [5, 4, 3, 2, 7, 6, 5, 4, 3, 2];
    $suma = 0;
    
    for ($i = 0; $i < 10; $i++) {
        $suma += intval($cuil[$i]) * $multiplicadores[$i];
    }
    
    $resto = $suma % 11;
    $digito = 11 - $resto;
    
    if ($digito == 11) $digito = 0;
    if ($digito == 10) $digito = 9;
    
    return $digito == intval($cuil[10]);
}

/**
 * Función para validar CBU
 */
function validarCBU($cbu) {
    $cbu = preg_replace('/[^0-9]/', '', $cbu);
    return strlen($cbu) == 22 && is_numeric($cbu);
}

/**
 * Función para generar token de sesión
 */
function generarToken() {
    return bin2hex(random_bytes(32));
}

/**
 * Función para obtener IP del cliente
 */
function obtenerIP() {
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        return $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        return $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        return $_SERVER['REMOTE_ADDR'];
    }
}

/**
 * Función para log de actividades
 */
function logActivity($mensaje, $usuario_id = null) {
    $log = date('Y-m-d H:i:s') . " - ";
    if ($usuario_id) {
        $log .= "Usuario $usuario_id - ";
    }
    $log .= $mensaje . " - IP: " . obtenerIP() . "\n";
    
    file_put_contents(__DIR__ . '/logs/activity.log', $log, FILE_APPEND | LOCK_EX);
}

// Crear directorio de logs si no existe
if (!file_exists(__DIR__ . '/logs/')) {
    mkdir(__DIR__ . '/logs/', 0755, true);
}
?>
