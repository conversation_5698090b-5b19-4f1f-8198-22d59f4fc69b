<?php
/**
 * API de Repuestos para RepuMovil
 * FASE 2: Backend/API - Paso 2
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Manejar preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Incluir configuración de base de datos
require_once 'db_config.php';

try {
    $pdo = connectDB();
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            handleGetRepuestos($pdo);
            break;
            
        case 'POST':
            handlePostRepuestos($pdo);
            break;
            
        default:
            throw new Exception('Método no permitido');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

/**
 * Obtener repuestos con filtros opcionales
 */
function handleGetRepuestos($pdo) {
    $categoria = $_GET['categoria'] ?? '';
    $marca = $_GET['marca'] ?? '';
    $search = $_GET['search'] ?? '';
    $limit = (int)($_GET['limit'] ?? 20);
    $offset = (int)($_GET['offset'] ?? 0);
    
    // Construir query base
    $sql = "
        SELECT r.*, s.name as supplier_name, s.phone as supplier_phone 
        FROM repuestos r 
        JOIN suppliers s ON r.supplier_id = s.id 
        WHERE r.estado = 'disponible'
    ";
    
    $params = [];
    
    // Agregar filtros
    if (!empty($categoria)) {
        $sql .= " AND r.categoria = ?";
        $params[] = $categoria;
    }
    
    if (!empty($marca)) {
        $sql .= " AND r.marca = ?";
        $params[] = $marca;
    }
    
    if (!empty($search)) {
        $sql .= " AND (r.nombre LIKE ? OR r.descripcion LIKE ? OR r.codigo_producto LIKE ?)";
        $searchTerm = "%$search%";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    
    // Agregar ordenamiento y límite
    $sql .= " ORDER BY r.nombre ASC LIMIT ? OFFSET ?";
    $params[] = $limit;
    $params[] = $offset;
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $repuestos = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Obtener total de registros para paginación
    $countSql = "
        SELECT COUNT(*) as total 
        FROM repuestos r 
        WHERE r.estado = 'disponible'
    ";
    
    $countParams = [];
    
    if (!empty($categoria)) {
        $countSql .= " AND r.categoria = ?";
        $countParams[] = $categoria;
    }
    
    if (!empty($marca)) {
        $countSql .= " AND r.marca = ?";
        $countParams[] = $marca;
    }
    
    if (!empty($search)) {
        $countSql .= " AND (r.nombre LIKE ? OR r.descripcion LIKE ? OR r.codigo_producto LIKE ?)";
        $searchTerm = "%$search%";
        $countParams[] = $searchTerm;
        $countParams[] = $searchTerm;
        $countParams[] = $searchTerm;
    }
    
    $countStmt = $pdo->prepare($countSql);
    $countStmt->execute($countParams);
    $total = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Obtener categorías disponibles
    $categoriesStmt = $pdo->query("SELECT DISTINCT categoria FROM repuestos WHERE estado = 'disponible' ORDER BY categoria");
    $categorias = $categoriesStmt->fetchAll(PDO::FETCH_COLUMN);
    
    // Obtener marcas disponibles
    $brandsStmt = $pdo->query("SELECT DISTINCT marca FROM repuestos WHERE estado = 'disponible' AND marca IS NOT NULL ORDER BY marca");
    $marcas = $brandsStmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo json_encode([
        'success' => true,
        'data' => $repuestos,
        'pagination' => [
            'total' => (int)$total,
            'limit' => $limit,
            'offset' => $offset,
            'has_more' => ($offset + $limit) < $total
        ],
        'filters' => [
            'categorias' => $categorias,
            'marcas' => $marcas
        ]
    ]);
}

/**
 * Obtener un repuesto específico por ID
 */
function handlePostRepuestos($pdo) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['action'])) {
        throw new Exception('Acción no especificada');
    }
    
    switch ($input['action']) {
        case 'get_by_id':
            if (!isset($input['id'])) {
                throw new Exception('ID de repuesto requerido');
            }
            
            $stmt = $pdo->prepare("
                SELECT r.*, s.name as supplier_name, s.phone as supplier_phone, s.address as supplier_address 
                FROM repuestos r 
                JOIN suppliers s ON r.supplier_id = s.id 
                WHERE r.id = ? AND r.estado = 'disponible'
            ");
            $stmt->execute([$input['id']]);
            $repuesto = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$repuesto) {
                throw new Exception('Repuesto no encontrado');
            }
            
            echo json_encode([
                'success' => true,
                'data' => $repuesto
            ]);
            break;
            
        default:
            throw new Exception('Acción no válida');
    }
}
?>
