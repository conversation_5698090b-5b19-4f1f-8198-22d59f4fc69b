import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';

const COLORS = {
  primary: '#FF6B35',
  red: '#E53E3E',
  white: '#FFFFFF',
  dark: '#2D3748',
  lightGray: '#F7FAFC',
  success: '#48BB78',
  warning: '#ED8936',
};

const PedidosScreen = () => {
  const [pedidos, setPedidos] = useState([
    {
      id: 1,
      cliente: '<PERSON>',
      direccion: 'Av. <PERSON> 123',
      distancia: '2.5 km',
      pago: 1500,
      estado: 'disponible',
      tiempo_estimado: '15 min',
    },
    {
      id: 2,
      cliente: '<PERSON>',
      direccion: 'Calle Rivadavia 456',
      distancia: '1.8 km',
      pago: 1200,
      estado: 'en_curso',
      tiempo_estimado: '10 min',
    },
  ]);
  const [refreshing, setRefreshing] = useState(false);

  const onRefresh = () => {
    setRefreshing(true);
    setTimeout(() => setRefreshing(false), 1000);
  };

  const PedidoCard = ({ item }) => (
    <View style={styles.pedidoCard}>
      <View style={styles.pedidoHeader}>
        <Text style={styles.clienteNombre}>{item.cliente}</Text>
        <View style={[styles.estadoBadge, 
          item.estado === 'disponible' ? styles.estadoDisponible : styles.estadoEnCurso
        ]}>
          <Text style={styles.estadoText}>
            {item.estado === 'disponible' ? 'Disponible' : 'En Curso'}
          </Text>
        </View>
      </View>
      
      <View style={styles.pedidoInfo}>
        <View style={styles.infoRow}>
          <MaterialIcons name="location-on" size={16} color={COLORS.red} />
          <Text style={styles.infoText}>{item.direccion}</Text>
        </View>
        
        <View style={styles.infoRow}>
          <MaterialIcons name="directions" size={16} color={COLORS.primary} />
          <Text style={styles.infoText}>{item.distancia} • {item.tiempo_estimado}</Text>
        </View>
      </View>
      
      <View style={styles.pedidoFooter}>
        <Text style={styles.pagoAmount}>${item.pago}</Text>
        <TouchableOpacity style={styles.actionButton}>
          <Text style={styles.actionButtonText}>
            {item.estado === 'disponible' ? 'Aceptar' : 'Ver Detalles'}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Pedidos Disponibles</Text>
        <TouchableOpacity style={styles.filterButton}>
          <MaterialIcons name="filter-list" size={24} color={COLORS.red} />
        </TouchableOpacity>
      </View>

      <FlatList
        data={pedidos}
        keyExtractor={(item) => item.id.toString()}
        renderItem={({ item }) => <PedidoCard item={item} />}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.lightGray,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 20,
    backgroundColor: COLORS.white,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '800',
    color: COLORS.dark,
  },
  filterButton: {
    padding: 8,
  },
  listContainer: {
    padding: 20,
  },
  pedidoCard: {
    backgroundColor: COLORS.white,
    borderRadius: 15,
    padding: 15,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  pedidoHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  clienteNombre: {
    fontSize: 16,
    fontWeight: '700',
    color: COLORS.dark,
  },
  estadoBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
  },
  estadoDisponible: {
    backgroundColor: '#C6F6D5',
  },
  estadoEnCurso: {
    backgroundColor: '#FEEBC8',
  },
  estadoText: {
    fontSize: 12,
    fontWeight: '600',
    color: COLORS.dark,
  },
  pedidoInfo: {
    marginBottom: 15,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 5,
  },
  infoText: {
    marginLeft: 8,
    color: '#666',
    fontSize: 14,
  },
  pedidoFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  pagoAmount: {
    fontSize: 20,
    fontWeight: '800',
    color: COLORS.success,
  },
  actionButton: {
    backgroundColor: COLORS.red,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
  },
  actionButtonText: {
    color: COLORS.white,
    fontWeight: '600',
    fontSize: 14,
  },
});

export default PedidosScreen;
