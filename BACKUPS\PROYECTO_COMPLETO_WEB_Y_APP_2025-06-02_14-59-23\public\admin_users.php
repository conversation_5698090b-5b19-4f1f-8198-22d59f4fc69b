<?php
// Mostrar todos los errores para depuración
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Iniciar sesión
session_start();

// Verificar si el usuario está autenticado y es administrador
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: login_reemplazo.php');
    exit;
}

// Obtener información del usuario actual
$username = $_SESSION['user_name'] ?? $_SESSION['username'] ?? 'Administrador';

// Incluir archivos necesarios
require_once 'db_config.php';

// Obtener lista de usuarios
try {
    $conn = connectDB();
    $query = "SELECT u.*, r.name as role_name
              FROM users u
              JOIN roles r ON u.role_id = r.id
              ORDER BY u.id";
    $users = $conn->query($query)->fetchAll();
} catch (PDOException $e) {
    $error = 'Error al cargar usuarios: ' . $e->getMessage();
    $users = [];
}

// Procesar acciones
$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        // Cambiar estado de usuario
        if ($_POST['action'] === 'change_status') {
            $userId = (int)$_POST['user_id'];
            $status = filter_input(INPUT_POST, 'status', FILTER_SANITIZE_STRING);

            try {
                $stmt = $conn->prepare("UPDATE users SET status = :status WHERE id = :id");
                $stmt->execute(['status' => $status, 'id' => $userId]);

                $message = 'Estado del usuario actualizado exitosamente.';

                // Recargar la lista de usuarios
                $users = $conn->query($query)->fetchAll();
            } catch (PDOException $e) {
                $error = 'Error al actualizar el estado: ' . $e->getMessage();
            }
        }

        // Eliminar usuario
        if ($_POST['action'] === 'delete_user') {
            $userId = (int)$_POST['user_id'];

            try {
                $conn->beginTransaction();

                // Eliminar registros relacionados primero
                $stmt = $conn->prepare("DELETE FROM workshops WHERE user_id = :user_id");
                $stmt->execute(['user_id' => $userId]);

                $stmt = $conn->prepare("DELETE FROM suppliers WHERE user_id = :user_id");
                $stmt->execute(['user_id' => $userId]);

                // Finalmente eliminar el usuario
                $stmt = $conn->prepare("DELETE FROM users WHERE id = :id");
                $stmt->execute(['id' => $userId]);

                $conn->commit();
                $message = 'Usuario eliminado exitosamente.';

                // Recargar la lista de usuarios
                $users = $conn->query($query)->fetchAll();
            } catch (PDOException $e) {
                $conn->rollBack();
                $error = 'Error al eliminar el usuario: ' . $e->getMessage();
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestión de Usuarios - Repumóvil</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding-top: 50px;
        }
        .admin-container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: #fff;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            backdrop-filter: blur(10px);
        }
        .row {
            margin: 0;
        }
        .admin-sidebar {
            background: linear-gradient(135deg, #FF6B35 0%, #FFA500 100%);
            color: white;
            padding: 40px 20px;
            height: 100%;
            min-height: 600px;
        }
        .admin-content {
            padding: 40px;
        }
        .logo-container {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo-circle {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
            box-shadow: 0 8px 25px rgba(255, 255, 255, 0.3);
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        .logo-icon {
            font-size: 35px;
            color: white;
        }
        .logo-title {
            font-size: 28px;
            font-weight: bold;
            margin: 0;
            color: white;
        }
        .logo-subtitle {
            font-size: 14px;
            opacity: 0.9;
            margin-top: 5px;
            color: white;
        }
        .admin-sidebar ul {
            padding-left: 0;
            margin-top: 20px;
        }
        .admin-sidebar li {
            margin-bottom: 8px;
            list-style: none;
        }
        .admin-sidebar .nav-link {
            color: rgba(255, 255, 255, 0.85);
            padding: 12px 15px;
            border-radius: 12px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: block;
        }
        .admin-sidebar .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.2);
            transform: translateX(5px);
        }
        .admin-sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.3);
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(255, 255, 255, 0.2);
        }
        .page-title {
            font-size: 2rem;
            margin-bottom: 30px;
            color: #FF6B35;
            font-weight: 600;
        }
        .btn-primary {
            background: linear-gradient(135deg, #FF6B35 0%, #FFA500 100%);
            border: none;
            border-radius: 12px;
            padding: 12px 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 107, 53, 0.4);
        }
        .alert {
            border-radius: 12px;
            border: none;
        }
        .card {
            border-radius: 15px;
            border: none;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        .card-header {
            background: linear-gradient(135deg, #FF6B35 0%, #FFA500 100%) !important;
            color: white !important;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }
        .table {
            font-size: 14px;
        }
        .table thead {
            background-color: #f8f9fa;
        }
        .table thead th {
            padding: 12px 8px;
            font-weight: 600;
            border-bottom: 2px solid #dee2e6;
            white-space: nowrap;
        }
        .table tbody td {
            padding: 8px 8px;
            vertical-align: middle;
            border-bottom: 1px solid #dee2e6;
        }
        .table-striped tbody tr:nth-of-type(odd) {
            background-color: rgba(255, 107, 53, 0.05);
        }
        .table-responsive {
            border-radius: 10px;
            overflow-x: auto;
            overflow-y: hidden;
            width: 100%;
        }
        .table {
            min-width: 1000px;
            width: 100%;
        }
        .user-email {
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-size: 13px;
        }
        .user-actions {
            min-width: 180px;
        }
        .action-buttons {
            display: flex;
            gap: 2px;
            align-items: center;
            flex-direction: column;
            justify-content: center;
        }
        .btn-sm {
            padding: 4px 8px;
            font-size: 12px;
        }
        .status-active {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        .status-inactive {
            background: linear-gradient(135deg, #6c757d 0%, #adb5bd 100%);
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        .status-suspended {
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        .btn-info {
            background: linear-gradient(135deg, #17a2b8 0%, #20c997 100%);
            border: none;
            border-radius: 8px;
        }
        .btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
            border: none;
            border-radius: 8px;
        }
        .form-control-sm {
            border-radius: 8px;
            border: 2px solid #e9ecef;
        }
        .admin-footer {
            margin-top: 30px;
            text-align: center;
            color: #6c757d;
        }

        /* Media Queries para diferentes zooms y pantallas */
        @media (max-width: 1400px) {
            .admin-container {
                max-width: 95%;
            }
            .table {
                min-width: 900px;
            }
        }

        @media (max-width: 1200px) {
            .admin-container {
                max-width: 98%;
            }
            .table {
                min-width: 800px;
            }
            .admin-content {
                padding: 20px;
            }
        }

        @media (max-width: 992px) {
            .admin-container {
                border-radius: 10px;
            }
            .table {
                font-size: 12px;
                min-width: 700px;
            }
            .btn-sm {
                padding: 2px 4px;
                font-size: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid" style="padding: 0 20px;">
        <div class="admin-container">
            <div class="row">
                <div class="col-md-3 d-none d-md-block">
                    <div class="admin-sidebar">
                        <div class="logo-container">
                            <div class="logo-circle">
                                <i class="fas fa-wrench logo-icon"></i>
                            </div>
                            <h1 class="logo-title">RepuMovil</h1>
                            <p class="logo-subtitle">Panel de Administración</p>
                        </div>
                        <p style="opacity: 0.9; margin-bottom: 20px;">Gestiona usuarios, talleres y proveedores de la plataforma RepuMovil.</p>
                        <ul class="nav flex-column">
                            <li class="nav-item">
                                <a class="nav-link" href="admin.php">
                                    <i class="fas fa-tachometer-alt mr-2"></i> Dashboard
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link active" href="admin_users.php">
                                    <i class="fas fa-users mr-2"></i> Usuarios
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="admin_add_user.php">
                                    <i class="fas fa-user-plus mr-2"></i> Agregar Usuario
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="admin_workshops.php">
                                    <i class="fas fa-tools mr-2"></i> Talleres
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="admin_suppliers.php">
                                    <i class="fas fa-truck mr-2"></i> Proveedores
                                </a>
                            </li>
                            <li class="nav-item mt-3">
                                <a class="nav-link" href="logout.php">
                                    <i class="fas fa-sign-out-alt mr-2"></i> Cerrar Sesión
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>

                <div class="col-md-9">
                    <div class="admin-content">
                        <div class="logo-container d-block d-md-none" style="margin-bottom: 30px;">
                            <div class="logo-circle" style="background: linear-gradient(135deg, #FF6B35 0%, #FFA500 100%);">
                                <i class="fas fa-wrench logo-icon"></i>
                            </div>
                            <h1 class="logo-title" style="color: #FF6B35;">RepuMovil</h1>
                            <p class="text-muted">Panel de Administración</p>
                        </div>

                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h2 class="page-title">Gestión de Usuarios</h2>
                            <div>
                                <span class="mr-2">Bienvenido, <strong><?php echo htmlspecialchars($username); ?></strong></span>
                                <a href="admin_add_user.php" class="btn btn-primary">
                                    <i class="fas fa-user-plus"></i> Agregar Usuario
                                </a>
                            </div>
                        </div>

                <?php if (!empty($message)): ?>
                    <div class="alert alert-success" role="alert">
                        <?php echo htmlspecialchars($message); ?>
                    </div>
                <?php endif; ?>

                <?php if (!empty($error)): ?>
                    <div class="alert alert-danger" role="alert">
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>

                <div class="card">
                    <div class="card-header">
                        <h5>Lista de Usuarios</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th style="min-width: 50px;">ID</th>
                                        <th style="min-width: 120px;">Usuario</th>
                                        <th style="min-width: 200px;">Email</th>
                                        <th style="min-width: 100px;">Rol</th>
                                        <th style="min-width: 80px;">Estado</th>
                                        <th style="min-width: 120px;">Fecha Registro</th>
                                        <th style="min-width: 120px;">Último Acceso</th>
                                        <th style="min-width: 200px;">Acciones</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (!empty($users)): ?>
                                        <?php foreach ($users as $user): ?>
                                            <tr>
                                                <td><strong>#<?php echo $user['id']; ?></strong></td>
                                                <td>
                                                    <div style="font-weight: 600; color: #333;">
                                                        <?php echo htmlspecialchars($user['username']); ?>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="user-email" title="<?php echo htmlspecialchars($user['email']); ?>">
                                                        <?php echo htmlspecialchars($user['email']); ?>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="badge badge-secondary" style="background: linear-gradient(135deg, #6c757d 0%, #adb5bd 100%); padding: 5px 10px; border-radius: 15px;">
                                                        <?php echo htmlspecialchars($user['role_name']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php if ($user['status'] == 'active'): ?>
                                                        <span class="status-active">Activo</span>
                                                    <?php elseif ($user['status'] == 'inactive'): ?>
                                                        <span class="status-inactive">Inactivo</span>
                                                    <?php else: ?>
                                                        <span class="status-suspended">Suspendido</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <small style="color: #666;">
                                                        <?php echo $user['created_at'] ? date('d/m/Y', strtotime($user['created_at'])) : 'N/A'; ?>
                                                    </small>
                                                </td>
                                                <td>
                                                    <small style="color: #666;">
                                                        <?php echo $user['last_login'] ? date('d/m/Y', strtotime($user['last_login'])) : 'Nunca'; ?>
                                                    </small>
                                                </td>
                                                <td class="user-actions">
                                                    <div class="action-buttons">
                                                        <div style="display: flex; gap: 2px; margin-bottom: 2px;">
                                                            <a href="admin_edit_user.php?id=<?php echo $user['id']; ?>" class="btn btn-sm btn-info" title="Editar" style="padding: 3px 6px;">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <?php if ($user['id'] != $_SESSION['user_id']): ?>
                                                                <form method="post" style="display: inline;" onsubmit="return confirm('¿Está seguro de eliminar este usuario? Esta acción no se puede deshacer.');">
                                                                    <input type="hidden" name="action" value="delete_user">
                                                                    <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                                    <button type="submit" class="btn btn-sm btn-danger" title="Eliminar" style="padding: 3px 6px;">
                                                                        <i class="fas fa-trash"></i>
                                                                    </button>
                                                                </form>
                                                            <?php endif; ?>
                                                        </div>
                                                        <form method="post" style="width: 100%;">
                                                            <input type="hidden" name="action" value="change_status">
                                                            <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                            <select name="status" onchange="this.form.submit()" class="form-control-sm" style="font-size: 9px; padding: 2px; width: 100%; text-align: center;">
                                                                <option value="">Estado</option>
                                                                <option value="active">Activar</option>
                                                                <option value="inactive">Desactivar</option>
                                                                <option value="suspended">Suspender</option>
                                                            </select>
                                                        </form>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="8" class="text-center">No hay usuarios registrados</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>

                        <div class="admin-footer">
                            <p>Panel de Administración - Repumóvil &copy; 2024</p>
                            <p>Gestiona tu plataforma de talleres y proveedores de manera eficiente.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
</body>
</html>




