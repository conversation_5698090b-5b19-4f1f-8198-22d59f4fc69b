<?php
// Configuración de la conexión
require_once '../public/db_config.php';

try {
    $conn = connectDB();
    
    // Verificar si ya existen los roles
    $stmt = $conn->prepare("SELECT COUNT(*) FROM roles");
    $stmt->execute();
    $roleCount = $stmt->fetchColumn();
    
    if ($roleCount > 0) {
        // Actualizar roles existentes
        $conn->exec("UPDATE roles SET name = 'admin', description = 'Administrador del sistema' WHERE id = 1");
        $conn->exec("UPDATE roles SET name = 'workshop', description = 'Taller mecánico' WHERE id = 2");
        $conn->exec("UPDATE roles SET name = 'mechanic', description = 'Mecánico independiente' WHERE id = 3");
        $conn->exec("UPDATE roles SET name = 'person', description = 'Usuario regular' WHERE id = 4");
        
        // Verificar si existe el rol de proveedor (antiguo id 3) y actualizarlo o crear uno nuevo
        $stmt = $conn->prepare("SELECT COUNT(*) FROM roles WHERE id = 5");
        $stmt->execute();
        if ($stmt->fetchColumn() == 0) {
            $conn->exec("INSERT INTO roles (id, name, description) VALUES (5, 'supplier', 'Proveedor de repuestos')");
        } else {
            $conn->exec("UPDATE roles SET name = 'supplier', description = 'Proveedor de repuestos' WHERE id = 5");
        }
    } else {
        // Insertar roles desde cero
        $conn->exec("INSERT INTO roles (id, name, description) VALUES 
            (1, 'admin', 'Administrador del sistema'),
            (2, 'workshop', 'Taller mecánico'),
            (3, 'mechanic', 'Mecánico independiente'),
            (4, 'person', 'Usuario regular'),
            (5, 'supplier', 'Proveedor de repuestos')");
    }
    
    // Crear tabla para mecánicos si no existe
    $conn->exec("CREATE TABLE IF NOT EXISTS mechanics (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        name VARCHAR(100) NOT NULL,
        specialties TEXT,
        experience VARCHAR(255),
        address VARCHAR(255),
        phone VARCHAR(20),
        maps_url VARCHAR(255),
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )");
    
    // Crear tabla para personas si no existe
    $conn->exec("CREATE TABLE IF NOT EXISTS persons (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        first_name VARCHAR(50),
        last_name VARCHAR(50),
        phone VARCHAR(20),
        address VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )");
    
    echo "Roles y tablas actualizados correctamente.";
    
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage();
}
?>