<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RepuMovil - Test Sistema de Pedidos 📦</title>
    <style>
        :root {
            --primary-color: #FF6B35;
            --secondary-color: #FFA500;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --dark-color: #2c3e50;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
            color: white;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .title {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .panel {
            background: white;
            color: var(--dark-color);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .panel h3 {
            color: var(--primary-color);
            margin-bottom: 20px;
            font-size: 1.3rem;
        }

        .btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
            width: 100%;
        }

        .btn:hover {
            background: #e55a2b;
            transform: translateY(-2px);
        }

        .btn-success {
            background: var(--success-color);
        }

        .btn-success:hover {
            background: #218838;
        }

        .btn-danger {
            background: var(--danger-color);
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .btn-warning {
            background: var(--warning-color);
            color: var(--dark-color);
        }

        .btn-warning:hover {
            background: #e0a800;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-label {
            display: block;
            font-weight: bold;
            margin-bottom: 5px;
            color: var(--primary-color);
        }

        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 10px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 1rem;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .pedido-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            border-left: 4px solid var(--primary-color);
        }

        .pedido-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .pedido-id {
            font-weight: bold;
            font-size: 1.1rem;
        }

        .pedido-estado {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .estado-nuevo { background: #e3f2fd; color: #1976d2; }
        .estado-asignado { background: #fff3e0; color: #f57c00; }
        .estado-en-camino { background: #e8f5e8; color: #388e3c; }
        .estado-entregado { background: #f3e5f5; color: #7b1fa2; }

        .pedido-info {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 5px;
        }

        .result-panel {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
            backdrop-filter: blur(10px);
        }

        .result-content {
            background: rgba(0,0,0,0.1);
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            backdrop-filter: blur(10px);
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: white;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">📦 Test Sistema de Pedidos</h1>
            <p class="subtitle">Panel de pruebas para aceptación/rechazo de pedidos</p>
        </div>

        <!-- Estadísticas -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="total-pedidos">0</div>
                <div class="stat-label">Total Pedidos</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="pedidos-disponibles">0</div>
                <div class="stat-label">Disponibles</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="pedidos-asignados">0</div>
                <div class="stat-label">Asignados</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="pedidos-entregados">0</div>
                <div class="stat-label">Entregados</div>
            </div>
        </div>

        <div class="grid">
            <!-- Panel de creación de pedidos -->
            <div class="panel">
                <h3>🆕 Crear Pedido de Prueba</h3>
                <div class="form-group">
                    <label class="form-label">Cliente:</label>
                    <input type="text" class="form-input" id="cliente-nombre" value="Taller de Prueba">
                </div>
                
                <div class="form-group">
                    <label class="form-label">Dirección:</label>
                    <input type="text" class="form-input" id="cliente-direccion" value="Av. Test 123, Centro">
                </div>
                
                <div class="form-group">
                    <label class="form-label">Total ($):</label>
                    <input type="number" class="form-input" id="pedido-total" value="1500" min="100" max="10000">
                </div>
                
                <div class="form-group">
                    <label class="form-label">Prioridad:</label>
                    <select class="form-select" id="pedido-prioridad">
                        <option value="normal">Normal</option>
                        <option value="alta">Alta</option>
                        <option value="urgente">Urgente</option>
                    </select>
                </div>
                
                <button class="btn btn-success" onclick="crearPedidoPrueba()">📦 Crear Pedido</button>
                <button class="btn btn-warning" onclick="crearPedidosMultiples()">📦📦 Crear 5 Pedidos</button>
            </div>

            <!-- Panel de pedidos disponibles -->
            <div class="panel">
                <h3>📋 Pedidos Disponibles</h3>
                <div id="pedidos-disponibles-container">
                    <p>Cargando pedidos...</p>
                </div>
                <button class="btn" onclick="cargarPedidosDisponibles()">🔄 Actualizar</button>
            </div>

            <!-- Panel de simulación de delivery -->
            <div class="panel">
                <h3>🚚 Simular Delivery</h3>
                <div class="form-group">
                    <label class="form-label">Delivery ID:</label>
                    <select class="form-select" id="delivery-id">
                        <option value="1">Juan Pérez (ID: 1)</option>
                        <option value="2">María González (ID: 2)</option>
                        <option value="3">Carlos Rodríguez (ID: 3)</option>
                        <option value="4">Ana López (ID: 4)</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Pedido ID:</label>
                    <input type="number" class="form-input" id="pedido-id" placeholder="ID del pedido">
                </div>
                
                <button class="btn btn-success" onclick="simularAceptacion()">✅ Simular Aceptación</button>
                <button class="btn btn-danger" onclick="simularRechazo()">❌ Simular Rechazo</button>
            </div>
        </div>

        <!-- Panel de resultados -->
        <div id="result-panel" class="result-panel" style="display: none;">
            <h3>📋 Resultado:</h3>
            <div id="result-content" class="result-content"></div>
        </div>
    </div>

    <script>
        // PASO 3: Cargar pedidos disponibles
        async function cargarPedidosDisponibles() {
            try {
                const response = await fetch('api/pedidos-delivery.php?action=get_pedidos_disponibles&delivery_id=1&limite=10');
                const result = await response.json();
                
                const container = document.getElementById('pedidos-disponibles-container');
                
                if (result.success && result.data.length > 0) {
                    container.innerHTML = result.data.map(pedido => `
                        <div class="pedido-card">
                            <div class="pedido-header">
                                <span class="pedido-id">Pedido #${pedido.id}</span>
                                <span class="pedido-estado estado-${pedido.estado}">${pedido.estado}</span>
                            </div>
                            <div class="pedido-info">👤 ${pedido.cliente_nombre || pedido.cliente}</div>
                            <div class="pedido-info">📍 ${pedido.direccion_entrega}</div>
                            <div class="pedido-info">💰 $${parseFloat(pedido.total).toLocaleString()}</div>
                            <div class="pedido-info">🎯 ${pedido.prioridad} • 🛣️ ${pedido.distancia}km • ⏱️ ${pedido.tiempo_estimado}min</div>
                            <div class="pedido-info">💵 Ganancia: $${pedido.ganancia_estimada}</div>
                        </div>
                    `).join('');
                    
                    document.getElementById('pedidos-disponibles').textContent = result.data.length;
                } else {
                    container.innerHTML = '<p>No hay pedidos disponibles</p>';
                    document.getElementById('pedidos-disponibles').textContent = '0';
                }
                
                mostrarResultado(`✅ ${result.data?.length || 0} pedidos disponibles cargados`);
            } catch (error) {
                mostrarResultado(`❌ Error cargando pedidos: ${error.message}`);
            }
        }

        // PASO 3: Crear pedido de prueba
        async function crearPedidoPrueba() {
            try {
                const cliente = document.getElementById('cliente-nombre').value;
                const direccion = document.getElementById('cliente-direccion').value;
                const total = parseFloat(document.getElementById('pedido-total').value);
                const prioridad = document.getElementById('pedido-prioridad').value;
                
                const response = await fetch('api/pedidos-delivery.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'crear_pedido_test',
                        cliente: cliente,
                        direccion: direccion,
                        total: total,
                        prioridad: prioridad
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    mostrarResultado(`✅ Pedido creado: ID ${result.data.pedido_id}\nCliente: ${result.data.cliente}\nTotal: $${result.data.total}\nPrioridad: ${result.data.prioridad}`);
                    cargarPedidosDisponibles();
                    actualizarEstadisticas();
                } else {
                    mostrarResultado(`❌ Error creando pedido: ${result.message}`);
                }
            } catch (error) {
                mostrarResultado(`❌ Error: ${error.message}`);
            }
        }

        // PASO 3: Crear múltiples pedidos
        async function crearPedidosMultiples() {
            const clientes = [
                'Taller Central', 'AutoService Plus', 'Mecánico López', 
                'Taller San Martín', 'Repuestos García'
            ];
            const direcciones = [
                'Av. Libertador 1234', 'Ruta 40 Km 15', 'Calle Rivadavia 567',
                'Av. San Martín 890', 'Calle Mitre 345'
            ];
            const prioridades = ['normal', 'alta', 'urgente'];
            
            let creados = 0;
            
            for (let i = 0; i < 5; i++) {
                try {
                    const response = await fetch('api/pedidos-delivery.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            action: 'crear_pedido_test',
                            cliente: clientes[i],
                            direccion: direcciones[i],
                            total: Math.floor(Math.random() * 3000) + 500,
                            prioridad: prioridades[Math.floor(Math.random() * prioridades.length)]
                        })
                    });
                    
                    const result = await response.json();
                    if (result.success) creados++;
                    
                } catch (error) {
                    console.error('Error creando pedido:', error);
                }
            }
            
            mostrarResultado(`✅ ${creados}/5 pedidos creados exitosamente`);
            cargarPedidosDisponibles();
            actualizarEstadisticas();
        }

        // PASO 3: Simular aceptación de pedido
        async function simularAceptacion() {
            try {
                const deliveryId = document.getElementById('delivery-id').value;
                const pedidoId = document.getElementById('pedido-id').value;
                
                if (!pedidoId) {
                    mostrarResultado('❌ Ingresa un ID de pedido');
                    return;
                }
                
                const response = await fetch('api/pedidos-delivery.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'aceptar_pedido',
                        pedido_id: parseInt(pedidoId),
                        delivery_id: parseInt(deliveryId)
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    mostrarResultado(`✅ Pedido ${pedidoId} aceptado por delivery ${deliveryId}\nNuevo estado: ${result.data.nuevo_estado}`);
                    cargarPedidosDisponibles();
                    actualizarEstadisticas();
                } else {
                    mostrarResultado(`❌ Error aceptando pedido: ${result.message}`);
                }
            } catch (error) {
                mostrarResultado(`❌ Error: ${error.message}`);
            }
        }

        // PASO 3: Simular rechazo de pedido
        async function simularRechazo() {
            try {
                const deliveryId = document.getElementById('delivery-id').value;
                const pedidoId = document.getElementById('pedido-id').value;
                
                if (!pedidoId) {
                    mostrarResultado('❌ Ingresa un ID de pedido');
                    return;
                }
                
                const motivos = ['distancia', 'no_disponible', 'otro'];
                const motivo = motivos[Math.floor(Math.random() * motivos.length)];
                
                const response = await fetch('api/pedidos-delivery.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'rechazar_pedido',
                        pedido_id: parseInt(pedidoId),
                        delivery_id: parseInt(deliveryId),
                        motivo: motivo,
                        observaciones: 'Rechazo simulado desde panel de testing'
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    mostrarResultado(`❌ Pedido ${pedidoId} rechazado por delivery ${deliveryId}\nMotivo: ${motivo}\nTotal rechazos: ${result.data.total_rechazos}`);
                    cargarPedidosDisponibles();
                    actualizarEstadisticas();
                } else {
                    mostrarResultado(`❌ Error rechazando pedido: ${result.message}`);
                }
            } catch (error) {
                mostrarResultado(`❌ Error: ${error.message}`);
            }
        }

        // PASO 3: Actualizar estadísticas
        async function actualizarEstadisticas() {
            try {
                // Aquí podrías hacer una llamada a una API de estadísticas
                // Por ahora simulamos los datos
                document.getElementById('total-pedidos').textContent = Math.floor(Math.random() * 50) + 10;
                document.getElementById('pedidos-asignados').textContent = Math.floor(Math.random() * 10) + 2;
                document.getElementById('pedidos-entregados').textContent = Math.floor(Math.random() * 20) + 5;
            } catch (error) {
                console.error('Error actualizando estadísticas:', error);
            }
        }

        // PASO 3: Mostrar resultado
        function mostrarResultado(contenido) {
            const panel = document.getElementById('result-panel');
            const content = document.getElementById('result-content');
            
            content.textContent = contenido;
            panel.style.display = 'block';
            
            // Scroll al resultado
            panel.scrollIntoView({ behavior: 'smooth' });
        }

        // PASO 3: Inicializar al cargar la página
        document.addEventListener('DOMContentLoaded', function() {
            cargarPedidosDisponibles();
            actualizarEstadisticas();
            
            // Auto-actualizar cada 30 segundos
            setInterval(() => {
                cargarPedidosDisponibles();
                actualizarEstadisticas();
            }, 30000);
        });
    </script>
</body>
</html>
