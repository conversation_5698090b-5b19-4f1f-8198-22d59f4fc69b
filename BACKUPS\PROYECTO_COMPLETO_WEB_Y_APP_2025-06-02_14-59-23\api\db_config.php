<?php
// Configuración de la base de datos
$config = [
    'db' => [
        'host' => 'localhost',
        'name' => 'autoconnect_db',
        'user' => 'root',
        'pass' => '',
        'charset' => 'utf8mb4'
    ]
];

// Función para conectar a la base de datos
function connectDB() {
    global $config;

    $dsn = "mysql:host={$config['db']['host']};dbname={$config['db']['name']};charset={$config['db']['charset']}";
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ];

    return new PDO($dsn, $config['db']['user'], $config['db']['pass'], $options);
}
?>
