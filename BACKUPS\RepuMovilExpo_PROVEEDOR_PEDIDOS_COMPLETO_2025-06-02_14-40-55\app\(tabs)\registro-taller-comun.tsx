import React, { useState } from 'react';
import {
  SafeAreaView,
  StatusBar,
  StyleSheet,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  Dimensions,
} from 'react-native';
import { useRouter } from 'expo-router';

const { width } = Dimensions.get('window');

export default function RegistroTallerComun() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    nombre: '',
    email: '',
    telefono: '',
    password: '',
    username: '',
    direccion: '',
    rubro_principal: '',
    datos_fiscales: '',
  });

  const [loading, setLoading] = useState(false);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const validarFormulario = () => {
    if (!formData.nombre || !formData.email || !formData.password || !formData.username) {
      Alert.alert('⚠️ Campos requeridos', 'Completa todos los campos obligatorios');
      return false;
    }

    if (formData.password.length < 6) {
      Alert.alert('⚠️ Contraseña', 'La contraseña debe tener al menos 6 caracteres');
      return false;
    }

    if (!formData.email.includes('@')) {
      Alert.alert('⚠️ Email', 'Ingresa un email válido');
      return false;
    }

    return true;
  };

  const registrarTaller = async () => {
    if (!validarFormulario()) return;

    setLoading(true);

    try {
      // Preparar datos para enviar a la API
      const datosRegistro = {
        nombre: formData.nombre,
        email: formData.email,
        telefono: formData.telefono,
        password: formData.password,
        username: formData.username,
        direccion: formData.direccion,
        rubro_principal: formData.rubro_principal,
        datos_fiscales: formData.datos_fiscales,
        user_type: 'taller_mecanico',
        plan_type: 'comun'
      };

      console.log('📤 Enviando datos a la API:', datosRegistro);

      // Llamar a la API PHP
      const response = await fetch('http://localhost/mechanical-workshop/public/api/registro-taller-comun.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify(datosRegistro),
      });

      console.log('📥 Respuesta del servidor:', response.status);

      const resultado = await response.json();
      console.log('📋 Datos de respuesta:', resultado);

      if (resultado.success) {
        Alert.alert(
          '🎉 ¡Registro Exitoso!',
          `¡Bienvenido a RepuMovil!\n\nTu taller "${formData.username}" ha sido registrado exitosamente en la base de datos.\n\n✅ Incluye:\n• Pedido de repuestos\n• Sistema changuito\n• Calificaciones\n• Notificaciones básicas\n\n🆔 ID de usuario: ${resultado.user_id}`,
          [
            {
              text: 'Ir al Dashboard',
              onPress: () => router.push('/dashboard-taller-comun')
            }
          ]
        );
      } else {
        Alert.alert('❌ Error de Registro', resultado.message || 'Error desconocido');
      }
    } catch (error) {
      console.error('💥 Error al registrar:', error);
      Alert.alert(
        '❌ Error de Conexión',
        'No se pudo conectar con el servidor. Verifica:\n\n• Que XAMPP esté ejecutándose\n• Que la API esté disponible\n• Tu conexión a internet\n\nError: ' + error.message
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#667eea" />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.logoText}>
            🔧 <Text style={styles.logoRepu}>Repu</Text><Text style={styles.logoMovil}>Movil</Text>
          </Text>
          <Text style={styles.subtitle}>Repuestos que llegan a tu taller, cuando los necesitas</Text>
        </View>

        {/* Características del Plan */}
        <View style={styles.featureHighlight}>
          <Text style={styles.featureTitle}>✨ Tu plan incluye:</Text>
          <View style={styles.featureList}>
            <Text style={styles.featureItem}>✅ Pedido de repuestos con changuito</Text>
            <Text style={styles.featureItem}>✅ Calificaciones de clientes</Text>
            <Text style={styles.featureItem}>✅ Búsqueda avanzada de repuestos</Text>
            <Text style={styles.featureItem}>✅ Notificaciones en tiempo real</Text>
          </View>
        </View>

        {/* Formulario */}
        <View style={styles.formContainer}>
          <Text style={styles.sectionTitle}>📝 Datos Básicos</Text>

          <TextInput
            style={styles.input}
            placeholder="Nombre Completo *"
            value={formData.nombre}
            onChangeText={(text) => handleInputChange('nombre', text)}
            placeholderTextColor="#999"
          />

          <TextInput
            style={styles.input}
            placeholder="Email *"
            value={formData.email}
            onChangeText={(text) => handleInputChange('email', text)}
            keyboardType="email-address"
            autoCapitalize="none"
            placeholderTextColor="#999"
          />

          <TextInput
            style={styles.input}
            placeholder="Teléfono *"
            value={formData.telefono}
            onChangeText={(text) => handleInputChange('telefono', text)}
            keyboardType="phone-pad"
            placeholderTextColor="#999"
          />

          <TextInput
            style={styles.input}
            placeholder="Contraseña *"
            value={formData.password}
            onChangeText={(text) => handleInputChange('password', text)}
            secureTextEntry
            placeholderTextColor="#999"
          />

          <Text style={styles.sectionTitle}>🔧 Datos del Taller</Text>

          <TextInput
            style={styles.input}
            placeholder="Nombre del Taller *"
            value={formData.username}
            onChangeText={(text) => handleInputChange('username', text)}
            placeholderTextColor="#999"
          />

          <TextInput
            style={styles.input}
            placeholder="Dirección del Taller"
            value={formData.direccion}
            onChangeText={(text) => handleInputChange('direccion', text)}
            placeholderTextColor="#999"
          />

          <TextInput
            style={styles.input}
            placeholder="Rubro Principal (ej: Mecánica General)"
            value={formData.rubro_principal}
            onChangeText={(text) => handleInputChange('rubro_principal', text)}
            placeholderTextColor="#999"
          />

          <TextInput
            style={styles.input}
            placeholder="Datos Fiscales (CUIT/DNI)"
            value={formData.datos_fiscales}
            onChangeText={(text) => handleInputChange('datos_fiscales', text)}
            placeholderTextColor="#999"
          />

          <TouchableOpacity
            style={[styles.btnRegistrar, loading && styles.btnDisabled]}
            onPress={registrarTaller}
            disabled={loading}
          >
            <Text style={styles.btnRegistrarText}>
              {loading ? '🔄 Creando cuenta...' : '🚀 Crear mi Cuenta RepuMovil'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.btnVolver}
            onPress={() => router.push('/seleccionar-plan')}
          >
            <Text style={styles.btnVolverText}>← Volver a Planes</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#667eea',
  },
  content: {
    flex: 1,
  },
  header: {
    alignItems: 'center',
    paddingVertical: 30,
    paddingHorizontal: 20,
  },
  logoText: {
    fontSize: 32,
    fontWeight: '900',
    color: 'white',
    marginBottom: 10,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 2, height: 2 },
    textShadowRadius: 4,
  },
  logoRepu: {
    color: '#FF6B35',
  },
  logoMovil: {
    color: '#FFE4B5',
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    lineHeight: 22,
  },
  featureHighlight: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    marginHorizontal: 20,
    borderRadius: 15,
    padding: 20,
    marginBottom: 20,
    borderLeftWidth: 4,
    borderLeftColor: '#4CAF50',
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: '#333',
    marginBottom: 10,
  },
  featureList: {
    gap: 5,
  },
  featureItem: {
    fontSize: 14,
    color: '#4CAF50',
    fontWeight: '600',
  },
  formContainer: {
    backgroundColor: 'white',
    marginHorizontal: 20,
    borderRadius: 25,
    padding: 25,
    marginBottom: 30,
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 5 },
    shadowOpacity: 0.3,
    shadowRadius: 10,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#333',
    marginBottom: 15,
    marginTop: 10,
  },
  input: {
    borderWidth: 2,
    borderColor: '#e9ecef',
    borderRadius: 12,
    padding: 15,
    fontSize: 16,
    marginBottom: 15,
    backgroundColor: '#fff',
  },
  btnRegistrar: {
    backgroundColor: '#FF6B35',
    borderRadius: 12,
    padding: 18,
    alignItems: 'center',
    marginTop: 20,
    elevation: 3,
    shadowColor: '#FF6B35',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  btnDisabled: {
    backgroundColor: '#ccc',
    elevation: 0,
    shadowOpacity: 0,
  },
  btnRegistrarText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '700',
  },
  btnVolver: {
    alignItems: 'center',
    marginTop: 15,
    padding: 10,
  },
  btnVolverText: {
    color: '#666',
    fontSize: 16,
    fontWeight: '600',
  },
});
