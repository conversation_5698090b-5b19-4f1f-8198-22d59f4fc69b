<?php
// Mostrar todos los errores para depuración
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Iniciar sesión
session_start();

// Verificar si el usuario está autenticado y es administrador
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: login.php');
    exit;
}

// Incluir archivos necesarios
require_once 'db_config.php';

// Variables para mensajes
$error = '';
$success = '';

// Procesar el formulario cuando se envía
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Obtener y sanitizar datos del formulario
    $username = filter_input(INPUT_POST, 'username', FILTER_SANITIZE_STRING);
    $email = filter_input(INPUT_POST, 'email', FILTER_SANITIZE_EMAIL);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    $role_id = (int)$_POST['role_id'];

    // Campos para talleres y proveedores
    $business_name = filter_input(INPUT_POST, 'business_name', FILTER_SANITIZE_STRING);
    $address = filter_input(INPUT_POST, 'address', FILTER_SANITIZE_STRING);
    $phone = filter_input(INPUT_POST, 'phone', FILTER_SANITIZE_STRING);
    $description = filter_input(INPUT_POST, 'description', FILTER_SANITIZE_STRING);
    $maps_url = filter_input(INPUT_POST, 'maps_url', FILTER_SANITIZE_URL);

    // Campos para mecánicos
    $mechanic_name = filter_input(INPUT_POST, 'mechanic_name', FILTER_SANITIZE_STRING);
    $specialties = filter_input(INPUT_POST, 'specialties', FILTER_SANITIZE_STRING);
    $experience = filter_input(INPUT_POST, 'experience', FILTER_SANITIZE_STRING);

    // Campos para personas
    $first_name = filter_input(INPUT_POST, 'first_name', FILTER_SANITIZE_STRING);
    $last_name = filter_input(INPUT_POST, 'last_name', FILTER_SANITIZE_STRING);

    // Validar campos
    if (empty($username) || empty($email) || empty($password) || empty($confirm_password) || empty($role_id)) {
        $error = 'Por favor, complete todos los campos obligatorios.';
    } else if ($password !== $confirm_password) {
        $error = 'Las contraseñas no coinciden.';
    } else if (strlen($password) < 6) {
        $error = 'La contraseña debe tener al menos 6 caracteres.';
    } else if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'Por favor, ingrese un correo electrónico válido.';
    } else {
        try {
            // Conectar a la base de datos
            $conn = connectDB();

            // Verificar si el nombre de usuario ya existe
            $stmt = $conn->prepare("SELECT COUNT(*) FROM users WHERE username = :username");
            $stmt->execute(['username' => $username]);
            if ($stmt->fetchColumn() > 0) {
                $error = 'Este nombre de usuario ya está registrado.';
            } else {
                // Verificar si el correo ya existe
                $stmt = $conn->prepare("SELECT COUNT(*) FROM users WHERE email = :email");
                $stmt->execute(['email' => $email]);
                if ($stmt->fetchColumn() > 0) {
                    $error = 'Este correo electrónico ya está registrado.';
                } else {
                    // Iniciar transacción
                    $conn->beginTransaction();

                    try {
                        // Crear usuario
                        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                        $stmt = $conn->prepare("INSERT INTO users (username, email, password, role_id, status, created_at) VALUES (:username, :email, :password, :role_id, 'active', NOW())");
                        $stmt->execute([
                            'username' => $username,
                            'email' => $email,
                            'password' => $hashedPassword,
                            'role_id' => $role_id
                        ]);

                        $userId = $conn->lastInsertId();

                        // Crear perfil según tipo de usuario
                        if ($role_id == 2) { // Taller
                            $stmt = $conn->prepare("INSERT INTO workshops (user_id, name, address, phone, description, maps_url, created_at) VALUES (:user_id, :name, :address, :phone, :description, :maps_url, NOW())");
                            $stmt->execute([
                                'user_id' => $userId,
                                'name' => $business_name ?: $username,
                                'address' => $address,
                                'phone' => $phone,
                                'description' => $description,
                                'maps_url' => $maps_url
                            ]);
                        } else if ($role_id == 3) { // Mecánico independiente
                            $stmt = $conn->prepare("INSERT INTO mechanics (user_id, name, specialties, experience, address, phone, description, maps_url, created_at) VALUES (:user_id, :name, :specialties, :experience, :address, :phone, :description, :maps_url, NOW())");
                            $stmt->execute([
                                'user_id' => $userId,
                                'name' => $mechanic_name ?: $username,
                                'specialties' => $specialties,
                                'experience' => $experience,
                                'address' => $address,
                                'phone' => $phone,
                                'description' => $description,
                                'maps_url' => $maps_url
                            ]);
                        } else if ($role_id == 4) { // Usuario regular
                            $stmt = $conn->prepare("INSERT INTO persons (user_id, first_name, last_name, address, phone, created_at) VALUES (:user_id, :first_name, :last_name, :address, :phone, NOW())");
                            $stmt->execute([
                                'user_id' => $userId,
                                'first_name' => $first_name,
                                'last_name' => $last_name,
                                'address' => $address,
                                'phone' => $phone
                            ]);
                        } else if ($role_id == 5) { // Proveedor
                            $stmt = $conn->prepare("INSERT INTO suppliers (user_id, name, address, phone, description, maps_url, created_at) VALUES (:user_id, :name, :address, :phone, :description, :maps_url, NOW())");
                            $stmt->execute([
                                'user_id' => $userId,
                                'name' => $business_name ?: $username,
                                'address' => $address,
                                'phone' => $phone,
                                'description' => $description,
                                'maps_url' => $maps_url
                            ]);
                        }

                        $conn->commit();
                        $success = 'Usuario creado exitosamente. ID: ' . $userId;

                        // Limpiar el formulario después de un registro exitoso
                        $username = $email = $business_name = $address = $phone = $description = $maps_url = '';
                        $mechanic_name = $specialties = $experience = '';
                        $first_name = $last_name = '';
                    } catch (Exception $e) {
                        $conn->rollBack();
                        $error = 'Error al crear el usuario: ' . $e->getMessage();
                    }
                }
            }
        } catch (PDOException $e) {
            $error = 'Error de conexión: ' . $e->getMessage();
        }
    }
}

// Obtener roles para el formulario
try {
    $conn = connectDB();
    $roles = $conn->query("SELECT id, name, description FROM roles ORDER BY id")->fetchAll();
} catch (PDOException $e) {
    $error = 'Error al cargar roles: ' . $e->getMessage();
    $roles = [];
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agregar Usuario - Repumóvil</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #343a40;
            color: white;
            padding-top: 20px;
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.75);
        }
        .sidebar .nav-link:hover {
            color: rgba(255, 255, 255, 1);
        }
        .sidebar .nav-link.active {
            color: white;
            font-weight: bold;
        }
        .main-content {
            padding: 20px;
        }
        .maps-button {
            margin-top: -15px;
            margin-bottom: 15px;
        }
        .logo-container {
            text-align: center;
            margin-bottom: 20px;
        }
        .logo-container img {
            max-width: 80%;
            height: auto;
        }
        .admin-panel-text {
            text-align: center;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.6);
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block bg-dark sidebar collapse">
                <div class="logo-container">
                    <img src="img/logo.png" alt="Repumóvil Logo">
                    <div class="admin-panel-text">Panel de Administración</div>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="admin_dashboard.php">
                            <i class="fas fa-tachometer-alt mr-2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="admin_users.php">
                            <i class="fas fa-users mr-2"></i> Usuarios
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="admin_workshops.php">
                            <i class="fas fa-tools mr-2"></i> Talleres
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="admin_suppliers.php">
                            <i class="fas fa-truck mr-2"></i> Proveedores
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="admin_settings.php">
                            <i class="fas fa-cog mr-2"></i> Configuración
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="logout.php">
                            <i class="fas fa-sign-out-alt mr-2"></i> Cerrar Sesión
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Contenido principal -->
            <main class="col-md-9 ml-sm-auto col-lg-10 px-md-4 main-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Agregar Nuevo Usuario</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="admin_users.php" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Volver a Usuarios
                        </a>
                    </div>
                </div>

                <?php if (!empty($success)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $success; ?>
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($error)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $error; ?>
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                <?php endif; ?>

                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Información del Usuario</h5>
                    </div>
                    <div class="card-body">
                        <form method="post" action="admin_add_user.php">
                            <div class="form-group">
                                <label for="username">Nombre de Usuario *</label>
                                <input type="text" class="form-control" id="username" name="username" value="<?php echo htmlspecialchars($username ?? ''); ?>" required>
                                <small class="form-text text-muted">El nombre de usuario debe ser único.</small>
                            </div>

                            <div class="form-group">
                                <label for="email">Correo Electrónico *</label>
                                <input type="email" class="form-control" id="email" name="email" value="<?php echo htmlspecialchars($email ?? ''); ?>" required>
                                <small class="form-text text-muted">El correo electrónico debe ser único.</small>
                            </div>

                            <div class="form-group">
                                <label for="password">Contraseña *</label>
                                <input type="password" class="form-control" id="password" name="password" required>
                                <small class="form-text text-muted">La contraseña debe tener al menos 6 caracteres.</small>
                            </div>

                            <div class="form-group">
                                <label for="confirm_password">Confirmar Contraseña *</label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            </div>

                            <!-- Selector de roles actualizado -->
                            <div class="form-group">
                                <label for="role_id">Tipo de cuenta</label>
                                <select class="form-control" id="role_id" name="role_id" required>
                                    <option value="">Seleccione un rol</option>
                                    <option value="2">Taller mecánico</option>
                                    <option value="3">Mecánico independiente</option>
                                    <option value="4">Usuario regular</option>
                                    <option value="5">Proveedor de repuestos</option>
                                </select>
                            </div>

                            <!-- Campos dinámicos según el rol seleccionado -->
                            <div id="workshop_fields" style="display: none;">
                                <div class="form-group">
                                    <label for="business_name">Nombre del taller</label>
                                    <input type="text" class="form-control" id="business_name" name="business_name">
                                </div>
                                <div class="form-group">
                                    <label for="address">Dirección</label>
                                    <input type="text" class="form-control" id="address" name="address">
                                </div>
                                <div class="form-group">
                                    <label for="phone">Teléfono</label>
                                    <input type="text" class="form-control" id="phone" name="phone">
                                </div>
                                <div class="form-group">
                                    <label for="description">Descripción</label>
                                    <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                                </div>
                                <div class="form-group">
                                    <label for="maps_url">URL de Google Maps</label>
                                    <input type="text" class="form-control" id="maps_url" name="maps_url">
                                    <button type="button" class="btn btn-sm btn-info maps-button" onclick="openGoogleMaps()">
                                        <i class="fas fa-map-marker-alt"></i> Abrir Google Maps
                                    </button>
                                </div>
                            </div>

                            <div id="mechanic_fields" style="display: none;">
                                <div class="form-group">
                                    <label for="mechanic_name">Nombre completo</label>
                                    <input type="text" class="form-control" id="mechanic_name" name="mechanic_name">
                                </div>
                                <div class="form-group">
                                    <label for="specialties">Especialidades</label>
                                    <input type="text" class="form-control" id="specialties" name="specialties" placeholder="Ej: Frenos, Suspensión, Motor">
                                </div>
                                <div class="form-group">
                                    <label for="experience">Experiencia</label>
                                    <input type="text" class="form-control" id="experience" name="experience" placeholder="Ej: 5 años">
                                </div>
                                <div class="form-group">
                                    <label for="mechanic_address">Dirección</label>
                                    <input type="text" class="form-control" id="mechanic_address" name="address">
                                </div>
                                <div class="form-group">
                                    <label for="mechanic_phone">Teléfono</label>
                                    <input type="text" class="form-control" id="mechanic_phone" name="phone">
                                </div>
                                <div class="form-group">
                                    <label for="mechanic_description">Descripción</label>
                                    <textarea class="form-control" id="mechanic_description" name="description" rows="3"></textarea>
                                </div>
                                <div class="form-group">
                                    <label for="mechanic_maps_url">URL de Google Maps</label>
                                    <input type="text" class="form-control" id="mechanic_maps_url" name="maps_url">
                                    <button type="button" class="btn btn-sm btn-info maps-button" onclick="openGoogleMaps()">
                                        <i class="fas fa-map-marker-alt"></i> Abrir Google Maps
                                    </button>
                                </div>
                            </div>

                            <div id="person_fields" style="display: none;">
                                <div class="form-group">
                                    <label for="first_name">Nombre</label>
                                    <input type="text" class="form-control" id="first_name" name="first_name">
                                </div>
                                <div class="form-group">
                                    <label for="last_name">Apellido</label>
                                    <input type="text" class="form-control" id="last_name" name="last_name">
                                </div>
                            </div>

                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Guardar Usuario
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script>
        // Mostrar/ocultar campos según el rol seleccionado
        document.getElementById('role_id').addEventListener('change', function() {
            var roleId = this.value;
            var workshopFields = document.getElementById('workshop_fields');
            var mechanicFields = document.getElementById('mechanic_fields');
            var personFields = document.getElementById('person_fields');

            // Ocultar todos los campos dinámicos
            workshopFields.style.display = 'none';
            mechanicFields.style.display = 'none';
            personFields.style.display = 'none';

            // Mostrar campos según el rol seleccionado
            if (roleId == '2') {
                workshopFields.style.display = 'block';
            } else if (roleId == '3') {
                mechanicFields.style.display = 'block';
            } else if (roleId == '4') {
                personFields.style.display = 'block';
            }
        });

        // Función para abrir Google Maps
        function openGoogleMaps() {
            window.open('https://www.google.com/maps', '_blank');
        }
    </script>
</body>
</html>

import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Platform
} from 'react-native';
import { Picker } from '@react-native-picker/picker';
import { useNavigation } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { API_URL } from '../config/api';

const RegisterScreen = () => {
  const navigation = useNavigation();
  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [roleId, setRoleId] = useState('');

  // Campos para talleres y proveedores
  const [businessName, setBusinessName] = useState('');
  const [address, setAddress] = useState('');
  const [phone, setPhone] = useState('');
  const [description, setDescription] = useState('');
  const [mapsUrl, setMapsUrl] = useState('');

  // Campos para mecánicos
  const [mechanicName, setMechanicName] = useState('');
  const [specialties, setSpecialties] = useState('');
  const [experience, setExperience] = useState('');

  // Campos para personas
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const validateForm = () => {
    if (!username || !email || !password || !confirmPassword || !roleId) {
      setError('Por favor, complete todos los campos obligatorios.');
      return false;
    }

    if (password !== confirmPassword) {
      setError('Las contraseñas no coinciden.');
      return false;
    }

    if (password.length < 6) {
      setError('La contraseña debe tener al menos 6 caracteres.');
      return false;
    }

    // Validar email con expresión regular
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError('Por favor, ingrese un correo electrónico válido.');
      return false;
    }

    // Validar campos específicos según el rol
    if (roleId === '2') { // Taller
      if (!businessName) {
        setError('Por favor, ingrese el nombre del taller.');
        return false;
      }
    } else if (roleId === '3') { // Mecánico
      if (!mechanicName) {
        setError('Por favor, ingrese el nombre del mecánico.');
        return false;
      }
    } else if (roleId === '4') { // Usuario regular
      if (!firstName || !lastName) {
        setError('Por favor, ingrese nombre y apellido.');
        return false;
      }
    } else if (roleId === '5') { // Proveedor
      if (!businessName) {
        setError('Por favor, ingrese el nombre de la empresa.');
        return false;
      }
    }

    return true;
  };

  const handleRegister = async () => {
    if (!validateForm()) return;

    setIsLoading(true);
    setError('');

    try {
      const formData = {
        username,
        email,
        password,
        confirm_password: confirmPassword,
        role_id: roleId,
        business_name: businessName,



