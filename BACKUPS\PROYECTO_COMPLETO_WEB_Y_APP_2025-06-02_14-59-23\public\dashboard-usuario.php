<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - RepuMovil</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .navbar-custom {
            background: linear-gradient(135deg, #FF6B35 0%, #FFA500 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-size: 24px;
            font-weight: bold;
            color: white !important;
        }

        .logo-repu {
            color: white;
        }

        .logo-movil {
            color: #FFE4B5;
        }

        .welcome-section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .welcome-section::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 107, 53, 0.05), transparent);
            animation: shine 3s infinite;
        }

        @keyframes shine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .welcome-title {
            font-size: 28px;
            color: #333;
            margin-bottom: 15px;
            position: relative;
            z-index: 1;
        }

        .welcome-subtitle {
            font-size: 18px;
            color: #666;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }

        .neeño-text {
            background: linear-gradient(135deg, #FF6B35, #FFA500, #FF1493, #00CED1);
            background-size: 300% 300%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 900;
            font-size: 24px;
            text-transform: uppercase;
            letter-spacing: 2px;
            animation: rainbow 2s ease-in-out infinite;
            text-shadow: 0 0 20px rgba(255, 107, 53, 0.5);
        }

        @keyframes rainbow {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .dashboard-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: none;
            position: relative;
            overflow: hidden;
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .dashboard-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #FF6B35 0%, #FFA500 100%);
        }

        .card-icon {
            font-size: 48px;
            margin-bottom: 15px;
            display: block;
        }

        .icon-search { color: #4CAF50; }
        .icon-requests { color: #2196F3; }
        .icon-favorites { color: #FF9800; }
        .icon-contact { color: #9C27B0; }
        .icon-history { color: #607D8B; }
        .icon-shop { color: #FF6B35; }

        .card-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .card-description {
            color: #666;
            font-size: 14px;
            margin-bottom: 20px;
        }

        .btn-card {
            background: linear-gradient(135deg, #FF6B35 0%, #FFA500 100%);
            border: none;
            border-radius: 10px;
            padding: 10px 20px;
            color: white;
            font-weight: 600;
            width: 100%;
            transition: all 0.3s ease;
        }

        .btn-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 107, 53, 0.4);
            color: white;
        }

        .special-card {
            background: linear-gradient(135deg, #FF6B35 0%, #FFA500 100%);
            color: white;
        }

        .special-card .card-title,
        .special-card .card-description {
            color: white;
        }

        .special-card .btn-card {
            background: white;
            color: #FF6B35;
        }

        .special-card .btn-card:hover {
            background: #f8f9fa;
            color: #FF6B35;
        }

        .stats-row {
            display: flex;
            justify-content: space-around;
            margin: 30px 0;
            flex-wrap: wrap;
            gap: 15px;
        }

        .stat-item {
            text-align: center;
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            flex: 1;
            min-width: 150px;
        }

        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #FF6B35;
            display: block;
        }

        .stat-label {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }

        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }

            .welcome-title {
                font-size: 24px;
            }

            .welcome-subtitle {
                font-size: 16px;
            }

            .neeño-text {
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-wrench me-2"></i>
                <span class="logo-repu">Repu</span><span class="logo-movil">Movil</span>
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="#" onclick="logout()">
                    <i class="fas fa-sign-out-alt me-1"></i>Cerrar Sesión
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Mensaje de Bienvenida -->
        <div class="welcome-section">
            <h1 class="welcome-title">¡Bienvenido a RepuMovil!</h1>
            <p class="welcome-subtitle">
                DONDE ENCONTRÁS TODOS LO QUE NECESITÁS PARA TU AUTO O MOTO
                <span class="neeño-text">NEEÑO</span>
            </p>
        </div>

        <!-- Estadísticas Rápidas -->
        <div class="stats-row">
            <div class="stat-item">
                <span class="stat-number">0</span>
                <div class="stat-label">Servicios Solicitados</div>
            </div>
            <div class="stat-item">
                <span class="stat-number">0</span>
                <div class="stat-label">Talleres Favoritos</div>
            </div>
            <div class="stat-item">
                <span class="stat-number">0</span>
                <div class="stat-label">Compras Realizadas</div>
            </div>
        </div>

        <!-- Dashboard Principal -->
        <div class="dashboard-grid">
            <!-- Buscar Talleres -->
            <div class="dashboard-card">
                <i class="fas fa-search card-icon icon-search"></i>
                <h3 class="card-title">🔍 Buscar Talleres Cercanos</h3>
                <p class="card-description">
                    Encuentra talleres mecánicos cerca de tu ubicación con las mejores calificaciones.
                </p>
                <button class="btn btn-card" onclick="buscarTalleres()">
                    Buscar Ahora
                </button>
            </div>

            <!-- Mis Solicitudes -->
            <div class="dashboard-card">
                <i class="fas fa-clipboard-list card-icon icon-requests"></i>
                <h3 class="card-title">📱 Mis Solicitudes de Servicio</h3>
                <p class="card-description">
                    Revisa el estado de tus solicitudes de reparación y mantenimiento.
                </p>
                <button class="btn btn-card" onclick="verSolicitudes()">
                    Ver Solicitudes
                </button>
            </div>

            <!-- Talleres Favoritos -->
            <div class="dashboard-card">
                <i class="fas fa-heart card-icon icon-favorites"></i>
                <h3 class="card-title">⭐ Talleres Favoritos</h3>
                <p class="card-description">
                    Accede rápidamente a tus talleres de confianza guardados.
                </p>
                <button class="btn btn-card" onclick="verFavoritos()">
                    Ver Favoritos
                </button>
            </div>

            <!-- Contactar Mecánicos -->
            <div class="dashboard-card">
                <i class="fas fa-phone card-icon icon-contact"></i>
                <h3 class="card-title">📞 Contactar Mecánicos</h3>
                <p class="card-description">
                    Conecta directamente con mecánicos independientes disponibles.
                </p>
                <button class="btn btn-card" onclick="contactarMecanicos()">
                    Contactar
                </button>
            </div>

            <!-- Historial -->
            <div class="dashboard-card">
                <i class="fas fa-history card-icon icon-history"></i>
                <h3 class="card-title">📊 Historial de Servicios</h3>
                <p class="card-description">
                    Consulta el historial completo de todos tus servicios realizados.
                </p>
                <button class="btn btn-card" onclick="verHistorial()">
                    Ver Historial
                </button>
            </div>

            <!-- Comprar Repuestos -->
            <div class="dashboard-card special-card">
                <i class="fas fa-tools card-icon icon-shop"></i>
                <h3 class="card-title">🔧 Compra tu Repuesto</h3>
                <p class="card-description">
                    Encuentra y compra repuestos originales y alternativos para tu vehículo.
                </p>
                <button class="btn btn-card" onclick="comprarRepuestos()">
                    Ir a Tienda
                </button>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Funciones del Dashboard
        function buscarTalleres() {
            alert('🔍 Función: Buscar Talleres Cercanos\n\nEsta función abrirá un mapa con talleres cercanos a tu ubicación, mostrando:\n• Distancia\n• Calificaciones\n• Especialidades\n• Precios estimados');
        }

        function verSolicitudes() {
            alert('📱 Función: Mis Solicitudes de Servicio\n\nAquí podrás ver:\n• Solicitudes pendientes\n• Estado de reparaciones\n• Historial de servicios\n• Comunicación con talleres');
        }

        function verFavoritos() {
            alert('⭐ Función: Talleres Favoritos\n\nTus talleres de confianza:\n• Acceso rápido\n• Contacto directo\n• Historial de servicios\n• Promociones especiales');
        }

        function contactarMecanicos() {
            alert('📞 Función: Contactar Mecánicos\n\nConecta con mecánicos independientes:\n• Servicios a domicilio\n• Especialidades específicas\n• Presupuestos inmediatos\n• Chat en tiempo real');
        }

        function verHistorial() {
            alert('📊 Función: Historial de Servicios\n\nTu historial completo:\n• Servicios realizados\n• Gastos por período\n• Mantenimientos programados\n• Reportes detallados');
        }

        function comprarRepuestos() {
            alert('🔧 Función: Compra tu Repuesto\n\n¡La tienda de repuestos más completa!\n• Repuestos originales\n• Alternativas de calidad\n• Entrega a domicilio\n• Garantía incluida\n\n¡NEEÑO, aquí encontrás todo!');
        }

        function logout() {
            if (confirm('¿Estás seguro que quieres cerrar sesión?')) {
                window.location.href = 'login-dinamico.php';
            }
        }

        // Animación de números en las estadísticas
        function animateNumbers() {
            const numbers = document.querySelectorAll('.stat-number');
            numbers.forEach(number => {
                const finalValue = parseInt(number.textContent);
                let currentValue = 0;
                const increment = finalValue / 20;

                const timer = setInterval(() => {
                    currentValue += increment;
                    if (currentValue >= finalValue) {
                        currentValue = finalValue;
                        clearInterval(timer);
                    }
                    number.textContent = Math.floor(currentValue);
                }, 50);
            });
        }

        // Ejecutar animaciones al cargar la página
        document.addEventListener('DOMContentLoaded', function() {
            // Animación de entrada para las cards
            const cards = document.querySelectorAll('.dashboard-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });

            // Animar números después de un delay
            setTimeout(animateNumbers, 1000);
        });

        // Efecto de partículas en el fondo (opcional)
        function createParticle() {
            const particle = document.createElement('div');
            particle.style.position = 'fixed';
            particle.style.width = '4px';
            particle.style.height = '4px';
            particle.style.background = '#FF6B35';
            particle.style.borderRadius = '50%';
            particle.style.pointerEvents = 'none';
            particle.style.opacity = '0.6';
            particle.style.left = Math.random() * window.innerWidth + 'px';
            particle.style.top = window.innerHeight + 'px';
            particle.style.zIndex = '1';

            document.body.appendChild(particle);

            const animation = particle.animate([
                { transform: 'translateY(0px)', opacity: 0.6 },
                { transform: `translateY(-${window.innerHeight + 100}px)`, opacity: 0 }
            ], {
                duration: 3000 + Math.random() * 2000,
                easing: 'linear'
            });

            animation.onfinish = () => particle.remove();
        }

        // Crear partículas ocasionalmente
        setInterval(createParticle, 2000);
    </script>
</body>
</html>
