-- PASO 2: <PERSON><PERSON><PERSON> para tracking GPS en tiempo real - SIMPLIFICADO
-- Sistema completo de ubicaciones para deliveries

-- Tabla para ubicaciones actuales de deliveries (solo la más reciente)
CREATE TABLE IF NOT EXISTS delivery_current_location (
    delivery_id INT PRIMARY KEY,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    accuracy FLOAT NULL, -- Precisión en metros
    speed FLOAT NULL, -- Velocidad en m/s
    heading FLOAT NULL, -- Dirección en grados (0-360)
    timestamp TIMESTAMP NOT NULL,
    ultima_actualizacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_ubicacion (latitude, longitude),
    INDEX idx_timestamp (timestamp),
    INDEX idx_ultima_actualizacion (ultima_actualizacion)
);

-- Tabla para historial completo de ubicaciones
CREATE TABLE IF NOT EXISTS delivery_locations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    delivery_id INT NOT NULL,
    pedido_id INT NULL, -- Pedido asociado (si aplica)
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    accuracy FLOAT NULL,
    speed FLOAT NULL,
    heading FLOAT NULL,
    timestamp TIMESTAMP NOT NULL, -- Timestamp del GPS
    fecha_registro TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- Timestamp del servidor
    INDEX idx_delivery (delivery_id),
    INDEX idx_pedido (pedido_id),
    INDEX idx_ubicacion (latitude, longitude),
    INDEX idx_timestamp (timestamp),
    INDEX idx_fecha_registro (fecha_registro),
    INDEX idx_delivery_fecha (delivery_id, fecha_registro)
);

-- Tabla para sesiones de tracking (cuando inicia/termina el tracking)
CREATE TABLE IF NOT EXISTS tracking_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    delivery_id INT NOT NULL,
    pedido_id INT NULL,
    fecha_inicio TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    fecha_fin TIMESTAMP NULL,
    ubicacion_inicio_lat DECIMAL(10, 8),
    ubicacion_inicio_lng DECIMAL(11, 8),
    ubicacion_fin_lat DECIMAL(10, 8) NULL,
    ubicacion_fin_lng DECIMAL(11, 8) NULL,
    distancia_total FLOAT NULL, -- En kilómetros
    tiempo_total INT NULL, -- En minutos
    velocidad_promedio FLOAT NULL, -- En km/h
    puntos_tracking INT DEFAULT 0, -- Cantidad de puntos registrados
    activa BOOLEAN DEFAULT TRUE,
    INDEX idx_delivery (delivery_id),
    INDEX idx_pedido (pedido_id),
    INDEX idx_fecha_inicio (fecha_inicio),
    INDEX idx_activa (activa)
);

-- Tabla para métricas de tracking por delivery
CREATE TABLE IF NOT EXISTS delivery_tracking_stats (
    delivery_id INT PRIMARY KEY,
    total_sesiones INT DEFAULT 0,
    total_distancia FLOAT DEFAULT 0, -- En kilómetros
    total_tiempo INT DEFAULT 0, -- En minutos
    velocidad_promedio FLOAT DEFAULT 0, -- En km/h
    precision_promedio FLOAT DEFAULT 0, -- En metros
    ultima_sesion TIMESTAMP NULL,
    fecha_actualizacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- PASO 2: Crear tabla simple de pedidos para testing si no existe
CREATE TABLE IF NOT EXISTS pedidos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    cliente_nombre VARCHAR(255),
    direccion_entrega TEXT,
    estado VARCHAR(50) DEFAULT 'nuevo',
    total DECIMAL(10,2),
    latitude DECIMAL(10, 8) NULL,
    longitude DECIMAL(11, 8) NULL,
    direccion_completa TEXT NULL,
    eta_minutos INT NULL,
    distancia_km FLOAT NULL,
    fecha_creacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_estado (estado),
    INDEX idx_ubicacion_destino (latitude, longitude),
    INDEX idx_eta (eta_minutos)
);

-- PASO 2: La tabla users ya existe, no necesitamos crearla

-- Vista simplificada para tracking
CREATE OR REPLACE VIEW vista_tracking_completo AS
SELECT
    dcl.delivery_id,
    COALESCE(u.nombre, CONCAT('Delivery ', dcl.delivery_id)) as delivery_nombre,
    COALESCE(u.telefono, 'N/A') as delivery_telefono,
    dcl.latitude,
    dcl.longitude,
    dcl.accuracy,
    dcl.speed,
    dcl.heading,
    dcl.timestamp as ubicacion_timestamp,
    dcl.ultima_actualizacion,
    TIMESTAMPDIFF(MINUTE, dcl.ultima_actualizacion, NOW()) as minutos_inactivo,
    COALESCE(dts.total_sesiones, 0) as total_sesiones,
    COALESCE(dts.total_distancia, 0) as total_distancia,
    COALESCE(dts.velocidad_promedio, 0) as velocidad_promedio,
    CASE
        WHEN TIMESTAMPDIFF(MINUTE, dcl.ultima_actualizacion, NOW()) <= 5 THEN 'activo'
        WHEN TIMESTAMPDIFF(MINUTE, dcl.ultima_actualizacion, NOW()) <= 15 THEN 'inactivo'
        ELSE 'desconectado'
    END as estado_tracking
FROM delivery_current_location dcl
LEFT JOIN users u ON dcl.delivery_id = u.id
LEFT JOIN delivery_tracking_stats dts ON dcl.delivery_id = dts.delivery_id;

-- Procedimiento para limpiar datos antiguos de tracking
DELIMITER //
CREATE OR REPLACE PROCEDURE LimpiarTrackingAntiguo()
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- Eliminar ubicaciones más antiguas de 30 días
    DELETE FROM delivery_locations 
    WHERE fecha_registro < DATE_SUB(NOW(), INTERVAL 30 DAY);
    
    -- Cerrar sesiones activas más antiguas de 24 horas
    UPDATE tracking_sessions 
    SET fecha_fin = DATE_ADD(fecha_inicio, INTERVAL 8 HOUR),
        activa = FALSE
    WHERE activa = TRUE 
        AND fecha_inicio < DATE_SUB(NOW(), INTERVAL 24 HOUR);
    
    COMMIT;
    
    SELECT 'Limpieza de tracking completada' as resultado;
END //
DELIMITER ;

-- Procedimiento para calcular estadísticas de tracking
DELIMITER //
CREATE OR REPLACE PROCEDURE CalcularEstadisticasTracking(IN p_delivery_id INT)
BEGIN
    DECLARE v_total_sesiones INT DEFAULT 0;
    DECLARE v_total_distancia FLOAT DEFAULT 0;
    DECLARE v_total_tiempo INT DEFAULT 0;
    DECLARE v_velocidad_promedio FLOAT DEFAULT 0;
    DECLARE v_precision_promedio FLOAT DEFAULT 0;
    DECLARE v_ultima_sesion TIMESTAMP;
    
    -- Calcular estadísticas
    SELECT 
        COUNT(*),
        COALESCE(SUM(distancia_total), 0),
        COALESCE(SUM(tiempo_total), 0),
        COALESCE(AVG(velocidad_promedio), 0),
        MAX(fecha_inicio)
    INTO 
        v_total_sesiones,
        v_total_distancia,
        v_total_tiempo,
        v_velocidad_promedio,
        v_ultima_sesion
    FROM tracking_sessions 
    WHERE delivery_id = p_delivery_id;
    
    -- Calcular precisión promedio de las últimas 100 ubicaciones
    SELECT COALESCE(AVG(accuracy), 0)
    INTO v_precision_promedio
    FROM delivery_locations 
    WHERE delivery_id = p_delivery_id 
        AND accuracy IS NOT NULL
    ORDER BY fecha_registro DESC 
    LIMIT 100;
    
    -- Insertar o actualizar estadísticas
    INSERT INTO delivery_tracking_stats (
        delivery_id, total_sesiones, total_distancia, total_tiempo,
        velocidad_promedio, precision_promedio, ultima_sesion
    ) VALUES (
        p_delivery_id, v_total_sesiones, v_total_distancia, v_total_tiempo,
        v_velocidad_promedio, v_precision_promedio, v_ultima_sesion
    ) ON DUPLICATE KEY UPDATE
        total_sesiones = VALUES(total_sesiones),
        total_distancia = VALUES(total_distancia),
        total_tiempo = VALUES(total_tiempo),
        velocidad_promedio = VALUES(velocidad_promedio),
        precision_promedio = VALUES(precision_promedio),
        ultima_sesion = VALUES(ultima_sesion);
        
    SELECT 'Estadísticas actualizadas correctamente' as resultado;
END //
DELIMITER ;

-- Trigger para actualizar estadísticas cuando se cierra una sesión
DELIMITER //
CREATE OR REPLACE TRIGGER actualizar_stats_tracking
AFTER UPDATE ON tracking_sessions
FOR EACH ROW
BEGIN
    IF NEW.activa = FALSE AND OLD.activa = TRUE THEN
        CALL CalcularEstadisticasTracking(NEW.delivery_id);
    END IF;
END //
DELIMITER ;

-- PASO 2: Insertar datos de ejemplo para testing
-- Verificar si la tabla users ya existe y tiene datos
INSERT IGNORE INTO users (id, nombre, email, telefono, password, user_type) VALUES
(1, 'Juan Pérez', '<EMAIL>', '+54 ************', 'password123', 'usuario_regular'),
(2, 'María González', '<EMAIL>', '+54 ************', 'password123', 'usuario_regular'),
(3, 'Carlos Rodríguez', '<EMAIL>', '+54 ************', 'password123', 'usuario_regular'),
(4, 'Ana López', '<EMAIL>', '+54 ************', 'password123', 'usuario_regular')
ON DUPLICATE KEY UPDATE
    nombre = VALUES(nombre),
    telefono = VALUES(telefono);

INSERT IGNORE INTO pedidos (id, cliente_nombre, direccion_entrega, estado, total, latitude, longitude) VALUES
(1, 'Taller Central', 'Av. Libertador 1234', 'en_camino', 1500.00, -31.5400, -68.5400),
(2, 'AutoService Plus', 'Ruta 40 Km 15', 'asignado', 2200.00, -31.5450, -68.5450),
(3, 'Mecánico López', 'Calle Rivadavia 567', 'nuevo', 850.00, -31.5350, -68.5350);

INSERT IGNORE INTO delivery_current_location (delivery_id, latitude, longitude, accuracy, speed, heading, timestamp) VALUES
(1, -31.5375, -68.5364, 5.0, 25.5, 45.0, NOW()),
(2, -31.5385, -68.5374, 8.0, 30.2, 90.0, NOW()),
(3, -31.5395, -68.5384, 3.0, 15.8, 180.0, NOW());

INSERT IGNORE INTO tracking_sessions (delivery_id, pedido_id, ubicacion_inicio_lat, ubicacion_inicio_lng, activa) VALUES
(1, 1, -31.5375, -68.5364, TRUE),
(2, 2, -31.5385, -68.5374, TRUE),
(3, NULL, -31.5395, -68.5384, FALSE);

INSERT IGNORE INTO delivery_locations (delivery_id, pedido_id, latitude, longitude, accuracy, speed, heading, timestamp) VALUES
(1, 1, -31.5375, -68.5364, 5.0, 25.5, 45.0, NOW()),
(1, 1, -31.5376, -68.5365, 4.0, 28.2, 50.0, DATE_SUB(NOW(), INTERVAL 1 MINUTE)),
(1, 1, -31.5377, -68.5366, 6.0, 22.1, 55.0, DATE_SUB(NOW(), INTERVAL 2 MINUTE)),
(2, 2, -31.5385, -68.5374, 8.0, 30.2, 90.0, NOW()),
(2, 2, -31.5386, -68.5375, 7.0, 32.5, 95.0, DATE_SUB(NOW(), INTERVAL 1 MINUTE)),
(3, NULL, -31.5395, -68.5384, 3.0, 15.8, 180.0, NOW());

INSERT IGNORE INTO delivery_tracking_stats (delivery_id, total_sesiones, total_distancia, velocidad_promedio) VALUES
(1, 5, 25.5, 28.5),
(2, 3, 18.2, 32.1),
(3, 2, 12.8, 22.3);

-- PASO 3: Tablas para sistema de pedidos y aceptación/rechazo
-- Tabla para clientes
CREATE TABLE IF NOT EXISTS clientes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nombre VARCHAR(255) NOT NULL,
    telefono VARCHAR(20),
    email VARCHAR(255),
    direccion TEXT,
    fecha_registro TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    activo BOOLEAN DEFAULT TRUE,
    INDEX idx_telefono (telefono),
    INDEX idx_email (email)
);

-- Tabla para repuestos
CREATE TABLE IF NOT EXISTS repuestos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nombre VARCHAR(255) NOT NULL,
    codigo VARCHAR(100),
    categoria VARCHAR(100),
    precio DECIMAL(10,2),
    stock INT DEFAULT 0,
    descripcion TEXT,
    activo BOOLEAN DEFAULT TRUE,
    fecha_creacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_codigo (codigo),
    INDEX idx_categoria (categoria),
    INDEX idx_activo (activo)
);

-- Actualizar tabla de pedidos para el sistema completo
ALTER TABLE pedidos
ADD COLUMN IF NOT EXISTS cliente_id INT NULL,
ADD COLUMN IF NOT EXISTS delivery_id INT NULL,
ADD COLUMN IF NOT EXISTS prioridad ENUM('normal', 'alta', 'urgente') DEFAULT 'normal',
ADD COLUMN IF NOT EXISTS fecha_asignacion TIMESTAMP NULL,
ADD COLUMN IF NOT EXISTS fecha_completado TIMESTAMP NULL,
ADD COLUMN IF NOT EXISTS observaciones TEXT NULL,
ADD COLUMN IF NOT EXISTS rechazos_count INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS ganancia_delivery DECIMAL(10,2) NULL,
ADD COLUMN IF NOT EXISTS tiempo_estimado INT NULL COMMENT 'Tiempo estimado en minutos',
ADD COLUMN IF NOT EXISTS ultima_actualizacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;

-- Índices para pedidos
ALTER TABLE pedidos
ADD INDEX IF NOT EXISTS idx_cliente_id (cliente_id),
ADD INDEX IF NOT EXISTS idx_delivery_id (delivery_id),
ADD INDEX IF NOT EXISTS idx_prioridad (prioridad),
ADD INDEX IF NOT EXISTS idx_fecha_asignacion (fecha_asignacion),
ADD INDEX IF NOT EXISTS idx_estado_prioridad (estado, prioridad);

-- Tabla para items de pedidos
CREATE TABLE IF NOT EXISTS pedido_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    pedido_id INT NOT NULL,
    repuesto_id INT NULL,
    repuesto_nombre VARCHAR(255) NOT NULL,
    cantidad INT NOT NULL DEFAULT 1,
    precio_unitario DECIMAL(10,2) NOT NULL,
    subtotal DECIMAL(10,2) GENERATED ALWAYS AS (cantidad * precio_unitario) STORED,
    INDEX idx_pedido_id (pedido_id),
    INDEX idx_repuesto_id (repuesto_id)
);

-- Tabla para historial de cambios de estado de pedidos
CREATE TABLE IF NOT EXISTS pedido_historial (
    id INT AUTO_INCREMENT PRIMARY KEY,
    pedido_id INT NOT NULL,
    delivery_id INT NULL,
    estado_anterior VARCHAR(50),
    estado_nuevo VARCHAR(50) NOT NULL,
    fecha_cambio TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    observaciones TEXT,
    ubicacion_lat DECIMAL(10,8) NULL,
    ubicacion_lng DECIMAL(11,8) NULL,
    INDEX idx_pedido_id (pedido_id),
    INDEX idx_delivery_id (delivery_id),
    INDEX idx_fecha_cambio (fecha_cambio)
);

-- Tabla para rechazos de pedidos por deliveries
CREATE TABLE IF NOT EXISTS pedido_rechazos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    pedido_id INT NOT NULL,
    delivery_id INT NOT NULL,
    motivo ENUM('distancia', 'no_disponible', 'timeout', 'otro') NOT NULL,
    observaciones TEXT,
    fecha_rechazo TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_pedido_id (pedido_id),
    INDEX idx_delivery_id (delivery_id),
    INDEX idx_fecha_rechazo (fecha_rechazo),
    INDEX idx_motivo (motivo)
);

-- Tabla para estado actual de deliveries
CREATE TABLE IF NOT EXISTS delivery_status (
    delivery_id INT PRIMARY KEY,
    estado ENUM('disponible', 'ocupado', 'desconectado', 'en_pausa') DEFAULT 'disponible',
    pedido_actual INT NULL,
    zona_asignada VARCHAR(100) NULL,
    ultima_actualizacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_estado (estado),
    INDEX idx_pedido_actual (pedido_actual)
);

-- Insertar datos de ejemplo
INSERT IGNORE INTO clientes (id, nombre, telefono, email, direccion) VALUES
(1, 'Taller Central', '+54 ************', '<EMAIL>', 'Av. Libertador 1234, San Juan'),
(2, 'AutoService Plus', '+54 ************', '<EMAIL>', 'Ruta 40 Km 15, Rawson'),
(3, 'Mecánico López', '+54 ************', '<EMAIL>', 'Calle Rivadavia 567, Capital'),
(4, 'Taller San Martín', '+54 ************', '<EMAIL>', 'Av. San Martín 890, Chimbas');

INSERT IGNORE INTO repuestos (id, nombre, codigo, categoria, precio, stock) VALUES
(1, 'Filtro de Aceite', 'FO-001', 'Filtros', 850.00, 50),
(2, 'Pastillas de Freno Delanteras', 'PF-002', 'Frenos', 2200.00, 30),
(3, 'Bujías NGK (Set 4)', 'BU-003', 'Encendido', 1800.00, 25),
(4, 'Aceite Motor 5W30 (4L)', 'AC-004', 'Lubricantes', 3500.00, 40),
(5, 'Correa de Distribución', 'CD-005', 'Motor', 4200.00, 15);

-- Actualizar pedidos existentes con nuevos campos
UPDATE pedidos SET
    cliente_id = CASE
        WHEN id = 1 THEN 1
        WHEN id = 2 THEN 2
        WHEN id = 3 THEN 3
        ELSE 1
    END,
    prioridad = CASE
        WHEN id % 3 = 0 THEN 'alta'
        WHEN id % 5 = 0 THEN 'urgente'
        ELSE 'normal'
    END,
    tiempo_estimado = 15 + (id * 5),
    ganancia_delivery = ROUND(total * 0.15, 2)
WHERE cliente_id IS NULL;

-- Insertar items para pedidos existentes
INSERT IGNORE INTO pedido_items (pedido_id, repuesto_id, repuesto_nombre, cantidad, precio_unitario) VALUES
(1, 1, 'Filtro de Aceite', 2, 850.00),
(1, 4, 'Aceite Motor 5W30 (4L)', 1, 3500.00),
(2, 2, 'Pastillas de Freno Delanteras', 1, 2200.00),
(2, 3, 'Bujías NGK (Set 4)', 1, 1800.00),
(3, 5, 'Correa de Distribución', 1, 4200.00);

-- Insertar estados iniciales de deliveries
INSERT IGNORE INTO delivery_status (delivery_id, estado) VALUES
(1, 'disponible'),
(2, 'disponible'),
(3, 'ocupado'),
(4, 'disponible');
