<?php
// API para registro de talleres RepuMovil Plus (plan premium)
session_start();

header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header("Content-Type: application/json; charset=UTF-8");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Método no permitido']);
    exit;
}

require_once '../db_config.php';

try {
    // Datos básicos del usuario
    $nombre = $_POST['nombre'] ?? '';
    $email = $_POST['email'] ?? '';
    $telefono = $_POST['telefono'] ?? '';
    $password = $_POST['password'] ?? '';
    $user_type = 'taller_mecanico';
    $plan_type = 'plus';

    // Datos específicos del taller
    $username = $_POST['username'] ?? '';
    $direccion = $_POST['direccion'] ?? '';
    $rubro_principal = $_POST['rubro_principal'] ?? '';
    $datos_fiscales = $_POST['datos_fiscales'] ?? '';
    
    // Datos adicionales Plus
    $capacidad_clientes = $_POST['capacidad_clientes'] ?? '';
    $servicios_adicionales = isset($_POST['servicios']) ? implode(',', $_POST['servicios']) : '';

    // Validaciones básicas
    if (empty($nombre) || empty($email) || empty($password) || empty($username)) {
        throw new Exception('Todos los campos obligatorios deben ser completados');
    }

    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('Email no válido');
    }

    if (strlen($password) < 6) {
        throw new Exception('La contraseña debe tener al menos 6 caracteres');
    }

    // Verificar si el email ya existe
    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
    $stmt->execute([$email]);
    if ($stmt->fetch()) {
        throw new Exception('El email ya está registrado');
    }

    // Iniciar transacción
    $pdo->beginTransaction();

    // Insertar usuario principal
    $password_hash = password_hash($password, PASSWORD_DEFAULT);
    $stmt = $pdo->prepare("
        INSERT INTO users (nombre, email, telefono, password, user_type, created_at) 
        VALUES (?, ?, ?, ?, ?, NOW())
    ");
    $stmt->execute([$nombre, $email, $telefono, $password_hash, $user_type]);
    $user_id = $pdo->lastInsertId();

    // Insertar datos específicos del taller con plan plus
    $stmt = $pdo->prepare("
        INSERT INTO talleres_mecanicos 
        (user_id, username, direccion, rubro_principal, datos_fiscales, plan_type, capacidad_clientes, servicios_adicionales, created_at) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
    ");
    $stmt->execute([$user_id, $username, $direccion, $rubro_principal, $datos_fiscales, $plan_type, $capacidad_clientes, $servicios_adicionales]);

    // Crear configuración inicial Plus
    try {
        $stmt = $pdo->prepare("
            INSERT INTO taller_config_plus 
            (user_id, dashboard_personalizado, notificaciones_whatsapp, reportes_avanzados, inventario_activo, created_at) 
            VALUES (?, 1, 1, 1, 1, NOW())
        ");
        $stmt->execute([$user_id]);
    } catch (PDOException $e) {
        // Si la tabla no existe, la creamos
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS taller_config_plus (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                dashboard_personalizado BOOLEAN DEFAULT TRUE,
                notificaciones_whatsapp BOOLEAN DEFAULT TRUE,
                reportes_avanzados BOOLEAN DEFAULT TRUE,
                inventario_activo BOOLEAN DEFAULT TRUE,
                limite_clientes INT DEFAULT 50,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        
        // Insertar configuración después de crear la tabla
        $stmt = $pdo->prepare("
            INSERT INTO taller_config_plus 
            (user_id, dashboard_personalizado, notificaciones_whatsapp, reportes_avanzados, inventario_activo) 
            VALUES (?, 1, 1, 1, 1)
        ");
        $stmt->execute([$user_id]);
    }

    // Enviar SMS de confirmación Premium
    try {
        $mensaje = "🎉 ¡Bienvenido a RepuMovil PLUS! Tu taller '$username' ahora tiene acceso a todas las funciones premium: gestión de clientes, calendario de turnos, reportes avanzados y mucho más. ¡Llevá tu taller al siguiente nivel!";
        
        $stmt = $pdo->prepare("
            INSERT INTO sms_logs (user_id, telefono, mensaje, tipo, created_at) 
            VALUES (?, ?, ?, 'registro_taller_plus', NOW())
        ");
        $stmt->execute([$user_id, $telefono, $mensaje]);
        
        // Aquí se integraría con el servicio de SMS real (Twilio, Nexmo, etc.)
        // sendSMS($telefono, $mensaje);
        
    } catch (Exception $sms_error) {
        // Log del error pero no fallar el registro
        error_log("Error enviando SMS: " . $sms_error->getMessage());
    }

    // Confirmar transacción
    $pdo->commit();

    // Respuesta exitosa
    echo json_encode([
        'success' => true,
        'message' => 'Taller RepuMovil Plus registrado exitosamente',
        'user_id' => $user_id,
        'plan' => 'RepuMovil Plus',
        'features' => [
            'dashboard_avanzado' => true,
            'gestion_clientes' => true,
            'calendario_turnos' => true,
            'reportes_premium' => true,
            'inventario' => true,
            'whatsapp_integrado' => true
        ],
        'redirect' => 'login-dinamico.php'
    ]);

} catch (Exception $e) {
    // Revertir transacción en caso de error
    if ($pdo->inTransaction()) {
        $pdo->rollback();
    }
    
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
