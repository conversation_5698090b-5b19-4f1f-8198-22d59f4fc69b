<?php
// PASO 1: API para notificaciones push de RepuMovil
// Manejo completo de notificaciones push para deliveries

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Manejar preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Configuración de base de datos
$host = 'localhost';
$dbname = 'mechanical_workshop';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    sendResponse(false, 'Error de conexión a la base de datos: ' . $e->getMessage());
}

// Función para enviar respuesta JSON
function sendResponse($success, $message, $data = null) {
    echo json_encode([
        'success' => $success,
        'message' => $message,
        'data' => $data,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    exit();
}

// Manejar diferentes métodos HTTP
switch ($_SERVER['REQUEST_METHOD']) {
    case 'GET':
        $action = $_GET['action'] ?? '';
        
        switch ($action) {
            case 'get_tokens':
                obtenerTokensDeliveries($pdo);
                break;
                
            case 'get_notifications_log':
                obtenerLogNotificaciones($pdo, $_GET);
                break;
                
            default:
                sendResponse(false, 'Acción GET no válida');
        }
        break;
        
    case 'POST':
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input || !isset($input['action'])) {
            sendResponse(false, 'Acción requerida');
        }
        
        switch ($input['action']) {
            case 'register_token':
                registrarToken($pdo, $input);
                break;
                
            case 'send_notification':
                enviarNotificacion($pdo, $input);
                break;
                
            case 'send_bulk_notifications':
                enviarNotificacionesMasivas($pdo, $input);
                break;
                
            case 'mark_as_read':
                marcarComoLeida($pdo, $input);
                break;
                
            default:
                sendResponse(false, 'Acción POST no válida');
        }
        break;
        
    default:
        sendResponse(false, 'Método no permitido');
}

/**
 * PASO 1: Registrar token de notificación push
 */
function registrarToken($pdo, $data) {
    try {
        $deliveryId = $data['delivery_id'];
        $expoPushToken = $data['expo_push_token'];
        $deviceInfo = $data['device_info'] ?? [];
        
        if (!$deliveryId || !$expoPushToken) {
            sendResponse(false, 'delivery_id y expo_push_token son requeridos');
        }
        
        // Verificar que el delivery existe
        $stmt = $pdo->prepare("SELECT id FROM users WHERE id = ? AND tipo_usuario = 'delivery'");
        $stmt->execute([$deliveryId]);
        if (!$stmt->fetch()) {
            sendResponse(false, 'Delivery no encontrado');
        }
        
        // Insertar o actualizar token
        $stmt = $pdo->prepare("
            INSERT INTO delivery_push_tokens (
                delivery_id, expo_push_token, device_platform, device_name, 
                device_model, fecha_registro, activo
            ) VALUES (?, ?, ?, ?, ?, NOW(), 1)
            ON DUPLICATE KEY UPDATE
                expo_push_token = VALUES(expo_push_token),
                device_platform = VALUES(device_platform),
                device_name = VALUES(device_name),
                device_model = VALUES(device_model),
                fecha_actualizacion = NOW(),
                activo = 1
        ");
        
        $stmt->execute([
            $deliveryId,
            $expoPushToken,
            $deviceInfo['platform'] ?? null,
            $deviceInfo['device_name'] ?? null,
            $deviceInfo['device_model'] ?? null
        ]);
        
        error_log("📱 Token registrado para delivery $deliveryId: $expoPushToken");
        
        sendResponse(true, 'Token registrado exitosamente', [
            'delivery_id' => $deliveryId,
            'token_registered' => true
        ]);
        
    } catch (Exception $e) {
        error_log("❌ Error registrando token: " . $e->getMessage());
        sendResponse(false, 'Error registrando token: ' . $e->getMessage());
    }
}

/**
 * PASO 1: Enviar notificación push a delivery específico
 */
function enviarNotificacion($pdo, $data) {
    try {
        $deliveryId = $data['delivery_id'];
        $titulo = $data['titulo'];
        $mensaje = $data['mensaje'];
        $datosExtra = $data['data'] ?? [];
        $prioridad = $data['prioridad'] ?? 'normal';
        
        if (!$deliveryId || !$titulo || !$mensaje) {
            sendResponse(false, 'delivery_id, titulo y mensaje son requeridos');
        }
        
        // Obtener token del delivery
        $stmt = $pdo->prepare("
            SELECT expo_push_token 
            FROM delivery_push_tokens 
            WHERE delivery_id = ? AND activo = 1
            ORDER BY fecha_actualizacion DESC 
            LIMIT 1
        ");
        $stmt->execute([$deliveryId]);
        $tokenData = $stmt->fetch();
        
        if (!$tokenData) {
            sendResponse(false, 'No se encontró token activo para el delivery');
        }
        
        $expoPushToken = $tokenData['expo_push_token'];
        
        // Preparar payload para Expo Push API
        $payload = [
            'to' => $expoPushToken,
            'title' => $titulo,
            'body' => $mensaje,
            'data' => $datosExtra,
            'priority' => $prioridad === 'alta' ? 'high' : 'normal',
            'sound' => 'default',
            'channelId' => $prioridad === 'alta' ? 'repumovil-urgent' : 'repumovil-notifications'
        ];
        
        // Enviar notificación a Expo Push API
        $resultado = enviarAExpoPushAPI([$payload]);
        
        // Registrar en log
        $stmt = $pdo->prepare("
            INSERT INTO notificaciones_log (
                delivery_id, titulo, mensaje, datos_json, expo_push_token,
                resultado_envio, fecha_envio
            ) VALUES (?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $stmt->execute([
            $deliveryId,
            $titulo,
            $mensaje,
            json_encode($datosExtra),
            $expoPushToken,
            json_encode($resultado)
        ]);
        
        $notificacionId = $pdo->lastInsertId();
        
        error_log("🔔 Notificación enviada a delivery $deliveryId: $titulo");
        
        sendResponse(true, 'Notificación enviada exitosamente', [
            'notification_id' => $notificacionId,
            'delivery_id' => $deliveryId,
            'expo_result' => $resultado
        ]);
        
    } catch (Exception $e) {
        error_log("❌ Error enviando notificación: " . $e->getMessage());
        sendResponse(false, 'Error enviando notificación: ' . $e->getMessage());
    }
}

/**
 * PASO 1: Enviar notificaciones masivas a múltiples deliveries
 */
function enviarNotificacionesMasivas($pdo, $data) {
    try {
        $deliveryIds = $data['delivery_ids'] ?? [];
        $titulo = $data['titulo'];
        $mensaje = $data['mensaje'];
        $datosExtra = $data['data'] ?? [];
        $prioridad = $data['prioridad'] ?? 'normal';
        
        if (empty($deliveryIds) || !$titulo || !$mensaje) {
            sendResponse(false, 'delivery_ids, titulo y mensaje son requeridos');
        }
        
        // Obtener tokens de todos los deliveries
        $placeholders = str_repeat('?,', count($deliveryIds) - 1) . '?';
        $stmt = $pdo->prepare("
            SELECT delivery_id, expo_push_token 
            FROM delivery_push_tokens 
            WHERE delivery_id IN ($placeholders) AND activo = 1
        ");
        $stmt->execute($deliveryIds);
        $tokens = $stmt->fetchAll();
        
        if (empty($tokens)) {
            sendResponse(false, 'No se encontraron tokens activos para los deliveries');
        }
        
        // Preparar payloads para Expo Push API
        $payloads = [];
        foreach ($tokens as $token) {
            $payloads[] = [
                'to' => $token['expo_push_token'],
                'title' => $titulo,
                'body' => $mensaje,
                'data' => array_merge($datosExtra, ['delivery_id' => $token['delivery_id']]),
                'priority' => $prioridad === 'alta' ? 'high' : 'normal',
                'sound' => 'default',
                'channelId' => $prioridad === 'alta' ? 'repumovil-urgent' : 'repumovil-notifications'
            ];
        }
        
        // Enviar notificaciones en lotes (máximo 100 por lote según Expo)
        $lotes = array_chunk($payloads, 100);
        $resultados = [];
        
        foreach ($lotes as $lote) {
            $resultado = enviarAExpoPushAPI($lote);
            $resultados = array_merge($resultados, $resultado);
        }
        
        // Registrar en log
        foreach ($tokens as $index => $token) {
            $stmt = $pdo->prepare("
                INSERT INTO notificaciones_log (
                    delivery_id, titulo, mensaje, datos_json, expo_push_token,
                    resultado_envio, fecha_envio
                ) VALUES (?, ?, ?, ?, ?, ?, NOW())
            ");
            
            $stmt->execute([
                $token['delivery_id'],
                $titulo,
                $mensaje,
                json_encode($datosExtra),
                $token['expo_push_token'],
                json_encode($resultados[$index] ?? null)
            ]);
        }
        
        error_log("📢 Notificaciones masivas enviadas a " . count($tokens) . " deliveries");
        
        sendResponse(true, 'Notificaciones masivas enviadas', [
            'total_sent' => count($tokens),
            'total_requested' => count($deliveryIds),
            'expo_results' => $resultados
        ]);
        
    } catch (Exception $e) {
        error_log("❌ Error enviando notificaciones masivas: " . $e->getMessage());
        sendResponse(false, 'Error enviando notificaciones masivas: ' . $e->getMessage());
    }
}

/**
 * PASO 1: Función para enviar a Expo Push API
 */
function enviarAExpoPushAPI($payloads) {
    $url = 'https://exp.host/--/api/v2/push/send';
    
    $options = [
        'http' => [
            'header' => [
                'Content-Type: application/json',
                'Accept: application/json',
                'Accept-Encoding: gzip, deflate'
            ],
            'method' => 'POST',
            'content' => json_encode($payloads)
        ]
    ];
    
    $context = stream_context_create($options);
    $result = file_get_contents($url, false, $context);
    
    if ($result === FALSE) {
        throw new Exception('Error conectando con Expo Push API');
    }
    
    $response = json_decode($result, true);
    
    if (!$response || !isset($response['data'])) {
        throw new Exception('Respuesta inválida de Expo Push API');
    }
    
    return $response['data'];
}

/**
 * PASO 1: Obtener tokens de deliveries activos
 */
function obtenerTokensDeliveries($pdo) {
    try {
        $stmt = $pdo->prepare("
            SELECT 
                dpt.delivery_id,
                u.nombre,
                dpt.expo_push_token,
                dpt.device_platform,
                dpt.fecha_registro,
                dpt.fecha_actualizacion
            FROM delivery_push_tokens dpt
            JOIN users u ON dpt.delivery_id = u.id
            WHERE dpt.activo = 1
            ORDER BY dpt.fecha_actualizacion DESC
        ");
        $stmt->execute();
        $tokens = $stmt->fetchAll();
        
        sendResponse(true, 'Tokens obtenidos', $tokens);
        
    } catch (Exception $e) {
        sendResponse(false, 'Error obteniendo tokens: ' . $e->getMessage());
    }
}

/**
 * PASO 1: Obtener log de notificaciones
 */
function obtenerLogNotificaciones($pdo, $params) {
    try {
        $deliveryId = $params['delivery_id'] ?? null;
        $limite = $params['limite'] ?? 50;
        $offset = $params['offset'] ?? 0;
        
        $whereClause = $deliveryId ? "WHERE nl.delivery_id = ?" : "";
        $queryParams = $deliveryId ? [$deliveryId, $limite, $offset] : [$limite, $offset];
        
        $stmt = $pdo->prepare("
            SELECT 
                nl.*,
                u.nombre as delivery_nombre
            FROM notificaciones_log nl
            LEFT JOIN users u ON nl.delivery_id = u.id
            $whereClause
            ORDER BY nl.fecha_envio DESC
            LIMIT ? OFFSET ?
        ");
        
        $stmt->execute($queryParams);
        $notificaciones = $stmt->fetchAll();
        
        sendResponse(true, 'Log de notificaciones obtenido', $notificaciones);
        
    } catch (Exception $e) {
        sendResponse(false, 'Error obteniendo log: ' . $e->getMessage());
    }
}

/**
 * PASO 1: Marcar notificación como leída
 */
function marcarComoLeida($pdo, $data) {
    try {
        $notificacionId = $data['notification_id'];
        $deliveryId = $data['delivery_id'];
        
        if (!$notificacionId || !$deliveryId) {
            sendResponse(false, 'notification_id y delivery_id son requeridos');
        }
        
        $stmt = $pdo->prepare("
            UPDATE notificaciones_log 
            SET leida = 1, fecha_lectura = NOW() 
            WHERE id = ? AND delivery_id = ?
        ");
        $stmt->execute([$notificacionId, $deliveryId]);
        
        sendResponse(true, 'Notificación marcada como leída');
        
    } catch (Exception $e) {
        sendResponse(false, 'Error marcando como leída: ' . $e->getMessage());
    }
}
?>
