<?php
/**
 * API de Pedidos para RepuMovil
 * FASE 2: Backend/API - Paso 4
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Manejar preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Incluir configuración de base de datos
require_once 'db_config.php';

try {
    $pdo = connectDB();
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            handleGetPedidos($pdo);
            break;
            
        case 'POST':
            handlePostPedidos($pdo);
            break;
            
        case 'PUT':
            handlePutPedidos($pdo);
            break;
            
        default:
            throw new Exception('Método no permitido');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

/**
 * Obtener pedidos del usuario
 */
function handleGetPedidos($pdo) {
    $userId = $_GET['user_id'] ?? '';
    $pedidoId = $_GET['pedido_id'] ?? '';
    
    if (empty($userId) && empty($pedidoId)) {
        throw new Exception('ID de usuario o pedido requerido');
    }
    
    if (!empty($pedidoId)) {
        // Obtener pedido específico con detalles
        $stmt = $pdo->prepare("
            SELECT p.*, s.name as supplier_name, s.phone as supplier_phone, s.address as supplier_address
            FROM pedidos p
            JOIN suppliers s ON p.supplier_id = s.id
            WHERE p.id = ?
        ");
        $stmt->execute([$pedidoId]);
        $pedido = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$pedido) {
            throw new Exception('Pedido no encontrado');
        }
        
        // Obtener detalles del pedido
        $stmt = $pdo->prepare("
            SELECT pd.*, r.nombre, r.descripcion, r.marca, r.modelo, r.imagen_url
            FROM pedido_detalles pd
            JOIN repuestos r ON pd.repuesto_id = r.id
            WHERE pd.pedido_id = ?
        ");
        $stmt->execute([$pedidoId]);
        $detalles = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $pedido['detalles'] = $detalles;
        
        echo json_encode([
            'success' => true,
            'data' => $pedido
        ]);
    } else {
        // Obtener todos los pedidos del usuario
        $stmt = $pdo->prepare("
            SELECT p.*, s.name as supplier_name, s.phone as supplier_phone,
                   COUNT(pd.id) as total_items
            FROM pedidos p
            JOIN suppliers s ON p.supplier_id = s.id
            LEFT JOIN pedido_detalles pd ON p.id = pd.pedido_id
            WHERE p.user_id = ?
            GROUP BY p.id
            ORDER BY p.created_at DESC
        ");
        $stmt->execute([$userId]);
        $pedidos = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode([
            'success' => true,
            'data' => $pedidos
        ]);
    }
}

/**
 * Crear nuevo pedido
 */
function handlePostPedidos($pdo) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Datos JSON inválidos');
    }
    
    $userId = $input['user_id'] ?? '';
    $metodoPago = $input['metodo_pago'] ?? '';
    $direccionEntrega = $input['direccion_entrega'] ?? '';
    $telefonoContacto = $input['telefono_contacto'] ?? '';
    $notas = $input['notas'] ?? '';
    
    if (empty($userId) || empty($metodoPago) || empty($direccionEntrega)) {
        throw new Exception('Datos requeridos faltantes');
    }
    
    // Iniciar transacción
    $pdo->beginTransaction();
    
    try {
        // Obtener items del carrito
        $stmt = $pdo->prepare("
            SELECT c.*, r.supplier_id, r.stock, r.nombre
            FROM carrito c
            JOIN repuestos r ON c.repuesto_id = r.id
            WHERE c.user_id = ?
        ");
        $stmt->execute([$userId]);
        $carritoItems = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($carritoItems)) {
            throw new Exception('El carrito está vacío');
        }
        
        // Agrupar por proveedor
        $pedidosPorProveedor = [];
        foreach ($carritoItems as $item) {
            $supplierId = $item['supplier_id'];
            if (!isset($pedidosPorProveedor[$supplierId])) {
                $pedidosPorProveedor[$supplierId] = [];
            }
            $pedidosPorProveedor[$supplierId][] = $item;
        }
        
        $pedidosCreados = [];
        
        // Crear un pedido por cada proveedor
        foreach ($pedidosPorProveedor as $supplierId => $items) {
            $total = 0;
            
            // Verificar stock y calcular total
            foreach ($items as $item) {
                if ($item['cantidad'] > $item['stock']) {
                    throw new Exception("Stock insuficiente para: {$item['nombre']}");
                }
                $total += $item['cantidad'] * $item['precio_unitario'];
            }
            
            // Crear pedido
            $stmt = $pdo->prepare("
                INSERT INTO pedidos (user_id, supplier_id, total, metodo_pago, direccion_entrega, telefono_contacto, notas)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([$userId, $supplierId, $total, $metodoPago, $direccionEntrega, $telefonoContacto, $notas]);
            $pedidoId = $pdo->lastInsertId();
            
            // Crear detalles del pedido
            foreach ($items as $item) {
                $subtotal = $item['cantidad'] * $item['precio_unitario'];
                
                $stmt = $pdo->prepare("
                    INSERT INTO pedido_detalles (pedido_id, repuesto_id, cantidad, precio_unitario, subtotal)
                    VALUES (?, ?, ?, ?, ?)
                ");
                $stmt->execute([$pedidoId, $item['repuesto_id'], $item['cantidad'], $item['precio_unitario'], $subtotal]);
                
                // Actualizar stock
                $stmt = $pdo->prepare("UPDATE repuestos SET stock = stock - ? WHERE id = ?");
                $stmt->execute([$item['cantidad'], $item['repuesto_id']]);
            }
            
            $pedidosCreados[] = $pedidoId;
        }
        
        // Limpiar carrito
        $stmt = $pdo->prepare("DELETE FROM carrito WHERE user_id = ?");
        $stmt->execute([$userId]);
        
        // Confirmar transacción
        $pdo->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'Pedido(s) creado(s) exitosamente',
            'pedidos_ids' => $pedidosCreados
        ]);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }
}

/**
 * Actualizar estado del pedido
 */
function handlePutPedidos($pdo) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Datos JSON inválidos');
    }
    
    $pedidoId = $input['pedido_id'] ?? '';
    $estado = $input['estado'] ?? '';
    
    if (empty($pedidoId) || empty($estado)) {
        throw new Exception('ID de pedido y estado requeridos');
    }
    
    $estadosValidos = ['pendiente', 'confirmado', 'en_preparacion', 'enviado', 'entregado', 'cancelado'];
    if (!in_array($estado, $estadosValidos)) {
        throw new Exception('Estado no válido');
    }
    
    $stmt = $pdo->prepare("UPDATE pedidos SET estado = ?, updated_at = NOW() WHERE id = ?");
    $result = $stmt->execute([$estado, $pedidoId]);
    
    if ($stmt->rowCount() === 0) {
        throw new Exception('Pedido no encontrado');
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Estado del pedido actualizado'
    ]);
}
?>
