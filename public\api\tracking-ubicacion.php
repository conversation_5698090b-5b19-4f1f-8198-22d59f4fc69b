<?php
// PASO 7: API para tracking de ubicación en tiempo real
// RepuMovil - Sistema de Tracking de Deliveries

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Manejar preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Configuración de base de datos
$host = 'localhost';
$dbname = 'mechanical_workshop';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    sendResponse(false, 'Error de conexión a la base de datos: ' . $e->getMessage());
}

// Función para enviar respuesta JSON
function sendResponse($success, $message, $data = null) {
    echo json_encode([
        'success' => $success,
        'message' => $message,
        'data' => $data,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    exit();
}

// Manejar diferentes métodos HTTP
switch ($_SERVER['REQUEST_METHOD']) {
    case 'GET':
        // Obtener ubicación actual del delivery
        $pedidoId = $_GET['pedido_id'] ?? null;
        $deliveryId = $_GET['delivery_id'] ?? null;
        
        if ($pedidoId) {
            obtenerUbicacionPedido($pdo, $pedidoId);
        } elseif ($deliveryId) {
            obtenerUbicacionDelivery($pdo, $deliveryId);
        } else {
            sendResponse(false, 'ID de pedido o delivery requerido');
        }
        break;
        
    case 'POST':
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input || !isset($input['action'])) {
            sendResponse(false, 'Acción requerida');
        }
        
        switch ($input['action']) {
            case 'update_location':
                actualizarUbicacion($pdo, $input);
                break;
                
            case 'get_tracking_data':
                obtenerDatosTracking($pdo, $input);
                break;
                
            case 'update_delivery_status':
                actualizarEstadoEntrega($pdo, $input);
                break;
                
            default:
                sendResponse(false, 'Acción no válida');
        }
        break;
        
    default:
        sendResponse(false, 'Método no permitido');
}

/**
 * PASO 7: Actualizar ubicación del delivery
 */
function actualizarUbicacion($pdo, $data) {
    try {
        $pedidoId = $data['pedido_id'];
        $deliveryId = $data['delivery_id'];
        $latitude = $data['latitude'];
        $longitude = $data['longitude'];
        $accuracy = $data['accuracy'] ?? null;
        $speed = $data['speed'] ?? null;
        $heading = $data['heading'] ?? null;
        $timestamp = $data['timestamp'] ?? date('Y-m-d H:i:s');
        
        // Validar datos requeridos
        if (!$pedidoId || !$deliveryId || !$latitude || !$longitude) {
            sendResponse(false, 'Datos de ubicación incompletos');
        }
        
        // Insertar nueva ubicación
        $stmt = $pdo->prepare("
            INSERT INTO tracking_ubicaciones (
                pedido_id, delivery_id, latitude, longitude, accuracy, 
                speed, heading, timestamp_ubicacion, fecha_registro
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $stmt->execute([
            $pedidoId, $deliveryId, $latitude, $longitude, 
            $accuracy, $speed, $heading, $timestamp
        ]);
        
        // Actualizar ubicación actual del delivery
        $stmt = $pdo->prepare("
            UPDATE repartidores 
            SET latitud_actual = ?, longitud_actual = ?, ultima_actualizacion = NOW() 
            WHERE user_id = ?
        ");
        $stmt->execute([$latitude, $longitude, $deliveryId]);
        
        // Calcular ETA si hay destino
        $eta = calcularETA($pdo, $pedidoId, $latitude, $longitude);
        
        // Actualizar ETA en el pedido
        if ($eta) {
            $stmt = $pdo->prepare("UPDATE pedidos SET eta_minutos = ? WHERE id = ?");
            $stmt->execute([$eta, $pedidoId]);
        }
        
        // Log de tracking
        error_log("📍 Ubicación actualizada - Delivery: $deliveryId, Pedido: $pedidoId, Lat: $latitude, Lng: $longitude, ETA: {$eta}min");
        
        sendResponse(true, 'Ubicación actualizada exitosamente', [
            'pedido_id' => $pedidoId,
            'delivery_id' => $deliveryId,
            'latitude' => $latitude,
            'longitude' => $longitude,
            'eta_minutos' => $eta,
            'timestamp' => $timestamp
        ]);
        
    } catch (Exception $e) {
        error_log("❌ Error actualizando ubicación: " . $e->getMessage());
        sendResponse(false, 'Error actualizando ubicación: ' . $e->getMessage());
    }
}

/**
 * PASO 7: Obtener datos de tracking para el cliente
 */
function obtenerDatosTracking($pdo, $data) {
    try {
        $pedidoId = $data['pedido_id'];
        
        if (!$pedidoId) {
            sendResponse(false, 'ID de pedido requerido');
        }
        
        // Obtener información del pedido y delivery
        $stmt = $pdo->prepare("
            SELECT p.*, u.nombre as delivery_nombre, u.telefono as delivery_telefono,
                   r.calificacion_promedio, r.entregas_completadas,
                   r.latitud_actual, r.longitud_actual, r.ultima_actualizacion
            FROM pedidos p
            LEFT JOIN users u ON p.repartidor_id = u.id
            LEFT JOIN repartidores r ON p.repartidor_id = r.user_id
            WHERE p.id = ?
        ");
        $stmt->execute([$pedidoId]);
        $pedido = $stmt->fetch();
        
        if (!$pedido) {
            sendResponse(false, 'Pedido no encontrado');
        }
        
        // Obtener últimas ubicaciones del tracking
        $stmt = $pdo->prepare("
            SELECT latitude, longitude, accuracy, speed, heading, timestamp_ubicacion
            FROM tracking_ubicaciones
            WHERE pedido_id = ?
            ORDER BY fecha_registro DESC
            LIMIT 10
        ");
        $stmt->execute([$pedidoId]);
        $ubicaciones = $stmt->fetchAll();
        
        // Calcular distancia restante y ETA
        $distanciaRestante = null;
        $eta = null;
        
        if ($pedido['latitud_actual'] && $pedido['longitud_actual']) {
            // Coordenadas de destino (simuladas - en producción vendrían de la dirección)
            $destLat = -31.5420; // Coordenadas del cliente
            $destLng = -68.5290;
            
            $distanciaRestante = calcularDistancia(
                $pedido['latitud_actual'], 
                $pedido['longitud_actual'],
                $destLat, 
                $destLng
            );
            
            $eta = $pedido['eta_minutos'] ?? round($distanciaRestante * 3); // 3 min por km aprox
        }
        
        sendResponse(true, 'Datos de tracking obtenidos', [
            'pedido' => [
                'id' => $pedido['id'],
                'estado' => $pedido['estado'],
                'direccion_entrega' => $pedido['direccion_entrega'],
                'total' => $pedido['total'],
                'eta_minutos' => $eta
            ],
            'delivery' => [
                'nombre' => $pedido['delivery_nombre'],
                'telefono' => $pedido['delivery_telefono'],
                'calificacion' => $pedido['calificacion_promedio'],
                'entregas_completadas' => $pedido['entregas_completadas'],
                'ubicacion_actual' => [
                    'latitude' => $pedido['latitud_actual'],
                    'longitude' => $pedido['longitud_actual'],
                    'ultima_actualizacion' => $pedido['ultima_actualizacion']
                ]
            ],
            'tracking' => [
                'ubicaciones_recientes' => $ubicaciones,
                'distancia_restante_km' => $distanciaRestante,
                'eta_minutos' => $eta
            ]
        ]);
        
    } catch (Exception $e) {
        error_log("❌ Error obteniendo datos de tracking: " . $e->getMessage());
        sendResponse(false, 'Error obteniendo datos de tracking: ' . $e->getMessage());
    }
}

/**
 * PASO 7: Obtener ubicación actual de un pedido
 */
function obtenerUbicacionPedido($pdo, $pedidoId) {
    try {
        $stmt = $pdo->prepare("
            SELECT t.*, u.nombre as delivery_nombre
            FROM tracking_ubicaciones t
            JOIN pedidos p ON t.pedido_id = p.id
            JOIN users u ON t.delivery_id = u.id
            WHERE t.pedido_id = ?
            ORDER BY t.fecha_registro DESC
            LIMIT 1
        ");
        $stmt->execute([$pedidoId]);
        $ubicacion = $stmt->fetch();
        
        if (!$ubicacion) {
            sendResponse(false, 'No se encontró ubicación para este pedido');
        }
        
        sendResponse(true, 'Ubicación obtenida', $ubicacion);
        
    } catch (Exception $e) {
        sendResponse(false, 'Error obteniendo ubicación: ' . $e->getMessage());
    }
}

/**
 * PASO 7: Calcular ETA basado en distancia y tráfico
 */
function calcularETA($pdo, $pedidoId, $currentLat, $currentLng) {
    try {
        // Obtener destino del pedido (simulado)
        $destLat = -31.5420; // En producción, obtener de la dirección real
        $destLng = -68.5290;
        
        $distancia = calcularDistancia($currentLat, $currentLng, $destLat, $destLng);
        
        // Calcular ETA considerando velocidad promedio en ciudad (20 km/h)
        $velocidadPromedio = 20; // km/h
        $eta = round(($distancia / $velocidadPromedio) * 60); // minutos
        
        // Agregar factor de tráfico (simulado)
        $factorTrafico = 1.2; // 20% más tiempo por tráfico
        $eta = round($eta * $factorTrafico);
        
        return max(1, $eta); // Mínimo 1 minuto
        
    } catch (Exception $e) {
        return null;
    }
}

/**
 * PASO 7: Calcular distancia entre dos puntos (fórmula de Haversine)
 */
function calcularDistancia($lat1, $lng1, $lat2, $lng2) {
    $radioTierra = 6371; // Radio de la Tierra en km
    
    $dLat = deg2rad($lat2 - $lat1);
    $dLng = deg2rad($lng2 - $lng1);
    
    $a = sin($dLat/2) * sin($dLat/2) +
         cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
         sin($dLng/2) * sin($dLng/2);
    
    $c = 2 * atan2(sqrt($a), sqrt(1-$a));
    
    return $radioTierra * $c;
}

/**
 * PASO 7: Actualizar estado de entrega
 */
function actualizarEstadoEntrega($pdo, $data) {
    try {
        $pedidoId = $data['pedido_id'];
        $nuevoEstado = $data['estado'];
        $deliveryId = $data['delivery_id'];
        $notas = $data['notas'] ?? '';
        
        // Validar estado
        $estadosValidos = ['asignado', 'recogido', 'en_camino', 'entregado'];
        if (!in_array($nuevoEstado, $estadosValidos)) {
            sendResponse(false, 'Estado no válido');
        }
        
        // Actualizar estado del pedido
        $stmt = $pdo->prepare("
            UPDATE pedidos 
            SET estado = ?, fecha_actualizacion = NOW() 
            WHERE id = ?
        ");
        $stmt->execute([$nuevoEstado, $pedidoId]);
        
        // Registrar cambio de estado
        $stmt = $pdo->prepare("
            INSERT INTO estados_pedido_log (pedido_id, estado_anterior, estado_nuevo, delivery_id, notas, fecha_cambio)
            VALUES (?, (SELECT estado FROM pedidos WHERE id = ?), ?, ?, ?, NOW())
        ");
        $stmt->execute([$pedidoId, $pedidoId, $nuevoEstado, $deliveryId, $notas]);
        
        // Si se entregó, liberar al delivery
        if ($nuevoEstado === 'entregado') {
            $stmt = $pdo->prepare("UPDATE repartidores SET estado = 'disponible' WHERE user_id = ?");
            $stmt->execute([$deliveryId]);
        }
        
        error_log("📦 Estado actualizado - Pedido: $pedidoId, Nuevo estado: $nuevoEstado");
        
        sendResponse(true, 'Estado actualizado exitosamente', [
            'pedido_id' => $pedidoId,
            'nuevo_estado' => $nuevoEstado,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
        
    } catch (Exception $e) {
        sendResponse(false, 'Error actualizando estado: ' . $e->getMessage());
    }
}
?>
