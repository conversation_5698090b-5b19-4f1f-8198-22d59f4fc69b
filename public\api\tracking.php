<?php
// PASO 2: API para tracking GPS en tiempo real de RepuMovil
// Manejo completo de ubicaciones de deliveries

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Manejar preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Configuración de base de datos
$host = 'localhost';
$dbname = 'mechanical_workshop';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    sendResponse(false, 'Error de conexión a la base de datos: ' . $e->getMessage());
}

// Función para enviar respuesta JSON
function sendResponse($success, $message, $data = null) {
    echo json_encode([
        'success' => $success,
        'message' => $message,
        'data' => $data,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    exit();
}

// Manejar diferentes métodos HTTP
switch ($_SERVER['REQUEST_METHOD']) {
    case 'GET':
        $action = $_GET['action'] ?? '';
        
        switch ($action) {
            case 'get_delivery_location':
                obtenerUbicacionDelivery($pdo, $_GET);
                break;
                
            case 'get_tracking_history':
                obtenerHistorialTracking($pdo, $_GET);
                break;
                
            case 'get_active_deliveries':
                obtenerDeliveriesActivos($pdo);
                break;
                
            case 'get_delivery_route':
                obtenerRutaDelivery($pdo, $_GET);
                break;
                
            default:
                sendResponse(false, 'Acción GET no válida');
        }
        break;
        
    case 'POST':
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input || !isset($input['action'])) {
            sendResponse(false, 'Acción requerida');
        }
        
        switch ($input['action']) {
            case 'update_location':
                actualizarUbicacion($pdo, $input);
                break;
                
            case 'start_tracking':
                iniciarTracking($pdo, $input);
                break;
                
            case 'stop_tracking':
                detenerTracking($pdo, $input);
                break;
                
            case 'update_delivery_status':
                actualizarEstadoDelivery($pdo, $input);
                break;
                
            default:
                sendResponse(false, 'Acción POST no válida');
        }
        break;
        
    default:
        sendResponse(false, 'Método no permitido');
}

/**
 * PASO 2: Actualizar ubicación del delivery en tiempo real
 */
function actualizarUbicacion($pdo, $data) {
    try {
        $deliveryId = $data['delivery_id'];
        $pedidoId = $data['pedido_id'] ?? null;
        $latitude = $data['latitude'];
        $longitude = $data['longitude'];
        $accuracy = $data['accuracy'] ?? null;
        $speed = $data['speed'] ?? null;
        $heading = $data['heading'] ?? null;
        $timestamp = $data['timestamp'] ?? time() * 1000; // Convertir a milisegundos
        
        if (!$deliveryId || !$latitude || !$longitude) {
            sendResponse(false, 'delivery_id, latitude y longitude son requeridos');
        }
        
        // Verificar que el delivery existe
        $stmt = $pdo->prepare("SELECT id, nombre FROM users WHERE id = ?");
        $stmt->execute([$deliveryId]);
        $delivery = $stmt->fetch();
        
        if (!$delivery) {
            sendResponse(false, 'Delivery no encontrado');
        }
        
        // Insertar nueva ubicación en el historial
        $stmt = $pdo->prepare("
            INSERT INTO delivery_locations (
                delivery_id, pedido_id, latitude, longitude, accuracy, 
                speed, heading, timestamp, fecha_registro
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $stmt->execute([
            $deliveryId,
            $pedidoId,
            $latitude,
            $longitude,
            $accuracy,
            $speed,
            $heading,
            date('Y-m-d H:i:s', $timestamp / 1000)
        ]);
        
        // Actualizar ubicación actual del delivery
        $stmt = $pdo->prepare("
            INSERT INTO delivery_current_location (
                delivery_id, latitude, longitude, accuracy, speed, heading,
                timestamp, ultima_actualizacion
            ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
            ON DUPLICATE KEY UPDATE
                latitude = VALUES(latitude),
                longitude = VALUES(longitude),
                accuracy = VALUES(accuracy),
                speed = VALUES(speed),
                heading = VALUES(heading),
                timestamp = VALUES(timestamp),
                ultima_actualizacion = NOW()
        ");
        
        $stmt->execute([
            $deliveryId,
            $latitude,
            $longitude,
            $accuracy,
            $speed,
            $heading,
            date('Y-m-d H:i:s', $timestamp / 1000)
        ]);
        
        // Si hay un pedido asociado, calcular ETA
        $eta = null;
        if ($pedidoId) {
            $eta = calcularETA($pdo, $deliveryId, $pedidoId, $latitude, $longitude);
        }
        
        error_log("📍 Ubicación actualizada para delivery $deliveryId: $latitude, $longitude");
        
        sendResponse(true, 'Ubicación actualizada correctamente', [
            'delivery_id' => $deliveryId,
            'delivery_name' => $delivery['nombre'],
            'latitude' => $latitude,
            'longitude' => $longitude,
            'accuracy' => $accuracy,
            'speed' => $speed,
            'eta_minutes' => $eta,
            'timestamp' => $timestamp
        ]);
        
    } catch (Exception $e) {
        error_log("❌ Error actualizando ubicación: " . $e->getMessage());
        sendResponse(false, 'Error actualizando ubicación: ' . $e->getMessage());
    }
}

/**
 * PASO 2: Obtener ubicación actual de un delivery
 */
function obtenerUbicacionDelivery($pdo, $params) {
    try {
        $deliveryId = $params['delivery_id'] ?? null;
        $pedidoId = $params['pedido_id'] ?? null;
        
        if (!$deliveryId) {
            sendResponse(false, 'delivery_id es requerido');
        }
        
        // Obtener ubicación actual
        $stmt = $pdo->prepare("
            SELECT 
                dcl.*,
                u.nombre as delivery_name,
                u.telefono as delivery_phone
            FROM delivery_current_location dcl
            JOIN users u ON dcl.delivery_id = u.id
            WHERE dcl.delivery_id = ?
        ");
        $stmt->execute([$deliveryId]);
        $ubicacion = $stmt->fetch();
        
        if (!$ubicacion) {
            sendResponse(false, 'No se encontró ubicación para el delivery');
        }
        
        // Calcular tiempo desde última actualización
        $ultimaActualizacion = new DateTime($ubicacion['ultima_actualizacion']);
        $ahora = new DateTime();
        $minutosDesdeActualizacion = $ahora->diff($ultimaActualizacion)->i;
        
        // Si hay pedido, obtener información adicional
        $pedidoInfo = null;
        if ($pedidoId) {
            $stmt = $pdo->prepare("
                SELECT 
                    id, cliente_nombre, direccion_entrega, estado,
                    latitude as destino_lat, longitude as destino_lng
                FROM pedidos 
                WHERE id = ?
            ");
            $stmt->execute([$pedidoId]);
            $pedidoInfo = $stmt->fetch();
            
            // Calcular ETA si hay destino
            if ($pedidoInfo && $pedidoInfo['destino_lat'] && $pedidoInfo['destino_lng']) {
                $eta = calcularETA(
                    $pdo, 
                    $deliveryId, 
                    $pedidoId, 
                    $ubicacion['latitude'], 
                    $ubicacion['longitude']
                );
                $pedidoInfo['eta_minutes'] = $eta;
            }
        }
        
        sendResponse(true, 'Ubicación obtenida', [
            'delivery_id' => $deliveryId,
            'delivery_name' => $ubicacion['delivery_name'],
            'delivery_phone' => $ubicacion['delivery_phone'],
            'latitude' => (float)$ubicacion['latitude'],
            'longitude' => (float)$ubicacion['longitude'],
            'accuracy' => $ubicacion['accuracy'],
            'speed' => $ubicacion['speed'],
            'heading' => $ubicacion['heading'],
            'timestamp' => $ubicacion['timestamp'],
            'ultima_actualizacion' => $ubicacion['ultima_actualizacion'],
            'minutos_desde_actualizacion' => $minutosDesdeActualizacion,
            'pedido_info' => $pedidoInfo
        ]);
        
    } catch (Exception $e) {
        sendResponse(false, 'Error obteniendo ubicación: ' . $e->getMessage());
    }
}

/**
 * PASO 2: Obtener historial de tracking de un delivery
 */
function obtenerHistorialTracking($pdo, $params) {
    try {
        $deliveryId = $params['delivery_id'] ?? null;
        $pedidoId = $params['pedido_id'] ?? null;
        $limite = $params['limite'] ?? 100;
        $fechaDesde = $params['fecha_desde'] ?? date('Y-m-d 00:00:00');
        
        if (!$deliveryId) {
            sendResponse(false, 'delivery_id es requerido');
        }
        
        $whereClause = "WHERE dl.delivery_id = ?";
        $params = [$deliveryId];
        
        if ($pedidoId) {
            $whereClause .= " AND dl.pedido_id = ?";
            $params[] = $pedidoId;
        }
        
        $whereClause .= " AND dl.fecha_registro >= ?";
        $params[] = $fechaDesde;
        
        $stmt = $pdo->prepare("
            SELECT 
                dl.*,
                u.nombre as delivery_name
            FROM delivery_locations dl
            JOIN users u ON dl.delivery_id = u.id
            $whereClause
            ORDER BY dl.fecha_registro DESC
            LIMIT ?
        ");
        
        $params[] = $limite;
        $stmt->execute($params);
        $historial = $stmt->fetchAll();
        
        // Calcular estadísticas del recorrido
        $estadisticas = [
            'total_puntos' => count($historial),
            'distancia_total' => 0,
            'velocidad_promedio' => 0,
            'tiempo_total' => 0
        ];
        
        if (count($historial) > 1) {
            $distanciaTotal = 0;
            $velocidades = [];
            
            for ($i = 0; $i < count($historial) - 1; $i++) {
                $punto1 = $historial[$i];
                $punto2 = $historial[$i + 1];
                
                $distancia = calcularDistancia(
                    $punto1['latitude'], $punto1['longitude'],
                    $punto2['latitude'], $punto2['longitude']
                );
                
                $distanciaTotal += $distancia;
                
                if ($punto1['speed'] !== null) {
                    $velocidades[] = $punto1['speed'];
                }
            }
            
            $estadisticas['distancia_total'] = round($distanciaTotal, 2);
            $estadisticas['velocidad_promedio'] = count($velocidades) > 0 ? 
                round(array_sum($velocidades) / count($velocidades), 2) : 0;
            
            $tiempoInicio = new DateTime($historial[count($historial) - 1]['fecha_registro']);
            $tiempoFin = new DateTime($historial[0]['fecha_registro']);
            $estadisticas['tiempo_total'] = $tiempoFin->diff($tiempoInicio)->i; // minutos
        }
        
        sendResponse(true, 'Historial obtenido', [
            'historial' => $historial,
            'estadisticas' => $estadisticas
        ]);
        
    } catch (Exception $e) {
        sendResponse(false, 'Error obteniendo historial: ' . $e->getMessage());
    }
}

/**
 * PASO 2: Obtener deliveries activos con sus ubicaciones
 */
function obtenerDeliveriesActivos($pdo) {
    try {
        $stmt = $pdo->prepare("
            SELECT
                u.id, u.nombre, u.telefono,
                dcl.latitude, dcl.longitude, dcl.accuracy,
                dcl.speed, dcl.heading, dcl.ultima_actualizacion,
                TIMESTAMPDIFF(MINUTE, dcl.ultima_actualizacion, NOW()) as minutos_inactivo
            FROM users u
            LEFT JOIN delivery_current_location dcl ON u.id = dcl.delivery_id
            ORDER BY dcl.ultima_actualizacion DESC
        ");
        $stmt->execute();
        $deliveries = $stmt->fetchAll();
        
        // Clasificar deliveries por estado
        $activos = [];
        $inactivos = [];
        
        foreach ($deliveries as $delivery) {
            if ($delivery['latitude'] && $delivery['minutos_inactivo'] <= 10) {
                $activos[] = $delivery;
            } else {
                $inactivos[] = $delivery;
            }
        }
        
        sendResponse(true, 'Deliveries obtenidos', [
            'activos' => $activos,
            'inactivos' => $inactivos,
            'total' => count($deliveries),
            'total_activos' => count($activos),
            'total_inactivos' => count($inactivos)
        ]);
        
    } catch (Exception $e) {
        sendResponse(false, 'Error obteniendo deliveries: ' . $e->getMessage());
    }
}

/**
 * PASO 2: Calcular ETA (tiempo estimado de llegada)
 */
function calcularETA($pdo, $deliveryId, $pedidoId, $latActual, $lngActual) {
    try {
        // Obtener destino del pedido
        $stmt = $pdo->prepare("
            SELECT latitude, longitude 
            FROM pedidos 
            WHERE id = ? AND latitude IS NOT NULL AND longitude IS NOT NULL
        ");
        $stmt->execute([$pedidoId]);
        $destino = $stmt->fetch();
        
        if (!$destino) {
            return null;
        }
        
        // Calcular distancia en línea recta
        $distancia = calcularDistancia(
            $latActual, $lngActual,
            $destino['latitude'], $destino['longitude']
        );
        
        // Estimar velocidad promedio (30 km/h en ciudad)
        $velocidadPromedio = 30; // km/h
        
        // Calcular tiempo en minutos
        $tiempoHoras = $distancia / $velocidadPromedio;
        $tiempoMinutos = $tiempoHoras * 60;
        
        // Agregar buffer por tráfico (20%)
        $tiempoConTrafico = $tiempoMinutos * 1.2;
        
        return round($tiempoConTrafico);
        
    } catch (Exception $e) {
        error_log("Error calculando ETA: " . $e->getMessage());
        return null;
    }
}

/**
 * PASO 2: Calcular distancia entre dos puntos (fórmula de Haversine)
 */
function calcularDistancia($lat1, $lng1, $lat2, $lng2) {
    $radioTierra = 6371; // km
    
    $dLat = deg2rad($lat2 - $lat1);
    $dLng = deg2rad($lng2 - $lng1);
    
    $a = sin($dLat/2) * sin($dLat/2) +
         cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
         sin($dLng/2) * sin($dLng/2);
    
    $c = 2 * atan2(sqrt($a), sqrt(1-$a));
    
    return $radioTierra * $c;
}
?>
