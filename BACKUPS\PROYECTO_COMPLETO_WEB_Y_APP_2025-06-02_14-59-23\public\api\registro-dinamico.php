<?php
// Mostrar errores para depuración
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Configuración de CORS y headers
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header("Content-Type: application/json; charset=UTF-8");

// Responder a solicitudes OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Verificar método POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Método no permitido']);
    exit;
}

// Incluir configuración de base de datos
require_once '../db_config.php';

try {
    // Conectar a la base de datos usando la función que funciona
    $conn = connectDB();

    // Obtener datos del formulario
    $user_type = $_POST['user_type'] ?? '';
    $nombre = $_POST['nombre'] ?? '';
    $email = $_POST['email'] ?? '';
    $telefono = $_POST['telefono'] ?? '';
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';

    // Validaciones básicas
    if (empty($user_type) || empty($nombre) || empty($email) || empty($telefono) || empty($password)) {
        throw new Exception('Todos los campos básicos son requeridos');
    }

    if ($password !== $confirm_password) {
        throw new Exception('Las contraseñas no coinciden');
    }

    if (strlen($password) < 6) {
        throw new Exception('La contraseña debe tener al menos 6 caracteres');
    }

    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('El email no es válido');
    }

    // Verificar si el email ya existe
    $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
    $stmt->execute([$email]);
    if ($stmt->fetch()) {
        throw new Exception('El email ya está registrado');
    }

    // Hash de la contraseña
    $password_hash = password_hash($password, PASSWORD_DEFAULT);

    // Mapear tipos de usuario a role_id
    $role_mapping = [
        'usuario_regular' => 4,
        'taller_mecanico' => 2,
        'mecanico_independiente' => 3,
        'proveedor_repuestos' => 5
    ];

    $role_id = $role_mapping[$user_type] ?? null;
    if (!$role_id) {
        throw new Exception('Tipo de usuario no válido');
    }

    // Iniciar transacción
    $conn->beginTransaction();

    // Crear usuario usando la misma estructura que funciona en admin
    $stmt = $conn->prepare("INSERT INTO users (username, email, password, role_id, status, created_at) VALUES (:username, :email, :password, :role_id, 'active', NOW())");
    $stmt->execute([
        'username' => $email, // Usar email como username por ahora
        'email' => $email,
        'password' => $password_hash,
        'role_id' => $role_id
    ]);
    $user_id = $conn->lastInsertId();

    // Insertar datos específicos según el tipo de usuario usando las tablas que funcionan
    switch ($user_type) {
        case 'taller_mecanico':
            $username = $_POST['username'] ?? '';
            $direccion = $_POST['direccion'] ?? '';
            $rubro_principal = $_POST['rubro_principal'] ?? '';
            $datos_fiscales = $_POST['datos_fiscales'] ?? '';

            $stmt = $conn->prepare("INSERT INTO workshops (user_id, name, address, phone, description, maps_url, created_at) VALUES (:user_id, :name, :address, :phone, :description, :maps_url, NOW())");
            $stmt->execute([
                'user_id' => $user_id,
                'name' => $nombre,
                'address' => $direccion,
                'phone' => $telefono,
                'description' => $datos_fiscales,
                'maps_url' => ''
            ]);
            break;

        case 'proveedor_repuestos':
            $ubicacion_local = $_POST['ubicacion_local'] ?? '';
            $zonas_entrega = $_POST['zonas_entrega'] ?? '';
            $datos_contacto = $_POST['datos_contacto'] ?? '';
            $horarios_atencion = $_POST['horarios_atencion'] ?? '';

            $stmt = $conn->prepare("INSERT INTO suppliers (user_id, name, address, phone, description, maps_url, created_at) VALUES (:user_id, :name, :address, :phone, :description, :maps_url, NOW())");
            $stmt->execute([
                'user_id' => $user_id,
                'name' => $nombre,
                'address' => $ubicacion_local,
                'phone' => $telefono,
                'description' => "Zonas: $zonas_entrega\nContacto: $datos_contacto\nHorarios: $horarios_atencion",
                'maps_url' => ''
            ]);
            break;

        case 'mecanico_independiente':
            $especialidades = $_POST['especialidades'] ?? '';
            $experiencia_anos = $_POST['experiencia_anos'] ?? 0;
            $descripcion = $_POST['descripcion'] ?? '';
            $zona_trabajo = $_POST['zona_trabajo'] ?? '';
            $precio_hora = $_POST['precio_hora'] ?? null;

            $stmt = $conn->prepare("INSERT INTO mechanics (user_id, name, specialties, experience, address, phone, description, maps_url, created_at) VALUES (:user_id, :name, :specialties, :experience, :address, :phone, :description, :maps_url, NOW())");
            $stmt->execute([
                'user_id' => $user_id,
                'name' => $nombre,
                'specialties' => $especialidades,
                'experience' => $experiencia_anos . ' años',
                'address' => $zona_trabajo,
                'phone' => $telefono,
                'description' => $descripcion . ($precio_hora ? "\nPrecio por hora: $" . $precio_hora : ''),
                'maps_url' => ''
            ]);
            break;

        case 'usuario_regular':
            $stmt = $conn->prepare("INSERT INTO persons (user_id, first_name, last_name, address, phone, created_at) VALUES (:user_id, :first_name, :last_name, :address, :phone, NOW())");
            $stmt->execute([
                'user_id' => $user_id,
                'first_name' => $nombre,
                'last_name' => '',
                'address' => '',
                'phone' => $telefono
            ]);
            break;

        default:
            throw new Exception('Tipo de usuario no válido');
    }

    // Confirmar transacción
    $conn->commit();

    // Enviar SMS si es taller mecánico
    if ($user_type === 'taller_mecanico') {
        enviarSMSConfirmacion($telefono, $nombre);
    }

    // Respuesta exitosa
    echo json_encode([
        'success' => true,
        'message' => 'Usuario registrado exitosamente',
        'user_id' => $user_id,
        'user_type' => $user_type
    ]);

} catch (Exception $e) {
    // Rollback en caso de error
    if ($conn->inTransaction()) {
        $conn->rollback();
    }

    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

// Función para enviar SMS de confirmación
function enviarSMSConfirmacion($telefono, $nombre) {
    // Aquí se integraría con un servicio de SMS como Twilio, Nexmo, etc.
    // Por ahora, solo registramos en log
    error_log("SMS enviado a $telefono: Bienvenido $nombre a RepuMovil. Tu cuenta de taller ha sido creada exitosamente.");

    // Ejemplo de integración con Twilio (comentado):
    /*
    require_once '../vendor/autoload.php';
    use Twilio\Rest\Client;

    $sid = 'TU_TWILIO_SID';
    $token = 'TU_TWILIO_TOKEN';
    $twilio = new Client($sid, $token);

    $message = $twilio->messages->create(
        $telefono,
        [
            'from' => '+1234567890',
            'body' => "Bienvenido $nombre a RepuMovil. Tu cuenta de taller ha sido creada exitosamente."
        ]
    );
    */
}
?>
