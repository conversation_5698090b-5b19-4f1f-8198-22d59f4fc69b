import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  FlatList,
  StyleSheet,
  Alert,
  ActivityIndicator,
  Modal,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

// Tipos
interface Categoria {
  id: number;
  nombre: string;
  descripcion: string;
  icono: string;
  total_productos: number;
}

interface Repuesto {
  id: number;
  nombre: string;
  descripcion: string;
  categoria: string;
  marca: string;
  modelo: string;
  precio: number;
  stock: number;
  imagen_url: string;
  codigo_producto: string;
  estado: string;
  supplier_name: string;
}

interface BuscarRepuestosProps {
  visible: boolean;
  onClose: () => void;
  onAgregarAlChanguito: (repuesto: Repuesto) => void;
}

const BuscarRepuestos: React.FC<BuscarRepuestosProps> = ({
  visible,
  onClose,
  onAgregarAlChanguito,
}) => {
  const [busqueda, setBusqueda] = useState('');
  const [categorias, setCategorias] = useState<Categoria[]>([]);
  const [repuestos, setRepuestos] = useState<Repuesto[]>([]);
  const [categoriaSeleccionada, setCategoriaSeleccionada] = useState('');
  const [loading, setLoading] = useState(false);
  const [mostrarCategorias, setMostrarCategorias] = useState(true);

  // URL base de la API
  const API_BASE = 'http://localhost/mechanical-workshop/api';

  // Cargar categorías al abrir el modal
  useEffect(() => {
    if (visible) {
      cargarCategorias();
    }
  }, [visible]);

  // Cargar categorías desde la API
  const cargarCategorias = async () => {
    try {
      setLoading(true);
      const response = await fetch(`${API_BASE}/obtener-categorias.php`);
      const data = await response.json();
      
      if (data.success) {
        setCategorias(data.data);
      } else {
        Alert.alert('Error', 'No se pudieron cargar las categorías');
      }
    } catch (error) {
      console.error('Error cargando categorías:', error);
      Alert.alert('Error', 'Error de conexión');
    } finally {
      setLoading(false);
    }
  };

  // Buscar repuestos
  const buscarRepuestos = async (termino: string = '', categoria: string = '') => {
    try {
      setLoading(true);
      setMostrarCategorias(false);
      
      const params = new URLSearchParams();
      if (termino) params.append('search', termino);
      if (categoria) params.append('categoria', categoria);
      params.append('limit', '20');
      
      const response = await fetch(`${API_BASE}/repuestos.php?${params}`);
      const data = await response.json();
      
      if (data.success) {
        setRepuestos(data.data);
        setCategoriaSeleccionada(categoria);
      } else {
        Alert.alert('Error', 'No se pudieron cargar los repuestos');
      }
    } catch (error) {
      console.error('Error buscando repuestos:', error);
      Alert.alert('Error', 'Error de conexión');
    } finally {
      setLoading(false);
    }
  };

  // Buscar por categoría
  const buscarPorCategoria = (categoria: string) => {
    setBusqueda('');
    buscarRepuestos('', categoria);
  };

  // Volver a categorías
  const volverACategorias = () => {
    setMostrarCategorias(true);
    setRepuestos([]);
    setCategoriaSeleccionada('');
    setBusqueda('');
  };

  // Renderizar categoría
  const renderCategoria = ({ item }: { item: Categoria }) => (
    <TouchableOpacity
      style={styles.categoriaCard}
      onPress={() => buscarPorCategoria(item.nombre)}
    >
      <View style={styles.categoriaIcon}>
        <Ionicons name="construct" size={24} color="#FF6B35" />
      </View>
      <Text style={styles.categoriaNombre}>{item.nombre}</Text>
      <Text style={styles.categoriaCount}>{item.total_productos} productos</Text>
    </TouchableOpacity>
  );

  // Renderizar repuesto
  const renderRepuesto = ({ item }: { item: Repuesto }) => (
    <View style={styles.repuestoCard}>
      <View style={styles.repuestoInfo}>
        <Text style={styles.repuestoNombre}>{item.nombre}</Text>
        <Text style={styles.repuestoMarca}>Marca: {item.marca}</Text>
        <Text style={styles.repuestoProveedor}>Proveedor: {item.supplier_name}</Text>
        <Text style={styles.repuestoCodigo}>Código: {item.codigo_producto}</Text>
        <View style={[
          styles.stockBadge,
          { backgroundColor: item.stock > 10 ? '#4CAF50' : '#FF9800' }
        ]}>
          <Text style={styles.stockText}>
            Stock: {item.stock} unidades
          </Text>
        </View>
      </View>
      <View style={styles.repuestoPrecio}>
        <Text style={styles.precio}>${item.precio.toLocaleString()}</Text>
        <TouchableOpacity
          style={styles.btnAgregar}
          onPress={() => onAgregarAlChanguito(item)}
        >
          <Ionicons name="cart" size={16} color="white" />
          <Text style={styles.btnAgregarText}>Agregar</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.title}>
            {mostrarCategorias ? '🔍 Buscar Repuestos' : `📦 ${categoriaSeleccionada}`}
          </Text>
          {!mostrarCategorias && (
            <TouchableOpacity onPress={volverACategorias} style={styles.backButton}>
              <Ionicons name="arrow-back" size={24} color="#FF6B35" />
            </TouchableOpacity>
          )}
        </View>

        {/* Búsqueda */}
        <View style={styles.searchSection}>
          <View style={styles.searchContainer}>
            <Ionicons name="search" size={20} color="#666" style={styles.searchIcon} />
            <TextInput
              style={styles.searchInput}
              placeholder="Buscar repuestos..."
              value={busqueda}
              onChangeText={setBusqueda}
              onSubmitEditing={() => buscarRepuestos(busqueda, categoriaSeleccionada)}
            />
            {busqueda.length > 0 && (
              <TouchableOpacity onPress={() => setBusqueda('')}>
                <Ionicons name="close-circle" size={20} color="#666" />
              </TouchableOpacity>
            )}
          </View>
          <TouchableOpacity
            style={styles.searchButton}
            onPress={() => buscarRepuestos(busqueda, categoriaSeleccionada)}
          >
            <Text style={styles.searchButtonText}>Buscar</Text>
          </TouchableOpacity>
        </View>

        {/* Contenido */}
        <View style={styles.content}>
          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#FF6B35" />
              <Text style={styles.loadingText}>Cargando...</Text>
            </View>
          ) : mostrarCategorias ? (
            <FlatList
              data={categorias}
              renderItem={renderCategoria}
              keyExtractor={(item) => item.id.toString()}
              numColumns={2}
              contentContainerStyle={styles.categoriasGrid}
            />
          ) : (
            <FlatList
              data={repuestos}
              renderItem={renderRepuesto}
              keyExtractor={(item) => item.id.toString()}
              contentContainerStyle={styles.repuestosList}
              ListEmptyComponent={
                <Text style={styles.emptyText}>No se encontraron repuestos</Text>
              }
            />
          )}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  closeButton: {
    padding: 8,
  },
  backButton: {
    padding: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
    textAlign: 'center',
  },
  searchSection: {
    padding: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
    borderRadius: 8,
    paddingHorizontal: 12,
    marginBottom: 12,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: 16,
  },
  searchButton: {
    backgroundColor: '#FF6B35',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  searchButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
  },
  categoriasGrid: {
    padding: 16,
  },
  categoriaCard: {
    flex: 1,
    backgroundColor: 'white',
    margin: 8,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  categoriaIcon: {
    marginBottom: 8,
  },
  categoriaNombre: {
    fontSize: 14,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 4,
  },
  categoriaCount: {
    fontSize: 12,
    color: '#666',
  },
  repuestosList: {
    padding: 16,
  },
  repuestoCard: {
    backgroundColor: 'white',
    marginBottom: 12,
    padding: 16,
    borderRadius: 12,
    flexDirection: 'row',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  repuestoInfo: {
    flex: 1,
  },
  repuestoNombre: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  repuestoMarca: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  repuestoProveedor: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  repuestoCodigo: {
    fontSize: 12,
    color: '#999',
    marginBottom: 8,
  },
  stockBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  stockText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  repuestoPrecio: {
    alignItems: 'flex-end',
    justifyContent: 'space-between',
  },
  precio: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FF6B35',
    marginBottom: 8,
  },
  btnAgregar: {
    backgroundColor: '#FF6B35',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
  },
  btnAgregarText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
    marginLeft: 4,
  },
  emptyText: {
    textAlign: 'center',
    fontSize: 16,
    color: '#666',
    marginTop: 32,
  },
});

export default BuscarRepuestos;
