import React, { useState, useEffect, useRef } from 'react';
import {
  SafeAreaView,
  StatusBar,
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert,
  Dimensions,
  Modal,
  Animated,
  Vibration,
  ActivityIndicator,
} from 'react-native';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import * as Location from 'expo-location';
import { useNotifications } from '../../hooks/useNotifications';
import { PedidoNotification } from '../../services/NotificationService';
import LocationService, { LocationData } from '../../services/LocationService';
import PedidoNotificationModal, { PedidoData } from '../../components/PedidoNotificationModal';

const { width } = Dimensions.get('window');

interface Pedido {
  id: string;
  cliente: string;
  direccion: string;
  distancia: string;
  pago: string;
  tiempo_estimado: string;
  tipo: 'repuestos' | 'servicio';
  estado: 'disponible' | 'en_curso' | 'completado';
  ganancia: string;
}

export default function DeliveryDashboard() {
  const router = useRouter();
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedPedido, setSelectedPedido] = useState<Pedido | null>(null);
  const [estadoRepartidor, setEstadoRepartidor] = useState<'disponible' | 'ocupado' | 'desconectado'>('disponible');

  // PASO 1: Hook de notificaciones push reales
  const {
    isInitialized: notificationsReady,
    token: notificationToken,
    error: notificationError,
    isLoading: notificationsLoading,
    pendingNotifications,
    lastNotification,
    initializeNotifications,
    sendTestNotification,
    clearNotifications,
    markNotificationAsRead
  } = useNotifications();

  // PASO 2: Estados para tracking GPS
  const [trackingActivo, setTrackingActivo] = useState(false);
  const [ubicacionActual, setUbicacionActual] = useState<LocationData | null>(null);
  const [locationService] = useState(() => new LocationService());
  const [errorUbicacion, setErrorUbicacion] = useState<string | null>(null);

  // PASO 3: Estados para sistema de pedidos
  const [pedidoModalVisible, setPedidoModalVisible] = useState(false);
  const [pedidoActual, setPedidoActual] = useState<PedidoData | null>(null);
  const [pedidosDisponibles, setPedidosDisponibles] = useState<PedidoData[]>([]);
  const [cargandoPedidos, setCargandoPedidos] = useState(false);

  // PASO 4: Estados para notificaciones push
  const [notificationVisible, setNotificationVisible] = useState(false);
  const [newPedidoNotification, setNewPedidoNotification] = useState<Pedido | null>(null);
  const notificationAnimation = useRef(new Animated.Value(-100)).current;
  const pulseAnimation = useRef(new Animated.Value(1)).current;

  // PASO 5: Estados para timer de 60 segundos
  const [timerSeconds, setTimerSeconds] = useState(60);
  const [timerActive, setTimerActive] = useState(false);
  const [responding, setResponding] = useState(false);
  const timerInterval = useRef<NodeJS.Timeout | null>(null);

  // PASO 7: Estados para tracking en tiempo real
  const [location, setLocation] = useState<Location.LocationObject | null>(null);
  const [locationPermission, setLocationPermission] = useState<boolean>(false);
  const [trackingActive, setTrackingActive] = useState(false);
  const [pedidoActivo, setPedidoActivo] = useState<Pedido | null>(null);
  const locationInterval = useRef<NodeJS.Timeout | null>(null);

  const estadisticasHoy = {
    entregas: 3,
    ganancias: '$900',
    tiempo_activo: '4h 30m',
    calificacion: 4.8
  };

  // PASO 1: useEffect para inicializar notificaciones push
  useEffect(() => {
    const initNotifications = async () => {
      const deliveryId = 1; // ID del delivery actual (en producción vendría del login)
      const success = await initializeNotifications(deliveryId);

      if (success) {
        console.log('✅ Notificaciones push inicializadas correctamente');
        console.log('🎯 Token:', notificationToken);
      } else {
        console.error('❌ Error inicializando notificaciones:', notificationError);
      }
    };

    initNotifications();
  }, []);

  // PASO 2: useEffect para inicializar tracking GPS
  useEffect(() => {
    const initLocationService = async () => {
      try {
        console.log('📍 Inicializando servicio de ubicación...');
        const result = await locationService.initialize();

        if (result.success) {
          console.log('✅ Servicio de ubicación inicializado');
          // Iniciar tracking automáticamente si el delivery está disponible
          if (estadoRepartidor === 'disponible') {
            await iniciarTracking();
          }
        } else {
          console.error('❌ Error inicializando ubicación:', result.error);
          setErrorUbicacion(result.error || 'Error desconocido');
        }
      } catch (error) {
        console.error('❌ Error en inicialización de ubicación:', error);
        setErrorUbicacion('Error inicializando servicio de ubicación');
      }
    };

    initLocationService();
  }, []);

  // PASO 2: useEffect para manejar cambios de estado del repartidor
  useEffect(() => {
    if (estadoRepartidor === 'disponible' && !trackingActivo) {
      iniciarTracking();
    } else if (estadoRepartidor === 'desconectado' && trackingActivo) {
      detenerTracking();
    }
  }, [estadoRepartidor]);

  // PASO 3: useEffect para cargar pedidos disponibles
  useEffect(() => {
    if (estadoRepartidor === 'disponible') {
      cargarPedidosDisponibles();
      // Verificar pedidos cada 30 segundos
      const interval = setInterval(cargarPedidosDisponibles, 30000);
      return () => clearInterval(interval);
    }
  }, [estadoRepartidor]);

  // PASO 7: useEffect para inicializar tracking de ubicación
  useEffect(() => {
    requestLocationPermission();
  }, []);

  // PASO 7: Solicitar permisos de ubicación
  const requestLocationPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status === 'granted') {
        setLocationPermission(true);
        getCurrentLocation();
      } else {
        Alert.alert(
          '📍 Permisos de Ubicación',
          'RepuMovil necesita acceso a tu ubicación para el tracking en tiempo real de los pedidos.',
          [
            { text: 'Cancelar', style: 'cancel' },
            { text: 'Configurar', onPress: () => requestLocationPermission() }
          ]
        );
      }
    } catch (error) {
      console.error('Error solicitando permisos:', error);
    }
  };

  // PASO 7: Obtener ubicación actual
  const getCurrentLocation = async () => {
    try {
      const currentLocation = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
      });
      setLocation(currentLocation);
    } catch (error) {
      console.error('Error obteniendo ubicación:', error);
    }
  };

  // PASO 7: Iniciar tracking en tiempo real
  const startLocationTracking = () => {
    if (!locationPermission) {
      requestLocationPermission();
      return;
    }

    setTrackingActive(true);

    // Actualizar ubicación cada 10 segundos
    locationInterval.current = setInterval(async () => {
      try {
        const newLocation = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.High,
        });
        setLocation(newLocation);

        // Enviar ubicación al servidor
        await sendLocationUpdate(newLocation);

      } catch (error) {
        console.error('Error actualizando ubicación:', error);
      }
    }, 10000);

    Alert.alert(
      '📍 Tracking Activado',
      'Tu ubicación se está compartiendo en tiempo real con el cliente. El tracking se detendrá automáticamente al completar la entrega.',
      [{ text: 'Entendido' }]
    );
  };

  // PASO 7: Detener tracking
  const stopLocationTracking = () => {
    setTrackingActive(false);
    if (locationInterval.current) {
      clearInterval(locationInterval.current);
      locationInterval.current = null;
    }
  };

  // PASO 7: Enviar actualización de ubicación al servidor
  const sendLocationUpdate = async (locationData: Location.LocationObject) => {
    if (!pedidoActivo) return;

    try {
      const response = await fetch('http://localhost/mechanical-workshop/public/api/tracking.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'update_location',
          pedido_id: pedidoActivo.id,
          delivery_id: 1, // ID del delivery actual
          latitude: locationData.coords.latitude,
          longitude: locationData.coords.longitude,
          accuracy: locationData.coords.accuracy,
          speed: locationData.coords.speed,
          heading: locationData.coords.heading,
          timestamp: new Date().toISOString()
        })
      });

      const result = await response.json();
      if (!result.success) {
        console.error('Error enviando ubicación:', result.message);
      }
    } catch (error) {
      console.error('Error en sendLocationUpdate:', error);
    }
  };

  // PASO 4: useEffect para simular notificaciones push
  useEffect(() => {
    // Simular llegada de nuevo pedido cada 30 segundos si está disponible
    const interval = setInterval(() => {
      if (estadoRepartidor === 'disponible' && Math.random() > 0.7) {
        const newPedido: Pedido = {
          id: `${Date.now()}`,
          cliente: `Cliente ${Math.floor(Math.random() * 100)}`,
          direccion: `Dirección ${Math.floor(Math.random() * 1000)}`,
          distancia: `${(Math.random() * 5 + 1).toFixed(1)} km`,
          pago: `$${Math.floor(Math.random() * 2000 + 500)}`,
          tiempo_estimado: `${Math.floor(Math.random() * 20 + 10)} min`,
          tipo: Math.random() > 0.5 ? 'repuestos' : 'servicio',
          estado: 'disponible',
          ganancia: `$${Math.floor(Math.random() * 400 + 100)}`
        };

        showPushNotification(newPedido);
      }
    }, 30000);

    return () => clearInterval(interval);
  }, [estadoRepartidor]);

  // PASO 4: Función para mostrar notificación push
  const showPushNotification = async (pedido: Pedido) => {
    try {
      // Vibración fuerte para notificación
      Vibration.vibrate([0, 500, 200, 500, 200, 500]);

      // Mostrar notificación
      setNewPedidoNotification(pedido);
      setNotificationVisible(true);

      // Animación de entrada
      Animated.sequence([
        Animated.timing(notificationAnimation, {
          toValue: 20,
          duration: 500,
          useNativeDriver: true,
        }),
        // Animación de pulso
        Animated.loop(
          Animated.sequence([
            Animated.timing(pulseAnimation, {
              toValue: 1.05,
              duration: 1000,
              useNativeDriver: true,
            }),
            Animated.timing(pulseAnimation, {
              toValue: 1,
              duration: 1000,
              useNativeDriver: true,
            }),
          ])
        ),
      ]).start();

      // PASO 5: Iniciar timer de 60 segundos
      startResponseTimer();

      // Auto-ocultar después de 60 segundos si no responde
      setTimeout(() => {
        if (notificationVisible) {
          handleTimeout();
        }
      }, 60000);

    } catch (error) {
      console.log('Error en notificación:', error);
      // Mostrar notificación básica
      setNewPedidoNotification(pedido);
      setNotificationVisible(true);
      Vibration.vibrate([0, 250, 250, 250]);
    }
  };

  // PASO 4: Función para ocultar notificación
  const hideNotification = () => {
    Animated.timing(notificationAnimation, {
      toValue: -100,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setNotificationVisible(false);
      setNewPedidoNotification(null);
      pulseAnimation.setValue(1);
    });
  };

  // PASO 5: Función para iniciar timer de respuesta
  const startResponseTimer = () => {
    setTimerSeconds(60);
    setTimerActive(true);

    timerInterval.current = setInterval(() => {
      setTimerSeconds(prev => {
        if (prev <= 1) {
          handleTimeout();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  // PASO 5: Función para detener timer
  const stopResponseTimer = () => {
    setTimerActive(false);
    if (timerInterval.current) {
      clearInterval(timerInterval.current);
      timerInterval.current = null;
    }
  };

  // PASO 5: Función para manejar timeout (60 segundos sin respuesta)
  const handleTimeout = () => {
    stopResponseTimer();

    if (newPedidoNotification) {
      Alert.alert(
        '⏰ Tiempo Agotado',
        `El pedido #${newPedidoNotification.id} fue reasignado automáticamente por falta de respuesta.`,
        [{ text: 'Entendido', onPress: () => hideNotification() }]
      );

      // Simular reasignación automática
      console.log(`🔄 Pedido ${newPedidoNotification.id} reasignado por timeout`);
    } else {
      hideNotification();
    }
  };

  // PASO 5: Función para aceptar pedido con confirmación (notificaciones push)
  const aceptarPedidoNotificacion = async () => {
    if (!newPedidoNotification || responding) return;

    setResponding(true);
    stopResponseTimer();

    try {
      // Simular llamada a API
      await new Promise(resolve => setTimeout(resolve, 1500));

      Alert.alert(
        '✅ ¡Pedido Aceptado!',
        `Pedido #${newPedidoNotification.id} confirmado.\n\n📍 Dirígete al proveedor para recoger el pedido.\n💰 Ganancia: ${newPedidoNotification.ganancia}\n\n🗺️ El tracking en tiempo real se iniciará automáticamente.`,
        [
          {
            text: 'Ver Ruta',
            onPress: () => {
              hideNotification();
              setSelectedPedido(newPedidoNotification);
              setModalVisible(true);
            }
          }
        ]
      );

      // PASO 7: Configurar pedido activo e iniciar tracking
      setPedidoActivo(newPedidoNotification);
      setEstadoRepartidor('ocupado');

      // Iniciar tracking automáticamente
      setTimeout(() => {
        startLocationTracking();
      }, 1000);

    } catch (error) {
      Alert.alert('Error', 'No se pudo aceptar el pedido. Intenta de nuevo.');
      setResponding(false);
      startResponseTimer(); // Reiniciar timer
    }
  };

  // ELIMINADO - Función duplicada

  // ELIMINADO - Función muy compleja, usaremos la nueva

  // PASO 4: Función para aceptar desde notificación (actualizada)
  const aceptarDesdeNotificacion = () => {
    aceptarPedidoNotificacion();
  };

  // PASO 5: Función para rechazar desde notificación
  const rechazarDesdeNotificacion = () => {
    // Usar la función nueva de rechazar pedidos
    if (pedidoActual) {
      rechazarPedido(pedidoActual.id, 'no_especificado');
    }
  };

  // PASO 8: Función para renderizar botones según estado del pedido
  const renderEstadoButtons = (pedido: Pedido) => {
    if (!pedido) return null;

    // Simular estado actual del pedido (en producción vendría del servidor)
    const estadoActual = pedido.estado || 'asignado';

    return (
      <View style={styles.estadoButtonsContainer}>
        <Text style={styles.estadoCurrentLabel}>Estado actual: <Text style={styles.estadoCurrentText}>{getEstadoDisplayName(estadoActual)}</Text></Text>

        <View style={styles.estadoButtons}>
          {estadoActual === 'asignado' && (
            <TouchableOpacity
              style={styles.estadoButton}
              onPress={() => cambiarEstadoPedido(pedido.id, 'recogido')}
            >
              <Text style={styles.estadoButtonIcon}>📦</Text>
              <Text style={styles.estadoButtonText}>Marcar como Recogido</Text>
            </TouchableOpacity>
          )}

          {estadoActual === 'recogido' && (
            <TouchableOpacity
              style={styles.estadoButton}
              onPress={() => cambiarEstadoPedido(pedido.id, 'en_camino')}
            >
              <Text style={styles.estadoButtonIcon}>🚚</Text>
              <Text style={styles.estadoButtonText}>Salir En Camino</Text>
            </TouchableOpacity>
          )}

          {estadoActual === 'en_camino' && (
            <TouchableOpacity
              style={styles.estadoButton}
              onPress={() => cambiarEstadoPedido(pedido.id, 'entregado')}
            >
              <Text style={styles.estadoButtonIcon}>✅</Text>
              <Text style={styles.estadoButtonText}>Marcar como Entregado</Text>
            </TouchableOpacity>
          )}

          {estadoActual === 'entregado' && (
            <View style={styles.estadoCompletado}>
              <Text style={styles.estadoCompletadoIcon}>🎉</Text>
              <Text style={styles.estadoCompletadoText}>¡Pedido Completado!</Text>
            </View>
          )}
        </View>
      </View>
    );
  };

  // PASO 8: Función para obtener nombre del estado para mostrar
  const getEstadoDisplayName = (estado: string) => {
    switch (estado) {
      case 'asignado': return '📋 Asignado';
      case 'recogido': return '📦 Recogido';
      case 'en_camino': return '🚚 En Camino';
      case 'entregado': return '✅ Entregado';
      default: return '❓ Desconocido';
    }
  };

  // PASO 8: Función para cambiar estado del pedido
  const cambiarEstadoPedido = async (pedidoId: string, nuevoEstado: string) => {
    try {
      Alert.alert(
        '🔄 Actualizando Estado',
        `Cambiando estado a: ${getEstadoDisplayName(nuevoEstado)}`,
        [{ text: 'OK' }]
      );

      // Llamada a la API para actualizar estado
      const response = await fetch('http://localhost/mechanical-workshop/public/api/tracking.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'update_delivery_status',
          pedido_id: pedidoId,
          estado: nuevoEstado,
          delivery_id: 1, // ID del delivery actual
          notas: `Estado actualizado desde app móvil - ${new Date().toLocaleString()}`
        })
      });

      const result = await response.json();

      if (result.success) {
        // Actualizar estado local del pedido
        if (selectedPedido) {
          setSelectedPedido({
            ...selectedPedido,
            estado: nuevoEstado
          });
        }

        // Mostrar confirmación según el estado
        let mensaje = '';
        let siguientePaso = '';

        switch (nuevoEstado) {
          case 'recogido':
            mensaje = '📦 ¡Pedido recogido exitosamente!';
            siguientePaso = 'Ahora dirígete hacia el cliente. El tracking se actualizará automáticamente.';
            break;
          case 'en_camino':
            mensaje = '🚚 ¡En camino hacia el cliente!';
            siguientePaso = 'El cliente puede ver tu ubicación en tiempo real. ¡Conduce seguro!';
            break;
          case 'entregado':
            mensaje = '✅ ¡Entrega completada!';
            siguientePaso = '¡Excelente trabajo! Tu ganancia ha sido registrada.';
            // Detener tracking y liberar delivery
            stopLocationTracking();
            setEstadoRepartidor('disponible');
            setPedidoActivo(null);
            setModalVisible(false);
            break;
        }

        setTimeout(() => {
          Alert.alert(
            mensaje,
            siguientePaso,
            [{ text: 'Perfecto' }]
          );
        }, 1000);

        console.log(`✅ Estado actualizado: ${pedidoId} → ${nuevoEstado}`);

      } else {
        Alert.alert(
          '❌ Error',
          `No se pudo actualizar el estado: ${result.message}`,
          [{ text: 'Reintentar', onPress: () => cambiarEstadoPedido(pedidoId, nuevoEstado) }]
        );
      }

    } catch (error) {
      Alert.alert(
        '❌ Error de Conexión',
        'No se pudo conectar con el servidor. Verifica tu conexión e intenta de nuevo.',
        [
          { text: 'Reintentar', onPress: () => cambiarEstadoPedido(pedidoId, nuevoEstado) },
          { text: 'Cancelar' }
        ]
      );
      console.error('Error cambiando estado:', error);
    }
  };

  const toggleEstado = () => {
    if (estadoRepartidor === 'disponible') {
      setEstadoRepartidor('desconectado');
    } else {
      setEstadoRepartidor('disponible');
    }
  };

  const aceptarPedidoModal = (pedido: Pedido) => {
    setSelectedPedido(pedido);
    setModalVisible(true);
  };

  const confirmarPedido = () => {
    if (selectedPedido) {
      Alert.alert(
        '🎉 ¡Pedido Aceptado!',
        `Has aceptado el pedido para ${selectedPedido.cliente}. Dirígete al punto de recogida.`,
        [
          {
            text: 'Ver Ruta',
            onPress: () => {
              setModalVisible(false);
              setEstadoRepartidor('ocupado');
              // Aquí se abriría el mapa con la ruta
              Alert.alert('🗺️ Navegación', 'Abriendo mapa con la ruta...');
            }
          }
        ]
      );
    }
  };

  const getEstadoColor = () => {
    switch (estadoRepartidor) {
      case 'disponible': return '#28a745';
      case 'ocupado': return '#ffc107';
      case 'desconectado': return '#6c757d';
      default: return '#6c757d';
    }
  };

  const getEstadoText = () => {
    switch (estadoRepartidor) {
      case 'disponible': return 'Disponible';
      case 'ocupado': return 'En Entrega';
      case 'desconectado': return 'Desconectado';
      default: return 'Desconectado';
    }
  };

  // PASO 2: Función para iniciar tracking GPS
  const iniciarTracking = async () => {
    try {
      console.log('🚀 Iniciando tracking GPS...');
      const deliveryId = 1; // En producción vendría del login
      const pedidoActivo = pedidos.find(p => p.estado === 'en_camino');

      const success = await locationService.startLocationTracking(
        deliveryId,
        pedidoActivo?.id,
        (location: LocationData) => {
          console.log('📍 Nueva ubicación:', location);
          setUbicacionActual(location);
          setErrorUbicacion(null);
        }
      );

      if (success) {
        setTrackingActivo(true);
        console.log('✅ Tracking GPS iniciado correctamente');
      } else {
        setErrorUbicacion('No se pudo iniciar el tracking GPS');
      }
    } catch (error) {
      console.error('❌ Error iniciando tracking:', error);
      setErrorUbicacion('Error iniciando tracking GPS');
    }
  };

  // PASO 2: Función para detener tracking GPS
  const detenerTracking = async () => {
    try {
      console.log('🛑 Deteniendo tracking GPS...');
      await locationService.stopLocationTracking();
      setTrackingActivo(false);
      setUbicacionActual(null);
      console.log('✅ Tracking GPS detenido');
    } catch (error) {
      console.error('❌ Error deteniendo tracking:', error);
    }
  };

  // PASO 3: Función para cargar pedidos disponibles
  const cargarPedidosDisponibles = async () => {
    try {
      setCargandoPedidos(true);
      const deliveryId = 1; // En producción vendría del login

      const response = await fetch(`http://localhost/mechanical-workshop/public/api/pedidos-delivery.php?action=get_pedidos_disponibles&delivery_id=${deliveryId}&limite=5`);
      const result = await response.json();

      if (result.success && result.data.length > 0) {
        const pedidosFormateados = result.data.map((pedido: any) => ({
          id: pedido.id,
          cliente: pedido.cliente_nombre || pedido.cliente,
          direccion: pedido.direccion_entrega,
          total: parseFloat(pedido.total),
          distancia: pedido.distancia,
          tiempo_estimado: pedido.tiempo_estimado,
          items: pedido.items || [],
          telefono: pedido.cliente_telefono,
          observaciones: pedido.observaciones,
          prioridad: pedido.prioridad || 'normal',
          ganancia_estimada: pedido.ganancia_estimada || Math.round(pedido.total * 0.15),
        }));

        setPedidosDisponibles(pedidosFormateados);

        // Si hay pedidos nuevos y el delivery está disponible, mostrar el primero
        if (pedidosFormateados.length > 0 && estadoRepartidor === 'disponible' && !pedidoModalVisible) {
          mostrarPedido(pedidosFormateados[0]);
        }
      } else {
        setPedidosDisponibles([]);
      }
    } catch (error) {
      console.error('❌ Error cargando pedidos:', error);
    } finally {
      setCargandoPedidos(false);
    }
  };

  // PASO 3: Función para mostrar pedido en modal
  const mostrarPedido = (pedido: PedidoData) => {
    setPedidoActual(pedido);
    setPedidoModalVisible(true);
  };

  // PASO 3: Función para aceptar pedido
  const aceptarPedido = async (pedidoId: number) => {
    try {
      const deliveryId = 1; // En producción vendría del login

      const response = await fetch('http://localhost/mechanical-workshop/public/api/pedidos-delivery.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'aceptar_pedido',
          pedido_id: pedidoId,
          delivery_id: deliveryId,
          ubicacion_delivery: ubicacionActual
        }),
      });

      const result = await response.json();

      if (result.success) {
        Alert.alert(
          '✅ Pedido Aceptado',
          'Has aceptado el pedido exitosamente. ¡Dirígete al punto de recogida!',
          [{ text: 'OK' }]
        );

        // Cambiar estado a ocupado
        setEstadoRepartidor('ocupado');

        // Recargar pedidos
        cargarPedidosDisponibles();

        console.log('✅ Pedido aceptado:', result.data);
      } else {
        Alert.alert(
          '❌ Error',
          result.message || 'No se pudo aceptar el pedido',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('❌ Error aceptando pedido:', error);
      Alert.alert(
        '❌ Error de Conexión',
        'No se pudo conectar con el servidor. Verifica tu conexión.',
        [{ text: 'OK' }]
      );
    }
  };

  // PASO 3: Función para rechazar pedido
  const rechazarPedido = async (pedidoId: number, motivo?: string) => {
    try {
      const deliveryId = 1; // En producción vendría del login

      const response = await fetch('http://localhost/mechanical-workshop/public/api/pedidos-delivery.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'rechazar_pedido',
          pedido_id: pedidoId,
          delivery_id: deliveryId,
          motivo: motivo || 'no_especificado',
          observaciones: `Rechazado desde app móvil. Motivo: ${motivo}`
        }),
      });

      const result = await response.json();

      if (result.success) {
        console.log('❌ Pedido rechazado:', result.data);

        // Recargar pedidos para mostrar el siguiente
        cargarPedidosDisponibles();
      } else {
        console.error('❌ Error rechazando pedido:', result.message);
      }
    } catch (error) {
      console.error('❌ Error rechazando pedido:', error);
    }
  };

  // PASO 1: Función para enviar notificación de prueba
  const enviarNotificacionPrueba = async () => {
    if (!notificationsReady) {
      Alert.alert(
        '⚠️ Notificaciones no listas',
        'Las notificaciones push aún no están inicializadas. Espera un momento e intenta de nuevo.',
        [{ text: 'OK' }]
      );
      return;
    }

    const pedidoPrueba: PedidoNotification = {
      id: `test_${Date.now()}`,
      cliente: 'Taller de Prueba',
      direccion: 'Av. Test 123, Centro',
      total: 1500,
      distancia: 2.5,
      tiempo_estimado: 15,
      tipo: 'nuevo_pedido',
      prioridad: 'alta'
    };

    try {
      await sendTestNotification(pedidoPrueba);
      Alert.alert(
        '✅ Notificación Enviada',
        'Se envió una notificación de prueba. Deberías verla en unos segundos.',
        [{ text: 'OK' }]
      );
    } catch (error) {
      Alert.alert(
        '❌ Error',
        'No se pudo enviar la notificación de prueba.',
        [{ text: 'OK' }]
      );
    }
  };

  // PASO 9: Función para ver calificaciones
  const verCalificaciones = () => {
    Alert.alert(
      '⭐ Mis Calificaciones',
      `Calificación promedio: ${estadisticasHoy.calificacion} ⭐\n\nTotal de calificaciones: 24\nEste mes: 8 calificaciones\nTasa de respuesta: 75%\n\n¿Quieres ver todas tus calificaciones y responder a los comentarios?`,
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Ver Todas',
          onPress: () => {
            // En producción, navegar a la página de calificaciones
            Alert.alert(
              '🚀 Redirigiendo...',
              'Te llevamos a la página de calificaciones donde puedes ver todos los comentarios y responder a tus clientes.',
              [{ text: 'OK' }]
            );
          }
        }
      ]
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#FF6B35" />
      
      {/* Header */}
      <LinearGradient
        colors={['#FF6B35', '#E53E3E']}
        style={styles.header}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={styles.headerContent}>
          <View style={styles.logoContainer}>
            <Text style={styles.logoIcon}>🏍️</Text>
          </View>
          <Text style={styles.logoText}>
            <Text style={styles.logoRepu}>Repu</Text>
            <Text style={styles.logoMovil}>Movil</Text>
          </Text>
          <Text style={styles.logoSubtitle}>DELIVERY</Text>
          
          {/* Estado del repartidor */}
          <TouchableOpacity style={styles.estadoContainer} onPress={toggleEstado}>
            <View style={[styles.estadoIndicator, { backgroundColor: getEstadoColor() }]} />
            <Text style={styles.estadoText}>{getEstadoText()}</Text>
          </TouchableOpacity>

          {/* PASO 1: Indicador de estado de notificaciones push */}
          <TouchableOpacity style={styles.notificationContainer} onPress={enviarNotificacionPrueba}>
            <Text style={styles.notificationIcon}>
              {notificationsReady ? '🔔' : notificationsLoading ? '⏳' : '🔕'}
            </Text>
            <Text style={styles.notificationText}>
              {notificationsReady ? 'Push Activo' : notificationsLoading ? 'Configurando...' : 'Push Inactivo'}
            </Text>
            {pendingNotifications.length > 0 && (
              <View style={styles.notificationBadge}>
                <Text style={styles.notificationBadgeText}>{pendingNotifications.length}</Text>
              </View>
            )}
          </TouchableOpacity>

          {/* PASO 2: Indicador de estado de tracking GPS */}
          <TouchableOpacity
            style={[styles.trackingContainer, { backgroundColor: trackingActivo ? 'rgba(40, 167, 69, 0.2)' : 'rgba(220, 53, 69, 0.2)' }]}
            onPress={trackingActivo ? detenerTracking : iniciarTracking}
          >
            <Text style={styles.trackingIcon}>
              {trackingActivo ? '📍' : errorUbicacion ? '❌' : '📍'}
            </Text>
            <Text style={styles.trackingText}>
              {trackingActivo ? 'GPS Activo' : errorUbicacion ? 'GPS Error' : 'GPS Inactivo'}
            </Text>
            {ubicacionActual && (
              <Text style={styles.trackingAccuracy}>
                ±{ubicacionActual.accuracy?.toFixed(0)}m
              </Text>
            )}
          </TouchableOpacity>
        </View>
      </LinearGradient>

      {/* PASO 7: Indicador de tracking en tiempo real */}
      {trackingActive && (
        <View style={styles.trackingBanner}>
          <LinearGradient
            colors={['#28a745', '#20c997']}
            style={styles.trackingGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
          >
            <View style={styles.trackingContent}>
              <View style={styles.trackingIconContainer}>
                <Animated.View style={[styles.trackingPulse, { transform: [{ scale: pulseAnimation }] }]} />
                <Text style={styles.trackingIcon}>📍</Text>
              </View>
              <View style={styles.trackingInfo}>
                <Text style={styles.trackingTitle}>Tracking Activo</Text>
                <Text style={styles.trackingSubtitle}>
                  {location ?
                    `Precisión: ${location.coords.accuracy?.toFixed(0)}m${location.coords.speed ? ` • ${(location.coords.speed * 3.6).toFixed(0)} km/h` : ''}` :
                    'Obteniendo ubicación...'
                  }
                </Text>
              </View>
              <TouchableOpacity style={styles.trackingStopButton} onPress={stopLocationTracking}>
                <Text style={styles.trackingStopText}>⏹️</Text>
              </TouchableOpacity>
            </View>
          </LinearGradient>
        </View>
      )}

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Estadísticas del día */}
        <View style={styles.statsContainer}>
          <Text style={styles.sectionTitle}>📊 Resumen de Hoy</Text>
          <View style={styles.statsGrid}>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>{estadisticasHoy.entregas}</Text>
              <Text style={styles.statLabel}>Entregas</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>{estadisticasHoy.ganancias}</Text>
              <Text style={styles.statLabel}>Ganancias</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>{estadisticasHoy.tiempo_activo}</Text>
              <Text style={styles.statLabel}>Tiempo Activo</Text>
            </View>
            <TouchableOpacity style={styles.statCard} onPress={verCalificaciones}>
              <Text style={styles.statNumber}>⭐ {estadisticasHoy.calificacion}</Text>
              <Text style={styles.statLabel}>Calificación</Text>
              <Text style={styles.statSubLabel}>👆 Ver todas</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Pedidos disponibles */}
        <View style={styles.pedidosSection}>
          <Text style={styles.sectionTitle}>
            🚚 Pedidos Disponibles ({pedidosDisponibles.length})
          </Text>
          
          {estadoRepartidor === 'desconectado' ? (
            <View style={styles.desconectadoMessage}>
              <Text style={styles.desconectadoIcon}>😴</Text>
              <Text style={styles.desconectadoText}>
                Estás desconectado. Actívate para ver pedidos disponibles.
              </Text>
              <TouchableOpacity style={styles.activarButton} onPress={toggleEstado}>
                <Text style={styles.activarButtonText}>🚀 Activarme</Text>
              </TouchableOpacity>
            </View>
          ) : estadoRepartidor === 'ocupado' ? (
            <View style={styles.ocupadoMessage}>
              <Text style={styles.ocupadoIcon}>🚚</Text>
              <Text style={styles.ocupadoText}>
                Tienes una entrega en curso. Complétala para ver más pedidos.
              </Text>
            </View>
          ) : (
            pedidosDisponibles.map((pedido) => (
              <View key={pedido.id} style={styles.pedidoCard}>
                <View style={styles.pedidoHeader}>
                  <Text style={styles.pedidoCliente}>{pedido.cliente}</Text>
                  <View style={styles.tipoBadge}>
                    <Text style={styles.tipoText}>
                      {pedido.tipo === 'repuestos' ? '🔧 Repuestos' : '⚙️ Servicio'}
                    </Text>
                  </View>
                </View>
                
                <Text style={styles.pedidoDireccion}>📍 {pedido.direccion}</Text>
                
                <View style={styles.pedidoDetails}>
                  <View style={styles.detailItem}>
                    <Text style={styles.detailIcon}>📏</Text>
                    <Text style={styles.detailText}>{pedido.distancia}</Text>
                  </View>
                  <View style={styles.detailItem}>
                    <Text style={styles.detailIcon}>⏱️</Text>
                    <Text style={styles.detailText}>{pedido.tiempo_estimado}</Text>
                  </View>
                  <View style={styles.detailItem}>
                    <Text style={styles.detailIcon}>💰</Text>
                    <Text style={styles.detailText}>{pedido.ganancia}</Text>
                  </View>
                </View>

                <TouchableOpacity
                  style={styles.aceptarButton}
                  onPress={() => aceptarPedidoModal(pedido)}
                >
                  <Text style={styles.aceptarButtonText}>✅ Aceptar Pedido</Text>
                </TouchableOpacity>
              </View>
            ))
          )}
        </View>

        {/* Acciones rápidas */}
        <View style={styles.accionesSection}>
          <Text style={styles.sectionTitle}>⚡ Acciones Rápidas</Text>
          
          <View style={styles.accionesGrid}>
            <TouchableOpacity
              style={styles.accionCard}
              onPress={() => router.push('/delivery-mapa')}
            >
              <Text style={styles.accionIcon}>🗺️</Text>
              <Text style={styles.accionText}>Mapa</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.accionCard}
              onPress={() => router.push('/delivery-historial')}
            >
              <Text style={styles.accionIcon}>📊</Text>
              <Text style={styles.accionText}>Historial</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.accionCard}
              onPress={() => router.push('/delivery-ganancias')}
            >
              <Text style={styles.accionIcon}>💰</Text>
              <Text style={styles.accionText}>Ganancias</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.accionCard}
              onPress={() => router.push('/delivery-perfil')}
            >
              <Text style={styles.accionIcon}>⚙️</Text>
              <Text style={styles.accionText}>Perfil</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>

      {/* Modal de confirmación */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>🚚 Confirmar Pedido</Text>
            
            {selectedPedido && (
              <>
                <Text style={styles.modalCliente}>{selectedPedido.cliente}</Text>
                <Text style={styles.modalDireccion}>📍 {selectedPedido.direccion}</Text>
                
                <View style={styles.modalDetails}>
                  <Text style={styles.modalDetailText}>💰 Ganancia: {selectedPedido.ganancia}</Text>
                  <Text style={styles.modalDetailText}>📏 Distancia: {selectedPedido.distancia}</Text>
                  <Text style={styles.modalDetailText}>⏱️ Tiempo: {selectedPedido.tiempo_estimado}</Text>
                </View>

                {/* PASO 8: Botones de cambio de estado */}
                {renderEstadoButtons(selectedPedido)}

                <TouchableOpacity
                  style={styles.cancelButton}
                  onPress={() => setModalVisible(false)}
                >
                  <Text style={styles.cancelButtonText}>❌ Cerrar</Text>
                </TouchableOpacity>
              </>
            )}
          </View>
        </View>
      </Modal>

      {/* PASO 4: Notificación Push */}
      {notificationVisible && newPedidoNotification && (
        <Animated.View
          style={[
            styles.pushNotification,
            {
              transform: [
                { translateY: notificationAnimation },
                { scale: pulseAnimation }
              ]
            }
          ]}
        >
          <LinearGradient
            colors={['#FF6B35', '#FFA500']}
            style={styles.notificationGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <View style={styles.notificationHeader}>
              <Text style={styles.notificationTitle}>🚨 ¡NUEVO PEDIDO!</Text>
              <View style={styles.timerContainer}>
                <Text style={styles.timerText}>⏱️ {timerSeconds}s</Text>
              </View>
            </View>

            {/* PASO 5: Barra de progreso del timer */}
            <View style={styles.timerProgressContainer}>
              <Animated.View
                style={[
                  styles.timerProgressBar,
                  {
                    width: `${(timerSeconds / 60) * 100}%`,
                    backgroundColor: timerSeconds > 20 ? '#28a745' : timerSeconds > 10 ? '#ffc107' : '#dc3545'
                  }
                ]}
              />
            </View>

            <View style={styles.notificationContent}>
              <Text style={styles.notificationCliente}>{newPedidoNotification.cliente}</Text>
              <Text style={styles.notificationDireccion}>📍 {newPedidoNotification.direccion}</Text>

              <View style={styles.notificationDetails}>
                <Text style={styles.notificationDetail}>💰 {newPedidoNotification.ganancia}</Text>
                <Text style={styles.notificationDetail}>📏 {newPedidoNotification.distancia}</Text>
                <Text style={styles.notificationDetail}>⏱️ {newPedidoNotification.tiempo_estimado}</Text>
              </View>
            </View>

            {/* PASO 5: Botones de acción mejorados */}
            <View style={styles.notificationActions}>
              <TouchableOpacity
                style={[styles.rejectNotificationButton, responding && styles.buttonDisabled]}
                onPress={rechazarDesdeNotificacion}
                disabled={responding}
              >
                {responding ? (
                  <ActivityIndicator size="small" color="white" />
                ) : (
                  <>
                    <Text style={styles.rejectNotificationIcon}>❌</Text>
                    <Text style={styles.rejectNotificationText}>RECHAZAR</Text>
                  </>
                )}
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.acceptNotificationButton, responding && styles.buttonDisabled]}
                onPress={aceptarDesdeNotificacion}
                disabled={responding}
              >
                {responding ? (
                  <ActivityIndicator size="small" color="white" />
                ) : (
                  <>
                    <Text style={styles.acceptNotificationIcon}>✅</Text>
                    <Text style={styles.acceptNotificationText}>ACEPTAR</Text>
                  </>
                )}
              </TouchableOpacity>
            </View>

            {/* PASO 5: Mensaje de urgencia */}
            <View style={styles.urgencyMessage}>
              <Text style={styles.urgencyText}>
                {timerSeconds > 30 ? '⚡ ¡Responde rápido para no perder el pedido!' :
                 timerSeconds > 10 ? '⚠️ ¡Tiempo limitado! Decide ahora.' :
                 '🚨 ¡ÚLTIMOS SEGUNDOS!'}
              </Text>
            </View>
          </LinearGradient>
        </Animated.View>
      )}

      {/* PASO 3: Modal de notificación de pedido */}
      <PedidoNotificationModal
        visible={pedidoModalVisible}
        pedido={pedidoActual}
        onAccept={aceptarPedido}
        onReject={rechazarPedido}
        onClose={() => {
          setPedidoModalVisible(false);
          setPedidoActual(null);
        }}
        timeoutSeconds={30}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    paddingTop: 20,
    paddingBottom: 25,
    paddingHorizontal: 20,
  },
  headerContent: {
    alignItems: 'center',
  },
  logoContainer: {
    width: 60,
    height: 60,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
  },
  logoIcon: {
    fontSize: 30,
  },
  logoText: {
    fontSize: 28,
    fontWeight: '800',
    color: 'white',
    marginBottom: 5,
  },
  logoRepu: {
    color: '#ffffff',
  },
  logoMovil: {
    color: '#FFF3E0',
  },
  logoSubtitle: {
    fontSize: 14,
    fontWeight: '600',
    color: 'white',
    letterSpacing: 1.5,
    marginBottom: 15,
  },
  estadoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
  },
  estadoIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  estadoText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  // PASO 1: Estilos para indicador de notificaciones
  notificationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
    marginTop: 10,
    position: 'relative',
  },
  notificationIcon: {
    fontSize: 16,
    marginRight: 6,
  },
  notificationText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '500',
  },
  notificationBadge: {
    position: 'absolute',
    top: -5,
    right: -5,
    backgroundColor: '#dc3545',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  notificationBadgeText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
  },
  // PASO 2: Estilos para indicador de tracking GPS
  trackingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
    marginTop: 8,
  },
  trackingIcon: {
    fontSize: 16,
    marginRight: 6,
  },
  trackingText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '500',
    marginRight: 6,
  },
  trackingAccuracy: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 10,
    fontWeight: '400',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  statsContainer: {
    marginTop: 20,
    marginBottom: 25,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#2D3748',
    marginBottom: 15,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statCard: {
    backgroundColor: 'white',
    width: (width - 60) / 2,
    padding: 20,
    borderRadius: 15,
    alignItems: 'center',
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  statNumber: {
    fontSize: 20,
    fontWeight: '800',
    color: '#FF6B35',
    marginBottom: 5,
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
  },
  statSubLabel: {
    fontSize: 10,
    color: '#FF6B35',
    fontWeight: '600',
    marginTop: 2,
  },
  pedidosSection: {
    marginBottom: 25,
  },
  desconectadoMessage: {
    backgroundColor: 'white',
    padding: 30,
    borderRadius: 15,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  desconectadoIcon: {
    fontSize: 50,
    marginBottom: 15,
  },
  desconectadoText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 22,
  },
  activarButton: {
    backgroundColor: '#28a745',
    paddingHorizontal: 25,
    paddingVertical: 12,
    borderRadius: 25,
  },
  activarButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  ocupadoMessage: {
    backgroundColor: 'white',
    padding: 30,
    borderRadius: 15,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  ocupadoIcon: {
    fontSize: 50,
    marginBottom: 15,
  },
  ocupadoText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 22,
  },
  pedidoCard: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 15,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  pedidoHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  pedidoCliente: {
    fontSize: 16,
    fontWeight: '700',
    color: '#2D3748',
    flex: 1,
  },
  tipoBadge: {
    backgroundColor: '#E3F2FD',
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
  },
  tipoText: {
    fontSize: 12,
    color: '#1976D2',
    fontWeight: '600',
  },
  pedidoDireccion: {
    fontSize: 14,
    color: '#666',
    marginBottom: 15,
    lineHeight: 20,
  },
  pedidoDetails: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 15,
  },
  detailItem: {
    alignItems: 'center',
  },
  detailIcon: {
    fontSize: 16,
    marginBottom: 5,
  },
  detailText: {
    fontSize: 12,
    color: '#666',
    fontWeight: '600',
  },
  aceptarButton: {
    backgroundColor: '#28a745',
    paddingVertical: 12,
    borderRadius: 10,
    alignItems: 'center',
  },
  aceptarButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '700',
  },
  accionesSection: {
    marginBottom: 30,
  },
  accionesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  accionCard: {
    backgroundColor: 'white',
    width: (width - 60) / 2,
    padding: 20,
    borderRadius: 15,
    alignItems: 'center',
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  accionIcon: {
    fontSize: 30,
    marginBottom: 10,
  },
  accionText: {
    fontSize: 14,
    color: '#2D3748',
    fontWeight: '600',
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: 'white',
    margin: 20,
    borderRadius: 20,
    padding: 25,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#2D3748',
    marginBottom: 15,
  },
  modalCliente: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FF6B35',
    marginBottom: 10,
  },
  modalDireccion: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 20,
  },
  modalDetails: {
    alignItems: 'center',
    marginBottom: 25,
  },
  modalDetailText: {
    fontSize: 14,
    color: '#2D3748',
    marginBottom: 5,
    fontWeight: '500',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  cancelButton: {
    backgroundColor: '#f8f9fa',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 10,
    flex: 1,
    marginRight: 10,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  cancelButtonText: {
    color: '#666',
    fontSize: 14,
    fontWeight: '600',
  },
  confirmButton: {
    backgroundColor: '#28a745',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 10,
    flex: 1,
    marginLeft: 10,
    alignItems: 'center',
  },
  confirmButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '700',
  },

  // PASO 4: Estilos para notificación push
  pushNotification: {
    position: 'absolute',
    top: 0,
    left: 20,
    right: 20,
    zIndex: 1000,
    elevation: 10,
  },
  notificationGradient: {
    borderRadius: 15,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 5 },
    shadowOpacity: 0.3,
    shadowRadius: 10,
    elevation: 15,
  },
  notificationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  notificationTitle: {
    color: 'white',
    fontSize: 18,
    fontWeight: '800',
    textShadowColor: 'rgba(0,0,0,0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
    flex: 1,
  },
  // PASO 5: Estilos para timer
  timerContainer: {
    backgroundColor: 'rgba(255,255,255,0.3)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
    borderWidth: 2,
    borderColor: 'rgba(255,255,255,0.5)',
  },
  timerText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '900',
    textShadowColor: 'rgba(0,0,0,0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  timerProgressContainer: {
    height: 4,
    backgroundColor: 'rgba(255,255,255,0.3)',
    borderRadius: 2,
    marginBottom: 15,
    overflow: 'hidden',
  },
  timerProgressBar: {
    height: '100%',
    borderRadius: 2,
    transition: 'width 1s ease-in-out',
  },
  notificationContent: {
    marginBottom: 15,
  },
  notificationCliente: {
    color: 'white',
    fontSize: 16,
    fontWeight: '700',
    marginBottom: 5,
  },
  notificationDireccion: {
    color: 'rgba(255,255,255,0.9)',
    fontSize: 14,
    marginBottom: 10,
  },
  notificationDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  notificationDetail: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  notificationActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 15,
    marginBottom: 10,
  },
  rejectNotificationButton: {
    flex: 1,
    backgroundColor: 'rgba(220, 53, 69, 0.9)',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'rgba(255,255,255,0.4)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.3,
    shadowRadius: 5,
    elevation: 5,
  },
  rejectNotificationIcon: {
    fontSize: 20,
    marginBottom: 2,
  },
  rejectNotificationText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '800',
    textShadowColor: 'rgba(0,0,0,0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  acceptNotificationButton: {
    flex: 1,
    backgroundColor: '#28a745',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'rgba(255,255,255,0.4)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.3,
    shadowRadius: 5,
    elevation: 5,
  },
  acceptNotificationIcon: {
    fontSize: 20,
    marginBottom: 2,
  },
  acceptNotificationText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '800',
    textShadowColor: 'rgba(0,0,0,0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  buttonDisabled: {
    opacity: 0.6,
    transform: [{ scale: 0.95 }],
  },
  urgencyMessage: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.3)',
  },
  urgencyText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '700',
    textAlign: 'center',
    textShadowColor: 'rgba(0,0,0,0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },

  // PASO 7: Estilos para tracking banner
  trackingBanner: {
    marginHorizontal: 20,
    marginTop: 10,
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  trackingGradient: {
    padding: 15,
  },
  trackingContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  trackingIconContainer: {
    position: 'relative',
    marginRight: 15,
  },
  trackingPulse: {
    position: 'absolute',
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255,255,255,0.3)',
    top: -5,
    left: -5,
  },
  trackingIcon: {
    fontSize: 24,
    width: 30,
    height: 30,
    textAlign: 'center',
    lineHeight: 30,
  },
  trackingInfo: {
    flex: 1,
  },
  trackingTitle: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  trackingSubtitle: {
    color: 'rgba(255,255,255,0.9)',
    fontSize: 12,
  },
  trackingStopButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    width: 35,
    height: 35,
    borderRadius: 17.5,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.3)',
  },
  trackingStopText: {
    fontSize: 16,
  },

  // PASO 8: Estilos para botones de estado
  estadoButtonsContainer: {
    width: '100%',
    marginBottom: 20,
  },
  estadoCurrentLabel: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 15,
  },
  estadoCurrentText: {
    fontWeight: 'bold',
    color: '#FF6B35',
  },
  estadoButtons: {
    width: '100%',
  },
  estadoButton: {
    backgroundColor: '#FF6B35',
    paddingVertical: 15,
    paddingHorizontal: 20,
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  estadoButtonIcon: {
    fontSize: 20,
    marginRight: 10,
  },
  estadoButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  estadoCompletado: {
    backgroundColor: '#28a745',
    paddingVertical: 15,
    paddingHorizontal: 20,
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 10,
  },
  estadoCompletadoIcon: {
    fontSize: 20,
    marginRight: 10,
  },
  estadoCompletadoText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
