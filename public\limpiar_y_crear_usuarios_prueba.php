<?php
/**
 * SCRIPT PARA LIMPIAR Y CREAR USUARIOS DE PRUEBA
 * 
 * Este script:
 * 1. Elimina usuarios viejos que no se usan
 * 2. Crea usuarios de prueba con emails modernos
 * 3. Configura todo para el sistema actual
 * 
 * Autor: RepuMovil Team
 */

session_start();
require_once 'db_config.php';

// Verificar que solo administradores puedan ejecutar
if (!isset($_SESSION['user_id']) || !in_array($_SESSION['user_type'], ['root', 'admin'])) {
    die("❌ Acceso denegado. Solo administradores pueden ejecutar este script.");
}

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Limpiar Usuarios - RepuMovil</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #FF6B35 0%, #FFA500 100%);
            min-height: 100vh;
            padding: 2rem;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #FF6B35;
        }

        .logo {
            font-size: 2rem;
            font-weight: 900;
            margin-bottom: 0.5rem;
        }

        .repu { color: #FF6B35; }
        .movil { color: #4CAF50; }

        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
        }

        .success-box {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
        }

        .error-box {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
        }

        .btn {
            padding: 1rem 2rem;
            border: none;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            margin: 0.5rem;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-primary {
            background: #FF6B35;
            color: white;
        }

        .log-output {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 400px;
            overflow-y: auto;
            margin: 1rem 0;
            white-space: pre-wrap;
        }

        .user-list {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
        }

        .user-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem;
            border-bottom: 1px solid #dee2e6;
        }

        .user-item:last-child {
            border-bottom: none;
        }

        .footer {
            text-align: center;
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid #dee2e6;
            color: #6c757d;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">
                <i class="fas fa-users-cog" style="color: #FF6B35;"></i>
                <span class="repu">Repu</span><span class="movil">Movil</span>
            </div>
            <h2>Limpiar y Crear Usuarios de Prueba</h2>
            <p>Depuración del sistema de usuarios</p>
        </div>

        <?php if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['accion'])): ?>
            
            <?php if ($_POST['accion'] === 'limpiar'): ?>
                <div class="success-box">
                    <h3><i class="fas fa-broom"></i> Limpiando Usuarios Viejos...</h3>
                </div>
                <div class="log-output">
<?php
try {
    $pdo = connectDB();
    
    echo "🧹 Iniciando limpieza de usuarios...\n";
    
    // Obtener usuarios actuales
    $stmt = $pdo->query("SELECT u.id, u.username, u.email, r.name as role_name FROM users u LEFT JOIN roles r ON u.role_id = r.id");
    $usuarios_actuales = $stmt->fetchAll();
    
    echo "📋 Usuarios encontrados: " . count($usuarios_actuales) . "\n\n";
    
    foreach ($usuarios_actuales as $user) {
        echo "👤 ID: {$user['id']} | Email: {$user['email']} | Rol: {$user['role_name']}\n";
    }
    
    echo "\n🗑️ Eliminando usuarios de prueba viejos...\n";
    
    // Eliminar usuarios específicos que ya no se usan
    $usuarios_a_eliminar = [
        'taller1', 'proveedor1', 'mecanico1', 'delivery1', 'cliente1',
        '<EMAIL>', '<EMAIL>'
    ];
    
    foreach ($usuarios_a_eliminar as $user_identifier) {
        $stmt = $pdo->prepare("DELETE FROM users WHERE username = ? OR email = ?");
        $result = $stmt->execute([$user_identifier, $user_identifier]);
        if ($stmt->rowCount() > 0) {
            echo "❌ Eliminado: $user_identifier\n";
        }
    }
    
    echo "\n✅ Limpieza completada!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
                </div>
            
            <?php elseif ($_POST['accion'] === 'crear'): ?>
                <div class="success-box">
                    <h3><i class="fas fa-user-plus"></i> Creando Usuarios de Prueba...</h3>
                </div>
                <div class="log-output">
<?php
try {
    $pdo = connectDB();
    
    echo "👥 Creando usuarios de prueba modernos...\n\n";
    
    // Usuarios de prueba con emails modernos
    $usuarios_prueba = [
        [
            'email' => '<EMAIL>',
            'password' => 'proveedor123',
            'role' => 'supplier',
            'business_name' => 'Repuestos Test S.A.',
            'address' => 'Av. Repuestos 123, San Juan',
            'phone' => '+54 ************'
        ],
        [
            'email' => '<EMAIL>', 
            'password' => 'taller123',
            'role' => 'workshop',
            'business_name' => 'Taller Test Mecánico',
            'address' => 'Calle Mecánicos 456, San Juan',
            'phone' => '+54 ************'
        ],
        [
            'email' => '<EMAIL>',
            'password' => 'mecanico123', 
            'role' => 'mechanic',
            'business_name' => 'Mecánico Independiente Test',
            'address' => 'Barrio Test, San Juan',
            'phone' => '+54 ************'
        ]
    ];
    
    foreach ($usuarios_prueba as $user_data) {
        echo "🔧 Creando usuario: {$user_data['email']}\n";
        
        // Verificar si el rol existe
        $stmt = $pdo->prepare("SELECT id FROM roles WHERE name = ?");
        $stmt->execute([$user_data['role']]);
        $role = $stmt->fetch();
        
        if (!$role) {
            // Crear el rol si no existe
            $stmt = $pdo->prepare("INSERT INTO roles (name, description) VALUES (?, ?)");
            $stmt->execute([$user_data['role'], ucfirst($user_data['role']) . ' role']);
            $role_id = $pdo->lastInsertId();
            echo "   📝 Rol '{$user_data['role']}' creado\n";
        } else {
            $role_id = $role['id'];
        }
        
        // Crear usuario
        $hashed_password = password_hash($user_data['password'], PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("
            INSERT INTO users (username, email, password, role_id, status, created_at) 
            VALUES (?, ?, ?, ?, 'active', NOW())
            ON DUPLICATE KEY UPDATE 
            password = VALUES(password), role_id = VALUES(role_id), status = 'active'
        ");
        $stmt->execute([
            explode('@', $user_data['email'])[0], // username del email
            $user_data['email'],
            $hashed_password,
            $role_id
        ]);
        
        $user_id = $pdo->lastInsertId() ?: $pdo->query("SELECT id FROM users WHERE email = '{$user_data['email']}'")->fetchColumn();
        
        // Crear registro específico según el tipo
        switch ($user_data['role']) {
            case 'supplier':
                $stmt = $pdo->prepare("
                    INSERT INTO suppliers (user_id, name, address, phone, description, created_at) 
                    VALUES (?, ?, ?, ?, ?, NOW())
                    ON DUPLICATE KEY UPDATE 
                    name = VALUES(name), address = VALUES(address), phone = VALUES(phone)
                ");
                $stmt->execute([
                    $user_id,
                    $user_data['business_name'],
                    $user_data['address'],
                    $user_data['phone'],
                    'Proveedor de repuestos para pruebas del sistema'
                ]);
                echo "   🏪 Datos de proveedor creados\n";
                break;
                
            case 'workshop':
                $stmt = $pdo->prepare("
                    INSERT INTO workshops (user_id, name, address, phone, description, created_at) 
                    VALUES (?, ?, ?, ?, ?, NOW())
                    ON DUPLICATE KEY UPDATE 
                    name = VALUES(name), address = VALUES(address), phone = VALUES(phone)
                ");
                $stmt->execute([
                    $user_id,
                    $user_data['business_name'],
                    $user_data['address'],
                    $user_data['phone'],
                    'Taller mecánico para pruebas del sistema'
                ]);
                echo "   🔧 Datos de taller creados\n";
                break;
                
            case 'mechanic':
                $stmt = $pdo->prepare("
                    INSERT INTO mechanics (user_id, name, address, phone, description, specialties, created_at) 
                    VALUES (?, ?, ?, ?, ?, ?, NOW())
                    ON DUPLICATE KEY UPDATE 
                    name = VALUES(name), address = VALUES(address), phone = VALUES(phone)
                ");
                $stmt->execute([
                    $user_id,
                    $user_data['business_name'],
                    $user_data['address'],
                    $user_data['phone'],
                    'Mecánico independiente para pruebas del sistema',
                    'Motor, Frenos, Suspensión'
                ]);
                echo "   👨‍🔧 Datos de mecánico creados\n";
                break;
        }
        
        echo "   ✅ Usuario {$user_data['email']} creado exitosamente\n\n";
    }
    
    echo "🎉 ¡Todos los usuarios de prueba creados!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
                </div>
            <?php endif; ?>
            
            <div style="text-align: center; margin-top: 2rem;">
                <a href="ver_usuarios_sistema.php" class="btn btn-success">
                    <i class="fas fa-users"></i> Ver Usuarios Actualizados
                </a>
                <a href="dashboard-proveedor.php" class="btn btn-primary">
                    <i class="fas fa-arrow-left"></i> Volver al Dashboard
                </a>
            </div>

        <?php else: ?>
            
            <div class="warning-box">
                <h4><i class="fas fa-exclamation-triangle"></i> ¿Qué hace este script?</h4>
                <ul style="margin-top: 1rem;">
                    <li>🧹 <strong>Limpiar:</strong> Elimina usuarios viejos que ya no se usan</li>
                    <li>👥 <strong>Crear:</strong> Crea usuarios de prueba con emails modernos</li>
                    <li>✅ <strong>Actualizar:</strong> Configura todo para el sistema actual</li>
                </ul>
            </div>

            <div style="text-align: center; margin: 2rem 0;">
                <form method="POST" style="display: inline;">
                    <input type="hidden" name="accion" value="limpiar">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-broom"></i> 1. Limpiar Usuarios Viejos
                    </button>
                </form>
                
                <form method="POST" style="display: inline;">
                    <input type="hidden" name="accion" value="crear">
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-user-plus"></i> 2. Crear Usuarios de Prueba
                    </button>
                </form>
            </div>

            <div class="warning-box">
                <h4><i class="fas fa-info-circle"></i> Usuarios que se crearán:</h4>
                <div class="user-list">
                    <div class="user-item">
                        <span><strong><EMAIL></strong> - Contraseña: proveedor123</span>
                        <span class="badge" style="background: #28a745; color: white; padding: 0.2rem 0.5rem; border-radius: 10px;">Proveedor</span>
                    </div>
                    <div class="user-item">
                        <span><strong><EMAIL></strong> - Contraseña: taller123</span>
                        <span class="badge" style="background: #007bff; color: white; padding: 0.2rem 0.5rem; border-radius: 10px;">Taller</span>
                    </div>
                    <div class="user-item">
                        <span><strong><EMAIL></strong> - Contraseña: mecanico123</span>
                        <span class="badge" style="background: #ffc107; color: black; padding: 0.2rem 0.5rem; border-radius: 10px;">Mecánico</span>
                    </div>
                </div>
            </div>

        <?php endif; ?>

        <div class="footer">
            © 2024 RepuMovil. Todos los derechos reservados. Hecho con ❤️ en San Juan, Argentina.
        </div>
    </div>
</body>
</html>
