<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RepuMovil - Buscar Repuestos</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #FF6B35 0%, #FFA500 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .logo {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .search-section {
            padding: 40px;
        }

        .search-container {
            position: relative;
            margin-bottom: 30px;
        }

        .search-input {
            width: 100%;
            padding: 15px 20px;
            font-size: 1.1rem;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            outline: none;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            border-color: #FF6B35;
            box-shadow: 0 0 10px rgba(255, 107, 53, 0.3);
        }

        .search-suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 0 0 10px 10px;
            max-height: 300px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }

        .suggestion-item {
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: background 0.2s ease;
        }

        .suggestion-item:hover {
            background: #f8f9fa;
        }

        .suggestion-item:last-child {
            border-bottom: none;
        }

        .suggestion-name {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .suggestion-details {
            font-size: 0.9rem;
            color: #666;
        }

        .suggestion-price {
            font-weight: bold;
            color: #FF6B35;
            float: right;
        }

        .categories-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .category-card {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .category-card:hover {
            border-color: #FF6B35;
            background: #fff5f0;
            transform: translateY(-2px);
        }

        .category-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .category-name {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .category-count {
            color: #666;
            font-size: 0.9rem;
        }

        .results-section {
            margin-top: 30px;
        }

        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }

        .product-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .product-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .product-name {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .product-brand {
            color: #666;
            margin-bottom: 5px;
        }

        .product-price {
            font-size: 1.2rem;
            font-weight: bold;
            color: #FF6B35;
            margin-bottom: 10px;
        }

        .product-stock {
            color: #28a745;
            font-size: 0.9rem;
        }

        .add-to-cart {
            background: #FF6B35;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            width: 100%;
            margin-top: 10px;
            font-weight: bold;
            transition: background 0.3s ease;
        }

        .add-to-cart:hover {
            background: #e55a2b;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }

        .no-results {
            text-align: center;
            padding: 40px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🔧 RepuMovil</div>
            <div class="subtitle">Buscar Repuestos - Sistema AJAX</div>
        </div>

        <div class="search-section">
            <!-- Buscador principal -->
            <div class="search-container">
                <input
                    type="text"
                    id="searchInput"
                    class="search-input"
                    placeholder="Buscar repuestos... (ej: filtro, pastillas, bujías)"
                    autocomplete="off"
                >
                <div id="searchSuggestions" class="search-suggestions"></div>
            </div>

            <!-- Categorías -->
            <div id="categoriesSection" class="categories-section">
                <div class="loading">Cargando categorías...</div>
            </div>

            <!-- Resultados -->
            <div id="resultsSection" class="results-section" style="display: none;">
                <h3>Resultados de búsqueda:</h3>
                <div id="resultsGrid" class="results-grid"></div>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let searchTimeout;
        let currentQuery = '';

        // Inicializar cuando carga la página
        document.addEventListener('DOMContentLoaded', function() {
            cargarCategorias();
            configurarBuscador();
        });

        // Configurar el buscador AJAX
        function configurarBuscador() {
            const searchInput = document.getElementById('searchInput');
            const suggestions = document.getElementById('searchSuggestions');

            searchInput.addEventListener('input', function() {
                const query = this.value.trim();

                // Limpiar timeout anterior
                clearTimeout(searchTimeout);

                if (query.length >= 2) {
                    // Buscar después de 300ms de pausa
                    searchTimeout = setTimeout(() => {
                        buscarRepuestos(query);
                    }, 300);
                } else {
                    ocultarSugerencias();
                    ocultarResultados();
                }
            });

            // Ocultar sugerencias al hacer click fuera
            document.addEventListener('click', function(e) {
                if (!searchInput.contains(e.target) && !suggestions.contains(e.target)) {
                    ocultarSugerencias();
                }
            });
        }

        // Buscar repuestos via AJAX
        async function buscarRepuestos(query, categoria = '') {
            try {
                const url = `../api/buscar-repuestos.php?q=${encodeURIComponent(query)}&categoria=${encodeURIComponent(categoria)}&limit=20`;
                const response = await fetch(url);
                const data = await response.json();

                if (data.success) {
                    mostrarSugerencias(data.data);
                    mostrarResultados(data.data, query);
                } else {
                    console.error('Error en búsqueda:', data.message);
                }
            } catch (error) {
                console.error('Error de conexión:', error);
            }
        }

        // Mostrar sugerencias
        function mostrarSugerencias(repuestos) {
            const suggestions = document.getElementById('searchSuggestions');

            if (repuestos.length === 0) {
                suggestions.style.display = 'none';
                return;
            }

            let html = '';
            repuestos.slice(0, 5).forEach(repuesto => {
                html += `
                    <div class="suggestion-item" onclick="seleccionarRepuesto('${repuesto.nombre}')">
                        <div class="suggestion-name">${repuesto.nombre}</div>
                        <div class="suggestion-details">
                            ${repuesto.marca} ${repuesto.modelo} - ${repuesto.categoria}
                            <span class="suggestion-price">${repuesto.precio_formateado}</span>
                        </div>
                    </div>
                `;
            });

            suggestions.innerHTML = html;
            suggestions.style.display = 'block';
        }

        // Ocultar sugerencias
        function ocultarSugerencias() {
            document.getElementById('searchSuggestions').style.display = 'none';
        }

        // Seleccionar repuesto de sugerencias
        function seleccionarRepuesto(nombre) {
            document.getElementById('searchInput').value = nombre;
            ocultarSugerencias();
            buscarRepuestos(nombre);
        }

        // Mostrar resultados completos
        function mostrarResultados(repuestos, query) {
            const resultsSection = document.getElementById('resultsSection');
            const resultsGrid = document.getElementById('resultsGrid');

            if (repuestos.length === 0) {
                resultsGrid.innerHTML = '<div class="no-results">No se encontraron repuestos para "' + query + '"</div>';
            } else {
                let html = '';
                repuestos.forEach(repuesto => {
                    html += `
                        <div class="product-card">
                            <div class="product-name">${repuesto.nombre}</div>
                            <div class="product-brand">${repuesto.marca} ${repuesto.modelo}</div>
                            <div class="product-price">${repuesto.precio_formateado}</div>
                            <div class="product-stock">Stock: ${repuesto.stock} unidades</div>
                            <button class="add-to-cart" onclick="agregarAlChanguito(${repuesto.id})">
                                Agregar al Changuito
                            </button>
                        </div>
                    `;
                });
                resultsGrid.innerHTML = html;
            }

            resultsSection.style.display = 'block';
        }

        // Ocultar resultados
        function ocultarResultados() {
            document.getElementById('resultsSection').style.display = 'none';
        }

        // Cargar categorías
        async function cargarCategorias() {
            try {
                const response = await fetch('../api/obtener-categorias.php');
                const data = await response.json();

                if (data.success) {
                    mostrarCategorias(data.data);
                } else {
                    console.error('Error cargando categorías:', data.message);
                }
            } catch (error) {
                console.error('Error de conexión:', error);
            }
        }

        // Mostrar categorías
        function mostrarCategorias(categorias) {
            const section = document.getElementById('categoriesSection');

            let html = '';
            categorias.forEach(categoria => {
                html += `
                    <div class="category-card" onclick="buscarPorCategoria('${categoria.nombre}')">
                        <div class="category-icon"><i class="${categoria.icono}"></i></div>
                        <div class="category-name">${categoria.nombre}</div>
                        <div class="category-count">${categoria.total_productos} productos</div>
                    </div>
                `;
            });

            section.innerHTML = html;
        }

        // Buscar por categoría
        function buscarPorCategoria(categoria) {
            document.getElementById('searchInput').value = '';
            buscarRepuestos('', categoria);
        }

        // Agregar al changuito
        function agregarAlChanguito(repuestoId) {
            alert('Repuesto agregado al changuito! ID: ' + repuestoId);
            // Aquí implementaremos la lógica del carrito
        }
    </script>
</body>
</html>
