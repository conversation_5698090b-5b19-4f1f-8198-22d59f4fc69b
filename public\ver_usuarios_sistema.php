<?php
/**
 * <PERSON><PERSON><PERSON>DO COMPLETO DE USUARIOS DEL SISTEMA
 * 
 * Muestra todos los usuarios registrados con sus credenciales
 * Para facilitar las pruebas del sistema
 * 
 * Autor: RepuMovil Team
 */

require_once 'db_config.php';

try {
    $pdo = connectDB();
    
    // Obtener todos los usuarios con sus roles y datos adicionales
    $stmt = $pdo->query("
        SELECT 
            u.id,
            u.username,
            u.email,
            u.nombre,
            u.telefono,
            u.user_type,
            u.status,
            u.created_at,
            u.last_login,
            -- Datos específicos según el tipo
            CASE 
                WHEN u.user_type = 'taller' THEN w.name
                WHEN u.user_type = 'proveedor_repuestos' THEN s.name
                WHEN u.user_type = 'mecanico' THEN m.name
                ELSE 'N/A'
            END as business_name,
            CASE 
                WHEN u.user_type = 'taller' THEN w.address
                WHEN u.user_type = 'proveedor_repuestos' THEN s.address
                WHEN u.user_type = 'mecanico' THEN m.address
                ELSE 'N/A'
            END as business_address
        FROM users u
        LEFT JOIN workshops w ON u.id = w.user_id
        LEFT JOIN suppliers s ON u.id = s.user_id
        LEFT JOIN mechanics m ON u.id = m.user_id
        ORDER BY u.user_type, u.username
    ");
    $usuarios = $stmt->fetchAll();
    
} catch (Exception $e) {
    $error = "Error al cargar usuarios: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Usuarios del Sistema - RepuMovil</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #FF6B35 0%, #FFA500 100%);
            min-height: 100vh;
            padding: 2rem;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #FF6B35;
        }

        .logo {
            font-size: 2rem;
            font-weight: 900;
            margin-bottom: 0.5rem;
        }

        .repu { color: #FF6B35; }
        .movil { color: #4CAF50; }

        .user-type-section {
            margin-bottom: 2rem;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            overflow: hidden;
        }

        .user-type-header {
            padding: 1rem 1.5rem;
            font-weight: bold;
            color: white;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .type-root { background: #dc3545; }
        .type-taller { background: #007bff; }
        .type-proveedor { background: #28a745; }
        .type-mecanico { background: #ffc107; color: #212529; }
        .type-delivery { background: #6f42c1; }
        .type-cliente { background: #17a2b8; }

        .users-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 1rem;
            padding: 1rem;
        }

        .user-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            border-left: 5px solid #FF6B35;
            transition: transform 0.3s ease;
        }

        .user-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .user-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .username {
            font-size: 1.2rem;
            font-weight: bold;
            color: #333;
        }

        .user-id {
            background: #FF6B35;
            color: white;
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
            margin-left: auto;
        }

        .credentials {
            background: #e9ecef;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            font-family: 'Courier New', monospace;
        }

        .credential-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }

        .credential-row:last-child {
            margin-bottom: 0;
        }

        .credential-label {
            font-weight: bold;
            color: #495057;
        }

        .credential-value {
            color: #007bff;
            font-weight: bold;
        }

        .user-info {
            margin-top: 1rem;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            padding: 0.3rem 0;
            border-bottom: 1px solid #dee2e6;
        }

        .info-row:last-child {
            border-bottom: none;
        }

        .status-badge {
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .status-active { background: #d4edda; color: #155724; }
        .status-inactive { background: #f8d7da; color: #721c24; }

        .quick-actions {
            margin-top: 1rem;
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .btn-quick {
            padding: 0.3rem 0.8rem;
            border: none;
            border-radius: 15px;
            font-size: 0.8rem;
            cursor: pointer;
            text-decoration: none;
            color: white;
            transition: all 0.3s ease;
        }

        .btn-login { background: #007bff; }
        .btn-login:hover { background: #0056b3; }

        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 1rem;
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #FF6B35;
        }

        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .footer {
            text-align: center;
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid #dee2e6;
            color: #6c757d;
            font-style: italic;
        }

        .copy-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 0.2rem 0.5rem;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.7rem;
            margin-left: 0.5rem;
        }

        .copy-btn:hover {
            background: #218838;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .users-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">
                <i class="fas fa-users" style="color: #FF6B35;"></i>
                <span class="repu">Repu</span><span class="movil">Movil</span>
            </div>
            <h2>Usuarios del Sistema</h2>
            <p>Listado completo para pruebas y desarrollo</p>
        </div>

        <?php if (isset($error)): ?>
            <div style="background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 10px; padding: 1rem; margin-bottom: 2rem;">
                <h3><i class="fas fa-exclamation-circle"></i> Error</h3>
                <p><?php echo htmlspecialchars($error); ?></p>
            </div>
        <?php else: ?>

            <!-- Estadísticas Resumen -->
            <div class="summary-stats">
                <?php
                $stats = [];
                foreach ($usuarios as $user) {
                    $type = $user['user_type'] ?? 'sin_tipo';
                    $stats[$type] = ($stats[$type] ?? 0) + 1;
                }
                ?>
                <div class="stat-card">
                    <div class="stat-number"><?php echo count($usuarios); ?></div>
                    <div class="stat-label">Total Usuarios</div>
                </div>
                <?php foreach ($stats as $type => $count): ?>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $count; ?></div>
                        <div class="stat-label"><?php echo ucfirst(str_replace('_', ' ', $type)); ?></div>
                    </div>
                <?php endforeach; ?>
            </div>

            <?php
            // Agrupar usuarios por tipo
            $usersByType = [];
            foreach ($usuarios as $user) {
                $type = $user['user_type'] ?? 'sin_tipo';
                $usersByType[$type][] = $user;
            }

            // Definir información de tipos
            $typeInfo = [
                'root' => ['name' => 'Administradores', 'icon' => 'fas fa-crown', 'class' => 'type-root'],
                'taller' => ['name' => 'Talleres Mecánicos', 'icon' => 'fas fa-wrench', 'class' => 'type-taller'],
                'proveedor_repuestos' => ['name' => 'Proveedores de Repuestos', 'icon' => 'fas fa-store', 'class' => 'type-proveedor'],
                'mecanico' => ['name' => 'Mecánicos Independientes', 'icon' => 'fas fa-user-cog', 'class' => 'type-mecanico'],
                'delivery' => ['name' => 'Repartidores', 'icon' => 'fas fa-motorcycle', 'class' => 'type-delivery'],
                'cliente' => ['name' => 'Clientes', 'icon' => 'fas fa-user', 'class' => 'type-cliente']
            ];
            ?>

            <?php foreach ($usersByType as $type => $users): ?>
                <div class="user-type-section">
                    <div class="user-type-header <?php echo $typeInfo[$type]['class'] ?? 'type-root'; ?>">
                        <i class="<?php echo $typeInfo[$type]['icon'] ?? 'fas fa-user'; ?>"></i>
                        <span><?php echo $typeInfo[$type]['name'] ?? ucfirst($type); ?></span>
                        <span style="margin-left: auto;">(<?php echo count($users); ?> usuarios)</span>
                    </div>
                    
                    <div class="users-grid">
                        <?php foreach ($users as $user): ?>
                            <div class="user-card">
                                <div class="user-header">
                                    <div class="username">
                                        <i class="fas fa-user"></i>
                                        <?php echo htmlspecialchars($user['username']); ?>
                                    </div>
                                    <div class="user-id">ID: <?php echo $user['id']; ?></div>
                                </div>

                                <div class="credentials">
                                    <div class="credential-row">
                                        <span class="credential-label">Usuario:</span>
                                        <span class="credential-value">
                                            <?php echo htmlspecialchars($user['username']); ?>
                                            <button class="copy-btn" onclick="copyToClipboard('<?php echo htmlspecialchars($user['username']); ?>')">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </span>
                                    </div>
                                    <div class="credential-row">
                                        <span class="credential-label">Contraseña:</span>
                                        <span class="credential-value">
                                            <?php 
                                            // Contraseñas conocidas del sistema
                                            $passwords = [
                                                'admin' => 'admin123',
                                                'taller1' => 'taller123',
                                                'proveedor1' => 'proveedor123',
                                                'mecanico1' => 'mecanico123',
                                                'delivery1' => 'delivery123'
                                            ];
                                            $password = $passwords[$user['username']] ?? $user['username'] . '123';
                                            echo $password;
                                            ?>
                                            <button class="copy-btn" onclick="copyToClipboard('<?php echo $password; ?>')">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </span>
                                    </div>
                                </div>

                                <div class="user-info">
                                    <?php if ($user['email']): ?>
                                        <div class="info-row">
                                            <span><i class="fas fa-envelope"></i> Email:</span>
                                            <span><?php echo htmlspecialchars($user['email']); ?></span>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if ($user['nombre']): ?>
                                        <div class="info-row">
                                            <span><i class="fas fa-id-card"></i> Nombre:</span>
                                            <span><?php echo htmlspecialchars($user['nombre']); ?></span>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if ($user['business_name'] && $user['business_name'] !== 'N/A'): ?>
                                        <div class="info-row">
                                            <span><i class="fas fa-building"></i> Negocio:</span>
                                            <span><?php echo htmlspecialchars($user['business_name']); ?></span>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="info-row">
                                        <span><i class="fas fa-calendar"></i> Creado:</span>
                                        <span><?php echo date('d/m/Y', strtotime($user['created_at'])); ?></span>
                                    </div>
                                    
                                    <div class="info-row">
                                        <span><i class="fas fa-circle"></i> Estado:</span>
                                        <span class="status-badge <?php echo $user['status'] === 'active' ? 'status-active' : 'status-inactive'; ?>">
                                            <?php echo ucfirst($user['status']); ?>
                                        </span>
                                    </div>
                                </div>

                                <div class="quick-actions">
                                    <a href="login-dinamico.php" class="btn-quick btn-login">
                                        <i class="fas fa-sign-in-alt"></i> Login
                                    </a>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endforeach; ?>

        <?php endif; ?>

        <div class="footer">
            © 2024 RepuMovil. Todos los derechos reservados. Hecho con ❤️ en San Juan, Argentina.
        </div>
    </div>

    <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                // Mostrar feedback visual
                const btn = event.target.closest('.copy-btn');
                const originalHTML = btn.innerHTML;
                btn.innerHTML = '<i class="fas fa-check"></i>';
                btn.style.background = '#28a745';
                
                setTimeout(() => {
                    btn.innerHTML = originalHTML;
                    btn.style.background = '#28a745';
                }, 1000);
            });
        }
    </script>
</body>
</html>
