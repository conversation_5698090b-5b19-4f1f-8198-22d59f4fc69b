/**
 * Configuración de API para RepuMovil Delivery
 */

// URL base de la API - Cambiar por tu IP local para desarrollo
// Para Expo Go, usar la IP de tu computadora, no localhost
export const API_BASE_URL = 'http://*************/talleraplicacion/public/delivery/api';

// Endpoints de la API
export const API_ENDPOINTS = {
  // Autenticación
  LOGIN: `${API_BASE_URL}/auth.php?action=login`,
  REGISTER: `${API_BASE_URL}/auth.php?action=register`,
  PROFILE: `${API_BASE_URL}/auth.php?action=profile`,
  VERIFY_EMAIL: `${API_BASE_URL}/auth.php?action=verify-email`,
  FORGOT_PASSWORD: `${API_BASE_URL}/auth.php?action=forgot-password`,
  
  // Pedidos
  PEDIDOS_DISPONIBLES: `${API_BASE_URL}/pedidos.php?action=disponibles`,
  MIS_PEDIDOS: `${API_BASE_URL}/pedidos.php?action=mis-pedidos`,
  HISTORIAL: `${API_BASE_URL}/pedidos.php?action=historial`,
  ESTADISTICAS: `${API_BASE_URL}/pedidos.php?action=estadisticas`,
  ACEPTAR_PEDIDO: `${API_BASE_URL}/pedidos.php?action=aceptar`,
  COMPLETAR_PEDIDO: `${API_BASE_URL}/pedidos.php?action=completar`,
  CANCELAR_PEDIDO: `${API_BASE_URL}/pedidos.php?action=cancelar`,
  ACTUALIZAR_ESTADO: `${API_BASE_URL}/pedidos.php?action=actualizar-estado`,
};

/**
 * Configuración de headers por defecto
 */
export const DEFAULT_HEADERS = {
  'Content-Type': 'application/json',
  'Accept': 'application/json',
};

/**
 * Función para hacer peticiones HTTP
 */
export const apiRequest = async (
  url: string, 
  options: RequestInit = {},
  token?: string
): Promise<any> => {
  try {
    const headers = {
      ...DEFAULT_HEADERS,
      ...options.headers,
    };

    // Agregar token de autorización si está disponible
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    const response = await fetch(url, {
      ...options,
      headers,
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || `HTTP error! status: ${response.status}`);
    }

    return data;
  } catch (error) {
    console.error('API Request Error:', error);
    throw error;
  }
};

/**
 * Funciones específicas de la API
 */
export const AuthAPI = {
  login: async (email: string, password: string) => {
    return apiRequest(API_ENDPOINTS.LOGIN, {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    });
  },

  register: async (userData: any) => {
    return apiRequest(API_ENDPOINTS.REGISTER, {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  },

  getProfile: async (userId: string, token: string) => {
    return apiRequest(`${API_ENDPOINTS.PROFILE}&user_id=${userId}`, {
      method: 'GET',
    }, token);
  },
};

export const PedidosAPI = {
  getDisponibles: async () => {
    return apiRequest(API_ENDPOINTS.PEDIDOS_DISPONIBLES, {
      method: 'GET',
    });
  },

  getMisPedidos: async (repartidorId: string) => {
    return apiRequest(`${API_ENDPOINTS.MIS_PEDIDOS}&repartidor_id=${repartidorId}`, {
      method: 'GET',
    });
  },

  getHistorial: async (repartidorId: string, limite: number = 50) => {
    return apiRequest(`${API_ENDPOINTS.HISTORIAL}&repartidor_id=${repartidorId}&limite=${limite}`, {
      method: 'GET',
    });
  },

  getEstadisticas: async (repartidorId: string, periodo: string = 'semana') => {
    return apiRequest(`${API_ENDPOINTS.ESTADISTICAS}&repartidor_id=${repartidorId}&periodo=${periodo}`, {
      method: 'GET',
    });
  },

  aceptarPedido: async (pedidoId: string, repartidorId: string) => {
    return apiRequest(API_ENDPOINTS.ACEPTAR_PEDIDO, {
      method: 'POST',
      body: JSON.stringify({ pedido_id: pedidoId, repartidor_id: repartidorId }),
    });
  },

  completarPedido: async (pedidoId: string, repartidorId: string) => {
    return apiRequest(API_ENDPOINTS.COMPLETAR_PEDIDO, {
      method: 'POST',
      body: JSON.stringify({ pedido_id: pedidoId, repartidor_id: repartidorId }),
    });
  },

  cancelarPedido: async (pedidoId: string, repartidorId: string) => {
    return apiRequest(API_ENDPOINTS.CANCELAR_PEDIDO, {
      method: 'POST',
      body: JSON.stringify({ pedido_id: pedidoId, repartidor_id: repartidorId }),
    });
  },

  actualizarEstado: async (pedidoId: string, estado: string) => {
    return apiRequest(API_ENDPOINTS.ACTUALIZAR_ESTADO, {
      method: 'PUT',
      body: JSON.stringify({ pedido_id: pedidoId, estado }),
    });
  },
};

/**
 * Función para manejar errores de la API
 */
export const handleApiError = (error: any): string => {
  if (error.message) {
    return error.message;
  }
  
  if (typeof error === 'string') {
    return error;
  }
  
  return 'Error desconocido. Por favor intenta nuevamente.';
};

/**
 * Función para validar la conexión con la API
 */
export const testApiConnection = async (): Promise<boolean> => {
  try {
    const response = await fetch(`${API_BASE_URL}/auth.php`, {
      method: 'GET',
      headers: DEFAULT_HEADERS,
    });
    
    return response.status !== 404;
  } catch (error) {
    console.error('Error testing API connection:', error);
    return false;
  }
};

/**
 * Tipos de datos para TypeScript
 */
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  timestamp: string;
}

export interface LoginResponse {
  id: string;
  email: string;
  nombre_completo: string;
  telefono: string;
  estado: string;
  verificado: boolean;
  fecha_registro: string;
  token: string;
}

export interface Pedido {
  id: string;
  cliente: string;
  telefono?: string;
  direccion_origen?: string;
  direccion_destino?: string;
  direccion?: string;
  descripcion?: string;
  valor_pedido?: number;
  ganancia: number;
  distancia: string;
  tiempo_estimado: string;
  tipo: 'repuestos' | 'servicio';
  estado?: string;
  fecha_pedido?: string;
  fecha_asignacion?: string;
}

export interface EntregaHistorial {
  id: string;
  fecha: string;
  hora: string;
  cliente: string;
  direccion: string;
  tipo: 'repuestos' | 'servicio';
  estado: 'completado' | 'cancelado';
  ganancia: string;
  distancia: string;
  tiempo_total: string;
  calificacion: number;
  comentario?: string;
}

export interface Estadisticas {
  resumen: {
    total_entregas: number;
    total_ganancias: number;
    calificacion_promedio: number;
    periodo: string;
  };
  ganancias_diarias: Array<{
    fecha: string;
    entregas: number;
    ganancia_bruta: number;
    comision_plataforma: number;
    ganancia_neta: number;
    tiempo_activo: string;
  }>;
}

/**
 * Configuración para desarrollo
 */
export const DEV_CONFIG = {
  // Habilitar logs de debug
  DEBUG: __DEV__,
  
  // Timeout para requests (en milisegundos)
  REQUEST_TIMEOUT: 10000,
  
  // Reintentos automáticos
  MAX_RETRIES: 3,
  
  // Datos de prueba
  TEST_CREDENTIALS: {
    email: '<EMAIL>',
    password: '123456'
  }
};

/**
 * Función para logs de debug
 */
export const debugLog = (message: string, data?: any) => {
  if (DEV_CONFIG.DEBUG) {
    console.log(`[RepuMovil API] ${message}`, data || '');
  }
};
