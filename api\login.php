<?php
/**
 * API de Login para RepuMovil
 * FASE 2: Backend/API - Paso 1
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Manejar preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Solo permitir POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Método no permitido']);
    exit();
}

// Incluir configuración de base de datos
require_once 'db_config.php';

try {
    // Obtener datos del POST
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Datos JSON inválidos');
    }
    
    $email = $input['email'] ?? '';
    $password = $input['password'] ?? '';
    
    // Validar campos requeridos
    if (empty($email) || empty($password)) {
        throw new Exception('Email y contraseña son requeridos');
    }
    
    // Conectar a la base de datos
    $pdo = connectDB();
    
    // Buscar usuario por email o username
    $stmt = $pdo->prepare("
        SELECT u.*, r.name as role_name 
        FROM users u 
        JOIN roles r ON u.role_id = r.id 
        WHERE u.email = ? OR u.username = ?
    ");
    $stmt->execute([$email, $email]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        throw new Exception('Usuario no encontrado');
    }
    
    // Verificar contraseña
    if (!password_verify($password, $user['password'])) {
        throw new Exception('Contraseña incorrecta');
    }
    
    // Verificar estado del usuario
    if ($user['status'] !== 'active') {
        throw new Exception('Usuario inactivo o suspendido');
    }
    
    // Actualizar último login
    $updateStmt = $pdo->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
    $updateStmt->execute([$user['id']]);
    
    // Obtener datos adicionales según el rol
    $additionalData = [];
    
    switch ($user['role_name']) {
        case 'workshop':
            $stmt = $pdo->prepare("SELECT * FROM workshops WHERE user_id = ?");
            $stmt->execute([$user['id']]);
            $additionalData = $stmt->fetch(PDO::FETCH_ASSOC) ?: [];
            break;
            
        case 'supplier':
            $stmt = $pdo->prepare("SELECT * FROM suppliers WHERE user_id = ?");
            $stmt->execute([$user['id']]);
            $additionalData = $stmt->fetch(PDO::FETCH_ASSOC) ?: [];
            break;
    }
    
    // Preparar respuesta (sin incluir la contraseña)
    unset($user['password']);
    
    $response = [
        'success' => true,
        'message' => 'Login exitoso',
        'user' => $user,
        'additional_data' => $additionalData,
        'token' => generateToken($user['id']) // Token simple para esta demo
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

/**
 * Generar token simple para autenticación
 * En producción usar JWT o similar
 */
function generateToken($userId) {
    return base64_encode($userId . ':' . time() . ':' . md5($userId . time()));
}
?>
