<?php
// Configuración de la base de datos
$config = [
    'db' => [
        'host' => 'localhost',
        'name' => 'autoconnect_bd',
        'user' => 'root',
        'pass' => '',
        'charset' => 'utf8mb4'
    ],
    'app' => [
        'name' => 'AutoConnect',
        'url' => 'http://localhost/mechanical-workshop',
        'version' => '1.0.0',
        'debug' => true
    ]
];

// Mostrar errores en modo debug
if ($config['app']['debug']) {
    ini_set('display_errors', 1);
    ini_set('display_startup_errors', 1);
    error_reporting(E_ALL);
} else {
    ini_set('display_errors', 0);
    ini_set('display_startup_errors', 0);
    error_reporting(0);
}

// Función para conectar a la base de datos
function connectDB() {
    global $config;

    $host = $config['db']['host'];
    $dbname = $config['db']['name'];
    $username = $config['db']['user'];
    $password = $config['db']['pass'];
    $charset = $config['db']['charset'];

    try {
        $dsn = "mysql:host=$host;dbname=$dbname;charset=$charset";
        $options = [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ];

        $conn = new PDO($dsn, $username, $password, $options);
        return $conn;
    } catch (PDOException $e) {
        // Si la base de datos no existe, intentar crearla
        try {
            $conn = new PDO("mysql:host=$host", $username, $password);
            $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

            // Crear la base de datos
            $conn->exec("CREATE DATABASE IF NOT EXISTS $dbname");

            // Conectar a la base de datos recién creada
            $conn = new PDO($dsn, $username, $password, $options);

            // Inicializar la base de datos
            initializeDB($conn);

            return $conn;
        } catch (PDOException $e) {
            if ($config['app']['debug']) {
                die("Error de conexión: " . $e->getMessage());
            } else {
                die("Error de conexión a la base de datos. Por favor, contacte al administrador.");
            }
        }
    }
}

// Función para inicializar la base de datos
function initializeDB($conn) {
    // Crear tabla de roles
    $conn->exec("CREATE TABLE IF NOT EXISTS roles (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(50) NOT NULL,
        description VARCHAR(255)
    )");

    // Crear tabla de usuarios
    $conn->exec("CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) NOT NULL UNIQUE,
        email VARCHAR(100) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        role_id INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        last_login TIMESTAMP NULL,
        status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
        FOREIGN KEY (role_id) REFERENCES roles(id)
    )");

    // Crear tabla de talleres
    $conn->exec("CREATE TABLE IF NOT EXISTS workshops (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        name VARCHAR(100) NOT NULL,
        location VARCHAR(255),
        phone VARCHAR(20),
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )");

    // Crear tabla de proveedores
    $conn->exec("CREATE TABLE IF NOT EXISTS suppliers (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        name VARCHAR(100) NOT NULL,
        location VARCHAR(255),
        phone VARCHAR(20),
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )");

    // Verificar si ya existen roles
    $stmt = $conn->query("SELECT COUNT(*) FROM roles");
    $roleCount = $stmt->fetchColumn();

    if ($roleCount == 0) {
        // Insertar roles
        $conn->exec("INSERT INTO roles (name, description) VALUES
            ('admin', 'Administrador del sistema'),
            ('workshop', 'Usuario de taller mecánico'),
            ('supplier', 'Proveedor de repuestos')");
    }

    // Verificar si ya existe el usuario administrador
    $stmt = $conn->query("SELECT COUNT(*) FROM users WHERE username = 'admin'");
    $adminCount = $stmt->fetchColumn();

    if ($adminCount == 0) {
        // Insertar usuario administrador
        // Contraseña: admin123
        $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $conn->prepare("INSERT INTO users (username, email, password, role_id) VALUES
            ('admin', '<EMAIL>', :password, 1)");
        $stmt->execute(['password' => $adminPassword]);
    }

    // Verificar si ya existe el usuario de taller de ejemplo
    $stmt = $conn->query("SELECT COUNT(*) FROM users WHERE username = 'taller1'");
    $workshopCount = $stmt->fetchColumn();

    if ($workshopCount == 0) {
        // Insertar usuario de taller de ejemplo
        // Contraseña: taller123
        $workshopPassword = password_hash('taller123', PASSWORD_DEFAULT);

        // Iniciar transacción
        $conn->beginTransaction();

        try {
            // Insertar usuario
            $stmt = $conn->prepare("INSERT INTO users (username, email, password, role_id) VALUES
                ('taller1', '<EMAIL>', :password, 2)");
            $stmt->execute(['password' => $workshopPassword]);
            $workshopUserId = $conn->lastInsertId();

            // Insertar datos del taller
            $stmt = $conn->prepare("INSERT INTO workshops (user_id, name, location, phone, description) VALUES
                (:user_id, 'Taller Mecánico Ejemplo', 'Calle Ejemplo 123, Ciudad', '************',
                'Taller especializado en reparación de motores y sistemas de frenos')");
            $stmt->execute(['user_id' => $workshopUserId]);

            $conn->commit();
        } catch (Exception $e) {
            $conn->rollBack();
            throw $e;
        }
    }
}

// Función para autenticar usuario
function authenticateUser($username, $password) {
    $conn = connectDB();

    $query = "SELECT u.*, r.name as role_name
             FROM users u
             JOIN roles r ON u.role_id = r.id
             WHERE u.username = :username AND u.status = 'active'";

    $stmt = $conn->prepare($query);
    $stmt->execute(['username' => $username]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($user && password_verify($password, $user['password'])) {
        // Actualizar último inicio de sesión
        $updateQuery = "UPDATE users SET last_login = NOW() WHERE id = :id";
        $updateStmt = $conn->prepare($updateQuery);
        $updateStmt->execute(['id' => $user['id']]);

        // Obtener datos adicionales según el rol
        if ($user['role_name'] === 'workshop') {
            $workshopQuery = "SELECT * FROM workshops WHERE user_id = :user_id";
            $workshopStmt = $conn->prepare($workshopQuery);
            $workshopStmt->execute(['user_id' => $user['id']]);
            $user['workshop'] = $workshopStmt->fetch(PDO::FETCH_ASSOC);
        } elseif ($user['role_name'] === 'supplier') {
            $supplierQuery = "SELECT * FROM suppliers WHERE user_id = :user_id";
            $supplierStmt = $conn->prepare($supplierQuery);
            $supplierStmt->execute(['user_id' => $user['id']]);
            $user['supplier'] = $supplierStmt->fetch(PDO::FETCH_ASSOC);
        }

        return $user;
    }

    return false;
}

// Función para obtener usuario por ID
function getUserById($userId) {
    $conn = connectDB();

    $query = "SELECT u.*, r.name as role_name
             FROM users u
             JOIN roles r ON u.role_id = r.id
             WHERE u.id = :id";

    $stmt = $conn->prepare($query);
    $stmt->execute(['id' => $userId]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($user) {
        // Obtener datos adicionales según el rol
        if ($user['role_name'] === 'workshop') {
            $workshopQuery = "SELECT * FROM workshops WHERE user_id = :user_id";
            $workshopStmt = $conn->prepare($workshopQuery);
            $workshopStmt->execute(['user_id' => $userId]);
            $user['workshop'] = $workshopStmt->fetch(PDO::FETCH_ASSOC);
        } elseif ($user['role_name'] === 'supplier') {
            $supplierQuery = "SELECT * FROM suppliers WHERE user_id = :user_id";
            $supplierStmt = $conn->prepare($supplierQuery);
            $supplierStmt->execute(['user_id' => $userId]);
            $user['supplier'] = $supplierStmt->fetch(PDO::FETCH_ASSOC);
        }
    }

    return $user;
}

// Inicializar la conexión a la base de datos
$conn = connectDB();
?>
