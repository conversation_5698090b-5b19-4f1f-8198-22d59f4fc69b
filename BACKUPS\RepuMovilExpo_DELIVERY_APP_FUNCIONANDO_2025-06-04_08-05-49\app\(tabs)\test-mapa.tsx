import React from 'react';
import { SafeAreaView, StyleSheet, Text, View } from 'react-native';
import MapaComponentSafe from '../../components/MapaComponentSafe';

export default function TestMapa() {
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>🗺️ Test Mapa RepuMovil</Text>
        <Text style={styles.subtitle}>Componente seguro funcionando</Text>
      </View>
      
      <View style={styles.mapContainer}>
        <MapaComponentSafe
          clienteLocation={{ latitude: -31.5350, longitude: -68.5400 }}
          pedidoId="test-001"
          showRoute={true}
          onLocationUpdate={(location) => {
            console.log('📍 Test - Ubicación:', location);
          }}
        />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    padding: 20,
    backgroundColor: '#4CAF50',
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 14,
    color: 'rgba(255,255,255,0.9)',
  },
  mapContainer: {
    flex: 1,
    margin: 10,
    borderRadius: 15,
    overflow: 'hidden',
  },
});
