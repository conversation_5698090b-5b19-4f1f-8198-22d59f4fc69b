import React, { useState } from 'react';
import {
  SafeAreaView,
  StatusBar,
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Modal,
  Alert,
  FlatList,
  Dimensions,
} from 'react-native';
import BuscarRepuestos from '../../components/BuscarRepuestos';

const { width } = Dimensions.get('window');

interface Trabajo {
  id: string;
  cliente: string;
  vehiculo: string;
  hora: string;
  descripcion: string;
  telefono: string;
}

const trabajosHoy: Trabajo[] = [
  { id: '1', cliente: '<PERSON>', vehiculo: 'Ford Focus 2018 - ABC123', hora: '09:00 AM', descripcion: 'Cambio de aceite', telefono: '+54 9 11 1234-5678' },
  { id: '2', cliente: '<PERSON>', vehiculo: 'Toyota Corolla 2020 - XYZ789', hora: '11:30 AM', descripcion: 'Revisión frenos', telefono: '+54 9 11 8765-4321' },
  { id: '3', cliente: '<PERSON>', vehiculo: 'Chevrolet Onix 2019 - DEF456', hora: '02:00 PM', descripcion: 'Diagnóstico eléctrico', telefono: '+54 9 11 5555-6666' },
];

export default function DashboardMecanico() {
  const [modalVisible, setModalVisible] = useState(false);
  const [buscarRepuestosVisible, setBuscarRepuestosVisible] = useState(false);
  const [modalType, setModalType] = useState<'trabajos' | 'contactos' | 'ubicaciones' | 'ingresos' | 'reputacion' | 'herramientas' | 'fotos' | 'repuestos' | 'calificaciones'>('trabajos');

  const abrirModal = (tipo: typeof modalType) => {
    setModalType(tipo);
    setModalVisible(true);
  };

  const contactarWhatsApp = (telefono: string, cliente: string) => {
    Alert.alert(
      '📱 WhatsApp',
      `Contactando a ${cliente}...\n\n${telefono}`,
      [{ text: 'OK' }]
    );
  };

  const agregarAlChanguito = (repuesto: any) => {
    Alert.alert(
      '✅ Agregado al Changuito',
      `${repuesto.nombre} agregado correctamente!`,
      [{ text: 'OK' }]
    );
    setBuscarRepuestosVisible(false);
  };

  const renderTrabajo = ({ item }: { item: Trabajo }) => (
    <View style={styles.trabajoItem}>
      <View style={styles.trabajoInfo}>
        <Text style={styles.trabajoCliente}>{item.cliente}</Text>
        <Text style={styles.trabajoVehiculo}>{item.vehiculo}</Text>
        <Text style={styles.trabajoHora}>🕐 {item.hora} - {item.descripcion}</Text>
      </View>
      <TouchableOpacity
        style={styles.btnWhatsapp}
        onPress={() => contactarWhatsApp(item.telefono, item.cliente)}
      >
        <Text style={styles.btnWhatsappText}>📱</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#2C3E50" />

      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <Text style={styles.logoText}>
            🔧 <Text style={styles.logoRepu}>Repu</Text><Text style={styles.logoMovil}>Movil</Text>
          </Text>

        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Welcome Section */}
        <View style={styles.welcomeSection}>
          <Text style={styles.welcomeSubtitle}>Gestiona tus trabajos, clientes y herramientas desde cualquier lugar</Text>
          <View style={styles.sloganSection}>
            <Text style={styles.sloganText}>
              TU TALLER VA DONDE VOS VAS, <Text style={styles.sloganHighlight}>NEEEÑO</Text>
            </Text>
          </View>
        </View>

        {/* Stats */}
        <View style={styles.statsContainer}>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>0</Text>
            <Text style={styles.statLabel}>Pedidos Realizados</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>0</Text>
            <Text style={styles.statLabel}>Repuestos en Demanda</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>4.8</Text>
            <Text style={styles.statLabel}>Calificación Promedio</Text>
          </View>
        </View>

        {/* Functions Grid */}
        <View style={styles.functionsGrid}>
          {/* Pedido de Repuestos */}
          <TouchableOpacity style={styles.functionCard} onPress={() => setBuscarRepuestosVisible(true)}>
            <Text style={styles.functionIcon}>📋</Text>
            <Text style={styles.functionTitle}>Pedido de Repuestos</Text>
            <Text style={styles.functionDescription}>Busca y solicita repuestos directamente a proveedores verificados... Agrega al carrito y realiza tu pedido.</Text>
          </TouchableOpacity>

          {/* Calificaciones Recibidas */}
          <TouchableOpacity style={styles.functionCard} onPress={() => abrirModal('calificaciones')}>
            <Text style={styles.functionIcon}>⭐</Text>
            <Text style={styles.functionTitle}>Calificaciones Recibidas</Text>
            <Text style={styles.functionDescription}>Revisa las calificaciones y comentarios de tus clientes. Mantén tu reputación y mejora tu servicio.</Text>
          </TouchableOpacity>
        </View>

      </ScrollView>

      {/* Modal Universal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                {modalType === 'trabajos' && '📋 Trabajos del Día'}
                {modalType === 'contactos' && '📱 Contacto Rápido'}
                {modalType === 'ubicaciones' && '🗺️ Mis Ubicaciones'}
                {modalType === 'ingresos' && '💰 Ingresos Diarios'}
                {modalType === 'reputacion' && '⭐ Mi Reputación'}
                {modalType === 'herramientas' && '🔧 Mis Herramientas'}
                {modalType === 'fotos' && '📸 Fotos de Trabajos'}
                {modalType === 'repuestos' && '🛒 Pedido de Repuestos'}
                {modalType === 'calificaciones' && '⭐ Calificaciones Recibidas'}
              </Text>
              <TouchableOpacity onPress={() => setModalVisible(false)}>
                <Text style={styles.closeButton}>✕</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.modalBody}>
              {modalType === 'trabajos' && (
                <FlatList
                  data={trabajosHoy}
                  renderItem={renderTrabajo}
                  keyExtractor={item => item.id}
                  style={styles.lista}
                />
              )}

              {modalType === 'contactos' && (
                <View style={styles.contactosContainer}>
                  <Text style={styles.featureTitle}>📱 Contacto Rápido</Text>
                  <Text style={styles.featureDescription}>
                    • WhatsApp directo con clientes{'\n'}
                    • Llamadas rápidas{'\n'}
                    • Lista de contactos frecuentes{'\n'}
                    • Historial de comunicaciones
                  </Text>
                </View>
              )}

              {modalType === 'ubicaciones' && (
                <View style={styles.ubicacionesContainer}>
                  <Text style={styles.featureTitle}>🗺️ Mis Ubicaciones</Text>
                  <Text style={styles.featureDescription}>
                    • Trabajos a domicilio{'\n'}
                    • Navegación GPS{'\n'}
                    • Rutas optimizadas{'\n'}
                    • Historial de ubicaciones
                  </Text>
                </View>
              )}

              {modalType === 'ingresos' && (
                <View style={styles.ingresosContainer}>
                  <View style={styles.ingresoDisplay}>
                    <Text style={styles.ingresoAmount}>$12,500</Text>
                    <Text style={styles.ingresoLabel}>Total del Día</Text>
                  </View>
                  <View style={styles.ingresosStats}>
                    <View style={styles.ingresoStat}>
                      <Text style={styles.ingresoStatNumber}>3</Text>
                      <Text style={styles.ingresoStatLabel}>Trabajos Completados</Text>
                    </View>
                    <View style={styles.ingresoStat}>
                      <Text style={styles.ingresoStatNumber}>$4,167</Text>
                      <Text style={styles.ingresoStatLabel}>Promedio por Trabajo</Text>
                    </View>
                  </View>
                </View>
              )}

              {modalType === 'reputacion' && (
                <View style={styles.reputacionContainer}>
                  <View style={styles.reputacionHeader}>
                    <Text style={styles.reputacionScore}>4.8</Text>
                    <Text style={styles.reputacionStars}>⭐⭐⭐⭐⭐</Text>
                    <Text style={styles.reputacionTotal}>25 reseñas</Text>
                  </View>
                  <View style={styles.comentarioItem}>
                    <Text style={styles.comentarioTexto}>"Excelente mecánico, muy profesional"</Text>
                    <Text style={styles.comentarioAutor}>- Juan Pérez</Text>
                  </View>
                  <View style={styles.comentarioItem}>
                    <Text style={styles.comentarioTexto}>"Rápido y confiable, lo recomiendo"</Text>
                    <Text style={styles.comentarioAutor}>- María García</Text>
                  </View>
                </View>
              )}

              {modalType === 'herramientas' && (
                <View style={styles.herramientasContainer}>
                  <Text style={styles.featureTitle}>🔧 Mis Herramientas</Text>
                  <View style={styles.herramientaItem}>
                    <Text style={styles.herramientaNombre}>🔧 Llaves combinadas</Text>
                    <Text style={styles.herramientaEstado}>✅ Disponible</Text>
                  </View>
                  <View style={styles.herramientaItem}>
                    <Text style={styles.herramientaNombre}>⚡ Multímetro</Text>
                    <Text style={styles.herramientaEstado}>✅ Disponible</Text>
                  </View>
                  <View style={styles.herramientaItem}>
                    <Text style={styles.herramientaNombre}>🔍 Scanner OBD</Text>
                    <Text style={styles.herramientaEstado}>✅ Disponible</Text>
                  </View>
                  <View style={styles.herramientaItem}>
                    <Text style={styles.herramientaNombre}>💨 Compresor</Text>
                    <Text style={styles.herramientaEstado}>⚠️ Mantenimiento</Text>
                  </View>
                </View>
              )}

              {modalType === 'fotos' && (
                <View style={styles.fotosContainer}>
                  <Text style={styles.featureTitle}>📸 Fotos de Trabajos</Text>
                  <Text style={styles.featureDescription}>
                    • Galería de antes/después{'\n'}
                    • Evidencia para clientes{'\n'}
                    • Portfolio de trabajos{'\n'}
                    • Compartir en redes sociales
                  </Text>
                </View>
              )}

              {modalType === 'repuestos' && (
                <View style={styles.repuestosContainer}>
                  <Text style={styles.featureTitle}>🛒 Pedido de Repuestos</Text>
                  <Text style={styles.featureDescription}>
                    • Buscar repuestos{'\n'}
                    • Agregar al carrito{'\n'}
                    • Solicitar cotización{'\n'}
                    • Entrega rápida
                  </Text>
                </View>
              )}

              {modalType === 'calificaciones' && (
                <View style={styles.calificacionesContainer}>
                  <View style={styles.calificacionHeader}>
                    <Text style={styles.calificacionScore}>4.8</Text>
                    <Text style={styles.calificacionStars}>⭐⭐⭐⭐⭐</Text>
                    <Text style={styles.calificacionTotal}>15 calificaciones</Text>
                  </View>
                  <View style={styles.comentarioItem}>
                    <Text style={styles.comentarioTexto}>"Excelente mecánico independiente, muy profesional"</Text>
                    <Text style={styles.comentarioAutor}>- Carlos Mendoza</Text>
                  </View>
                  <View style={styles.comentarioItem}>
                    <Text style={styles.comentarioTexto}>"Rápido y confiable, resolvió mi problema al instante"</Text>
                    <Text style={styles.comentarioAutor}>- Ana López</Text>
                  </View>
                  <View style={styles.comentarioItem}>
                    <Text style={styles.comentarioTexto}>"Muy buen servicio móvil, lo recomiendo"</Text>
                    <Text style={styles.comentarioAutor}>- Roberto Silva</Text>
                  </View>
                </View>
              )}
            </View>
          </View>
        </View>
      </Modal>

      {/* Componente de Búsqueda de Repuestos */}
      <BuscarRepuestos
        visible={buscarRepuestosVisible}
        onClose={() => setBuscarRepuestosVisible(false)}
        onAgregarAlChanguito={agregarAlChanguito}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#2C3E50',
  },
  header: {
    backgroundColor: '#34495E',
    paddingTop: 20,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  logoText: {
    fontSize: 24,
    fontWeight: '900',
    color: 'white',
  },
  logoRepu: {
    color: '#3498DB',
  },
  logoMovil: {
    color: '#E8F4FD',
  },
  userGreeting: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    backgroundColor: '#f8f9fa',
    paddingHorizontal: 15,
  },
  welcomeSection: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 20,
    padding: 25,
    margin: 20,
    alignItems: 'center',
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 5 },
    shadowOpacity: 0.3,
    shadowRadius: 10,
  },
  welcomeTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#2C3E50',
    textAlign: 'center',
    marginBottom: 10,
  },
  welcomeSubtitle: {
    fontSize: 14,
    color: '#7F8C8D',
    textAlign: 'center',
    marginBottom: 15,
  },
  sloganSection: {
    marginTop: 10,
  },
  sloganText: {
    fontSize: 16,
    fontWeight: '900',
    color: '#3498DB',
    textAlign: 'center',
  },
  sloganHighlight: {
    color: '#E74C3C',
    fontSize: 18,
  },
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  statCard: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    flexBasis: '48%',
    marginBottom: 10,
    alignItems: 'center',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  statNumber: {
    fontSize: 28,
    fontWeight: '900',
    color: '#3498DB',
    marginBottom: 8,
  },
  statLabel: {
    fontSize: 12,
    color: '#7F8C8D',
    textAlign: 'center',
    fontWeight: '600',
  },
  functionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingBottom: 30,
  },
  functionCard: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 20,
    flexBasis: '48%',
    marginBottom: 15,
    alignItems: 'center',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  functionIcon: {
    fontSize: 40,
    marginBottom: 10,
  },
  functionTitle: {
    fontSize: 14,
    fontWeight: '700',
    color: '#2C3E50',
    textAlign: 'center',
    marginBottom: 5,
  },
  functionDescription: {
    fontSize: 11,
    color: '#7F8C8D',
    textAlign: 'center',
    lineHeight: 16,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 20,
    width: width * 0.9,
    maxHeight: '80%',
    elevation: 10,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#3498DB',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: 'white',
  },
  closeButton: {
    fontSize: 24,
    color: 'white',
    fontWeight: 'bold',
  },
  modalBody: {
    padding: 20,
  },
  lista: {
    maxHeight: 300,
  },
  trabajoItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
    marginBottom: 10,
    borderLeftWidth: 4,
    borderLeftColor: '#3498DB',
  },
  trabajoInfo: {
    flex: 1,
  },
  trabajoCliente: {
    fontSize: 16,
    fontWeight: '700',
    color: '#2C3E50',
  },
  trabajoVehiculo: {
    fontSize: 12,
    color: '#7F8C8D',
    marginTop: 2,
  },
  trabajoHora: {
    fontSize: 14,
    color: '#3498DB',
    fontWeight: '600',
    marginTop: 4,
  },
  btnWhatsapp: {
    backgroundColor: '#25D366',
    borderRadius: 20,
    width: 35,
    height: 35,
    justifyContent: 'center',
    alignItems: 'center',
  },
  btnWhatsappText: {
    fontSize: 16,
  },
  contactosContainer: {
    alignItems: 'center',
  },
  ubicacionesContainer: {
    alignItems: 'center',
  },
  featureTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#2C3E50',
    marginBottom: 15,
    textAlign: 'center',
  },
  featureDescription: {
    fontSize: 16,
    color: '#7F8C8D',
    lineHeight: 24,
    textAlign: 'center',
  },
  ingresosContainer: {
    alignItems: 'center',
  },
  ingresoDisplay: {
    backgroundColor: '#27AE60',
    borderRadius: 15,
    padding: 20,
    alignItems: 'center',
    marginBottom: 20,
    width: '100%',
  },
  ingresoAmount: {
    fontSize: 32,
    fontWeight: '900',
    color: 'white',
    marginBottom: 5,
  },
  ingresoLabel: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
  },
  ingresosStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
  },
  ingresoStat: {
    alignItems: 'center',
    padding: 15,
  },
  ingresoStatNumber: {
    fontSize: 20,
    fontWeight: '700',
    color: '#3498DB',
    marginBottom: 5,
  },
  ingresoStatLabel: {
    fontSize: 12,
    color: '#7F8C8D',
    textAlign: 'center',
  },
  reputacionContainer: {
    alignItems: 'center',
  },
  reputacionHeader: {
    alignItems: 'center',
    marginBottom: 20,
  },
  reputacionScore: {
    fontSize: 48,
    fontWeight: '900',
    color: '#3498DB',
    marginBottom: 5,
  },
  reputacionStars: {
    fontSize: 20,
    marginBottom: 5,
  },
  reputacionTotal: {
    fontSize: 14,
    color: '#7F8C8D',
  },
  comentarioItem: {
    backgroundColor: '#F8F9FA',
    borderRadius: 10,
    padding: 15,
    marginBottom: 10,
    width: '100%',
  },
  comentarioTexto: {
    fontSize: 14,
    color: '#2C3E50',
    fontStyle: 'italic',
    marginBottom: 5,
  },
  comentarioAutor: {
    fontSize: 12,
    color: '#7F8C8D',
    textAlign: 'right',
  },
  herramientasContainer: {
    width: '100%',
  },
  herramientaItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#F8F9FA',
    borderRadius: 10,
    marginBottom: 8,
  },
  herramientaNombre: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2C3E50',
  },
  herramientaEstado: {
    fontSize: 12,
    fontWeight: '600',
  },
  fotosContainer: {
    alignItems: 'center',
  },
  repuestosContainer: {
    alignItems: 'center',
  },
  calificacionesContainer: {
    alignItems: 'center',
  },
  calificacionHeader: {
    alignItems: 'center',
    marginBottom: 20,
    backgroundColor: '#F8F9FA',
    borderRadius: 15,
    padding: 20,
    width: '100%',
  },
  calificacionScore: {
    fontSize: 48,
    fontWeight: '900',
    color: '#3498DB',
    marginBottom: 5,
  },
  calificacionStars: {
    fontSize: 24,
    marginBottom: 5,
  },
  calificacionTotal: {
    fontSize: 14,
    color: '#7F8C8D',
    fontWeight: '600',
  },
});
