import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Dimensions,
  StatusBar,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { MaterialIcons } from '@expo/vector-icons';

const { width, height } = Dimensions.get('window');

const COLORS = {
  primary: '#FF6B35',
  secondary: '#F7931E',
  red: '#E53E3E',
  white: '#FFFFFF',
  gradient: ['#FF6B35', '#E53E3E', '#F7931E'],
};

const SplashScreen = () => {
  const logoScale = useRef(new Animated.Value(0)).current;
  const logoRotate = useRef(new Animated.Value(0)).current;
  const textOpacity = useRef(new Animated.Value(0)).current;
  const sloganOpacity = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    startAnimations();
  }, []);

  const startAnimations = () => {
    // Animación del logo
    Animated.sequence([
      Animated.timing(logoScale, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(logoRotate, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();

    // Animación del texto
    Animated.timing(textOpacity, {
      toValue: 1,
      duration: 1000,
      delay: 500,
      useNativeDriver: true,
    }).start();

    // Animación del slogan
    Animated.timing(sloganOpacity, {
      toValue: 1,
      duration: 1000,
      delay: 1000,
      useNativeDriver: true,
    }).start();
  };

  const rotateInterpolate = logoRotate.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  return (
    <LinearGradient
      colors={COLORS.gradient}
      style={styles.container}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
    >
      <StatusBar
        barStyle="light-content"
        backgroundColor="transparent"
        translucent
      />
      
      {/* Patrón de fondo */}
      <View style={styles.backgroundPattern}>
        {[...Array(20)].map((_, i) => (
          <View
            key={i}
            style={[
              styles.patternDot,
              {
                left: Math.random() * width,
                top: Math.random() * height,
                animationDelay: Math.random() * 2000,
              },
            ]}
          />
        ))}
      </View>

      <View style={styles.content}>
        {/* Logo animado */}
        <Animated.View
          style={[
            styles.logoContainer,
            {
              transform: [
                { scale: logoScale },
                { rotate: rotateInterpolate },
              ],
            },
          ]}
        >
          <View style={styles.logoCircle}>
            <MaterialIcons name="motorcycle" size={60} color={COLORS.white} />
          </View>
        </Animated.View>

        {/* Texto principal */}
        <Animated.View
          style={[
            styles.textContainer,
            { opacity: textOpacity },
          ]}
        >
          <Text style={styles.appName}>
            <Text style={styles.repu}>Repu</Text>
            <Text style={styles.movil}>Movil</Text>
          </Text>
          <Text style={styles.delivery}>DELIVERY</Text>
        </Animated.View>

        {/* Slogan */}
        <Animated.View
          style={[
            styles.sloganContainer,
            { opacity: sloganOpacity },
          ]}
        >
          <Text style={styles.slogan}>GENERÁ INGRESOS</Text>
          <Text style={styles.slogan}>CON NOSOTROS</Text>
          
          <View style={styles.vehicleIcons}>
            <View style={styles.vehicleIcon}>
              <MaterialIcons name="directions-bike" size={24} color={COLORS.white} />
              <Text style={styles.vehicleText}>Bici</Text>
            </View>
            <View style={styles.vehicleIcon}>
              <MaterialIcons name="motorcycle" size={24} color={COLORS.white} />
              <Text style={styles.vehicleText}>Moto</Text>
            </View>
            <View style={styles.vehicleIcon}>
              <MaterialIcons name="directions-car" size={24} color={COLORS.white} />
              <Text style={styles.vehicleText}>Auto</Text>
            </View>
          </View>
        </Animated.View>

        {/* Loading indicator */}
        <View style={styles.loadingContainer}>
          <View style={styles.loadingBar}>
            <Animated.View
              style={[
                styles.loadingProgress,
                {
                  width: logoScale.interpolate({
                    inputRange: [0, 1],
                    outputRange: ['0%', '100%'],
                  }),
                },
              ]}
            />
          </View>
          <Text style={styles.loadingText}>Cargando...</Text>
        </View>
      </View>

      {/* Footer */}
      <View style={styles.footer}>
        <Text style={styles.footerText}>
          Hecho con ❤️ en San Juan, Argentina
        </Text>
      </View>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  backgroundPattern: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
  patternDot: {
    position: 'absolute',
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  logoContainer: {
    marginBottom: 40,
  },
  logoCircle: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  textContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  appName: {
    fontSize: 42,
    fontWeight: '800',
    textAlign: 'center',
    marginBottom: 5,
  },
  repu: {
    color: COLORS.white,
  },
  movil: {
    color: '#FFE5D9',
  },
  delivery: {
    fontSize: 24,
    fontWeight: '900',
    color: COLORS.white,
    letterSpacing: 3,
    textAlign: 'center',
  },
  sloganContainer: {
    alignItems: 'center',
    marginBottom: 50,
  },
  slogan: {
    fontSize: 18,
    fontWeight: '700',
    color: COLORS.white,
    textAlign: 'center',
    letterSpacing: 1,
  },
  vehicleIcons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    marginTop: 20,
  },
  vehicleIcon: {
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 15,
    minWidth: 60,
  },
  vehicleText: {
    color: COLORS.white,
    fontSize: 12,
    fontWeight: '600',
    marginTop: 5,
  },
  loadingContainer: {
    alignItems: 'center',
    width: '100%',
  },
  loadingBar: {
    width: '80%',
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 2,
    overflow: 'hidden',
    marginBottom: 10,
  },
  loadingProgress: {
    height: '100%',
    backgroundColor: COLORS.white,
    borderRadius: 2,
  },
  loadingText: {
    color: COLORS.white,
    fontSize: 14,
    fontWeight: '500',
  },
  footer: {
    position: 'absolute',
    bottom: 30,
    alignItems: 'center',
  },
  footerText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 12,
    fontWeight: '500',
  },
});

export default SplashScreen;
