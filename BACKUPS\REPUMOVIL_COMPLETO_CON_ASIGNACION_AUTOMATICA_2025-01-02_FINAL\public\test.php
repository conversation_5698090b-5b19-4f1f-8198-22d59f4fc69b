<?php
// Mostrar todos los errores
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Información básica de PHP
echo "<h1>Prueba de PHP</h1>";
echo "<p>PHP está funcionando correctamente.</p>";
echo "<p>Versión de PHP: " . phpversion() . "</p>";

// Verificar si PDO está disponible
echo "<h2>Verificación de PDO:</h2>";
if (extension_loaded('pdo')) {
    echo "<p>✅ PDO está disponible</p>";
    
    // Verificar drivers de PDO
    echo "<p>Drivers de PDO disponibles:</p>";
    echo "<ul>";
    foreach (PDO::getAvailableDrivers() as $driver) {
        echo "<li>$driver</li>";
    }
    echo "</ul>";
} else {
    echo "<p>❌ PDO no está disponible</p>";
}

// Verificar si MySQL está disponible
echo "<h2>Verificación de MySQL:</h2>";
if (extension_loaded('pdo_mysql')) {
    echo "<p>✅ PDO MySQL está disponible</p>";
    
    // Intentar conectar a MySQL
    try {
        $conn = new PDO("mysql:host=localhost", "root", "");
        echo "<p>✅ Conexión a MySQL exitosa</p>";
        
        // Verificar si la base de datos existe
        $stmt = $conn->query("SHOW DATABASES LIKE 'autoconnect_db'");
        if ($stmt->rowCount() > 0) {
            echo "<p>✅ Base de datos 'autoconnect_db' existe</p>";
            
            // Conectar a la base de datos
            $conn = new PDO("mysql:host=localhost;dbname=autoconnect_db", "root", "");
            
            // Verificar si existen las tablas
            $tables = ['roles', 'users', 'workshops', 'suppliers'];
            $existingTables = [];
            
            foreach ($tables as $table) {
                $stmt = $conn->query("SHOW TABLES LIKE '$table'");
                if ($stmt && $stmt->rowCount() > 0) {
                    $existingTables[] = $table;
                }
            }
            
            echo "<p>Tablas en la base de datos:</p>";
            echo "<ul>";
            foreach ($tables as $table) {
                $exists = in_array($table, $existingTables);
                echo "<li>$table: " . ($exists ? "✅ Existe" : "❌ No existe") . "</li>";
            }
            echo "</ul>";
            
            // Verificar si existen usuarios
            if (in_array('users', $existingTables)) {
                $stmt = $conn->query("SELECT COUNT(*) FROM users");
                $userCount = $stmt->fetchColumn();
                echo "<p>Número de usuarios: $userCount</p>";
                
                if ($userCount > 0) {
                    echo "<p>Usuarios en la base de datos:</p>";
                    echo "<table border='1' cellpadding='5'>";
                    echo "<tr><th>ID</th><th>Usuario</th><th>Email</th><th>Rol</th></tr>";
                    
                    $stmt = $conn->query("SELECT u.id, u.username, u.email, r.name as role_name FROM users u JOIN roles r ON u.role_id = r.id");
                    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                        echo "<tr>";
                        echo "<td>" . $row['id'] . "</td>";
                        echo "<td>" . $row['username'] . "</td>";
                        echo "<td>" . $row['email'] . "</td>";
                        echo "<td>" . $row['role_name'] . "</td>";
                        echo "</tr>";
                    }
                    
                    echo "</table>";
                }
            }
        } else {
            echo "<p>❌ Base de datos 'autoconnect_db' no existe</p>";
        }
    } catch (PDOException $e) {
        echo "<p>❌ Error de conexión a MySQL: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p>❌ PDO MySQL no está disponible</p>";
}

// Verificar si session está disponible
echo "<h2>Verificación de sesiones:</h2>";
if (extension_loaded('session')) {
    echo "<p>✅ Session está disponible</p>";
    
    // Verificar si la sesión está iniciada
    if (session_status() === PHP_SESSION_ACTIVE) {
        echo "<p>✅ Sesión iniciada</p>";
        
        // Mostrar variables de sesión
        echo "<p>Variables de sesión:</p>";
        echo "<pre>";
        print_r($_SESSION);
        echo "</pre>";
    } else {
        echo "<p>❌ Sesión no iniciada</p>";
    }
} else {
    echo "<p>❌ Session no está disponible</p>";
}

// Verificar permisos de archivos
echo "<h2>Verificación de permisos:</h2>";
$directories = [
    '.' => 'Directorio actual',
    '..' => 'Directorio padre',
    '../src' => 'Directorio src'
];

echo "<ul>";
foreach ($directories as $dir => $description) {
    if (file_exists($dir)) {
        echo "<li>$description ($dir): ✅ Existe";
        if (is_writable($dir)) {
            echo " (Escritura: ✅)";
        } else {
            echo " (Escritura: ❌)";
        }
        if (is_readable($dir)) {
            echo " (Lectura: ✅)";
        } else {
            echo " (Lectura: ❌)";
        }
        echo "</li>";
    } else {
        echo "<li>$description ($dir): ❌ No existe</li>";
    }
}
echo "</ul>";

// Mostrar información del servidor
echo "<h2>Información del servidor:</h2>";
echo "<p>Servidor: " . $_SERVER['SERVER_SOFTWARE'] . "</p>";
echo "<p>Documento raíz: " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p>Ruta del script: " . $_SERVER['SCRIPT_FILENAME'] . "</p>";
echo "<p>URI del script: " . $_SERVER['SCRIPT_NAME'] . "</p>";
echo "<p>Método de solicitud: " . $_SERVER['REQUEST_METHOD'] . "</p>";
echo "<p>Dirección IP del cliente: " . $_SERVER['REMOTE_ADDR'] . "</p>";

// Mostrar enlaces útiles
echo "<h2>Enlaces útiles:</h2>";
echo "<ul>";
echo "<li><a href='login_ultra_simple.php'>Página de login ultra simple</a></li>";
echo "<li><a href='login_simple.php'>Página de login simple</a></li>";
echo "<li><a href='login.php'>Página de login original</a></li>";
echo "<li><a href='index.php'>Página de inicio</a></li>";
echo "</ul>";
?>
