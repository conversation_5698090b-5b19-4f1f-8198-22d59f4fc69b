import React, { useState } from 'react';
import {
  SafeAreaView,
  StatusBar,
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  FlatList,
  Dimensions,
} from 'react-native';

const { width } = Dimensions.get('window');

interface Pedido {
  id: string;
  cliente: string;
  repuestos: string[];
  total: number;
  estado: 'nuevo' | 'preparando' | 'listo' | 'entregado';
  hora: string;
  direccion: string;
  telefono: string;
}

interface RepuestoInventario {
  id: string;
  nombre: string;
  categoria: string;
  stock: number;
  precio: number;
  marca: string;
  codigo: string;
}

const pedidosRecibidos: Pedido[] = [
  {
    id: '1',
    cliente: 'Taller Mecánico Central',
    repuestos: ['Bujía NGK', 'Filtro de aceite'],
    total: 4500,
    estado: 'nuevo',
    hora: '10:30 AM',
    direccion: 'Av. <PERSON> 1234',
    telefono: '+54 9 ************'
  },
  {
    id: '2',
    cliente: '<PERSON><PERSON><PERSON><PERSON>',
    repuestos: ['Pastillas de freno', 'Líquido de frenos'],
    total: 8200,
    estado: 'preparando',
    hora: '11:15 AM',
    direccion: 'Calle Rivadavia 567',
    telefono: '+54 9 ************'
  },
];

const inventarioEjemplo: RepuestoInventario[] = [
  { id: '1', nombre: 'Bujía NGK', categoria: 'Motor', stock: 25, precio: 2500, marca: 'NGK', codigo: 'NGK001' },
  { id: '2', nombre: 'Filtro de aceite', categoria: 'Motor', stock: 15, precio: 1200, marca: 'Mann', codigo: 'MAN002' },
  { id: '3', nombre: 'Pastillas de freno', categoria: 'Frenos', stock: 8, precio: 4500, marca: 'Bosch', codigo: 'BSH003' },
  { id: '4', nombre: 'Líquido de frenos', categoria: 'Frenos', stock: 12, precio: 800, marca: 'Castrol', codigo: 'CAS004' },
];

export default function DashboardProveedor() {
  const [modalVisible, setModalVisible] = useState(false);
  const [modalType, setModalType] = useState<'pedidos' | 'inventario' | 'clientes' | 'estadisticas' | 'entregas' | 'configuracion'>('pedidos');

  const abrirModal = (tipo: typeof modalType) => {
    setModalType(tipo);
    setModalVisible(true);
  };

  const cambiarEstadoPedido = (pedidoId: string, nuevoEstado: Pedido['estado']) => {
    Alert.alert(
      '✅ Estado Actualizado',
      `Pedido #${pedidoId} marcado como: ${nuevoEstado}`,
      [{ text: 'OK' }]
    );
  };

  const contactarCliente = (telefono: string, cliente: string) => {
    Alert.alert(
      '📱 Contactar Cliente',
      `Llamando a ${cliente}...\n\n${telefono}`,
      [{ text: 'OK' }]
    );
  };

  const renderPedido = ({ item }: { item: Pedido }) => (
    <View style={styles.pedidoCard}>
      <View style={styles.pedidoHeader}>
        <Text style={styles.pedidoCliente}>{item.cliente}</Text>
        <View style={[styles.estadoBadge, { backgroundColor: getEstadoColor(item.estado) }]}>
          <Text style={styles.estadoText}>{item.estado.toUpperCase()}</Text>
        </View>
      </View>
      
      <Text style={styles.pedidoHora}>🕐 {item.hora}</Text>
      <Text style={styles.pedidoDireccion}>📍 {item.direccion}</Text>
      
      <View style={styles.repuestosList}>
        {item.repuestos.map((repuesto, index) => (
          <Text key={index} style={styles.repuestoItem}>• {repuesto}</Text>
        ))}
      </View>
      
      <View style={styles.pedidoFooter}>
        <Text style={styles.pedidoTotal}>Total: ${item.total.toLocaleString()}</Text>
        <View style={styles.pedidoActions}>
          <TouchableOpacity
            style={styles.btnContactar}
            onPress={() => contactarCliente(item.telefono, item.cliente)}
          >
            <Text style={styles.btnText}>📱</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.btnCambiarEstado}
            onPress={() => cambiarEstadoPedido(item.id, getNextEstado(item.estado))}
          >
            <Text style={styles.btnText}>✅</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );

  const renderInventario = ({ item }: { item: RepuestoInventario }) => (
    <View style={styles.inventarioCard}>
      <View style={styles.inventarioHeader}>
        <Text style={styles.inventarioNombre}>{item.nombre}</Text>
        <Text style={styles.inventarioCodigo}>{item.codigo}</Text>
      </View>
      <Text style={styles.inventarioMarca}>Marca: {item.marca}</Text>
      <Text style={styles.inventarioCategoria}>Categoría: {item.categoria}</Text>
      <View style={styles.inventarioFooter}>
        <Text style={styles.inventarioPrecio}>${item.precio.toLocaleString()}</Text>
        <View style={[styles.stockBadge, { backgroundColor: item.stock > 10 ? '#4CAF50' : '#FF9800' }]}>
          <Text style={styles.stockText}>Stock: {item.stock}</Text>
        </View>
      </View>
    </View>
  );

  const getEstadoColor = (estado: Pedido['estado']) => {
    switch (estado) {
      case 'nuevo': return '#2196F3';
      case 'preparando': return '#FF9800';
      case 'listo': return '#4CAF50';
      case 'entregado': return '#9E9E9E';
      default: return '#2196F3';
    }
  };

  const getNextEstado = (estadoActual: Pedido['estado']): Pedido['estado'] => {
    switch (estadoActual) {
      case 'nuevo': return 'preparando';
      case 'preparando': return 'listo';
      case 'listo': return 'entregado';
      default: return 'nuevo';
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#4CAF50" />

      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <Text style={styles.logoText}>
            🏪 <Text style={styles.logoRepu}>Repu</Text><Text style={styles.logoMovil}>Movil</Text>
          </Text>
          <Text style={styles.proveedorBadge}>PROVEEDOR</Text>
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Welcome Section */}
        <View style={styles.welcomeSection}>
          <Text style={styles.welcomeTitle}>Panel de Proveedor</Text>
          <Text style={styles.welcomeSubtitle}>
            Gestiona tu inventario, pedidos y entregas desde un solo lugar
          </Text>
        </View>

        {/* Stats */}
        <View style={styles.statsContainer}>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>2</Text>
            <Text style={styles.statLabel}>Pedidos Nuevos</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>60</Text>
            <Text style={styles.statLabel}>Productos en Stock</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>$12.7K</Text>
            <Text style={styles.statLabel}>Ventas del Mes</Text>
          </View>
        </View>

        {/* Functions Grid */}
        <View style={styles.functionsGrid}>
          {/* Pedidos Recibidos */}
          <TouchableOpacity style={[styles.functionCard, styles.functionCardPrimary]} onPress={() => abrirModal('pedidos')}>
            <Text style={styles.functionIcon}>📋</Text>
            <Text style={styles.functionTitle}>Pedidos Recibidos</Text>
            <Text style={styles.functionDescription}>Gestiona los pedidos de talleres y mecánicos</Text>
          </TouchableOpacity>

          {/* Inventario */}
          <TouchableOpacity style={styles.functionCard} onPress={() => abrirModal('inventario')}>
            <Text style={styles.functionIcon}>📦</Text>
            <Text style={styles.functionTitle}>Mi Inventario</Text>
            <Text style={styles.functionDescription}>Administra tu stock de repuestos</Text>
          </TouchableOpacity>

          {/* Clientes */}
          <TouchableOpacity style={styles.functionCard} onPress={() => abrirModal('clientes')}>
            <Text style={styles.functionIcon}>👥</Text>
            <Text style={styles.functionTitle}>Mis Clientes</Text>
            <Text style={styles.functionDescription}>Lista de talleres y mecánicos</Text>
          </TouchableOpacity>

          {/* Estadísticas */}
          <TouchableOpacity style={styles.functionCard} onPress={() => abrirModal('estadisticas')}>
            <Text style={styles.functionIcon}>📊</Text>
            <Text style={styles.functionTitle}>Estadísticas</Text>
            <Text style={styles.functionDescription}>Reportes de ventas y productos</Text>
          </TouchableOpacity>

          {/* Entregas */}
          <TouchableOpacity style={styles.functionCard} onPress={() => abrirModal('entregas')}>
            <Text style={styles.functionIcon}>🚚</Text>
            <Text style={styles.functionTitle}>Gestión de Entregas</Text>
            <Text style={styles.functionDescription}>Coordina las entregas a clientes</Text>
          </TouchableOpacity>

          {/* Configuración */}
          <TouchableOpacity style={styles.functionCard} onPress={() => abrirModal('configuracion')}>
            <Text style={styles.functionIcon}>⚙️</Text>
            <Text style={styles.functionTitle}>Configuración</Text>
            <Text style={styles.functionDescription}>Ajustes del negocio y perfil</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* Modal Universal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                {modalType === 'pedidos' && '📋 Pedidos Recibidos'}
                {modalType === 'inventario' && '📦 Mi Inventario'}
                {modalType === 'clientes' && '👥 Mis Clientes'}
                {modalType === 'estadisticas' && '📊 Estadísticas de Ventas'}
                {modalType === 'entregas' && '🚚 Gestión de Entregas'}
                {modalType === 'configuracion' && '⚙️ Configuración'}
              </Text>
              <TouchableOpacity onPress={() => setModalVisible(false)}>
                <Text style={styles.closeButton}>✕</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.modalBody}>
              {modalType === 'pedidos' && (
                <FlatList
                  data={pedidosRecibidos}
                  renderItem={renderPedido}
                  keyExtractor={item => item.id}
                  style={styles.lista}
                  showsVerticalScrollIndicator={false}
                />
              )}

              {modalType === 'inventario' && (
                <FlatList
                  data={inventarioEjemplo}
                  renderItem={renderInventario}
                  keyExtractor={item => item.id}
                  style={styles.lista}
                  showsVerticalScrollIndicator={false}
                />
              )}

              {modalType === 'clientes' && (
                <View style={styles.clientesContainer}>
                  <Text style={styles.featureTitle}>👥 Mis Clientes Frecuentes</Text>
                  <View style={styles.clienteItem}>
                    <Text style={styles.clienteNombre}>🔧 Taller Mecánico Central</Text>
                    <Text style={styles.clienteInfo}>15 pedidos • $45.2K total</Text>
                  </View>
                  <View style={styles.clienteItem}>
                    <Text style={styles.clienteNombre}>👨‍🔧 Mecánico Juan Pérez</Text>
                    <Text style={styles.clienteInfo}>8 pedidos • $18.7K total</Text>
                  </View>
                  <View style={styles.clienteItem}>
                    <Text style={styles.clienteNombre}>🏪 Taller Rodriguez</Text>
                    <Text style={styles.clienteInfo}>12 pedidos • $32.1K total</Text>
                  </View>
                </View>
              )}

              {modalType === 'estadisticas' && (
                <View style={styles.estadisticasContainer}>
                  <Text style={styles.featureTitle}>📊 Resumen del Mes</Text>
                  <View style={styles.estadisticaItem}>
                    <Text style={styles.estadisticaLabel}>Ventas Totales:</Text>
                    <Text style={styles.estadisticaValor}>$127.500</Text>
                  </View>
                  <View style={styles.estadisticaItem}>
                    <Text style={styles.estadisticaLabel}>Pedidos Completados:</Text>
                    <Text style={styles.estadisticaValor}>35</Text>
                  </View>
                  <View style={styles.estadisticaItem}>
                    <Text style={styles.estadisticaLabel}>Producto Más Vendido:</Text>
                    <Text style={styles.estadisticaValor}>Bujías NGK</Text>
                  </View>
                  <View style={styles.estadisticaItem}>
                    <Text style={styles.estadisticaLabel}>Cliente Top:</Text>
                    <Text style={styles.estadisticaValor}>Taller Central</Text>
                  </View>
                </View>
              )}

              {modalType === 'entregas' && (
                <View style={styles.entregasContainer}>
                  <Text style={styles.featureTitle}>🚚 Entregas Programadas</Text>
                  <Text style={styles.featureDescription}>
                    • Zona Norte: 2 entregas pendientes{'\n'}
                    • Zona Centro: 1 entrega en curso{'\n'}
                    • Zona Sur: 3 entregas programadas{'\n'}
                    • Tiempo promedio: 45 minutos
                  </Text>
                </View>
              )}

              {modalType === 'configuracion' && (
                <View style={styles.configuracionContainer}>
                  <Text style={styles.featureTitle}>⚙️ Configuración del Negocio</Text>
                  <Text style={styles.featureDescription}>
                    • Horarios de atención{'\n'}
                    • Zonas de cobertura{'\n'}
                    • Métodos de pago{'\n'}
                    • Tiempo de preparación{'\n'}
                    • Costo de delivery{'\n'}
                    • Notificaciones
                  </Text>
                </View>
              )}
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f7fa',
  },
  header: {
    backgroundColor: '#4CAF50',
    paddingVertical: 20,
    paddingHorizontal: 20,
    borderBottomLeftRadius: 25,
    borderBottomRightRadius: 25,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  logoText: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  logoRepu: {
    color: 'white',
  },
  logoMovil: {
    color: '#E8F5E8',
  },
  proveedorBadge: {
    backgroundColor: 'white',
    color: '#4CAF50',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 15,
    fontSize: 12,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  welcomeSection: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 25,
    marginVertical: 20,
    alignItems: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
  },
  welcomeTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
    textAlign: 'center',
  },
  welcomeSubtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 20,
  },
  statCard: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 15,
    alignItems: 'center',
    flex: 1,
    marginHorizontal: 5,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
    marginTop: 5,
  },
  functionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingBottom: 30,
  },
  functionCard: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    width: '48%',
    marginBottom: 15,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    borderLeftWidth: 4,
    borderLeftColor: '#4CAF50',
  },
  functionCardPrimary: {
    backgroundColor: '#4CAF50',
    borderLeftColor: 'white',
  },
  functionIcon: {
    fontSize: 40,
    marginBottom: 10,
  },
  functionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  functionDescription: {
    fontSize: 12,
    color: '#666',
    lineHeight: 18,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 20,
    width: width * 0.95,
    maxHeight: '85%',
    elevation: 10,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#4CAF50',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
  },
  closeButton: {
    fontSize: 24,
    color: 'white',
    fontWeight: 'bold',
  },
  modalBody: {
    padding: 20,
    maxHeight: 400,
  },
  lista: {
    maxHeight: 350,
  },
  // Estilos para Pedidos
  pedidoCard: {
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: 15,
    marginBottom: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#4CAF50',
  },
  pedidoHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  pedidoCliente: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  estadoBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  estadoText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
  },
  pedidoHora: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  pedidoDireccion: {
    fontSize: 12,
    color: '#666',
    marginBottom: 8,
  },
  repuestosList: {
    marginBottom: 12,
  },
  repuestoItem: {
    fontSize: 12,
    color: '#333',
    marginBottom: 2,
  },
  pedidoFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  pedidoTotal: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  pedidoActions: {
    flexDirection: 'row',
  },
  btnContactar: {
    backgroundColor: '#2196F3',
    padding: 8,
    borderRadius: 8,
    marginRight: 8,
  },
  btnCambiarEstado: {
    backgroundColor: '#4CAF50',
    padding: 8,
    borderRadius: 8,
  },
  btnText: {
    color: 'white',
    fontSize: 12,
  },
  // Estilos para Inventario
  inventarioCard: {
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: 15,
    marginBottom: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#FF9800',
  },
  inventarioHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  inventarioNombre: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  inventarioCodigo: {
    fontSize: 12,
    color: '#666',
    backgroundColor: '#e9ecef',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8,
  },
  inventarioMarca: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  inventarioCategoria: {
    fontSize: 12,
    color: '#666',
    marginBottom: 8,
  },
  inventarioFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  inventarioPrecio: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FF9800',
  },
  stockBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  stockText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
  },
  // Estilos para otras secciones
  featureTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  featureDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 22,
  },
  clientesContainer: {
    paddingVertical: 10,
  },
  clienteItem: {
    backgroundColor: '#f8f9fa',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  clienteNombre: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  clienteInfo: {
    fontSize: 12,
    color: '#666',
  },
  estadisticasContainer: {
    paddingVertical: 10,
  },
  estadisticaItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  estadisticaLabel: {
    fontSize: 14,
    color: '#666',
  },
  estadisticaValor: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  entregasContainer: {
    paddingVertical: 10,
  },
  configuracionContainer: {
    paddingVertical: 10,
  },
});
