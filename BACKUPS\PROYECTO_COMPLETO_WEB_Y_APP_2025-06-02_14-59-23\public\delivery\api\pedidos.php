<?php
/**
 * API de Pedidos para RepuMovil Delivery
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, PUT, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Manejar preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once '../config.php';

/**
 * Función para enviar respuesta JSON
 */
function sendResponse($success, $message, $data = null, $code = 200) {
    http_response_code($code);
    echo json_encode([
        'success' => $success,
        'message' => $message,
        'data' => $data,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    exit();
}

/**
 * Función para validar entrada JSON
 */
function getJsonInput() {
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        sendResponse(false, 'JSON inválido', null, 400);
    }
    
    return $data;
}

try {
    $pdo = connectDB();
    $method = $_SERVER['REQUEST_METHOD'];
    $action = $_GET['action'] ?? '';

    switch ($method) {
        case 'GET':
            switch ($action) {
                case 'disponibles':
                    handleGetPedidosDisponibles($pdo);
                    break;
                case 'mis-pedidos':
                    handleGetMisPedidos($pdo);
                    break;
                case 'historial':
                    handleGetHistorial($pdo);
                    break;
                case 'estadisticas':
                    handleGetEstadisticas($pdo);
                    break;
                default:
                    sendResponse(false, 'Acción no válida', null, 400);
            }
            break;
        
        case 'POST':
            switch ($action) {
                case 'aceptar':
                    handleAceptarPedido($pdo);
                    break;
                case 'completar':
                    handleCompletarPedido($pdo);
                    break;
                case 'cancelar':
                    handleCancelarPedido($pdo);
                    break;
                default:
                    sendResponse(false, 'Acción no válida', null, 400);
            }
            break;
        
        case 'PUT':
            switch ($action) {
                case 'actualizar-estado':
                    handleActualizarEstado($pdo);
                    break;
                default:
                    sendResponse(false, 'Acción no válida', null, 400);
            }
            break;
        
        default:
            sendResponse(false, 'Método no permitido', null, 405);
    }

} catch (Exception $e) {
    logActivity("Error en API pedidos: " . $e->getMessage());
    sendResponse(false, 'Error interno del servidor', null, 500);
}

/**
 * Obtener pedidos disponibles
 */
function handleGetPedidosDisponibles($pdo) {
    try {
        $stmt = $pdo->prepare("
            SELECT p.*, 
                   ROUND(RAND() * 5 + 1, 1) as distancia_km,
                   ROUND(RAND() * 30 + 10) as tiempo_estimado_min
            FROM pedidos p 
            WHERE p.estado = 'pendiente' 
            ORDER BY p.fecha_pedido DESC 
            LIMIT 20
        ");
        $stmt->execute();
        $pedidos = $stmt->fetchAll();
        
        // Formatear datos para la app
        $pedidosFormateados = array_map(function($pedido) {
            return [
                'id' => $pedido['id'],
                'cliente' => $pedido['cliente_nombre'],
                'telefono' => $pedido['cliente_telefono'],
                'direccion_origen' => $pedido['direccion_origen'],
                'direccion_destino' => $pedido['direccion_destino'],
                'descripcion' => $pedido['descripcion_pedido'],
                'valor_pedido' => floatval($pedido['valor_pedido']),
                'ganancia' => floatval($pedido['comision_repartidor']),
                'distancia' => $pedido['distancia_km'] . ' km',
                'tiempo_estimado' => $pedido['tiempo_estimado_min'] . ' min',
                'tipo' => strpos(strtolower($pedido['descripcion_pedido']), 'repuesto') !== false ? 'repuestos' : 'servicio',
                'fecha_pedido' => $pedido['fecha_pedido']
            ];
        }, $pedidos);
        
        sendResponse(true, 'Pedidos disponibles obtenidos', $pedidosFormateados);
        
    } catch (PDOException $e) {
        logActivity("Error obteniendo pedidos disponibles: " . $e->getMessage());
        sendResponse(false, 'Error en el servidor', null, 500);
    }
}

/**
 * Obtener pedidos del repartidor actual
 */
function handleGetMisPedidos($pdo) {
    $repartidorId = $_GET['repartidor_id'] ?? null;
    
    if (!$repartidorId) {
        sendResponse(false, 'ID de repartidor requerido', null, 400);
    }
    
    try {
        $stmt = $pdo->prepare("
            SELECT p.*, 
                   ROUND(RAND() * 5 + 1, 1) as distancia_km,
                   ROUND(RAND() * 30 + 10) as tiempo_estimado_min
            FROM pedidos p 
            WHERE p.repartidor_id = ? 
            AND p.estado IN ('asignado', 'en_camino')
            ORDER BY p.fecha_asignacion DESC
        ");
        $stmt->execute([$repartidorId]);
        $pedidos = $stmt->fetchAll();
        
        // Formatear datos
        $pedidosFormateados = array_map(function($pedido) {
            return [
                'id' => $pedido['id'],
                'cliente' => $pedido['cliente_nombre'],
                'telefono' => $pedido['cliente_telefono'],
                'direccion_origen' => $pedido['direccion_origen'],
                'direccion_destino' => $pedido['direccion_destino'],
                'descripcion' => $pedido['descripcion_pedido'],
                'valor_pedido' => floatval($pedido['valor_pedido']),
                'ganancia' => floatval($pedido['comision_repartidor']),
                'estado' => $pedido['estado'],
                'distancia' => $pedido['distancia_km'] . ' km',
                'tiempo_estimado' => $pedido['tiempo_estimado_min'] . ' min',
                'fecha_asignacion' => $pedido['fecha_asignacion']
            ];
        }, $pedidos);
        
        sendResponse(true, 'Mis pedidos obtenidos', $pedidosFormateados);
        
    } catch (PDOException $e) {
        logActivity("Error obteniendo mis pedidos: " . $e->getMessage());
        sendResponse(false, 'Error en el servidor', null, 500);
    }
}

/**
 * Obtener historial de entregas
 */
function handleGetHistorial($pdo) {
    $repartidorId = $_GET['repartidor_id'] ?? null;
    $limite = $_GET['limite'] ?? 50;
    
    if (!$repartidorId) {
        sendResponse(false, 'ID de repartidor requerido', null, 400);
    }
    
    try {
        $stmt = $pdo->prepare("
            SELECT p.*, c.calificacion, c.comentario,
                   ROUND(RAND() * 5 + 1, 1) as distancia_km,
                   ROUND(RAND() * 30 + 10) as tiempo_total_min
            FROM pedidos p 
            LEFT JOIN calificaciones c ON p.id = c.pedido_id
            WHERE p.repartidor_id = ? 
            AND p.estado IN ('entregado', 'cancelado')
            ORDER BY p.fecha_entrega DESC, p.fecha_pedido DESC
            LIMIT ?
        ");
        $stmt->execute([$repartidorId, $limite]);
        $historial = $stmt->fetchAll();
        
        // Formatear datos
        $historialFormateado = array_map(function($entrega) {
            return [
                'id' => $entrega['id'],
                'fecha' => date('Y-m-d', strtotime($entrega['fecha_entrega'] ?? $entrega['fecha_pedido'])),
                'hora' => date('H:i', strtotime($entrega['fecha_entrega'] ?? $entrega['fecha_pedido'])),
                'cliente' => $entrega['cliente_nombre'],
                'direccion' => $entrega['direccion_destino'],
                'tipo' => strpos(strtolower($entrega['descripcion_pedido']), 'repuesto') !== false ? 'repuestos' : 'servicio',
                'estado' => $entrega['estado'] === 'entregado' ? 'completado' : 'cancelado',
                'ganancia' => $entrega['estado'] === 'entregado' ? '$' . number_format($entrega['comision_repartidor'], 0) : '$0',
                'distancia' => $entrega['distancia_km'] . ' km',
                'tiempo_total' => $entrega['tiempo_total_min'] . ' min',
                'calificacion' => $entrega['calificacion'] ?? 0,
                'comentario' => $entrega['comentario']
            ];
        }, $historial);
        
        sendResponse(true, 'Historial obtenido', $historialFormateado);
        
    } catch (PDOException $e) {
        logActivity("Error obteniendo historial: " . $e->getMessage());
        sendResponse(false, 'Error en el servidor', null, 500);
    }
}

/**
 * Obtener estadísticas del repartidor
 */
function handleGetEstadisticas($pdo) {
    $repartidorId = $_GET['repartidor_id'] ?? null;
    $periodo = $_GET['periodo'] ?? 'semana'; // semana, mes, año
    
    if (!$repartidorId) {
        sendResponse(false, 'ID de repartidor requerido', null, 400);
    }
    
    try {
        // Determinar rango de fechas
        $fechaInicio = match($periodo) {
            'semana' => date('Y-m-d', strtotime('-7 days')),
            'mes' => date('Y-m-d', strtotime('-30 days')),
            'año' => date('Y-m-d', strtotime('-365 days')),
            default => date('Y-m-d', strtotime('-7 days'))
        };
        
        // Estadísticas generales
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(*) as total_entregas,
                SUM(comision_repartidor) as total_ganancias,
                AVG(c.calificacion) as calificacion_promedio
            FROM pedidos p
            LEFT JOIN calificaciones c ON p.id = c.pedido_id
            WHERE p.repartidor_id = ? 
            AND p.estado = 'entregado'
            AND DATE(p.fecha_entrega) >= ?
        ");
        $stmt->execute([$repartidorId, $fechaInicio]);
        $estadisticas = $stmt->fetch();
        
        // Ganancias por día
        $stmt = $pdo->prepare("
            SELECT 
                DATE(fecha_entrega) as fecha,
                COUNT(*) as entregas,
                SUM(comision_repartidor) as ganancia_bruta,
                SUM(comision_repartidor * 0.1) as comision_plataforma,
                SUM(comision_repartidor * 0.9) as ganancia_neta
            FROM pedidos 
            WHERE repartidor_id = ? 
            AND estado = 'entregado'
            AND DATE(fecha_entrega) >= ?
            GROUP BY DATE(fecha_entrega)
            ORDER BY fecha DESC
        ");
        $stmt->execute([$repartidorId, $fechaInicio]);
        $gananciasDiarias = $stmt->fetchAll();
        
        $resultado = [
            'resumen' => [
                'total_entregas' => intval($estadisticas['total_entregas']),
                'total_ganancias' => floatval($estadisticas['total_ganancias']),
                'calificacion_promedio' => round(floatval($estadisticas['calificacion_promedio']), 1),
                'periodo' => $periodo
            ],
            'ganancias_diarias' => array_map(function($dia) {
                return [
                    'fecha' => $dia['fecha'],
                    'entregas' => intval($dia['entregas']),
                    'ganancia_bruta' => floatval($dia['ganancia_bruta']),
                    'comision_plataforma' => floatval($dia['comision_plataforma']),
                    'ganancia_neta' => floatval($dia['ganancia_neta']),
                    'tiempo_activo' => rand(3, 8) . 'h ' . rand(10, 50) . 'm' // Simulado
                ];
            }, $gananciasDiarias)
        ];
        
        sendResponse(true, 'Estadísticas obtenidas', $resultado);
        
    } catch (PDOException $e) {
        logActivity("Error obteniendo estadísticas: " . $e->getMessage());
        sendResponse(false, 'Error en el servidor', null, 500);
    }
}

/**
 * Aceptar un pedido
 */
function handleAceptarPedido($pdo) {
    $data = getJsonInput();
    
    if (!isset($data['pedido_id']) || !isset($data['repartidor_id'])) {
        sendResponse(false, 'ID de pedido y repartidor requeridos', null, 400);
    }
    
    try {
        $pdo->beginTransaction();
        
        // Verificar que el pedido esté disponible
        $stmt = $pdo->prepare("SELECT * FROM pedidos WHERE id = ? AND estado = 'pendiente'");
        $stmt->execute([$data['pedido_id']]);
        $pedido = $stmt->fetch();
        
        if (!$pedido) {
            sendResponse(false, 'Pedido no disponible', null, 404);
        }
        
        // Asignar pedido al repartidor
        $stmt = $pdo->prepare("
            UPDATE pedidos 
            SET repartidor_id = ?, estado = 'asignado', fecha_asignacion = NOW() 
            WHERE id = ?
        ");
        $stmt->execute([$data['repartidor_id'], $data['pedido_id']]);
        
        $pdo->commit();
        
        logActivity("Pedido {$data['pedido_id']} aceptado", $data['repartidor_id']);
        sendResponse(true, 'Pedido aceptado exitosamente', [
            'pedido_id' => $data['pedido_id'],
            'estado' => 'asignado'
        ]);
        
    } catch (PDOException $e) {
        $pdo->rollBack();
        logActivity("Error aceptando pedido: " . $e->getMessage());
        sendResponse(false, 'Error en el servidor', null, 500);
    }
}

/**
 * Completar un pedido
 */
function handleCompletarPedido($pdo) {
    $data = getJsonInput();
    
    if (!isset($data['pedido_id']) || !isset($data['repartidor_id'])) {
        sendResponse(false, 'ID de pedido y repartidor requeridos', null, 400);
    }
    
    try {
        $pdo->beginTransaction();
        
        // Verificar que el pedido pertenezca al repartidor
        $stmt = $pdo->prepare("
            SELECT * FROM pedidos 
            WHERE id = ? AND repartidor_id = ? AND estado IN ('asignado', 'en_camino')
        ");
        $stmt->execute([$data['pedido_id'], $data['repartidor_id']]);
        $pedido = $stmt->fetch();
        
        if (!$pedido) {
            sendResponse(false, 'Pedido no encontrado o no autorizado', null, 404);
        }
        
        // Completar pedido
        $stmt = $pdo->prepare("
            UPDATE pedidos 
            SET estado = 'entregado', fecha_entrega = NOW() 
            WHERE id = ?
        ");
        $stmt->execute([$data['pedido_id']]);
        
        // Registrar ganancia
        $stmt = $pdo->prepare("
            INSERT INTO ganancias (repartidor_id, pedido_id, monto, fecha_ganancia) 
            VALUES (?, ?, ?, NOW())
        ");
        $stmt->execute([$data['repartidor_id'], $data['pedido_id'], $pedido['comision_repartidor']]);
        
        $pdo->commit();
        
        logActivity("Pedido {$data['pedido_id']} completado", $data['repartidor_id']);
        sendResponse(true, 'Pedido completado exitosamente', [
            'pedido_id' => $data['pedido_id'],
            'ganancia' => floatval($pedido['comision_repartidor'])
        ]);
        
    } catch (PDOException $e) {
        $pdo->rollBack();
        logActivity("Error completando pedido: " . $e->getMessage());
        sendResponse(false, 'Error en el servidor', null, 500);
    }
}

/**
 * Cancelar un pedido
 */
function handleCancelarPedido($pdo) {
    $data = getJsonInput();
    
    if (!isset($data['pedido_id']) || !isset($data['repartidor_id'])) {
        sendResponse(false, 'ID de pedido y repartidor requeridos', null, 400);
    }
    
    try {
        // Verificar que el pedido pertenezca al repartidor
        $stmt = $pdo->prepare("
            SELECT * FROM pedidos 
            WHERE id = ? AND repartidor_id = ? AND estado IN ('asignado', 'en_camino')
        ");
        $stmt->execute([$data['pedido_id'], $data['repartidor_id']]);
        $pedido = $stmt->fetch();
        
        if (!$pedido) {
            sendResponse(false, 'Pedido no encontrado o no autorizado', null, 404);
        }
        
        // Cancelar pedido (volver a pendiente para que otro repartidor lo tome)
        $stmt = $pdo->prepare("
            UPDATE pedidos 
            SET repartidor_id = NULL, estado = 'pendiente', fecha_asignacion = NULL 
            WHERE id = ?
        ");
        $stmt->execute([$data['pedido_id']]);
        
        logActivity("Pedido {$data['pedido_id']} cancelado", $data['repartidor_id']);
        sendResponse(true, 'Pedido cancelado exitosamente');
        
    } catch (PDOException $e) {
        logActivity("Error cancelando pedido: " . $e->getMessage());
        sendResponse(false, 'Error en el servidor', null, 500);
    }
}

/**
 * Actualizar estado de pedido
 */
function handleActualizarEstado($pdo) {
    $data = getJsonInput();
    
    if (!isset($data['pedido_id']) || !isset($data['estado'])) {
        sendResponse(false, 'ID de pedido y estado requeridos', null, 400);
    }
    
    $estadosValidos = ['asignado', 'en_camino', 'entregado', 'cancelado'];
    if (!in_array($data['estado'], $estadosValidos)) {
        sendResponse(false, 'Estado no válido', null, 400);
    }
    
    try {
        $stmt = $pdo->prepare("UPDATE pedidos SET estado = ? WHERE id = ?");
        $stmt->execute([$data['estado'], $data['pedido_id']]);
        
        if ($stmt->rowCount() === 0) {
            sendResponse(false, 'Pedido no encontrado', null, 404);
        }
        
        logActivity("Estado de pedido {$data['pedido_id']} actualizado a {$data['estado']}");
        sendResponse(true, 'Estado actualizado exitosamente');
        
    } catch (PDOException $e) {
        logActivity("Error actualizando estado: " . $e->getMessage());
        sendResponse(false, 'Error en el servidor', null, 500);
    }
}
?>
