<?php
// api/requests.php - Funciones para gestión de solicitudes de repuestos

/**
 * Obtiene todas las solicitudes de repuestos
 * @return array Lista de solicitudes
 */
function getRequests() {
    global $current_user_id;
    $conn = connectDB();
    
    // Determinar si el usuario es un taller o un proveedor
    $stmt = $conn->prepare("SELECT role_id FROM users WHERE id = :user_id");
    $stmt->execute(['user_id' => $current_user_id]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user['role_id'] == 2) {
        // Es un taller, mostrar solo sus solicitudes
        $query = "
            SELECT r.*, w.name as workshop_name, 
                   (SELECT COUNT(*) FROM request_offers WHERE request_id = r.id) as offers_count
            FROM part_requests r
            JOIN workshops w ON r.workshop_id = w.id
            WHERE w.user_id = :user_id
            ORDER BY r.created_at DESC
        ";
        $stmt = $conn->prepare($query);
        $stmt->execute(['user_id' => $current_user_id]);
    } else {
        // Es un proveedor, mostrar todas las solicitudes públicas
        $query = "
            SELECT r.*, w.name as workshop_name, 
                   (SELECT COUNT(*) FROM request_offers WHERE request_id = r.id) as offers_count
            FROM part_requests r
            JOIN workshops w ON r.workshop_id = w.id
            WHERE r.status != 'completed' AND r.visibility = 'public'
            ORDER BY r.created_at DESC
        ";
        $stmt = $conn->query($query);
    }
    
    $requests = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    return [
        'status' => 'success',
        'requests' => $requests
    ];
}

/**
 * Obtiene información de una solicitud específica
 * @param int $id ID de la solicitud
 * @return array Datos de la solicitud
 */
function getRequest($id) {
    global $current_user_id;
    $conn = connectDB();
    
    // Obtener la solicitud
    $query = "
        SELECT r.*, w.name as workshop_name, w.location as workshop_location, w.phone as workshop_phone
        FROM part_requests r
        JOIN workshops w ON r.workshop_id = w.id
        WHERE r.id = :id
    ";
    $stmt = $conn->prepare($query);
    $stmt->execute(['id' => $id]);
    $request = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$request) {
        return [
            'status' => 'error',
            'message' => 'Solicitud no encontrada'
        ];
    }
    
    // Verificar permisos de acceso
    $stmt = $conn->prepare("SELECT role_id FROM users WHERE id = :user_id");
    $stmt->execute(['user_id' => $current_user_id]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Si es un taller, verificar que sea el propietario
    if ($user['role_id'] == 2) {
        $stmt = $conn->prepare("SELECT user_id FROM workshops WHERE id = :workshop_id");
        $stmt->execute(['workshop_id' => $request['workshop_id']]);
        $workshop = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($workshop['user_id'] != $current_user_id) {
            return [
                'status' => 'error',
                'message' => 'No tienes permiso para ver esta solicitud'
            ];
        }
    } 
    // Si es un proveedor, verificar que la solicitud sea pública o tenga una oferta
    else if ($user['role_id'] == 3 && $request['visibility'] != 'public') {
        $stmt = $conn->prepare("
            SELECT o.id FROM request_offers o
            JOIN suppliers s ON o.supplier_id = s.id
            WHERE o.request_id = :request_id AND s.user_id = :user_id
        ");
        $stmt->execute([
            'request_id' => $id,
            'user_id' => $current_user_id
        ]);
        
        if ($stmt->rowCount() == 0) {
            return [
                'status' => 'error',
                'message' => 'No tienes permiso para ver esta solicitud'
            ];
        }
    }
    
    // Obtener imágenes de la solicitud
    $query = "SELECT * FROM request_images WHERE request_id = :request_id";
    $stmt = $conn->prepare($query);
    $stmt->execute(['request_id' => $id]);
    $images = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $request['images'] = $images;
    
    // Si es el taller propietario, obtener también las ofertas
    if ($user['role_id'] == 2) {
        $query = "
            SELECT o.*, s.name as supplier_name, s.location as supplier_location
            FROM request_offers o
            JOIN suppliers s ON o.supplier_id = s.id
            WHERE o.request_id = :request_id
            ORDER BY o.created_at DESC
        ";
        $stmt = $conn->prepare($query);
        $stmt->execute(['request_id' => $id]);
        $offers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $request['offers'] = $offers;
    }
    
    return [
        'status' => 'success',
        'request' => $request
    ];
}

/**
 * Crea una nueva solicitud de repuesto
 * @param array $data Datos de la solicitud
 * @return array Resultado de la operación
 */
function createRequest($data) {
    global $current_user_id;
    $conn = connectDB();
    
    // Verificar que el usuario es un taller
    $stmt = $conn->prepare("SELECT w.id FROM workshops w WHERE w.user_id = :user_id");
    $stmt->execute(['user_id' => $current_user_id]);
    $workshop = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$workshop) {
        return [
            'status' => 'error',
            'message' => 'Solo los talleres pueden crear solicitudes'
        ];
    }
    
    $workshopId = $workshop['id'];
    
    // Validar campos requeridos
    $requiredFields = ['part_name', 'vehicle_brand', 'vehicle_model', 'vehicle_year', 'urgency'];
    foreach ($requiredFields as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            return [
                'status' => 'error',
                'message' => "El campo '$field' es requerido"
            ];
        }
    }
    
    // Insertar solicitud
    $stmt = $conn->prepare("INSERT INTO part_requests (
        workshop_id, part_name, description, vehicle_brand, vehicle_model, 
        vehicle_year, urgency, visibility, status
    ) VALUES (
        :workshop_id, :part_name, :description, :vehicle_brand, :vehicle_model, 
        :vehicle_year, :urgency, :visibility, 'pending'
    )");
    
    $result = $stmt->execute([
        'workshop_id' => $workshopId,
        'part_name' => $data['part_name'],
        'description' => $data['description'] ?? '',
        'vehicle_brand' => $data['vehicle_brand'],
        'vehicle_model' => $data['vehicle_model'],
        'vehicle_year' => $data['vehicle_year'],
        'urgency' => $data['urgency'],
        'visibility' => $data['visibility'] ?? 'public'
    ]);
    
    if (!$result) {
        return [
            'status' => 'error',
            'message' => 'Error al crear la solicitud'
        ];
    }
    
    $requestId = $conn->lastInsertId();
    
    // Procesar imágenes si se proporcionaron
    if (isset($data['images']) && is_array($data['images'])) {
        foreach ($data['images'] as $imageUrl) {
            $stmt = $conn->prepare("INSERT INTO request_images (request_id, image_url) VALUES (:request_id, :image_url)");
            $stmt->execute([
                'request_id' => $requestId,
                'image_url' => $imageUrl
            ]);
        }
    }
    