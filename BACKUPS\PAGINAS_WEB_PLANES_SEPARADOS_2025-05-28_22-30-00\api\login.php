<?php
// API para login de usuarios
session_start();

header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header("Content-Type: application/json; charset=UTF-8");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Método no permitido']);
    exit;
}

require_once '../db_config.php';

try {
    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';
    $remember = isset($_POST['remember']);

    // Validaciones básicas
    if (empty($email) || empty($password)) {
        throw new Exception('Email y contraseña son requeridos');
    }

    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('Email no válido');
    }

    // Buscar usuario en la base de datos
    $stmt = $pdo->prepare("
        SELECT id, email, password, user_type, nombre, telefono, status, created_at
        FROM users
        WHERE email = ?
    ");
    $stmt->execute([$email]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$user) {
        throw new Exception('Email o contraseña incorrectos');
    }

    // Verificar contraseña
    if (!password_verify($password, $user['password'])) {
        throw new Exception('Email o contraseña incorrectos');
    }

    // Verificar estado de la cuenta
    if ($user['status'] === 'inactive') {
        throw new Exception('Tu cuenta está inactiva. Contacta al soporte.');
    }

    // Obtener datos adicionales según el tipo de usuario
    $additional_data = [];
    switch ($user['user_type']) {
        case 'taller_mecanico':
            $stmt = $pdo->prepare("
                SELECT username, direccion, rubro_principal, datos_fiscales,
                       plan_type, capacidad_clientes, servicios_adicionales,
                       verificado, calificacion, total_trabajos
                FROM talleres_mecanicos
                WHERE user_id = ?
            ");
            $stmt->execute([$user['id']]);
            $additional_data = $stmt->fetch(PDO::FETCH_ASSOC) ?: [];
            break;

        case 'proveedor_repuestos':
            $stmt = $pdo->prepare("
                SELECT ubicacion_local, zonas_entrega, datos_contacto,
                       horarios_atencion, verificado, calificacion, total_ventas
                FROM proveedores_repuestos
                WHERE user_id = ?
            ");
            $stmt->execute([$user['id']]);
            $additional_data = $stmt->fetch(PDO::FETCH_ASSOC) ?: [];
            break;

        case 'mecanico_independiente':
            $stmt = $pdo->prepare("
                SELECT especialidades, experiencia_anos, descripcion,
                       zona_trabajo, precio_hora, disponible, calificacion, total_trabajos
                FROM mecanicos_independientes
                WHERE user_id = ?
            ");
            $stmt->execute([$user['id']]);
            $additional_data = $stmt->fetch(PDO::FETCH_ASSOC) ?: [];
            break;
    }

    // Actualizar último login
    $stmt = $pdo->prepare("UPDATE users SET updated_at = NOW() WHERE id = ?");
    $stmt->execute([$user['id']]);

    // Crear sesión
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['user_email'] = $user['email'];
    $_SESSION['user_type'] = $user['user_type'];
    $_SESSION['user_name'] = $user['nombre'];
    $_SESSION['logged_in'] = true;

    // Configurar cookie si se seleccionó "recordar"
    if ($remember) {
        $token = bin2hex(random_bytes(32));

        // Guardar token en la base de datos (crear tabla remember_tokens si no existe)
        try {
            $stmt = $pdo->prepare("
                INSERT INTO remember_tokens (user_id, token, expires_at)
                VALUES (?, ?, DATE_ADD(NOW(), INTERVAL 30 DAY))
                ON DUPLICATE KEY UPDATE
                token = VALUES(token), expires_at = VALUES(expires_at)
            ");
            $stmt->execute([$user['id'], hash('sha256', $token)]);

            // Configurar cookie
            setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/', '', false, true);
        } catch (PDOException $e) {
            // Si la tabla no existe, la creamos
            $pdo->exec("
                CREATE TABLE IF NOT EXISTS remember_tokens (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    token VARCHAR(255) NOT NULL,
                    expires_at TIMESTAMP NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                    UNIQUE KEY unique_user (user_id)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
        }
    }

    // Respuesta exitosa
    echo json_encode([
        'success' => true,
        'message' => 'Inicio de sesión exitoso',
        'user' => [
            'id' => $user['id'],
            'email' => $user['email'],
            'user_type' => $user['user_type'],
            'nombre' => $user['nombre'],
            'telefono' => $user['telefono'],
            'status' => $user['status'],
            'member_since' => $user['created_at'],
            'additional_data' => $additional_data
        ],
        'redirect' => getDashboardUrl($user['user_type'], $additional_data)
    ]);

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

// Función para determinar la URL del dashboard según el tipo de usuario y plan
function getDashboardUrl($user_type, $additional_data = []) {
    switch ($user_type) {
        case 'taller_mecanico':
            $plan_type = $additional_data['plan_type'] ?? 'comun';
            return $plan_type === 'plus' ? 'dashboard-taller-plus.php' : 'dashboard-taller-comun.php';
        case 'proveedor_repuestos':
            return 'dashboard-proveedor.php';
        case 'mecanico_independiente':
            return 'dashboard-mecanico.php';
        case 'usuario_regular':
        default:
            return 'dashboard-usuario.php';
    }
}
?>
