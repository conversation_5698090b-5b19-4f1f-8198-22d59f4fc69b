# RepuMovil Delivery - App Móvil

## 🚀 Instrucciones para correr con Expo Go

### 1️⃣ Instalar dependencias
```bash
cd public/delivery/mobile
npm install
```

### 2️⃣ Instalar Expo CLI (si no lo tenés)
```bash
npm install -g @expo/cli
```

### 3️⃣ Iniciar el proyecto
```bash
expo start
```

### 4️⃣ Abrir en tu celular
1. **Instalá Expo Go** desde Play Store o App Store
2. **Escaneá el QR** que aparece en la terminal
3. **¡Listo!** La app se abrirá en tu celular

## 📱 Funcionalidades

### ✅ Implementadas:
- **Splash Screen** animado con logo RepuMovil Delivery
- **Login** con validaciones
- **Dashboard** con estadísticas y acciones rápidas
- **Pedidos** disponibles y en curso
- **Mapa** (placeholder)
- **Ganancias** con historial
- **Perfil** con configuraciones
- **Navegación** por pestañas

### 🔄 En desarrollo:
- Integración con API real
- Mapa con GPS
- Notificaciones push
- Cámara para fotos

## 🎨 Diseño
- **Colores:** Naranja (#FF6B35) + Rojo (#E53E3E)
- **Gradientes** y animaciones
- **Iconos** Material Design
- **Responsive** para todos los tamaños

## 📦 Para generar APK:
```bash
expo build:android
```

## 🛠️ Tecnologías:
- **React Native** con Expo
- **Expo Go** para testing
- **Material Icons**
- **Linear Gradients**
- **AsyncStorage**
- **Navigation**

---
**Hecho con ❤️ en San Juan, Argentina** 🇦🇷
