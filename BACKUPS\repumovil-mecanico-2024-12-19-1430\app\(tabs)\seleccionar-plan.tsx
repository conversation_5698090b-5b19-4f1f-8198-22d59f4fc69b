import React from 'react';
import {
  SafeAreaView,
  StatusBar,
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Alert,
  ScrollView,
  Dimensions,
} from 'react-native';
import { useRouter } from 'expo-router';

const { width } = Dimensions.get('window');

export default function SeleccionarPlanScreen() {
  const router = useRouter();

  const seleccionarComun = () => {
    Alert.alert(
      '🔧 RepuMovil',
      'Has seleccionado el plan RepuMovil.\n\nRepuestos que llegan a tu taller, cuando los necesitas.\n\n✅ Incluye:\n• Pedido de repuestos\n• Sistema changuito\n• Calificaciones\n• Notificaciones básicas',
      [
        { text: 'Cancelar', style: 'cancel' },
        { 
          text: 'Continuar', 
          onPress: () => router.push('/registro-taller-comun')
        }
      ]
    );
  };

  const seleccionarPlus = () => {
    Alert.alert(
      '⭐ RepuMovil Plus',
      'Has seleccionado el plan RepuMovil Plus.\n\nLa solución completa para tu taller, desde repuestos hasta administración.\n\n✨ Incluye TODO lo de RepuMovil +:\n• Gestión de clientes\n• Calendario de turnos\n• Órdenes de trabajo\n• Reportes avanzados\n• Inventario\n• WhatsApp integrado',
      [
        { text: 'Cancelar', style: 'cancel' },
        { 
          text: 'Elegir Premium', 
          onPress: () => router.push('/registro-taller-plus')
        }
      ]
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#667eea" />
      
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.mainTitle}>
            🔧 <Text style={styles.logoRepu}>Repu</Text><Text style={styles.logoMovil}>Movil</Text>
          </Text>
          <Text style={styles.subtitle}>Elige tu Plan para Talleres</Text>
          <Text style={styles.description}>
            Selecciona el plan que mejor se adapte a las necesidades de tu taller
          </Text>
        </View>

        {/* Plans */}
        <View style={styles.plansContainer}>
          {/* Plan RepuMovil */}
          <TouchableOpacity style={styles.planCard} onPress={seleccionarComun}>
            <Text style={styles.planIcon}>🔧</Text>
            <Text style={[styles.planTitle, styles.planTitleCommon]}>RepuMovil</Text>
            <Text style={[styles.planPrice, styles.planPriceFree]}>GRATIS</Text>
            <Text style={styles.planDescription}>
              Repuestos que llegan a tu taller, cuando los necesitas.
            </Text>
            
            <View style={styles.featuresList}>
              <View style={styles.featureItem}>
                <Text style={styles.featureIcon}>✅</Text>
                <Text style={styles.featureText}>📋 Pedido de repuestos</Text>
              </View>
              <View style={styles.featureItem}>
                <Text style={styles.featureIcon}>✅</Text>
                <Text style={styles.featureText}>🛒 Sistema changuito</Text>
              </View>
              <View style={styles.featureItem}>
                <Text style={styles.featureIcon}>✅</Text>
                <Text style={styles.featureText}>⭐ Calificaciones de clientes</Text>
              </View>
              <View style={styles.featureItem}>
                <Text style={styles.featureIcon}>✅</Text>
                <Text style={styles.featureText}>📱 Notificaciones básicas</Text>
              </View>
              <View style={styles.featureItem}>
                <Text style={styles.featureIcon}>✅</Text>
                <Text style={styles.featureText}>🔍 Búsqueda de repuestos</Text>
              </View>
            </View>
            
            <View style={styles.btnContainer}>
              <Text style={styles.btnText}>🚀 Empezar Gratis</Text>
            </View>
          </TouchableOpacity>

          {/* Plan Plus */}
          <TouchableOpacity style={[styles.planCard, styles.planCardPremium]} onPress={seleccionarPlus}>
            <Text style={styles.crownIcon}>👑</Text>
            <Text style={styles.planIcon}>🛠️</Text>
            <Text style={[styles.planTitle, styles.planTitlePremium]}>
              RepuMovil <Text style={styles.plusText}>Plus</Text>
            </Text>
            <Text style={[styles.planPrice, styles.planPricePremium]}>PREMIUM</Text>
            <Text style={styles.planDescription}>
              La solución completa para tu taller, desde repuestos hasta administración.
            </Text>
            
            <View style={styles.featuresList}>
              <View style={styles.featureItem}>
                <Text style={styles.featureIconPremium}>⭐</Text>
                <Text style={styles.featureTextBold}>Todo lo de RepuMovil +</Text>
              </View>
              <View style={styles.featureItem}>
                <Text style={styles.featureIconPremium}>⭐</Text>
                <Text style={styles.featureText}>👥 Gestión completa de clientes</Text>
              </View>
              <View style={styles.featureItem}>
                <Text style={styles.featureIconPremium}>⭐</Text>
                <Text style={styles.featureText}>📅 Calendario de turnos</Text>
              </View>
              <View style={styles.featureItem}>
                <Text style={styles.featureIconPremium}>⭐</Text>
                <Text style={styles.featureText}>📋 Órdenes de trabajo</Text>
              </View>
              <View style={styles.featureItem}>
                <Text style={styles.featureIconPremium}>⭐</Text>
                <Text style={styles.featureText}>💰 Reportes y estadísticas</Text>
              </View>
              <View style={styles.featureItem}>
                <Text style={styles.featureIconPremium}>⭐</Text>
                <Text style={styles.featureText}>🔧 Inventario de herramientas</Text>
              </View>
              <View style={styles.featureItem}>
                <Text style={styles.featureIconPremium}>⭐</Text>
                <Text style={styles.featureText}>📊 Dashboard avanzado</Text>
              </View>
              <View style={styles.featureItem}>
                <Text style={styles.featureIconPremium}>⭐</Text>
                <Text style={styles.featureText}>📱 WhatsApp integrado</Text>
              </View>
            </View>
            
            <View style={[styles.btnContainer, styles.btnContainerPremium]}>
              <Text style={[styles.btnText, styles.btnTextPremium]}>👑 Elegir Premium</Text>
            </View>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#667eea',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  header: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  mainTitle: {
    fontSize: 36,
    fontWeight: '900',
    color: 'white',
    marginBottom: 15,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 2, height: 2 },
    textShadowRadius: 4,
  },
  logoRepu: {
    color: '#FF6B35',
  },
  logoMovil: {
    color: '#FFE4B5',
  },
  subtitle: {
    fontSize: 20,
    fontWeight: '600',
    color: 'white',
    marginBottom: 10,
  },
  description: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    lineHeight: 22,
  },
  plansContainer: {
    paddingBottom: 40,
  },
  planCard: {
    backgroundColor: 'white',
    borderRadius: 25,
    padding: 25,
    marginBottom: 30,
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 5 },
    shadowOpacity: 0.3,
    shadowRadius: 10,
    position: 'relative',
  },
  planCardPremium: {
    borderWidth: 3,
    borderColor: '#FFD700',
    backgroundColor: '#fffbf0',
  },
  crownIcon: {
    position: 'absolute',
    top: -10,
    right: 20,
    fontSize: 30,
    transform: [{ rotate: '15deg' }],
  },
  planIcon: {
    fontSize: 60,
    textAlign: 'center',
    marginBottom: 15,
  },
  planTitle: {
    fontSize: 28,
    fontWeight: '900',
    textAlign: 'center',
    marginBottom: 10,
  },
  planTitleCommon: {
    color: '#FF6B35',
  },
  planTitlePremium: {
    color: '#FF6B35',
  },
  plusText: {
    color: '#FFD700',
  },
  planPrice: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 15,
  },
  planPriceFree: {
    color: '#4CAF50',
  },
  planPricePremium: {
    color: '#FF6B35',
  },
  planDescription: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 20,
  },
  featuresList: {
    marginBottom: 25,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  featureIcon: {
    color: '#4CAF50',
    marginRight: 8,
    fontSize: 14,
  },
  featureIconPremium: {
    color: '#FFD700',
    marginRight: 8,
    fontSize: 14,
  },
  featureText: {
    fontSize: 12,
    color: '#666',
    flex: 1,
  },
  featureTextBold: {
    fontSize: 12,
    color: '#333',
    fontWeight: 'bold',
    flex: 1,
  },
  btnContainer: {
    backgroundColor: '#FF6B35',
    borderRadius: 15,
    paddingVertical: 15,
    paddingHorizontal: 25,
    alignItems: 'center',
  },
  btnContainerPremium: {
    backgroundColor: '#FFD700',
  },
  btnText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '700',
  },
  btnTextPremium: {
    color: '#333',
  },
});
