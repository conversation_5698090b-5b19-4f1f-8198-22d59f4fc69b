-- Tablas para Sistema de Asignación Automática - RepuMovil
-- Ejecutar después de la base de datos principal

-- Tabla de repartidores (extender usuarios)
CREATE TABLE IF NOT EXISTS repartidores (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    estado ENUM('disponible', 'ocupado', 'desconectado') DEFAULT 'disponible',
    calificacion_promedio DECIMAL(3,2) DEFAULT 5.00,
    entregas_completadas INT DEFAULT 0,
    latitud_actual DECIMAL(10, 8) NULL,
    longitud_actual DECIMAL(11, 8) NULL,
    ultimo_pedido_asignado TIMESTAMP NULL,
    activo BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_estado (estado),
    INDEX idx_ubicacion (latitud_actual, longitud_actual),
    INDEX idx_calificacion (calificacion_promedio)
);

-- Tabla de log de asignaciones
CREATE TABLE IF NOT EXISTS asignaciones_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    pedido_id INT NOT NULL,
    repartidor_id INT NOT NULL,
    metodo_asignacion ENUM('automatico', 'manual', 'reasignacion') DEFAULT 'automatico',
    distancia_km DECIMAL(5,2) NULL,
    tiempo_estimado_min INT NULL,
    fecha_asignacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    estado_asignacion ENUM('exitosa', 'fallida', 'cancelada') DEFAULT 'exitosa',
    motivo_falla TEXT NULL,
    FOREIGN KEY (pedido_id) REFERENCES pedidos(id) ON DELETE CASCADE,
    FOREIGN KEY (repartidor_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_fecha (fecha_asignacion),
    INDEX idx_metodo (metodo_asignacion)
);

-- Tabla de zonas de entrega
CREATE TABLE IF NOT EXISTS zonas_entrega (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nombre VARCHAR(100) NOT NULL,
    descripcion TEXT,
    latitud_centro DECIMAL(10, 8) NOT NULL,
    longitud_centro DECIMAL(11, 8) NOT NULL,
    radio_km DECIMAL(5,2) NOT NULL DEFAULT 5.0,
    activa BOOLEAN DEFAULT TRUE,
    tarifa_base DECIMAL(8,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabla de configuración del sistema de asignación
CREATE TABLE IF NOT EXISTS config_asignacion (
    id INT AUTO_INCREMENT PRIMARY KEY,
    clave VARCHAR(50) NOT NULL UNIQUE,
    valor TEXT NOT NULL,
    descripcion TEXT,
    tipo ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insertar configuraciones por defecto
INSERT INTO config_asignacion (clave, valor, descripcion, tipo) VALUES
('max_pedidos_por_repartidor', '3', 'Máximo de pedidos activos por repartidor', 'number'),
('radio_busqueda_km', '10', 'Radio de búsqueda de repartidores en km', 'number'),
('tiempo_respuesta_max', '300', 'Tiempo máximo de respuesta en segundos', 'number'),
('asignacion_automatica_activa', 'true', 'Activar asignación automática', 'boolean'),
('factor_distancia', '1.0', 'Factor de peso para distancia en algoritmo', 'number'),
('factor_calificacion', '0.5', 'Factor de peso para calificación', 'number'),
('factor_carga_trabajo', '2.0', 'Factor de peso para carga de trabajo', 'number')
ON DUPLICATE KEY UPDATE valor = VALUES(valor);

-- Insertar zonas de San Juan
INSERT INTO zonas_entrega (nombre, descripcion, latitud_centro, longitud_centro, radio_km, tarifa_base) VALUES
('Centro', 'Centro de San Juan', -31.5375, -68.5364, 3.0, 150.00),
('Rivadavia', 'Departamento Rivadavia', -31.5500, -68.5500, 5.0, 200.00),
('Chimbas', 'Departamento Chimbas', -31.4800, -68.5300, 4.0, 180.00),
('Rawson', 'Departamento Rawson', -31.5700, -68.5000, 6.0, 220.00),
('Santa Lucía', 'Departamento Santa Lucía', -31.5200, -68.4800, 4.5, 190.00)
ON DUPLICATE KEY UPDATE nombre = VALUES(nombre);

-- Insertar repartidores de ejemplo
INSERT INTO repartidores (user_id, estado, calificacion_promedio, entregas_completadas, latitud_actual, longitud_actual) 
SELECT u.id, 'disponible', 4.5 + (RAND() * 0.5), FLOOR(RAND() * 100), 
       -31.5375 + (RAND() * 0.02 - 0.01), -68.5364 + (RAND() * 0.02 - 0.01)
FROM users u 
WHERE u.tipo_usuario = 'delivery' 
AND NOT EXISTS (SELECT 1 FROM repartidores r WHERE r.user_id = u.id);

-- Vistas útiles para reportes
CREATE OR REPLACE VIEW vista_repartidores_activos AS
SELECT 
    u.id,
    u.nombre,
    u.email,
    u.telefono,
    r.estado,
    r.calificacion_promedio,
    r.entregas_completadas,
    r.latitud_actual,
    r.longitud_actual,
    COUNT(p.id) as pedidos_activos,
    r.ultimo_pedido_asignado
FROM users u
JOIN repartidores r ON u.id = r.user_id
LEFT JOIN pedidos p ON r.user_id = p.repartidor_id AND p.estado IN ('asignado', 'en_camino')
WHERE u.tipo_usuario = 'delivery' AND r.activo = 1
GROUP BY u.id;

CREATE OR REPLACE VIEW vista_estadisticas_asignacion AS
SELECT 
    DATE(fecha_asignacion) as fecha,
    COUNT(*) as total_asignaciones,
    COUNT(CASE WHEN metodo_asignacion = 'automatico' THEN 1 END) as automaticas,
    COUNT(CASE WHEN metodo_asignacion = 'manual' THEN 1 END) as manuales,
    AVG(distancia_km) as distancia_promedio,
    AVG(tiempo_estimado_min) as tiempo_promedio
FROM asignaciones_log
WHERE fecha_asignacion >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
GROUP BY DATE(fecha_asignacion)
ORDER BY fecha DESC;

-- Triggers para mantener estadísticas actualizadas
DELIMITER //

CREATE TRIGGER actualizar_stats_repartidor_entrega
AFTER UPDATE ON pedidos
FOR EACH ROW
BEGIN
    IF NEW.estado = 'entregado' AND OLD.estado != 'entregado' AND NEW.repartidor_id IS NOT NULL THEN
        UPDATE repartidores 
        SET entregas_completadas = entregas_completadas + 1,
            estado = 'disponible'
        WHERE user_id = NEW.repartidor_id;
    END IF;
END//

CREATE TRIGGER actualizar_estado_repartidor_asignacion
AFTER INSERT ON asignaciones_log
FOR EACH ROW
BEGIN
    IF NEW.estado_asignacion = 'exitosa' THEN
        UPDATE repartidores 
        SET estado = 'ocupado',
            ultimo_pedido_asignado = NEW.fecha_asignacion
        WHERE user_id = NEW.repartidor_id;
    END IF;
END//

DELIMITER ;

-- Índices adicionales para optimización
CREATE INDEX idx_pedidos_estado_repartidor ON pedidos(estado, repartidor_id);
CREATE INDEX idx_repartidores_estado_activo ON repartidores(estado, activo);
CREATE INDEX idx_asignaciones_fecha_metodo ON asignaciones_log(fecha_asignacion, metodo_asignacion);

-- Comentarios para documentación
ALTER TABLE repartidores COMMENT = 'Información extendida de repartidores para asignación automática';
ALTER TABLE asignaciones_log COMMENT = 'Log de todas las asignaciones de pedidos a repartidores';
ALTER TABLE zonas_entrega COMMENT = 'Definición de zonas geográficas para entrega';
ALTER TABLE config_asignacion COMMENT = 'Configuración del algoritmo de asignación automática';
