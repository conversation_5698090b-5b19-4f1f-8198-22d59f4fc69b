<!DOCTYPE html>
<html lang='es'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Guía de Instalación - RepuMovil</title>
    
<style>
    :root {
        --primary-color: #FF6B35;
        --secondary-color: #FFA500;
        --dark-color: #2c3e50;
        --light-color: #f8f9fa;
        --success-color: #28a745;
        --danger-color: #dc3545;
    }

    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        line-height: 1.6;
        color: var(--dark-color);
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
    }

    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
        background: white;
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
        border-radius: 10px;
        margin-top: 20px;
        margin-bottom: 20px;
    }

    .header {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        padding: 30px;
        border-radius: 10px;
        margin-bottom: 30px;
        text-align: center;
    }

    .header h1 {
        font-size: 2.5rem;
        margin-bottom: 10px;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }

    .header p {
        font-size: 1.2rem;
        opacity: 0.9;
    }

    h1, h2, h3, h4, h5, h6 {
        color: var(--primary-color);
        margin-top: 30px;
        margin-bottom: 15px;
    }

    h1 { font-size: 2.2rem; border-bottom: 3px solid var(--primary-color); padding-bottom: 10px; }
    h2 { font-size: 1.8rem; border-bottom: 2px solid var(--secondary-color); padding-bottom: 8px; }
    h3 { font-size: 1.5rem; }
    h4 { font-size: 1.3rem; }

    p {
        margin-bottom: 15px;
        text-align: justify;
    }

    ul, ol {
        margin-left: 30px;
        margin-bottom: 15px;
    }

    li {
        margin-bottom: 8px;
    }

    .highlight {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        padding: 20px;
        border-radius: 10px;
        margin: 20px 0;
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }

    .success-box {
        background: var(--success-color);
        color: white;
        padding: 15px;
        border-radius: 8px;
        margin: 15px 0;
    }

    .info-box {
        background: #e3f2fd;
        border-left: 4px solid #2196f3;
        padding: 15px;
        margin: 15px 0;
        border-radius: 0 8px 8px 0;
    }

    .warning-box {
        background: #fff3e0;
        border-left: 4px solid #ff9800;
        padding: 15px;
        margin: 15px 0;
        border-radius: 0 8px 8px 0;
    }

    .code-block {
        background: #2c3e50;
        color: #ecf0f1;
        padding: 20px;
        border-radius: 8px;
        font-family: "Courier New", monospace;
        overflow-x: auto;
        margin: 15px 0;
        border-left: 4px solid var(--primary-color);
    }

    .table-container {
        overflow-x: auto;
        margin: 20px 0;
    }

    table {
        width: 100%;
        border-collapse: collapse;
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    th {
        background: var(--primary-color);
        color: white;
        padding: 15px;
        text-align: left;
        font-weight: bold;
    }

    td {
        padding: 12px 15px;
        border-bottom: 1px solid #eee;
    }

    tr:hover {
        background: #f5f5f5;
    }

    .grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin: 20px 0;
    }

    .card {
        background: white;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border-left: 4px solid var(--primary-color);
    }

    .card h3 {
        color: var(--primary-color);
        margin-top: 0;
    }

    .footer {
        background: var(--dark-color);
        color: white;
        padding: 30px;
        border-radius: 10px;
        margin-top: 40px;
        text-align: center;
    }

    .emoji {
        font-size: 1.2em;
    }

    @media print {
        body { background: white; }
        .container { box-shadow: none; margin: 0; }
        .header { background: var(--primary-color) !important; }
    }

    @media (max-width: 768px) {
        .container { padding: 10px; margin: 10px; }
        .header h1 { font-size: 2rem; }
        .grid { grid-template-columns: 1fr; }
    }
</style>

</head>
<body>
    <div class='container'>
        <div class='header'>
            <h1>🚚 RepuMovil</h1>
            <p>Guía de Instalación</p>
        </div>
        
        <div class='content'>
            <h1>🛠️ GUÍA DE INSTALACIÓN Y CONFIGURACIÓN - REPUMOVIL</h1><h2>📋 <strong>REQUISITOS DEL SISTEMA</strong></h2><p><h3><strong>Servidor de Desarrollo:</strong></h3>
<ul><li><strong>XAMPP 8.2+</strong> (Apache, MySQL, PHP)</li>
<li><strong>Node.js 18+</strong></li>
<li><strong>Expo CLI</strong></li>
<li><strong>Git</strong></li>
<li><strong>Editor de código</strong> (VS Code recomendado)</li></p><p><h3><strong>Servidor de Producción:</strong></h3>
<li><strong>Apache 2.4+</strong></li>
<li><strong>PHP 8.0+</strong></li>
<li><strong>MySQL 8.0+</strong></li>
<li><strong>SSL Certificate</strong></li>
<li><strong>2GB RAM mínimo</strong></li>
<li><strong>10GB espacio en disco</strong></li></p><p>---</p><h2>🚀 <strong>INSTALACIÓN PASO A PASO</strong></h2><h3><strong>PASO 1: CONFIGURAR ENTORNO DE DESARROLLO</strong></h3><p><h4><strong>1.1 Instalar XAMPP:</strong></h4>
1. Descargar desde: https://www.apachefriends.org/
2. Ejecutar instalador y seguir pasos
3. Iniciar Apache y MySQL desde el panel de control
4. Verificar que funcione en: http://localhost</p><p><h4><strong>1.2 Instalar Node.js:</strong></h4>
1. Descargar desde: https://nodejs.org/
2. Instalar versión LTS (18+)
3. Verificar instalación:
<code style="background:#f4f4f4;padding:2px 4px;border-radius:3px;">`</code>bash
node --version
npm --version
<code style="background:#f4f4f4;padding:2px 4px;border-radius:3px;">`</code></p><p><h4><strong>1.3 Instalar Expo CLI:</strong></h4>
<code style="background:#f4f4f4;padding:2px 4px;border-radius:3px;">`</code>bash
npm install -g @expo/cli
expo --version
<code style="background:#f4f4f4;padding:2px 4px;border-radius:3px;">`</code></p><h3><strong>PASO 2: CONFIGURAR BASE DE DATOS</strong></h3><p><h4><strong>2.1 Crear Base de Datos:</strong></h4>
1. Abrir phpMyAdmin: http://localhost/phpmyadmin
2. Crear nueva base de datos: <code style="background:#f4f4f4;padding:2px 4px;border-radius:3px;">mechanical_workshop</code>
3. Configurar charset: <code style="background:#f4f4f4;padding:2px 4px;border-radius:3px;">utf8_general_ci</code></p><p><h4><strong>2.2 Ejecutar Scripts SQL:</strong></h4>
<code style="background:#f4f4f4;padding:2px 4px;border-radius:3px;">`</code>sql
-- Ejecutar en orden:
SOURCE db/01_usuarios.sql;
SOURCE db/02_notificaciones.sql;
SOURCE db/03_pedidos_basicos.sql;
SOURCE db/04_notificaciones_push.sql;
SOURCE db/05_tracking_gps.sql;
<code style="background:#f4f4f4;padding:2px 4px;border-radius:3px;">`</code></p><p><h4><strong>2.3 Verificar Tablas Creadas:</strong></h4>
<code style="background:#f4f4f4;padding:2px 4px;border-radius:3px;">`</code>sql
SHOW TABLES;
-- Deberías ver 15+ tablas
<code style="background:#f4f4f4;padding:2px 4px;border-radius:3px;">`</code></p><h3><strong>PASO 3: CONFIGURAR BACKEND</strong></h3><p><h4><strong>3.1 Ubicar Archivos:</strong></h4>
<code style="background:#f4f4f4;padding:2px 4px;border-radius:3px;">`</code>
C:\xampp\htdocs\mechanical-workshop\
├── public/
│   ├── api/
│   │   ├── notifications.php
│   │   ├── tracking.php
│   │   └── pedidos-delivery.php
│   ├── test-notifications.php
│   ├── test-tracking.php
│   └── test-pedidos.php
└── db/
    ├── 01_usuarios.sql
    ├── 02_notificaciones.sql
    ├── 03_pedidos_basicos.sql
    ├── 04_notificaciones_push.sql
    └── 05_tracking_gps.sql
<code style="background:#f4f4f4;padding:2px 4px;border-radius:3px;">`</code></p><p><h4><strong>3.2 Configurar Variables:</strong></h4>
Editar archivos PHP con configuración correcta:
<code style="background:#f4f4f4;padding:2px 4px;border-radius:3px;">`</code>php
// En todos los archivos API
$host = 'localhost';
$dbname = 'mechanical_workshop';
$username = 'root';
$password = ''; // Tu password de MySQL
<code style="background:#f4f4f4;padding:2px 4px;border-radius:3px;">`</code></p><p><h4><strong>3.3 Probar APIs:</strong></h4>
<code style="background:#f4f4f4;padding:2px 4px;border-radius:3px;">`</code>bash
<h1>Probar notificaciones</h1>
curl http://localhost/mechanical-workshop/public/api/notifications.php?action=get_tokens</p><p><h1>Probar tracking</h1>
curl http://localhost/mechanical-workshop/public/api/tracking.php?action=get_active_deliveries</p><p><h1>Probar pedidos</h1>
curl http://localhost/mechanical-workshop/public/api/pedidos-delivery.php?action=get_pedidos_disponibles&delivery_id=1
<code style="background:#f4f4f4;padding:2px 4px;border-radius:3px;">`</code></p><h3><strong>PASO 4: CONFIGURAR APP MÓVIL</strong></h3><p><h4><strong>4.1 Clonar/Crear Proyecto:</strong></h4>
<code style="background:#f4f4f4;padding:2px 4px;border-radius:3px;">`</code>bash
<h1>Si tienes el código:</h1>
cd RepuMovilExpo</p><p><h1>Si empiezas desde cero:</h1>
npx create-expo-app RepuMovilExpo --template tabs
cd RepuMovilExpo
<code style="background:#f4f4f4;padding:2px 4px;border-radius:3px;">`</code></p><p><h4><strong>4.2 Instalar Dependencias:</strong></h4>
<code style="background:#f4f4f4;padding:2px 4px;border-radius:3px;">`</code>bash
npx expo install expo-notifications expo-device expo-constants expo-location
npm install @expo/vector-icons expo-linear-gradient
<code style="background:#f4f4f4;padding:2px 4px;border-radius:3px;">`</code></p><p><h4><strong>4.3 Configurar app.json:</strong></h4>
<code style="background:#f4f4f4;padding:2px 4px;border-radius:3px;">`</code>json
{
  "expo": {
    "name": "RepuMovil",
    "slug": "repumovil",
    "version": "1.0.0",
    "platforms": ["ios", "android"],
    "permissions": [
      "NOTIFICATIONS",
      "ACCESS_FINE_LOCATION",
      "ACCESS_COARSE_LOCATION",
      "ACCESS_BACKGROUND_LOCATION"
    ],
    "android": {
      "permissions": [
        "android.permission.ACCESS_FINE_LOCATION",
        "android.permission.ACCESS_COARSE_LOCATION",
        "android.permission.ACCESS_BACKGROUND_LOCATION",
        "android.permission.RECEIVE_BOOT_COMPLETED",
        "android.permission.VIBRATE"
      ]
    },
    "ios": {
      "infoPlist": {
        "NSLocationWhenInUseUsageDescription": "RepuMovil necesita acceso a tu ubicación para mostrar tu posición a los clientes.",
        "NSLocationAlwaysAndWhenInUseUsageDescription": "RepuMovil necesita acceso a tu ubicación para el tracking en tiempo real."
      }
    }
  }
}
<code style="background:#f4f4f4;padding:2px 4px;border-radius:3px;">`</code></p><p><h4><strong>4.4 Configurar URLs de API:</strong></h4>
<code style="background:#f4f4f4;padding:2px 4px;border-radius:3px;">`</code>javascript
// En services/NotificationService.ts, LocationService.ts, etc.
const API_BASE_URL = 'http://localhost/mechanical-workshop/public/api';</p><p>// Para testing en dispositivo físico, usar IP local:
const API_BASE_URL = 'http://192.168.1.XXX/mechanical-workshop/public/api';
<code style="background:#f4f4f4;padding:2px 4px;border-radius:3px;">`</code></p><h3><strong>PASO 5: TESTING INICIAL</strong></h3><p><h4><strong>5.1 Probar Backend:</strong></h4>
<code style="background:#f4f4f4;padding:2px 4px;border-radius:3px;">`</code>bash
<h1>Ejecutar tests automáticos</h1>
cd C:\xampp\htdocs\mechanical-workshop
php test-api-tracking.php
php test-sistema-pedidos.php
<code style="background:#f4f4f4;padding:2px 4px;border-radius:3px;">`</code></p><p><h4><strong>5.2 Probar App Móvil:</strong></h4>
<code style="background:#f4f4f4;padding:2px 4px;border-radius:3px;">`</code>bash
cd RepuMovilExpo
npx expo start
<code style="background:#f4f4f4;padding:2px 4px;border-radius:3px;">`</code></p><p><h4><strong>5.3 Verificar Funcionalidades:</strong></h4>
<li>✅ Notificaciones push funcionando</li>
<li>✅ GPS tracking activo</li>
<li>✅ Modal de pedidos aparece</li>
<li>✅ APIs responden correctamente</li></p><p>---</p><h2>🔧 <strong>CONFIGURACIÓN AVANZADA</strong></h2><p><h3><strong>Configurar HTTPS (Producción):</strong></h3>
<code style="background:#f4f4f4;padding:2px 4px;border-radius:3px;">`</code>apache
<h1>En .htaccess</h1>
RewriteEngine On
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
<code style="background:#f4f4f4;padding:2px 4px;border-radius:3px;">`</code></p><p><h3><strong>Optimizar Base de Datos:</strong></h3>
<code style="background:#f4f4f4;padding:2px 4px;border-radius:3px;">`</code>sql
-- Índices adicionales para rendimiento
CREATE INDEX idx_pedidos_estado_fecha ON pedidos(estado, fecha_creacion);
CREATE INDEX idx_tracking_delivery_fecha ON delivery_locations(delivery_id, fecha_registro);
CREATE INDEX idx_notifications_fecha ON notificaciones_log(fecha_envio);
<code style="background:#f4f4f4;padding:2px 4px;border-radius:3px;">`</code></p><p><h3><strong>Configurar Backup Automático:</strong></h3>
<code style="background:#f4f4f4;padding:2px 4px;border-radius:3px;">`</code>bash
<h1>Script de backup diario</h1>
#!/bin/bash
mysqldump -u root -p mechanical_workshop > backup_$(date +%Y%m%d).sql
<code style="background:#f4f4f4;padding:2px 4px;border-radius:3px;">`</code></p><p>---</p><h2>🧪 <strong>HERRAMIENTAS DE TESTING</strong></h2><p><h3><strong>Paneles Web de Testing:</strong></h3>
<li><strong>Notificaciones:</strong> http://localhost/mechanical-workshop/public/test-notifications.php</li>
<li><strong>Tracking GPS:</strong> http://localhost/mechanical-workshop/public/test-tracking.php</li>
<li><strong>Pedidos:</strong> http://localhost/mechanical-workshop/public/test-pedidos.php</li>
<li><strong>Monitor en vivo:</strong> http://localhost/mechanical-workshop/public/monitor-tracking.php</li></p><p><h3><strong>Scripts de Testing Automático:</strong></h3>
<code style="background:#f4f4f4;padding:2px 4px;border-radius:3px;">`</code>bash
<h1>Testing completo del sistema</h1>
php test-api-tracking.php
php test-sistema-pedidos.php
php simulate-mobile-tracking.php
<code style="background:#f4f4f4;padding:2px 4px;border-radius:3px;">`</code></p><p><h3><strong>Testing en Dispositivos:</strong></h3>
<code style="background:#f4f4f4;padding:2px 4px;border-radius:3px;">`</code>bash
<h1>Para Android</h1>
npx expo start --android</p><p><h1>Para iOS</h1>
npx expo start --ios</p><p><h1>Para dispositivo físico (tunnel)</h1>
npx expo start --tunnel
<code style="background:#f4f4f4;padding:2px 4px;border-radius:3px;">`</code></p><p>---</p><h2>🚨 <strong>TROUBLESHOOTING</strong></h2><h3><strong>Problemas Comunes:</strong></h3><p><h4><strong>Error: "Access denied for user 'root'"</strong></h4>
<code style="background:#f4f4f4;padding:2px 4px;border-radius:3px;">`</code>sql
-- Solución: Configurar password de MySQL
ALTER USER 'root'@'localhost' IDENTIFIED BY 'tu_password';
FLUSH PRIVILEGES;
<code style="background:#f4f4f4;padding:2px 4px;border-radius:3px;">`</code></p><p><h4><strong>Error: "CORS policy"</strong></h4>
<code style="background:#f4f4f4;padding:2px 4px;border-radius:3px;">`</code>php
// Agregar en archivos PHP:
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
<code style="background:#f4f4f4;padding:2px 4px;border-radius:3px;">`</code></p><p><h4><strong>Error: "Expo CLI not found"</strong></h4>
<code style="background:#f4f4f4;padding:2px 4px;border-radius:3px;">`</code>bash
npm install -g @expo/cli
<h1>O usar npx:</h1>
npx expo start
<code style="background:#f4f4f4;padding:2px 4px;border-radius:3px;">`</code></p><p><h4><strong>Error: "Network request failed"</strong></h4>
<code style="background:#f4f4f4;padding:2px 4px;border-radius:3px;">`</code>javascript
// Verificar URL en la app:
// Para emulador: http://localhost
// Para dispositivo físico: http://192.168.1.XXX
<code style="background:#f4f4f4;padding:2px 4px;border-radius:3px;">`</code></p><p><h3><strong>Logs de Debugging:</strong></h3>
<code style="background:#f4f4f4;padding:2px 4px;border-radius:3px;">`</code>bash
<h1>Logs de Apache</h1>
tail -f C:\xampp\apache\logs\error.log</p><p><h1>Logs de MySQL</h1>
tail -f C:\xampp\mysql\data\*.err</p><p><h1>Logs de Expo</h1>
npx expo start --clear
<code style="background:#f4f4f4;padding:2px 4px;border-radius:3px;">`</code></p><p>---</p><h2>📊 <strong>VERIFICACIÓN DE INSTALACIÓN</strong></h2><h3><strong>Checklist Final:</strong></h3><p><h4><strong>Backend:</strong></h4>
<li>[ ] XAMPP funcionando (Apache + MySQL)</li>
<li>[ ] Base de datos creada con todas las tablas</li>
<li>[ ] APIs respondiendo correctamente</li>
<li>[ ] Paneles de testing accesibles</li></p><p><h4><strong>App Móvil:</strong></h4>
<li>[ ] Expo CLI instalado</li>
<li>[ ] Dependencias instaladas</li>
<li>[ ] App inicia sin errores</li>
<li>[ ] Permisos configurados</li></p><p><h4><strong>Funcionalidades:</strong></h4>
<li>[ ] Notificaciones push funcionando</li>
<li>[ ] GPS tracking activo</li>
<li>[ ] Modal de pedidos aparece</li>
<li>[ ] Estados del delivery cambian</li></p><p><h4><strong>Testing:</strong></h4>
<li>[ ] Scripts automáticos pasan</li>
<li>[ ] Paneles web funcionan</li>
<li>[ ] App se conecta al backend</li>
<li>[ ] Datos se guardan en BD</li></p><p>---</p><h2>🎯 <strong>PRÓXIMOS PASOS</strong></h2><p><h3><strong>Después de la Instalación:</strong></h3>
1. <strong>Crear usuarios de prueba</strong> en la base de datos
2. <strong>Configurar datos de ejemplo</strong> (clientes, repuestos)
3. <strong>Probar flujo completo</strong> de pedidos
4. <strong>Configurar notificaciones</strong> en dispositivos reales
5. <strong>Optimizar rendimiento</strong> según uso</p><p><h3><strong>Para Producción:</strong></h3>
1. <strong>Configurar dominio</strong> y SSL
2. <strong>Migrar base de datos</strong> a servidor
3. <strong>Configurar backups</strong> automáticos
4. <strong>Implementar monitoreo</strong> de logs
5. <strong>Configurar CDN</strong> para assets</p><p>---</p><h2>📞 <strong>SOPORTE DE INSTALACIÓN</strong></h2><p><h3><strong>¿Necesitas ayuda?</strong></h3>
<li><strong>Email:</strong> <EMAIL></li>
<li><strong>WhatsApp:</strong> +54 264 XXX-XXXX</li>
<li><strong>Horario:</strong> Lunes a Viernes 9:00-18:00</li></p><p><h3><strong>Recursos Adicionales:</strong></h3>
<li><strong>Documentación técnica completa</strong></li>
<li><strong>Videos tutoriales</strong> (próximamente)</li>
<li><strong>Foro de desarrolladores</strong></li>
<li><strong>Base de conocimientos</strong></li></p><p>---</p><h2>✅ <strong>¡INSTALACIÓN COMPLETADA!</strong></h2><p><strong>¡Felicitaciones!</strong> 🎉 Has instalado exitosamente RepuMovil.</p><p><strong>El sistema está listo para:</strong>
<li>✅ Recibir y procesar pedidos</li>
<li>✅ Manejar deliveries en tiempo real</li>
<li>✅ Enviar notificaciones push</li>
<li>✅ Trackear ubicaciones GPS</li>
<li>✅ Escalar a producción</li></ul></p><p><strong>¡Que tengas mucho éxito con RepuMovil!</strong> 🚚📱💪
</p>
        </div>
        
        <div class='footer'>
            <p><strong>RepuMovil - Sistema de Delivery de Repuestos</strong></p>
            <p>Documentación generada el 04/06/2025 05:54:27</p>
            <p>© 2024 RepuMovil. Todos los derechos reservados.</p>
        </div>
    </div>
</body>
</html>