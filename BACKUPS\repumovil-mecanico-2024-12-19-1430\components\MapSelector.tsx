import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Alert,
  Linking,
} from 'react-native';
import { geocodeAddress, openInGoogleMaps } from '../config/maps';

interface MapSelectorProps {
  onAddressSelect: (address: string, coordinates?: { lat: number; lng: number }) => void;
  initialAddress?: string;
}

export default function MapSelector({ onAddressSelect, initialAddress = '' }: MapSelectorProps) {
  const [address, setAddress] = useState(initialAddress);
  const [isSearching, setIsSearching] = useState(false);

  const searchAddress = async () => {
    if (!address.trim()) {
      Alert.alert('Error', 'Por favor ingrese una dirección');
      return;
    }

    setIsSearching(true);

    try {
      const result = await geocodeAddress(address);

      if (result.success) {
        onAddressSelect(result.address!, result.coordinates);
        Alert.alert('Éxito', 'Dirección encontrada y guardada');
      } else {
        Alert.alert('Error', result.error || 'No se encontró la dirección');
      }
    } catch (error) {
      Alert.alert('Error', 'Error al buscar la dirección');
    } finally {
      setIsSearching(false);
    }
  };

  const openGoogleMaps = async () => {
    if (!address.trim()) {
      Alert.alert('Error', 'Por favor ingrese una dirección primero');
      return;
    }

    try {
      const mapsUrl = openInGoogleMaps(address);
      const supported = await Linking.canOpenURL(mapsUrl);

      if (supported) {
        await Linking.openURL(mapsUrl);
      } else {
        Alert.alert('Error', 'No se puede abrir Google Maps');
      }
    } catch (error) {
      Alert.alert('Error', 'Error al abrir Google Maps');
    }
  };

  return (
    <View style={styles.container}>
      <TextInput
        style={styles.input}
        value={address}
        onChangeText={setAddress}
        placeholder="Ingrese la dirección del taller"
        multiline={true}
        numberOfLines={2}
      />

      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.button, styles.searchButton]}
          onPress={searchAddress}
          disabled={isSearching}
        >
          <Text style={styles.buttonText}>
            {isSearching ? '🔍 Buscando...' : '🔍 Buscar'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.mapButton]}
          onPress={openGoogleMaps}
        >
          <Text style={styles.buttonText}>
            🗺️ Abrir Mapa
          </Text>
        </TouchableOpacity>
      </View>

      <Text style={styles.helperText}>
        💡 Tip: Ingresa la dirección completa para mejores resultados
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 10,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 15,
    paddingVertical: 12,
    fontSize: 16,
    backgroundColor: 'white',
    marginBottom: 10,
    minHeight: 50,
    textAlignVertical: 'top',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  button: {
    flex: 1,
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 5,
  },
  searchButton: {
    backgroundColor: '#FF8C00',
  },
  mapButton: {
    backgroundColor: '#4285F4',
  },
  buttonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  helperText: {
    fontSize: 12,
    color: '#666',
    fontStyle: 'italic',
    textAlign: 'center',
  },
});
