<?php
// PASO 9: API para sistema de calificaciones
// RepuMovil - Gestión de Calificaciones y Feedback

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Manejar preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Configuración de base de datos
$host = 'localhost';
$dbname = 'mechanical_workshop';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    sendResponse(false, 'Error de conexión a la base de datos: ' . $e->getMessage());
}

// Función para enviar respuesta JSON
function sendResponse($success, $message, $data = null) {
    echo json_encode([
        'success' => $success,
        'message' => $message,
        'data' => $data,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    exit();
}

// Manejar diferentes métodos HTTP
switch ($_SERVER['REQUEST_METHOD']) {
    case 'GET':
        $action = $_GET['action'] ?? '';
        
        switch ($action) {
            case 'obtener_calificaciones_delivery':
                obtenerCalificacionesDelivery($pdo, $_GET);
                break;
                
            case 'obtener_estadisticas_calificaciones':
                obtenerEstadisticasCalificaciones($pdo);
                break;
                
            case 'obtener_calificacion_pedido':
                obtenerCalificacionPedido($pdo, $_GET);
                break;
                
            default:
                sendResponse(false, 'Acción GET no válida');
        }
        break;
        
    case 'POST':
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input || !isset($input['action'])) {
            sendResponse(false, 'Acción requerida');
        }
        
        switch ($input['action']) {
            case 'crear_calificacion':
                crearCalificacion($pdo, $input);
                break;
                
            case 'responder_calificacion':
                responderCalificacion($pdo, $input);
                break;
                
            default:
                sendResponse(false, 'Acción POST no válida');
        }
        break;
        
    default:
        sendResponse(false, 'Método no permitido');
}

/**
 * PASO 9: Crear nueva calificación
 */
function crearCalificacion($pdo, $data) {
    try {
        $pedidoId = $data['pedido_id'];
        $deliveryId = $data['delivery_id'];
        $clienteId = $data['cliente_id'];
        $calificacion = $data['calificacion'];
        $comentario = $data['comentario'] ?? '';
        
        // Validar datos
        if (!$pedidoId || !$deliveryId || !$clienteId || !$calificacion) {
            sendResponse(false, 'Datos incompletos para crear calificación');
        }
        
        if ($calificacion < 1 || $calificacion > 5) {
            sendResponse(false, 'La calificación debe estar entre 1 y 5 estrellas');
        }
        
        // Verificar que el pedido existe y está entregado
        $stmt = $pdo->prepare("SELECT estado FROM pedidos WHERE id = ? AND repartidor_id = ?");
        $stmt->execute([$pedidoId, $deliveryId]);
        $pedido = $stmt->fetch();
        
        if (!$pedido) {
            sendResponse(false, 'Pedido no encontrado o delivery incorrecto');
        }
        
        if ($pedido['estado'] !== 'entregado') {
            sendResponse(false, 'Solo se pueden calificar pedidos entregados');
        }
        
        // Verificar que no existe calificación previa
        $stmt = $pdo->prepare("SELECT id FROM calificaciones WHERE pedido_id = ?");
        $stmt->execute([$pedidoId]);
        if ($stmt->fetch()) {
            sendResponse(false, 'Este pedido ya ha sido calificado');
        }
        
        $pdo->beginTransaction();
        
        // Insertar calificación
        $stmt = $pdo->prepare("
            INSERT INTO calificaciones (
                pedido_id, delivery_id, cliente_id, calificacion, comentario, fecha_calificacion
            ) VALUES (?, ?, ?, ?, ?, NOW())
        ");
        $stmt->execute([$pedidoId, $deliveryId, $clienteId, $calificacion, $comentario]);
        
        $calificacionId = $pdo->lastInsertId();
        
        // Actualizar calificación promedio del delivery
        actualizarCalificacionPromedio($pdo, $deliveryId);
        
        // Registrar en log de actividad
        $stmt = $pdo->prepare("
            INSERT INTO actividad_log (
                tipo_actividad, usuario_id, descripcion, datos_json, fecha_actividad
            ) VALUES (?, ?, ?, ?, NOW())
        ");
        $stmt->execute([
            'calificacion_creada',
            $clienteId,
            "Cliente calificó delivery con {$calificacion} estrellas",
            json_encode([
                'pedido_id' => $pedidoId,
                'delivery_id' => $deliveryId,
                'calificacion' => $calificacion,
                'comentario' => $comentario
            ])
        ]);
        
        $pdo->commit();
        
        // Obtener nueva calificación promedio
        $stmt = $pdo->prepare("SELECT calificacion_promedio FROM repartidores WHERE user_id = ?");
        $stmt->execute([$deliveryId]);
        $nuevoPromedio = $stmt->fetchColumn();
        
        error_log("⭐ Nueva calificación - Pedido: $pedidoId, Delivery: $deliveryId, Calificación: $calificacion/5");
        
        sendResponse(true, 'Calificación registrada exitosamente', [
            'calificacion_id' => $calificacionId,
            'calificacion' => $calificacion,
            'nuevo_promedio' => round($nuevoPromedio, 1),
            'comentario' => $comentario
        ]);
        
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollback();
        }
        error_log("❌ Error creando calificación: " . $e->getMessage());
        sendResponse(false, 'Error al registrar calificación: ' . $e->getMessage());
    }
}

/**
 * PASO 9: Actualizar calificación promedio del delivery
 */
function actualizarCalificacionPromedio($pdo, $deliveryId) {
    $stmt = $pdo->prepare("
        SELECT AVG(calificacion) as promedio, COUNT(*) as total
        FROM calificaciones 
        WHERE delivery_id = ?
    ");
    $stmt->execute([$deliveryId]);
    $resultado = $stmt->fetch();
    
    $promedio = $resultado['promedio'] ?? 0;
    $totalCalificaciones = $resultado['total'] ?? 0;
    
    // Actualizar en tabla repartidores
    $stmt = $pdo->prepare("
        UPDATE repartidores 
        SET calificacion_promedio = ?, total_calificaciones = ?
        WHERE user_id = ?
    ");
    $stmt->execute([$promedio, $totalCalificaciones, $deliveryId]);
}

/**
 * PASO 9: Obtener calificaciones de un delivery
 */
function obtenerCalificacionesDelivery($pdo, $params) {
    try {
        $deliveryId = $params['delivery_id'] ?? null;
        $limite = $params['limite'] ?? 10;
        $offset = $params['offset'] ?? 0;
        
        if (!$deliveryId) {
            sendResponse(false, 'ID de delivery requerido');
        }
        
        // Obtener calificaciones con información del pedido
        $stmt = $pdo->prepare("
            SELECT c.*, p.direccion_entrega, u.nombre as cliente_nombre
            FROM calificaciones c
            JOIN pedidos p ON c.pedido_id = p.id
            LEFT JOIN users u ON c.cliente_id = u.id
            WHERE c.delivery_id = ?
            ORDER BY c.fecha_calificacion DESC
            LIMIT ? OFFSET ?
        ");
        $stmt->execute([$deliveryId, $limite, $offset]);
        $calificaciones = $stmt->fetchAll();
        
        // Obtener estadísticas del delivery
        $stmt = $pdo->prepare("
            SELECT 
                AVG(calificacion) as promedio,
                COUNT(*) as total,
                SUM(CASE WHEN calificacion = 5 THEN 1 ELSE 0 END) as cinco_estrellas,
                SUM(CASE WHEN calificacion = 4 THEN 1 ELSE 0 END) as cuatro_estrellas,
                SUM(CASE WHEN calificacion = 3 THEN 1 ELSE 0 END) as tres_estrellas,
                SUM(CASE WHEN calificacion = 2 THEN 1 ELSE 0 END) as dos_estrellas,
                SUM(CASE WHEN calificacion = 1 THEN 1 ELSE 0 END) as una_estrella
            FROM calificaciones 
            WHERE delivery_id = ?
        ");
        $stmt->execute([$deliveryId]);
        $estadisticas = $stmt->fetch();
        
        sendResponse(true, 'Calificaciones obtenidas', [
            'calificaciones' => $calificaciones,
            'estadisticas' => $estadisticas,
            'total_registros' => count($calificaciones)
        ]);
        
    } catch (Exception $e) {
        sendResponse(false, 'Error obteniendo calificaciones: ' . $e->getMessage());
    }
}

/**
 * PASO 9: Obtener estadísticas generales de calificaciones
 */
function obtenerEstadisticasCalificaciones($pdo) {
    try {
        // Estadísticas generales
        $stmt = $pdo->prepare("
            SELECT 
                AVG(calificacion) as promedio_general,
                COUNT(*) as total_calificaciones,
                COUNT(DISTINCT delivery_id) as deliveries_calificados,
                SUM(CASE WHEN calificacion >= 4 THEN 1 ELSE 0 END) as calificaciones_positivas
            FROM calificaciones
            WHERE fecha_calificacion >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        ");
        $stmt->execute();
        $estadisticasGenerales = $stmt->fetch();
        
        // Top deliveries por calificación
        $stmt = $pdo->prepare("
            SELECT u.nombre, r.calificacion_promedio, r.total_calificaciones
            FROM repartidores r
            JOIN users u ON r.user_id = u.id
            WHERE r.total_calificaciones >= 5
            ORDER BY r.calificacion_promedio DESC, r.total_calificaciones DESC
            LIMIT 10
        ");
        $stmt->execute();
        $topDeliveries = $stmt->fetchAll();
        
        // Distribución de calificaciones por día (últimos 7 días)
        $stmt = $pdo->prepare("
            SELECT 
                DATE(fecha_calificacion) as fecha,
                AVG(calificacion) as promedio_dia,
                COUNT(*) as total_dia
            FROM calificaciones
            WHERE fecha_calificacion >= DATE_SUB(NOW(), INTERVAL 7 DAY)
            GROUP BY DATE(fecha_calificacion)
            ORDER BY fecha DESC
        ");
        $stmt->execute();
        $tendenciaSemanal = $stmt->fetchAll();
        
        sendResponse(true, 'Estadísticas obtenidas', [
            'estadisticas_generales' => $estadisticasGenerales,
            'top_deliveries' => $topDeliveries,
            'tendencia_semanal' => $tendenciaSemanal
        ]);
        
    } catch (Exception $e) {
        sendResponse(false, 'Error obteniendo estadísticas: ' . $e->getMessage());
    }
}

/**
 * PASO 9: Obtener calificación de un pedido específico
 */
function obtenerCalificacionPedido($pdo, $params) {
    try {
        $pedidoId = $params['pedido_id'] ?? null;
        
        if (!$pedidoId) {
            sendResponse(false, 'ID de pedido requerido');
        }
        
        $stmt = $pdo->prepare("
            SELECT c.*, u.nombre as cliente_nombre, d.nombre as delivery_nombre
            FROM calificaciones c
            LEFT JOIN users u ON c.cliente_id = u.id
            LEFT JOIN users d ON c.delivery_id = d.id
            WHERE c.pedido_id = ?
        ");
        $stmt->execute([$pedidoId]);
        $calificacion = $stmt->fetch();
        
        if (!$calificacion) {
            sendResponse(false, 'No se encontró calificación para este pedido');
        }
        
        sendResponse(true, 'Calificación encontrada', $calificacion);
        
    } catch (Exception $e) {
        sendResponse(false, 'Error obteniendo calificación: ' . $e->getMessage());
    }
}

/**
 * PASO 9: Responder a una calificación (para deliveries)
 */
function responderCalificacion($pdo, $data) {
    try {
        $calificacionId = $data['calificacion_id'];
        $deliveryId = $data['delivery_id'];
        $respuesta = $data['respuesta'];
        
        if (!$calificacionId || !$deliveryId || !$respuesta) {
            sendResponse(false, 'Datos incompletos para responder calificación');
        }
        
        // Verificar que la calificación pertenece al delivery
        $stmt = $pdo->prepare("SELECT delivery_id FROM calificaciones WHERE id = ?");
        $stmt->execute([$calificacionId]);
        $calificacion = $stmt->fetch();
        
        if (!$calificacion || $calificacion['delivery_id'] != $deliveryId) {
            sendResponse(false, 'Calificación no encontrada o no autorizada');
        }
        
        // Actualizar respuesta
        $stmt = $pdo->prepare("
            UPDATE calificaciones 
            SET respuesta_delivery = ?, fecha_respuesta = NOW() 
            WHERE id = ?
        ");
        $stmt->execute([$respuesta, $calificacionId]);
        
        error_log("💬 Delivery respondió calificación - ID: $calificacionId");
        
        sendResponse(true, 'Respuesta registrada exitosamente', [
            'calificacion_id' => $calificacionId,
            'respuesta' => $respuesta
        ]);
        
    } catch (Exception $e) {
        sendResponse(false, 'Error al responder calificación: ' . $e->getMessage());
    }
}
?>
