<?php

namespace Src;

/**
 * Clase que representa un taller mecánico en la plataforma
 */
class Workshop {
    private $id;
    private $name;
    private $location;
    private $services;
    private $contactInfo;
    private $rating;
    private $specialties;
    private $partRequests = [];

    /**
     * Constructor de la clase Workshop
     *
     * @param string $name Nombre del taller
     * @param string $location Ubicación del taller
     * @param array $services Servicios que ofrece el taller
     * @param array $contactInfo Información de contacto
     * @param array $specialties Especialidades del taller
     */
    public function __construct($name, $location, $services, $contactInfo, $specialties = []) {
        $this->id = uniqid('workshop_');
        $this->name = $name;
        $this->location = $location;
        $this->services = $services;
        $this->contactInfo = $contactInfo;
        $this->rating = 0;
        $this->specialties = $specialties;
    }

    /**
     * Obtiene el ID único del taller
     *
     * @return string
     */
    public function getId() {
        return $this->id;
    }

    /**
     * Obtiene el nombre del taller
     *
     * @return string
     */
    public function getName() {
        return $this->name;
    }

    /**
     * Establece el nombre del taller
     *
     * @param string $name
     * @return void
     */
    public function setName($name) {
        $this->name = $name;
    }

    /**
     * Obtiene la ubicación del taller
     *
     * @return string
     */
    public function getLocation() {
        return $this->location;
    }

    /**
     * Establece la ubicación del taller
     *
     * @param string $location
     * @return void
     */
    public function setLocation($location) {
        $this->location = $location;
    }

    /**
     * Obtiene los servicios que ofrece el taller
     *
     * @return array
     */
    public function getServices() {
        return $this->services;
    }

    /**
     * Agrega un nuevo servicio al taller
     *
     * @param string $service
     * @return void
     */
    public function addService($service) {
        if (!in_array($service, $this->services)) {
            $this->services[] = $service;
        }
    }

    /**
     * Obtiene la información de contacto del taller
     *
     * @return array
     */
    public function getContactInfo() {
        return $this->contactInfo;
    }

    /**
     * Establece la información de contacto del taller
     *
     * @param array $contactInfo
     * @return void
     */
    public function setContactInfo($contactInfo) {
        $this->contactInfo = $contactInfo;
    }

    /**
     * Obtiene la calificación del taller
     *
     * @return float
     */
    public function getRating() {
        return $this->rating;
    }

    /**
     * Actualiza la calificación del taller
     *
     * @param float $rating
     * @return void
     */
    public function updateRating($rating) {
        $this->rating = $rating;
    }

    /**
     * Obtiene las especialidades del taller
     *
     * @return array
     */
    public function getSpecialties() {
        return $this->specialties;
    }

    /**
     * Agrega una especialidad al taller
     *
     * @param string $specialty
     * @return void
     */
    public function addSpecialty($specialty) {
        if (!in_array($specialty, $this->specialties)) {
            $this->specialties[] = $specialty;
        }
    }

    /**
     * Crea una solicitud de repuestos
     *
     * @param string $partName Nombre del repuesto
     * @param string $partNumber Número de parte
     * @param int $quantity Cantidad requerida
     * @param string $urgency Nivel de urgencia (normal, urgente, crítico)
     * @return string ID de la solicitud
     */
    public function createPartRequest($partName, $partNumber, $quantity, $urgency = 'normal') {
        $requestId = uniqid('request_');
        $request = [
            'id' => $requestId,
            'partName' => $partName,
            'partNumber' => $partNumber,
            'quantity' => $quantity,
            'urgency' => $urgency,
            'status' => 'pending',
            'createdAt' => date('Y-m-d H:i:s'),
            'offers' => []
        ];

        $this->partRequests[$requestId] = $request;
        return $requestId;
    }

    /**
     * Obtiene todas las solicitudes de repuestos
     *
     * @return array
     */
    public function getPartRequests() {
        return $this->partRequests;
    }

    /**
     * Obtiene una solicitud específica por su ID
     *
     * @param string $requestId
     * @return array|null
     */
    public function getPartRequest($requestId) {
        return isset($this->partRequests[$requestId]) ? $this->partRequests[$requestId] : null;
    }

    /**
     * Acepta una oferta para una solicitud de repuestos
     *
     * @param string $requestId ID de la solicitud
     * @param string $offerId ID de la oferta
     * @return bool
     */
    public function acceptOffer($requestId, $offerId) {
        if (!isset($this->partRequests[$requestId])) {
            return false;
        }

        $request = &$this->partRequests[$requestId];

        foreach ($request['offers'] as &$offer) {
            if ($offer['id'] === $offerId) {
                $offer['status'] = 'accepted';
                $request['status'] = 'in_progress';
                return true;
            }
        }

        return false;
    }

    /**
     * Marca una solicitud como completada
     *
     * @param string $requestId
     * @return bool
     */
    public function completeRequest($requestId) {
        if (!isset($this->partRequests[$requestId])) {
            return false;
        }

        $this->partRequests[$requestId]['status'] = 'completed';
        return true;
    }

    /**
     * Convierte el objeto a un array para JSON
     *
     * @return array
     */
    public function toArray() {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'location' => $this->location,
            'services' => $this->services,
            'contactInfo' => $this->contactInfo,
            'rating' => $this->rating,
            'specialties' => $this->specialties
        ];
    }
}