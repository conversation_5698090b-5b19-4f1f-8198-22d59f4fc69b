-- PASO 4: <PERSON><PERSON> para log de notificaciones push
-- RepuMovil - Sistema de Notificaciones Push para Deliveries

-- PASO 1: Tabla para registrar todas las notificaciones push enviadas (ACTUALIZADA)
CREATE TABLE IF NOT EXISTS notificaciones_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    delivery_id INT NOT NULL, -- Cambiado de repartidor_id a delivery_id para consistencia
    pedido_id INT,
    tipo VARCHAR(50) NOT NULL,
    titulo VARCHAR(255) NOT NULL,
    mensaje TEXT,
    datos_json JSON,
    expo_push_token VARCHAR(500), -- Token específico de Expo
    resultado_envio JSON, -- Respuesta completa de Expo Push API
    enviada BOOLEAN DEFAULT FALSE,
    leida BOOLEAN DEFAULT FALSE,
    respondida BOOLEAN DEFAULT FALSE,
    respuesta VARCHAR(20), -- 'aceptado', 'rechazado', 'timeout'
    fecha_envio TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    fecha_lectura TIMESTAMP NULL,
    fecha_respuesta TIMESTAMP NULL,
    plataforma VARCHAR(20), -- 'android', 'ios', 'web'
    error_mensaje TEXT,
    intentos_envio INT DEFAULT 1,
    expo_receipt_id VARCHAR(255), -- ID del recibo de Expo
    INDEX idx_delivery (delivery_id),
    INDEX idx_pedido (pedido_id),
    INDEX idx_fecha_envio (fecha_envio),
    INDEX idx_tipo (tipo),
    INDEX idx_expo_receipt (expo_receipt_id),
    FOREIGN KEY (delivery_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Tabla para tokens de dispositivos push
CREATE TABLE IF NOT EXISTS dispositivos_push (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    push_token VARCHAR(255) NOT NULL UNIQUE,
    plataforma VARCHAR(20) NOT NULL, -- 'android', 'ios', 'web'
    modelo_dispositivo VARCHAR(100),
    version_app VARCHAR(20),
    activo BOOLEAN DEFAULT TRUE,
    fecha_registro TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    fecha_ultimo_uso TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user (user_id),
    INDEX idx_token (push_token),
    INDEX idx_activo (activo),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- PASO 1: Tabla específica para tokens de Expo Push (para React Native)
CREATE TABLE IF NOT EXISTS delivery_push_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    delivery_id INT NOT NULL,
    expo_push_token VARCHAR(500) NOT NULL, -- Tokens de Expo pueden ser largos
    device_platform VARCHAR(20), -- 'android', 'ios'
    device_name VARCHAR(100),
    device_model VARCHAR(100),
    device_id VARCHAR(255), -- ID único del dispositivo
    app_version VARCHAR(50), -- Versión de la app RepuMovil
    fecha_registro TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    fecha_actualizacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    ultima_actividad TIMESTAMP NULL, -- Última vez que se usó
    activo BOOLEAN DEFAULT TRUE,
    UNIQUE KEY unique_delivery_token (delivery_id),
    INDEX idx_delivery_id (delivery_id),
    INDEX idx_expo_token (expo_push_token(255)), -- Índice parcial para tokens largos
    INDEX idx_activo (activo),
    INDEX idx_ultima_actividad (ultima_actividad),
    FOREIGN KEY (delivery_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Tabla para configuración de notificaciones por usuario
CREATE TABLE IF NOT EXISTS configuracion_notificaciones (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL UNIQUE,
    nuevos_pedidos BOOLEAN DEFAULT TRUE,
    pedidos_urgentes BOOLEAN DEFAULT TRUE,
    bonificaciones BOOLEAN DEFAULT TRUE,
    recordatorios BOOLEAN DEFAULT TRUE,
    promociones BOOLEAN DEFAULT FALSE,
    sonido_activo BOOLEAN DEFAULT TRUE,
    vibracion_activa BOOLEAN DEFAULT TRUE,
    horario_inicio TIME DEFAULT '06:00:00',
    horario_fin TIME DEFAULT '23:00:00',
    dias_activos JSON DEFAULT '["lunes","martes","miercoles","jueves","viernes","sabado","domingo"]',
    fecha_actualizacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Tabla para estadísticas de notificaciones
CREATE TABLE IF NOT EXISTS estadisticas_notificaciones (
    id INT AUTO_INCREMENT PRIMARY KEY,
    fecha DATE NOT NULL,
    total_enviadas INT DEFAULT 0,
    total_entregadas INT DEFAULT 0,
    total_leidas INT DEFAULT 0,
    total_aceptadas INT DEFAULT 0,
    total_rechazadas INT DEFAULT 0,
    total_timeout INT DEFAULT 0,
    tiempo_respuesta_promedio DECIMAL(5,2), -- en segundos
    tasa_entrega DECIMAL(5,2), -- porcentaje
    tasa_lectura DECIMAL(5,2), -- porcentaje
    tasa_aceptacion DECIMAL(5,2), -- porcentaje
    fecha_calculo TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_fecha (fecha)
);

-- Insertar configuración por defecto para repartidores existentes
INSERT IGNORE INTO configuracion_notificaciones (user_id)
SELECT id FROM users WHERE tipo_usuario = 'repartidor';

-- Insertar algunos tokens de prueba para testing
INSERT IGNORE INTO dispositivos_push (user_id, push_token, plataforma, modelo_dispositivo, version_app) VALUES
(1, 'token_android_juan_perez_123', 'android', 'Samsung Galaxy S21', '1.0.0'),
(2, 'token_ios_maria_gonzalez_456', 'ios', 'iPhone 13 Pro', '1.0.0'),
(3, 'token_android_carlos_rodriguez_789', 'android', 'Xiaomi Redmi Note 10', '1.0.0'),
(4, 'token_web_ana_lopez_012', 'web', 'Chrome Desktop', '1.0.0');

-- Insertar algunas notificaciones de ejemplo para testing
INSERT INTO notificaciones_log (
    repartidor_id, pedido_id, tipo, titulo, mensaje, datos_json, 
    enviada, leida, respondida, respuesta, fecha_envio, fecha_lectura, fecha_respuesta
) VALUES
(1, 1001, 'nuevo_pedido_asignado', '🚨 ¡NUEVO PEDIDO ASIGNADO!', 'Pedido #1001 listo para recoger', 
 '{"cliente": "Taller Central", "direccion": "Av. Libertador 1234", "ganancia": "$280", "distancia": "2.3 km"}',
 TRUE, TRUE, TRUE, 'aceptado', 
 DATE_SUB(NOW(), INTERVAL 15 MINUTE), 
 DATE_SUB(NOW(), INTERVAL 14 MINUTE), 
 DATE_SUB(NOW(), INTERVAL 13 MINUTE)),

(2, 1002, 'nuevo_pedido_asignado', '🚨 ¡NUEVO PEDIDO ASIGNADO!', 'Pedido #1002 listo para recoger',
 '{"cliente": "AutoService Plus", "direccion": "Ruta 40 Km 15", "ganancia": "$450", "distancia": "4.1 km"}',
 TRUE, TRUE, TRUE, 'aceptado',
 DATE_SUB(NOW(), INTERVAL 30 MINUTE),
 DATE_SUB(NOW(), INTERVAL 29 MINUTE),
 DATE_SUB(NOW(), INTERVAL 28 MINUTE)),

(3, 1003, 'pedido_urgente', '⚡ ¡PEDIDO URGENTE!', 'Pedido #1003 - Cliente esperando',
 '{"cliente": "Mecánico López", "direccion": "Calle Rivadavia 567", "ganancia": "$350", "distancia": "1.8 km"}',
 TRUE, TRUE, TRUE, 'rechazado',
 DATE_SUB(NOW(), INTERVAL 45 MINUTE),
 DATE_SUB(NOW(), INTERVAL 44 MINUTE),
 DATE_SUB(NOW(), INTERVAL 43 MINUTE)),

(1, 1004, 'bonificacion_disponible', '🎁 ¡BONUS DISPONIBLE!', 'Zona premium con bonificación extra',
 '{"zona": "Centro Comercial", "bonus": "+$100", "distancia": "3.2 km"}',
 TRUE, TRUE, FALSE, NULL,
 DATE_SUB(NOW(), INTERVAL 60 MINUTE),
 DATE_SUB(NOW(), INTERVAL 59 MINUTE),
 NULL);

-- PASO 6: Tabla para log de rechazos de deliveries
CREATE TABLE IF NOT EXISTS rechazos_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    repartidor_id INT NOT NULL,
    pedido_id INT NOT NULL,
    motivo VARCHAR(255) NOT NULL,
    motivo_detalle TEXT,
    fecha_rechazo TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    reasignado BOOLEAN DEFAULT FALSE,
    nuevo_repartidor_id INT,
    tiempo_reasignacion INT, -- en segundos
    INDEX idx_repartidor (repartidor_id),
    INDEX idx_pedido (pedido_id),
    INDEX idx_fecha_rechazo (fecha_rechazo),
    INDEX idx_motivo (motivo),
    FOREIGN KEY (repartidor_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (nuevo_repartidor_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Tabla para estadísticas de reasignaciones
CREATE TABLE IF NOT EXISTS estadisticas_reasignaciones (
    id INT AUTO_INCREMENT PRIMARY KEY,
    fecha DATE NOT NULL,
    total_rechazos INT DEFAULT 0,
    rechazos_por_distancia INT DEFAULT 0,
    rechazos_por_disponibilidad INT DEFAULT 0,
    rechazos_por_vehiculo INT DEFAULT 0,
    rechazos_otros INT DEFAULT 0,
    total_reasignaciones INT DEFAULT 0,
    reasignaciones_exitosas INT DEFAULT 0,
    tiempo_promedio_reasignacion DECIMAL(5,2), -- en segundos
    tasa_reasignacion_exitosa DECIMAL(5,2), -- porcentaje
    fecha_calculo TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_fecha_reasignacion (fecha)
);

-- Insertar algunos rechazos de ejemplo para testing
INSERT INTO rechazos_log (
    repartidor_id, pedido_id, motivo, motivo_detalle, fecha_rechazo,
    reasignado, nuevo_repartidor_id, tiempo_reasignacion
) VALUES
(3, 1003, 'Muy lejos', 'Distancia superior a 5km desde mi ubicación actual',
 DATE_SUB(NOW(), INTERVAL 45 MINUTE), TRUE, 1, 3),
(2, 1005, 'No disponible', 'Tengo que recoger a mi hijo del colegio',
 DATE_SUB(NOW(), INTERVAL 2 HOUR), TRUE, 4, 5),
(1, 1007, 'Problema con vehículo', 'Moto con problemas en el motor',
 DATE_SUB(NOW(), INTERVAL 3 HOUR), TRUE, 2, 8),
(4, 1009, 'Otro motivo', 'Cliente conocido con historial de problemas',
 DATE_SUB(NOW(), INTERVAL 4 HOUR), FALSE, NULL, NULL);

-- Insertar estadísticas de ejemplo
INSERT IGNORE INTO estadisticas_notificaciones (
    fecha, total_enviadas, total_entregadas, total_leidas, total_aceptadas,
    total_rechazadas, total_timeout, tiempo_respuesta_promedio,
    tasa_entrega, tasa_lectura, tasa_aceptacion
) VALUES
(CURDATE(), 45, 42, 38, 32, 6, 4, 18.5, 93.33, 90.48, 84.21),
(DATE_SUB(CURDATE(), INTERVAL 1 DAY), 52, 48, 44, 38, 6, 4, 22.1, 92.31, 91.67, 86.36),
(DATE_SUB(CURDATE(), INTERVAL 2 DAY), 38, 35, 32, 28, 4, 3, 16.8, 92.11, 91.43, 87.50);

-- Insertar estadísticas de reasignaciones de ejemplo
INSERT IGNORE INTO estadisticas_reasignaciones (
    fecha, total_rechazos, rechazos_por_distancia, rechazos_por_disponibilidad,
    rechazos_por_vehiculo, rechazos_otros, total_reasignaciones,
    reasignaciones_exitosas, tiempo_promedio_reasignacion, tasa_reasignacion_exitosa
) VALUES
(CURDATE(), 8, 3, 2, 1, 2, 7, 6, 4.2, 85.71),
(DATE_SUB(CURDATE(), INTERVAL 1 DAY), 12, 5, 3, 2, 2, 11, 9, 5.1, 81.82),
(DATE_SUB(CURDATE(), INTERVAL 2 DAY), 6, 2, 2, 1, 1, 6, 5, 3.8, 83.33);

-- PASO 7: Tablas para tracking en tiempo real
CREATE TABLE IF NOT EXISTS tracking_ubicaciones (
    id INT AUTO_INCREMENT PRIMARY KEY,
    pedido_id INT NOT NULL,
    delivery_id INT NOT NULL,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    accuracy DECIMAL(8, 2), -- precisión en metros
    speed DECIMAL(6, 2), -- velocidad en m/s
    heading DECIMAL(6, 2), -- dirección en grados
    timestamp_ubicacion TIMESTAMP NOT NULL,
    fecha_registro TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_pedido (pedido_id),
    INDEX idx_delivery (delivery_id),
    INDEX idx_timestamp (timestamp_ubicacion),
    INDEX idx_fecha_registro (fecha_registro),
    FOREIGN KEY (pedido_id) REFERENCES pedidos(id) ON DELETE CASCADE,
    FOREIGN KEY (delivery_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Tabla para log de estados del pedido
CREATE TABLE IF NOT EXISTS estados_pedido_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    pedido_id INT NOT NULL,
    estado_anterior VARCHAR(50),
    estado_nuevo VARCHAR(50) NOT NULL,
    delivery_id INT,
    notas TEXT,
    fecha_cambio TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_pedido (pedido_id),
    INDEX idx_delivery (delivery_id),
    INDEX idx_fecha_cambio (fecha_cambio),
    FOREIGN KEY (pedido_id) REFERENCES pedidos(id) ON DELETE CASCADE,
    FOREIGN KEY (delivery_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Tabla para métricas de tracking
CREATE TABLE IF NOT EXISTS metricas_tracking (
    id INT AUTO_INCREMENT PRIMARY KEY,
    fecha DATE NOT NULL,
    total_pedidos_tracked INT DEFAULT 0,
    tiempo_promedio_entrega DECIMAL(5,2), -- en minutos
    distancia_promedio_recorrida DECIMAL(6,2), -- en km
    velocidad_promedio DECIMAL(5,2), -- en km/h
    precision_promedio DECIMAL(6,2), -- en metros
    actualizaciones_ubicacion INT DEFAULT 0,
    pedidos_entregados_tiempo INT DEFAULT 0,
    tasa_entrega_tiempo DECIMAL(5,2), -- porcentaje
    fecha_calculo TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_fecha_tracking (fecha)
);

-- Agregar columnas para tracking a tabla pedidos si no existen
ALTER TABLE pedidos
ADD COLUMN IF NOT EXISTS eta_minutos INT,
ADD COLUMN IF NOT EXISTS distancia_recorrida DECIMAL(6,2),
ADD COLUMN IF NOT EXISTS tiempo_tracking_inicio TIMESTAMP NULL,
ADD COLUMN IF NOT EXISTS tiempo_tracking_fin TIMESTAMP NULL;

-- Agregar columnas para ubicación actual a tabla repartidores si no existen
ALTER TABLE repartidores
ADD COLUMN IF NOT EXISTS latitud_actual DECIMAL(10, 8),
ADD COLUMN IF NOT EXISTS longitud_actual DECIMAL(11, 8),
ADD COLUMN IF NOT EXISTS ultima_actualizacion TIMESTAMP NULL;

-- Insertar datos de tracking de ejemplo
INSERT INTO tracking_ubicaciones (
    pedido_id, delivery_id, latitude, longitude, accuracy, speed, heading, timestamp_ubicacion
) VALUES
(1001, 1, -31.5375, -68.5364, 5.0, 8.33, 45.0, DATE_SUB(NOW(), INTERVAL 5 MINUTE)),
(1001, 1, -31.5380, -68.5360, 4.5, 9.72, 42.0, DATE_SUB(NOW(), INTERVAL 4 MINUTE)),
(1001, 1, -31.5385, -68.5355, 3.8, 11.11, 38.0, DATE_SUB(NOW(), INTERVAL 3 MINUTE)),
(1001, 1, -31.5390, -68.5350, 4.2, 10.00, 35.0, DATE_SUB(NOW(), INTERVAL 2 MINUTE)),
(1001, 1, -31.5395, -68.5345, 3.5, 8.89, 32.0, DATE_SUB(NOW(), INTERVAL 1 MINUTE)),
(1002, 2, -31.5400, -68.5320, 6.0, 7.50, 180.0, DATE_SUB(NOW(), INTERVAL 10 MINUTE)),
(1002, 2, -31.5405, -68.5315, 5.5, 8.33, 175.0, DATE_SUB(NOW(), INTERVAL 8 MINUTE));

-- Insertar log de estados de ejemplo
INSERT INTO estados_pedido_log (
    pedido_id, estado_anterior, estado_nuevo, delivery_id, notas, fecha_cambio
) VALUES
(1001, 'empacado', 'asignado', 1, 'Pedido asignado automáticamente', DATE_SUB(NOW(), INTERVAL 20 MINUTE)),
(1001, 'asignado', 'recogido', 1, 'Pedido recogido del proveedor', DATE_SUB(NOW(), INTERVAL 15 MINUTE)),
(1001, 'recogido', 'en_camino', 1, 'En camino hacia el cliente', DATE_SUB(NOW(), INTERVAL 10 MINUTE)),
(1002, 'empacado', 'asignado', 2, 'Pedido asignado automáticamente', DATE_SUB(NOW(), INTERVAL 25 MINUTE)),
(1002, 'asignado', 'recogido', 2, 'Pedido recogido del proveedor', DATE_SUB(NOW(), INTERVAL 18 MINUTE));

-- Actualizar ubicaciones actuales de deliveries
UPDATE repartidores SET
    latitud_actual = -31.5395,
    longitud_actual = -68.5345,
    ultima_actualizacion = NOW()
WHERE user_id = 1;

UPDATE repartidores SET
    latitud_actual = -31.5405,
    longitud_actual = -68.5315,
    ultima_actualizacion = DATE_SUB(NOW(), INTERVAL 5 MINUTE)
WHERE user_id = 2;

-- Insertar métricas de tracking de ejemplo
INSERT IGNORE INTO metricas_tracking (
    fecha, total_pedidos_tracked, tiempo_promedio_entrega, distancia_promedio_recorrida,
    velocidad_promedio, precision_promedio, actualizaciones_ubicacion,
    pedidos_entregados_tiempo, tasa_entrega_tiempo
) VALUES
(CURDATE(), 15, 18.5, 3.2, 22.5, 4.8, 180, 13, 86.67),
(DATE_SUB(CURDATE(), INTERVAL 1 DAY), 18, 20.2, 3.8, 21.2, 5.2, 210, 15, 83.33),
(DATE_SUB(CURDATE(), INTERVAL 2 DAY), 12, 17.8, 2.9, 23.1, 4.5, 145, 11, 91.67);

-- Crear vista para tracking en tiempo real
CREATE OR REPLACE VIEW vista_tracking_activo AS
SELECT
    p.id as pedido_id,
    p.estado as pedido_estado,
    p.direccion_entrega,
    p.eta_minutos,
    u.nombre as delivery_nombre,
    u.telefono as delivery_telefono,
    r.calificacion_promedio,
    r.latitud_actual,
    r.longitud_actual,
    r.ultima_actualizacion,
    t.latitude as ultima_lat,
    t.longitude as ultima_lng,
    t.accuracy,
    t.speed,
    t.timestamp_ubicacion as ultima_ubicacion
FROM pedidos p
JOIN users u ON p.repartidor_id = u.id
JOIN repartidores r ON p.repartidor_id = r.user_id
LEFT JOIN tracking_ubicaciones t ON p.id = t.pedido_id
    AND t.id = (
        SELECT MAX(id) FROM tracking_ubicaciones
        WHERE pedido_id = p.id
    )
WHERE p.estado IN ('asignado', 'recogido', 'en_camino')
ORDER BY p.fecha_asignacion DESC;

-- PASO 9: Tablas para sistema de calificaciones
CREATE TABLE IF NOT EXISTS calificaciones (
    id INT AUTO_INCREMENT PRIMARY KEY,
    pedido_id INT NOT NULL,
    delivery_id INT NOT NULL,
    cliente_id INT NOT NULL,
    calificacion INT NOT NULL CHECK (calificacion BETWEEN 1 AND 5),
    comentario TEXT,
    respuesta_delivery TEXT,
    fecha_calificacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    fecha_respuesta TIMESTAMP NULL,
    tags JSON, -- etiquetas como "rápido", "amable", etc.
    INDEX idx_pedido (pedido_id),
    INDEX idx_delivery (delivery_id),
    INDEX idx_cliente (cliente_id),
    INDEX idx_calificacion (calificacion),
    INDEX idx_fecha_calificacion (fecha_calificacion),
    UNIQUE KEY unique_pedido_calificacion (pedido_id),
    FOREIGN KEY (pedido_id) REFERENCES pedidos(id) ON DELETE CASCADE,
    FOREIGN KEY (delivery_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (cliente_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Tabla para log de actividades del sistema
CREATE TABLE IF NOT EXISTS actividad_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tipo_actividad VARCHAR(50) NOT NULL,
    usuario_id INT,
    descripcion TEXT NOT NULL,
    datos_json JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    fecha_actividad TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_tipo_actividad (tipo_actividad),
    INDEX idx_usuario (usuario_id),
    INDEX idx_fecha_actividad (fecha_actividad),
    FOREIGN KEY (usuario_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Tabla para estadísticas de calificaciones por período
CREATE TABLE IF NOT EXISTS estadisticas_calificaciones (
    id INT AUTO_INCREMENT PRIMARY KEY,
    fecha DATE NOT NULL,
    total_calificaciones INT DEFAULT 0,
    promedio_calificaciones DECIMAL(3,2) DEFAULT 0,
    calificaciones_5_estrellas INT DEFAULT 0,
    calificaciones_4_estrellas INT DEFAULT 0,
    calificaciones_3_estrellas INT DEFAULT 0,
    calificaciones_2_estrellas INT DEFAULT 0,
    calificaciones_1_estrella INT DEFAULT 0,
    total_comentarios INT DEFAULT 0,
    total_respuestas_delivery INT DEFAULT 0,
    tasa_respuesta_delivery DECIMAL(5,2) DEFAULT 0,
    fecha_calculo TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_fecha_calificaciones (fecha)
);

-- Agregar columnas para calificaciones a tabla repartidores si no existen
ALTER TABLE repartidores
ADD COLUMN IF NOT EXISTS total_calificaciones INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS ultima_calificacion TIMESTAMP NULL;

-- Insertar calificaciones de ejemplo
INSERT INTO calificaciones (
    pedido_id, delivery_id, cliente_id, calificacion, comentario,
    fecha_calificacion, tags
) VALUES
(1001, 1, 101, 5, 'Excelente servicio, muy rápido y amable. El pedido llegó en perfecto estado.',
 DATE_SUB(NOW(), INTERVAL 2 HOUR), '["rápido", "amable", "profesional"]'),
(1002, 2, 102, 4, 'Buen servicio, aunque se demoró un poco más de lo esperado.',
 DATE_SUB(NOW(), INTERVAL 4 HOUR), '["buena comunicación", "pedido perfecto"]'),
(1003, 1, 103, 5, 'Increíble! Juan es muy profesional y el tracking funcionó perfecto.',
 DATE_SUB(NOW(), INTERVAL 6 HOUR), '["profesional", "tracking perfecto", "muy recomendado"]'),
(1004, 3, 104, 3, 'Servicio regular, el delivery llegó tarde pero fue amable.',
 DATE_SUB(NOW(), INTERVAL 8 HOUR), '["amable", "llegó tarde"]'),
(1005, 2, 105, 5, 'María es excelente! Muy rápida y siempre con buena actitud.',
 DATE_SUB(NOW(), INTERVAL 10 HOUR), '["excelente", "rápida", "buena actitud"]'),
(1006, 1, 106, 4, 'Muy buen servicio, comunicación constante durante la entrega.',
 DATE_SUB(NOW(), INTERVAL 12 HOUR), '["buena comunicación", "puntual"]');

-- Insertar respuestas de deliveries a algunas calificaciones
UPDATE calificaciones SET
    respuesta_delivery = '¡Muchas gracias por tu comentario! Siempre trato de dar lo mejor en cada entrega. ¡Espero verte pronto!',
    fecha_respuesta = DATE_SUB(NOW(), INTERVAL 1 HOUR)
WHERE id = 1;

UPDATE calificaciones SET
    respuesta_delivery = 'Gracias por tu paciencia. Hubo un poco de tráfico, pero me alegra que el pedido haya llegado bien.',
    fecha_respuesta = DATE_SUB(NOW(), INTERVAL 3 HOUR)
WHERE id = 2;

UPDATE calificaciones SET
    respuesta_delivery = '¡Wow! Muchas gracias por tan lindo comentario. Me motiva a seguir mejorando cada día.',
    fecha_respuesta = DATE_SUB(NOW(), INTERVAL 5 HOUR)
WHERE id = 3;

-- Actualizar calificaciones promedio de deliveries
UPDATE repartidores r SET
    calificacion_promedio = (
        SELECT AVG(c.calificacion)
        FROM calificaciones c
        WHERE c.delivery_id = r.user_id
    ),
    total_calificaciones = (
        SELECT COUNT(*)
        FROM calificaciones c
        WHERE c.delivery_id = r.user_id
    ),
    ultima_calificacion = (
        SELECT MAX(c.fecha_calificacion)
        FROM calificaciones c
        WHERE c.delivery_id = r.user_id
    )
WHERE r.user_id IN (SELECT DISTINCT delivery_id FROM calificaciones);

-- Insertar log de actividades de ejemplo
INSERT INTO actividad_log (
    tipo_actividad, usuario_id, descripcion, datos_json, fecha_actividad
) VALUES
('calificacion_creada', 101, 'Cliente calificó delivery con 5 estrellas',
 '{"pedido_id": 1001, "delivery_id": 1, "calificacion": 5}', DATE_SUB(NOW(), INTERVAL 2 HOUR)),
('calificacion_creada', 102, 'Cliente calificó delivery con 4 estrellas',
 '{"pedido_id": 1002, "delivery_id": 2, "calificacion": 4}', DATE_SUB(NOW(), INTERVAL 4 HOUR)),
('respuesta_calificacion', 1, 'Delivery respondió a calificación',
 '{"calificacion_id": 1, "respuesta": "Muchas gracias por tu comentario!"}', DATE_SUB(NOW(), INTERVAL 1 HOUR)),
('calificacion_creada', 103, 'Cliente calificó delivery con 5 estrellas',
 '{"pedido_id": 1003, "delivery_id": 1, "calificacion": 5}', DATE_SUB(NOW(), INTERVAL 6 HOUR));

-- Insertar estadísticas de calificaciones de ejemplo
INSERT IGNORE INTO estadisticas_calificaciones (
    fecha, total_calificaciones, promedio_calificaciones,
    calificaciones_5_estrellas, calificaciones_4_estrellas, calificaciones_3_estrellas,
    calificaciones_2_estrellas, calificaciones_1_estrella, total_comentarios,
    total_respuestas_delivery, tasa_respuesta_delivery
) VALUES
(CURDATE(), 8, 4.25, 4, 2, 1, 1, 0, 8, 3, 37.50),
(DATE_SUB(CURDATE(), INTERVAL 1 DAY), 12, 4.42, 6, 4, 1, 1, 0, 11, 5, 45.45),
(DATE_SUB(CURDATE(), INTERVAL 2 DAY), 15, 4.33, 7, 5, 2, 1, 0, 13, 7, 53.85);

-- Crear vista para calificaciones con información completa
CREATE OR REPLACE VIEW vista_calificaciones_completa AS
SELECT
    c.id,
    c.pedido_id,
    c.calificacion,
    c.comentario,
    c.respuesta_delivery,
    c.fecha_calificacion,
    c.fecha_respuesta,
    c.tags,
    u_cliente.nombre as cliente_nombre,
    u_cliente.email as cliente_email,
    u_delivery.nombre as delivery_nombre,
    u_delivery.telefono as delivery_telefono,
    r.calificacion_promedio as delivery_promedio,
    r.total_calificaciones as delivery_total_calificaciones,
    p.direccion_entrega,
    p.total as pedido_total
FROM calificaciones c
JOIN users u_cliente ON c.cliente_id = u_cliente.id
JOIN users u_delivery ON c.delivery_id = u_delivery.id
JOIN repartidores r ON c.delivery_id = r.user_id
JOIN pedidos p ON c.pedido_id = p.id
ORDER BY c.fecha_calificacion DESC;

-- Crear procedimiento para calcular estadísticas diarias
DELIMITER //
CREATE PROCEDURE CalcularEstadisticasCalificaciones(IN fecha_calculo DATE)
BEGIN
    INSERT INTO estadisticas_calificaciones (
        fecha, total_calificaciones, promedio_calificaciones,
        calificaciones_5_estrellas, calificaciones_4_estrellas, calificaciones_3_estrellas,
        calificaciones_2_estrellas, calificaciones_1_estrella, total_comentarios,
        total_respuestas_delivery, tasa_respuesta_delivery
    )
    SELECT
        fecha_calculo,
        COUNT(*) as total,
        AVG(calificacion) as promedio,
        SUM(CASE WHEN calificacion = 5 THEN 1 ELSE 0 END) as cinco,
        SUM(CASE WHEN calificacion = 4 THEN 1 ELSE 0 END) as cuatro,
        SUM(CASE WHEN calificacion = 3 THEN 1 ELSE 0 END) as tres,
        SUM(CASE WHEN calificacion = 2 THEN 1 ELSE 0 END) as dos,
        SUM(CASE WHEN calificacion = 1 THEN 1 ELSE 0 END) as uno,
        SUM(CASE WHEN comentario IS NOT NULL AND comentario != '' THEN 1 ELSE 0 END) as comentarios,
        SUM(CASE WHEN respuesta_delivery IS NOT NULL THEN 1 ELSE 0 END) as respuestas,
        ROUND((SUM(CASE WHEN respuesta_delivery IS NOT NULL THEN 1 ELSE 0 END) / COUNT(*)) * 100, 2) as tasa_respuesta
    FROM calificaciones
    WHERE DATE(fecha_calificacion) = fecha_calculo
    ON DUPLICATE KEY UPDATE
        total_calificaciones = VALUES(total_calificaciones),
        promedio_calificaciones = VALUES(promedio_calificaciones),
        calificaciones_5_estrellas = VALUES(calificaciones_5_estrellas),
        calificaciones_4_estrellas = VALUES(calificaciones_4_estrellas),
        calificaciones_3_estrellas = VALUES(calificaciones_3_estrellas),
        calificaciones_2_estrellas = VALUES(calificaciones_2_estrellas),
        calificaciones_1_estrella = VALUES(calificaciones_1_estrella),
        total_comentarios = VALUES(total_comentarios),
        total_respuestas_delivery = VALUES(total_respuestas_delivery),
        tasa_respuesta_delivery = VALUES(tasa_respuesta_delivery);
END //
DELIMITER ;

-- Crear vista para estadísticas rápidas
CREATE OR REPLACE VIEW vista_estadisticas_notificaciones AS
SELECT 
    DATE(fecha_envio) as fecha,
    COUNT(*) as total_enviadas,
    SUM(CASE WHEN enviada = 1 THEN 1 ELSE 0 END) as entregadas,
    SUM(CASE WHEN leida = 1 THEN 1 ELSE 0 END) as leidas,
    SUM(CASE WHEN respuesta = 'aceptado' THEN 1 ELSE 0 END) as aceptadas,
    SUM(CASE WHEN respuesta = 'rechazado' THEN 1 ELSE 0 END) as rechazadas,
    SUM(CASE WHEN respondida = 0 AND fecha_envio < DATE_SUB(NOW(), INTERVAL 5 MINUTE) THEN 1 ELSE 0 END) as timeout,
    AVG(CASE 
        WHEN fecha_respuesta IS NOT NULL AND fecha_envio IS NOT NULL 
        THEN TIMESTAMPDIFF(SECOND, fecha_envio, fecha_respuesta) 
        ELSE NULL 
    END) as tiempo_respuesta_promedio,
    ROUND((SUM(CASE WHEN enviada = 1 THEN 1 ELSE 0 END) / COUNT(*)) * 100, 2) as tasa_entrega,
    ROUND((SUM(CASE WHEN leida = 1 THEN 1 ELSE 0 END) / COUNT(*)) * 100, 2) as tasa_lectura,
    ROUND((SUM(CASE WHEN respuesta = 'aceptado' THEN 1 ELSE 0 END) / COUNT(*)) * 100, 2) as tasa_aceptacion
FROM notificaciones_log 
WHERE fecha_envio >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
GROUP BY DATE(fecha_envio)
ORDER BY fecha DESC;

-- Crear procedimiento para limpiar notificaciones antiguas
DELIMITER //
CREATE PROCEDURE LimpiarNotificacionesAntiguas()
BEGIN
    -- Eliminar notificaciones de más de 90 días
    DELETE FROM notificaciones_log 
    WHERE fecha_envio < DATE_SUB(NOW(), INTERVAL 90 DAY);
    
    -- Eliminar tokens de dispositivos inactivos por más de 30 días
    DELETE FROM dispositivos_push 
    WHERE activo = FALSE AND fecha_ultimo_uso < DATE_SUB(NOW(), INTERVAL 30 DAY);
    
    SELECT ROW_COUNT() as registros_eliminados;
END //
DELIMITER ;

-- Crear evento para ejecutar limpieza automática cada semana
-- CREATE EVENT IF NOT EXISTS evento_limpieza_notificaciones
-- ON SCHEDULE EVERY 1 WEEK
-- STARTS CURRENT_TIMESTAMP
-- DO CALL LimpiarNotificacionesAntiguas();

-- Comentarios de documentación
/*
DOCUMENTACIÓN - SISTEMA DE NOTIFICACIONES PUSH REPUMOVIL

TABLAS PRINCIPALES:
1. notificaciones_log: Registro completo de todas las notificaciones enviadas
2. dispositivos_push: Tokens de dispositivos registrados para push notifications
3. configuracion_notificaciones: Preferencias de notificaciones por usuario
4. estadisticas_notificaciones: Métricas agregadas por día

TIPOS DE NOTIFICACIONES:
- nuevo_pedido_asignado: Pedido asignado automáticamente
- pedido_urgente: Pedido con prioridad alta
- bonificacion_disponible: Ofertas especiales o bonos
- recordatorio_pedido: Recordatorios de pedidos pendientes
- promocion_especial: Promociones y ofertas

FLUJO DE NOTIFICACIONES:
1. Sistema asigna pedido automáticamente
2. Se crea registro en notificaciones_log
3. Se envía push notification al dispositivo
4. Se actualiza estado (enviada, leida, respondida)
5. Se registra respuesta del delivery
6. Se calculan estadísticas

MÉTRICAS IMPORTANTES:
- Tasa de entrega: % de notificaciones que llegan al dispositivo
- Tasa de lectura: % de notificaciones que el usuario ve
- Tasa de aceptación: % de pedidos aceptados
- Tiempo de respuesta promedio: Segundos entre envío y respuesta

INTEGRACIÓN:
- Firebase Cloud Messaging (FCM) para Android
- Apple Push Notification Service (APNs) para iOS
- Web Push API para navegadores
- OneSignal como alternativa unificada
*/
