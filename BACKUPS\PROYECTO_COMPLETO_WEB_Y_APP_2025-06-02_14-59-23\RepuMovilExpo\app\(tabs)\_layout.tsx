import { Tabs } from 'expo-router';
import React from 'react';
import { Platform } from 'react-native';

import { HapticTab } from '@/components/HapticTab';
import { IconSymbol } from '@/components/ui/IconSymbol';
import TabBarBackground from '@/components/ui/TabBarBackground';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

export default function TabLayout() {
  const colorScheme = useColorScheme();

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: Colors[colorScheme ?? 'light'].tint,
        headerShown: false,
        tabBarButton: HapticTab,
        tabBarBackground: TabBarBackground,
        tabBarStyle: Platform.select({
          ios: {
            // Use a transparent background on iOS to show the blur effect
            position: 'absolute',
          },
          default: {},
        }),
      }}>
      <Tabs.Screen
        name="index"
        options={{
          title: 'Login',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="person.fill" color={color} />,
        }}
      />
      <Tabs.Screen
        name="dashboard"
        options={{
          title: 'Dashboard',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="house.fill" color={color} />,
        }}
      />
      <Tabs.Screen
        name="explore"
        options={{
          title: 'Explore',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="paperplane.fill" color={color} />,
        }}
      />
      <Tabs.Screen
        name="dashboard-taller-comun"
        options={{
          title: 'Taller',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="wrench.fill" color={color} />,
        }}
      />
      <Tabs.Screen
        name="dashboard-taller-plus"
        options={{
          title: 'Taller Plus',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="crown.fill" color={color} />,
          href: null, // OCULTAR PLUS TEMPORALMENTE
        }}
      />
      <Tabs.Screen
        name="seleccionar-plan"
        options={{
          title: 'Planes',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="star.fill" color={color} />,
        }}
      />
      <Tabs.Screen
        name="registro-taller-comun"
        options={{
          title: 'Registro',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="person.badge.plus" color={color} />,
          href: null, // Ocultar de la navegación principal
        }}
      />
      <Tabs.Screen
        name="registro-taller-plus"
        options={{
          title: 'Registro Plus',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="person.badge.plus" color={color} />,
          href: null, // Ocultar de la navegación principal
        }}
      />
      <Tabs.Screen
        name="dashboard-mecanico"
        options={{
          title: 'Mecánico',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="wrench.fill" color={color} />,
        }}
      />
      <Tabs.Screen
        name="dashboard-mecanico-plus"
        options={{
          title: 'Mecánico Plus',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="crown.fill" color={color} />,
          href: null, // OCULTAR PLUS TEMPORALMENTE
        }}
      />
      <Tabs.Screen
        name="dashboard-proveedor"
        options={{
          title: 'Proveedor',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="house.fill" color={color} />,
        }}
      />
      <Tabs.Screen
        name="delivery-welcome"
        options={{
          title: 'Delivery',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="bicycle" color={color} />,
        }}
      />
      <Tabs.Screen
        name="delivery-registro"
        options={{
          title: 'Registro Delivery',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="motorcycle" color={color} />,
          href: null, // Ocultar de la navegación principal
        }}
      />
      <Tabs.Screen
        name="delivery-dashboard"
        options={{
          title: 'Dashboard Delivery',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="truck.box.fill" color={color} />,
          href: null, // Ocultar de la navegación principal
        }}
      />
    </Tabs>
  );
}
