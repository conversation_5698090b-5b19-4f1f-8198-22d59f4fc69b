import React, { useState, useEffect } from 'react';
import {
  SafeAreaView,
  ScrollView,
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';

interface CartItem {
  id: number;
  nombre: string;
  marca: string;
  modelo?: string;
  precio_unitario: number;
  cantidad: number;
  stock: number;
}

interface CartSummary {
  total_items: number;
  total_amount: number;
}

export default function CarritoScreen() {
  const router = useRouter();
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [cartSummary, setCartSummary] = useState<CartSummary>({ total_items: 0, total_amount: 0 });
  const [deliveryAddress, setDeliveryAddress] = useState('');
  const [loading, setLoading] = useState(true);
  const [creating, setCreating] = useState(false);

  useEffect(() => {
    loadCart();
  }, []);

  const loadCart = async () => {
    try {
      setLoading(true);
      // Simular carga del carrito
      // En producción, aquí iría la llamada a la API
      setTimeout(() => {
        setCartItems([
          {
            id: 1,
            nombre: 'Filtro de Aceite',
            marca: 'Bosch',
            modelo: 'Universal',
            precio_unitario: 2500,
            cantidad: 2,
            stock: 10
          },
          {
            id: 2,
            nombre: 'Pastillas de Freno',
            marca: 'Brembo',
            modelo: 'Delanteras',
            precio_unitario: 8500,
            cantidad: 1,
            stock: 5
          }
        ]);
        setCartSummary({ total_items: 3, total_amount: 13500 });
        setLoading(false);
      }, 1000);
    } catch (error) {
      console.error('Error loading cart:', error);
      setLoading(false);
    }
  };

  const updateQuantity = (itemId: number, newQuantity: number) => {
    if (newQuantity <= 0) return;
    
    setCartItems(items => 
      items.map(item => 
        item.id === itemId 
          ? { ...item, cantidad: Math.min(newQuantity, item.stock) }
          : item
      )
    );
    
    // Recalcular totales
    const newTotal = cartItems.reduce((sum, item) => 
      sum + (item.id === itemId ? newQuantity * item.precio_unitario : item.cantidad * item.precio_unitario), 0
    );
    const newCount = cartItems.reduce((sum, item) => 
      sum + (item.id === itemId ? newQuantity : item.cantidad), 0
    );
    
    setCartSummary({ total_items: newCount, total_amount: newTotal });
  };

  const removeItem = (itemId: number) => {
    Alert.alert(
      'Eliminar producto',
      '¿Estás seguro de que querés eliminar este producto?',
      [
        { text: 'Cancelar', style: 'cancel' },
        { 
          text: 'Eliminar', 
          style: 'destructive',
          onPress: () => {
            setCartItems(items => items.filter(item => item.id !== itemId));
            // Recalcular totales
            const remainingItems = cartItems.filter(item => item.id !== itemId);
            const newTotal = remainingItems.reduce((sum, item) => sum + (item.cantidad * item.precio_unitario), 0);
            const newCount = remainingItems.reduce((sum, item) => sum + item.cantidad, 0);
            setCartSummary({ total_items: newCount, total_amount: newTotal });
          }
        }
      ]
    );
  };

  const proceedToCheckout = () => {
    if (cartItems.length === 0) {
      Alert.alert('Carrito vacío', 'Tu changuito está vacío');
      return;
    }

    if (!deliveryAddress.trim()) {
      Alert.alert('Dirección requerida', 'Por favor ingresá tu dirección de entrega');
      return;
    }

    Alert.alert(
      '🚀 ¿Confirmar pedido?',
      `📦 ${cartItems.length} productos\n📍 Dirección: ${deliveryAddress}\n⏱️ Tiempo estimado: 25-35 min\n\n¡El delivery será asignado automáticamente!`,
      [
        { text: 'Cancelar', style: 'cancel' },
        { text: 'Confirmar', onPress: createOrder }
      ]
    );
  };

  const createOrder = async () => {
    setCreating(true);
    
    try {
      // Simular creación del pedido
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      Alert.alert(
        '✅ ¡PEDIDO CONFIRMADO!',
        `📦 Pedido #${Math.floor(Math.random() * 1000)}\n🚀 Asignación automática activada\n📱 Te notificaremos cuando el delivery esté en camino\n\n¡Gracias por elegir RepuMovil!`,
        [
          { text: 'Ver Mis Pedidos', onPress: () => router.push('/mis-pedidos') }
        ]
      );
      
      // Limpiar carrito
      setCartItems([]);
      setCartSummary({ total_items: 0, total_amount: 0 });
      setDeliveryAddress('');
      
    } catch (error) {
      Alert.alert('Error', 'No se pudo crear el pedido. Intentá de nuevo.');
    } finally {
      setCreating(false);
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#FF6B35" />
          <Text style={styles.loadingText}>Cargando tu changuito...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="white" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Mi Changuito 🛒</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content}>
        {/* Motivational Header */}
        <View style={styles.motivationalHeader}>
          <Text style={styles.motivationalText}>
            "CLICK, PEDÍ, RECIBÍ - REPARAR NUNCA FUE TAN FÁCIL"
          </Text>
        </View>

        {cartItems.length === 0 ? (
          <View style={styles.emptyCart}>
            <Ionicons name="cart-outline" size={80} color="#FF6B35" />
            <Text style={styles.emptyTitle}>Tu changuito está vacío</Text>
            <Text style={styles.emptySubtitle}>¡Agregá algunos repuestos para empezar!</Text>
            <TouchableOpacity style={styles.shopButton} onPress={() => router.push('/catalogo')}>
              <Ionicons name="search" size={20} color="white" />
              <Text style={styles.shopButtonText}>Explorar Catálogo</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <>
            {/* Cart Items */}
            <View style={styles.itemsSection}>
              <Text style={styles.sectionTitle}>
                <Ionicons name="list" size={20} color="#2c3e50" />
                {' '}Productos ({cartSummary.total_items})
              </Text>
              
              {cartItems.map(item => (
                <View key={item.id} style={styles.cartItem}>
                  <View style={styles.itemIcon}>
                    <Ionicons name="settings" size={24} color="#FF6B35" />
                  </View>
                  
                  <View style={styles.itemDetails}>
                    <Text style={styles.itemName}>{item.nombre}</Text>
                    <Text style={styles.itemBrand}>{item.marca} - {item.modelo || 'Universal'}</Text>
                    <Text style={styles.itemPrice}>${item.precio_unitario.toFixed(2)} c/u</Text>
                  </View>
                  
                  <View style={styles.itemActions}>
                    <View style={styles.quantityControls}>
                      <TouchableOpacity 
                        style={[styles.qtyButton, item.cantidad <= 1 && styles.qtyButtonDisabled]}
                        onPress={() => updateQuantity(item.id, item.cantidad - 1)}
                        disabled={item.cantidad <= 1}
                      >
                        <Ionicons name="remove" size={16} color="white" />
                      </TouchableOpacity>
                      
                      <Text style={styles.quantity}>{item.cantidad}</Text>
                      
                      <TouchableOpacity 
                        style={[styles.qtyButton, item.cantidad >= item.stock && styles.qtyButtonDisabled]}
                        onPress={() => updateQuantity(item.id, item.cantidad + 1)}
                        disabled={item.cantidad >= item.stock}
                      >
                        <Ionicons name="add" size={16} color="white" />
                      </TouchableOpacity>
                    </View>
                    
                    <TouchableOpacity 
                      style={styles.removeButton}
                      onPress={() => removeItem(item.id)}
                    >
                      <Ionicons name="trash" size={16} color="white" />
                    </TouchableOpacity>
                  </View>
                </View>
              ))}
            </View>

            {/* Checkout Section */}
            <View style={styles.checkoutSection}>
              <Text style={styles.sectionTitle}>
                <Ionicons name="calculator" size={20} color="white" />
                {' '}Resumen del Pedido
              </Text>
              
              {/* Steps */}
              <View style={styles.steps}>
                <View style={[styles.step, styles.stepActive]}>
                  <Ionicons name="cart" size={16} color="white" />
                  <Text style={styles.stepText}>Carrito</Text>
                </View>
                <View style={styles.step}>
                  <Ionicons name="location" size={16} color="rgba(255,255,255,0.7)" />
                  <Text style={[styles.stepText, styles.stepInactive]}>Dirección</Text>
                </View>
                <View style={styles.step}>
                  <Ionicons name="bicycle" size={16} color="rgba(255,255,255,0.7)" />
                  <Text style={[styles.stepText, styles.stepInactive]}>Entrega</Text>
                </View>
              </View>

              {/* Motivational Phrase */}
              <View style={styles.motivationalBox}>
                <Text style={styles.motivationalBoxText}>
                  💪 "¡Estás a un click de tener tus repuestos en la puerta!"
                </Text>
              </View>

              {/* Delivery Info */}
              <View style={styles.deliveryInfo}>
                <View style={styles.deliveryTime}>
                  <Ionicons name="time" size={20} color="#ffc107" />
                  <Text style={styles.deliveryTimeText}>Tiempo estimado: 25-35 min</Text>
                </View>
                <Text style={styles.deliverySubtext}>Delivery automático asignado al confirmar</Text>
              </View>

              {/* Address Input */}
              <View style={styles.addressSection}>
                <Text style={styles.addressLabel}>
                  <Ionicons name="location" size={16} color="white" />
                  {' '}Dirección de entrega:
                </Text>
                <TextInput
                  style={styles.addressInput}
                  placeholder="Ej: Av. Libertador 1234, San Juan Centro"
                  placeholderTextColor="#999"
                  value={deliveryAddress}
                  onChangeText={setDeliveryAddress}
                />
              </View>

              {/* Summary */}
              <View style={styles.summary}>
                <View style={styles.summaryRow}>
                  <Text style={styles.summaryLabel}>Productos ({cartSummary.total_items})</Text>
                  <Text style={styles.summaryValue}>${cartSummary.total_amount.toFixed(2)}</Text>
                </View>
                <View style={styles.summaryRow}>
                  <Text style={styles.summaryLabel}>Envío</Text>
                  <Text style={[styles.summaryValue, { color: '#28a745' }]}>GRATIS</Text>
                </View>
                <View style={[styles.summaryRow, styles.summaryTotal]}>
                  <Text style={styles.summaryTotalLabel}>Total</Text>
                  <Text style={styles.summaryTotalValue}>${cartSummary.total_amount.toFixed(2)}</Text>
                </View>
              </View>

              {/* Checkout Button */}
              <TouchableOpacity 
                style={[styles.checkoutButton, creating && styles.checkoutButtonDisabled]}
                onPress={proceedToCheckout}
                disabled={creating}
              >
                {creating ? (
                  <ActivityIndicator size="small" color="white" />
                ) : (
                  <Ionicons name="rocket" size={20} color="white" />
                )}
                <Text style={styles.checkoutButtonText}>
                  {creating ? 'Creando pedido...' : '¡CONFIRMAR PEDIDO!'}
                </Text>
              </TouchableOpacity>
            </View>
          </>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f7fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#FF6B35',
    paddingHorizontal: 20,
    paddingVertical: 15,
    paddingTop: 50,
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
  },
  placeholder: {
    width: 34,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  motivationalHeader: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    marginBottom: 20,
    alignItems: 'center',
  },
  motivationalText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    textAlign: 'center',
  },
  emptyCart: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 40,
    alignItems: 'center',
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginTop: 20,
    marginBottom: 10,
  },
  emptySubtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 30,
  },
  shopButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FF6B35',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  shopButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  itemsSection: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 15,
  },
  cartItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  itemIcon: {
    width: 50,
    height: 50,
    backgroundColor: '#f8f9fa',
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  itemDetails: {
    flex: 1,
  },
  itemName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 4,
  },
  itemBrand: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  itemPrice: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#FF6B35',
  },
  itemActions: {
    alignItems: 'center',
    gap: 10,
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  qtyButton: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#FF6B35',
    justifyContent: 'center',
    alignItems: 'center',
  },
  qtyButtonDisabled: {
    backgroundColor: '#ccc',
  },
  quantity: {
    fontSize: 16,
    fontWeight: 'bold',
    minWidth: 20,
    textAlign: 'center',
  },
  removeButton: {
    backgroundColor: '#dc3545',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 5,
  },
  checkoutSection: {
    backgroundColor: '#28a745',
    borderRadius: 15,
    padding: 20,
  },
  steps: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15,
  },
  step: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 5,
  },
  stepActive: {
    opacity: 1,
  },
  stepText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  stepInactive: {
    color: 'rgba(255,255,255,0.7)',
  },
  motivationalBox: {
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
    borderLeftWidth: 4,
    borderLeftColor: '#ffc107',
  },
  motivationalBoxText: {
    color: 'white',
    fontStyle: 'italic',
    textAlign: 'center',
  },
  deliveryInfo: {
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
  },
  deliveryTime: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    marginBottom: 5,
  },
  deliveryTimeText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  deliverySubtext: {
    color: 'rgba(255,255,255,0.8)',
    fontSize: 12,
  },
  addressSection: {
    marginBottom: 15,
  },
  addressLabel: {
    color: 'white',
    fontSize: 14,
    marginBottom: 8,
  },
  addressInput: {
    backgroundColor: 'white',
    borderRadius: 5,
    padding: 12,
    fontSize: 16,
  },
  summary: {
    marginBottom: 20,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  summaryLabel: {
    color: 'white',
    fontSize: 14,
  },
  summaryValue: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  summaryTotal: {
    borderTopWidth: 1,
    borderTopColor: 'rgba(255,255,255,0.3)',
    paddingTop: 8,
    marginTop: 8,
  },
  summaryTotalLabel: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  summaryTotalValue: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  checkoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FF6B35',
    paddingVertical: 15,
    borderRadius: 10,
    gap: 10,
  },
  checkoutButtonDisabled: {
    backgroundColor: '#ccc',
  },
  checkoutButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
