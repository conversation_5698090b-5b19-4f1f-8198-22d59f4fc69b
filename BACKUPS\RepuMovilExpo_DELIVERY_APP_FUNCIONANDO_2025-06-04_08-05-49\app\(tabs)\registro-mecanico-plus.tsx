import React, { useState } from 'react';
import {
  SafeAreaView,
  StatusBar,
  StyleSheet,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  Dimensions,
} from 'react-native';
import { useRouter } from 'expo-router';

const { width } = Dimensions.get('window');

export default function RegistroMecanicoPlus() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    nombre: '',
    email: '',
    telefono: '',
    password: '',
    especialidades: '',
    experiencia_anos: '',
    descripcion: '',
    zona_trabajo: '',
    precio_hora: '',
    disponibilidad: '',
  });

  const [serviciosAdicionales, setServiciosAdicionales] = useState({
    grua: false,
    domicilio: false,
    atencion24hs: false,
    diagnostico_avanzado: false,
  });

  const [loading, setLoading] = useState(false);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const toggleServicio = (servicio: string) => {
    setServiciosAdicionales(prev => ({
      ...prev,
      [servicio]: !prev[servicio]
    }));
  };

  const validarFormulario = () => {
    if (!formData.nombre || !formData.email || !formData.password || !formData.especialidades) {
      Alert.alert('⚠️ Campos requeridos', 'Completa todos los campos obligatorios');
      return false;
    }

    if (formData.password.length < 6) {
      Alert.alert('⚠️ Contraseña', 'La contraseña debe tener al menos 6 caracteres');
      return false;
    }

    if (!formData.email.includes('@')) {
      Alert.alert('⚠️ Email', 'Ingresa un email válido');
      return false;
    }

    return true;
  };

  const registrarMecanicoPlus = async () => {
    if (!validarFormulario()) return;

    setLoading(true);

    try {
      // Preparar servicios adicionales seleccionados
      const serviciosSeleccionados = Object.entries(serviciosAdicionales)
        .filter(([_, selected]) => selected)
        .map(([servicio, _]) => servicio);

      // Preparar datos para enviar a la API
      const datosRegistro = {
        nombre: formData.nombre,
        email: formData.email,
        telefono: formData.telefono,
        password: formData.password,
        especialidades: formData.especialidades,
        experiencia_anos: parseInt(formData.experiencia_anos) || 0,
        descripcion: formData.descripcion,
        zona_trabajo: formData.zona_trabajo,
        precio_hora: parseFloat(formData.precio_hora) || null,
        disponibilidad: formData.disponibilidad,
        servicios_adicionales: serviciosSeleccionados,
        user_type: 'mecanico_independiente',
        plan_type: 'plus'
      };

      console.log('📤 Enviando datos mecánico Plus a la API:', datosRegistro);

      // Llamar a la API PHP
      const response = await fetch('http://localhost/mechanical-workshop/public/api/registro-dinamico.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify(datosRegistro),
      });

      console.log('📥 Respuesta del servidor mecánico Plus:', response.status);

      const resultado = await response.json();
      console.log('📋 Datos de respuesta mecánico Plus:', resultado);

      if (resultado.success) {
        const serviciosTexto = serviciosSeleccionados.length > 0
          ? `\n\n🔧 Servicios adicionales:\n${serviciosSeleccionados.join(', ')}`
          : '';

        Alert.alert(
          '🎉 ¡Cuenta RepuMovil Plus Creada!',
          `¡Bienvenido al plan Premium!\n\nTu perfil de mecánico independiente "${formData.nombre}" ha sido registrado exitosamente en la base de datos.\n\n✨ Todas las funciones premium:\n• Gestión avanzada de clientes\n• Calendario inteligente\n• Reportes empresariales\n• Herramientas premium\n• WhatsApp integrado${serviciosTexto}\n\n🆔 ID de usuario: ${resultado.user_id}`,
          [
            {
              text: 'Ir al Dashboard Plus',
              onPress: () => router.push('/dashboard-mecanico-plus')
            }
          ]
        );
      } else {
        Alert.alert('❌ Error de Registro Plus', resultado.message || 'Error desconocido');
      }
    } catch (error) {
      console.error('💥 Error al registrar mecánico Plus:', error);
      Alert.alert(
        '❌ Error de Conexión',
        'No se pudo conectar con el servidor. Verifica:\n\n• Que XAMPP esté ejecutándose\n• Que la API esté disponible\n• Tu conexión a internet\n\nError: ' + error.message
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#2C3E50" />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Header Premium */}
        <View style={styles.header}>
          <View style={styles.headerContent}>
            <Text style={styles.logoText}>
              🔧 <Text style={styles.logoRepu}>Repu</Text><Text style={styles.logoMovil}>Movil</Text>
              <Text style={styles.logoPlus}> Plus</Text>
            </Text>
            <Text style={styles.crownIcon}>👑</Text>
          </View>
          <Text style={styles.subtitle}>TU TALLER VA DONDE VOS VAS, NEEEÑO</Text>
        </View>

        {/* Características Premium */}
        <View style={styles.featureHighlight}>
          <Text style={styles.featureTitle}>✨ Tu plan Premium incluye:</Text>
          <View style={styles.featureGrid}>
            <Text style={styles.featureItem}>⭐ Todo lo de RepuMovil</Text>
            <Text style={styles.featureItem}>⭐ Gestión avanzada clientes</Text>
            <Text style={styles.featureItem}>⭐ Calendario inteligente</Text>
            <Text style={styles.featureItem}>⭐ Reportes empresariales</Text>
            <Text style={styles.featureItem}>⭐ Herramientas premium</Text>
            <Text style={styles.featureItem}>⭐ Dashboard completo</Text>
            <Text style={styles.featureItem}>⭐ WhatsApp integrado</Text>
            <Text style={styles.featureItem}>⭐ Análisis de rendimiento</Text>
          </View>
        </View>

        {/* Formulario */}
        <View style={styles.formContainer}>
          <Text style={styles.sectionTitle}>📝 Datos Básicos</Text>

          <TextInput
            style={styles.input}
            placeholder="Nombre Completo *"
            value={formData.nombre}
            onChangeText={(text) => handleInputChange('nombre', text)}
            placeholderTextColor="#999"
          />

          <TextInput
            style={styles.input}
            placeholder="Email *"
            value={formData.email}
            onChangeText={(text) => handleInputChange('email', text)}
            keyboardType="email-address"
            autoCapitalize="none"
            placeholderTextColor="#999"
          />

          <TextInput
            style={styles.input}
            placeholder="Teléfono *"
            value={formData.telefono}
            onChangeText={(text) => handleInputChange('telefono', text)}
            keyboardType="phone-pad"
            placeholderTextColor="#999"
          />

          <TextInput
            style={styles.input}
            placeholder="Contraseña *"
            value={formData.password}
            onChangeText={(text) => handleInputChange('password', text)}
            secureTextEntry
            placeholderTextColor="#999"
          />

          <Text style={styles.sectionTitle}>🏆 Datos Profesionales Premium</Text>

          <TextInput
            style={styles.input}
            placeholder="Especialidades *"
            value={formData.especialidades}
            onChangeText={(text) => handleInputChange('especialidades', text)}
            placeholderTextColor="#999"
          />

          <TextInput
            style={styles.input}
            placeholder="Años de Experiencia"
            value={formData.experiencia_anos}
            onChangeText={(text) => handleInputChange('experiencia_anos', text)}
            keyboardType="numeric"
            placeholderTextColor="#999"
          />

          <TextInput
            style={styles.input}
            placeholder="Descripción de Servicios"
            value={formData.descripcion}
            onChangeText={(text) => handleInputChange('descripcion', text)}
            multiline
            numberOfLines={3}
            placeholderTextColor="#999"
          />

          <TextInput
            style={styles.input}
            placeholder="Zona de Trabajo"
            value={formData.zona_trabajo}
            onChangeText={(text) => handleInputChange('zona_trabajo', text)}
            placeholderTextColor="#999"
          />

          <TextInput
            style={styles.input}
            placeholder="Precio por Hora"
            value={formData.precio_hora}
            onChangeText={(text) => handleInputChange('precio_hora', text)}
            keyboardType="numeric"
            placeholderTextColor="#999"
          />

          <TextInput
            style={styles.input}
            placeholder="Disponibilidad (ej: Lun-Vie 8-18hs)"
            value={formData.disponibilidad}
            onChangeText={(text) => handleInputChange('disponibilidad', text)}
            placeholderTextColor="#999"
          />

          {/* Servicios Adicionales */}
          <Text style={styles.sectionTitle}>🔧 Servicios Adicionales Premium</Text>
          <View style={styles.serviciosContainer}>
            <TouchableOpacity
              style={styles.servicioItem}
              onPress={() => toggleServicio('grua')}
            >
              <Text style={styles.servicioIcon}>
                {serviciosAdicionales.grua ? '✅' : '⬜'}
              </Text>
              <Text style={styles.servicioText}>🚛 Servicio de Grúa</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.servicioItem}
              onPress={() => toggleServicio('domicilio')}
            >
              <Text style={styles.servicioIcon}>
                {serviciosAdicionales.domicilio ? '✅' : '⬜'}
              </Text>
              <Text style={styles.servicioText}>🏠 Servicio a Domicilio</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.servicioItem}
              onPress={() => toggleServicio('atencion24hs')}
            >
              <Text style={styles.servicioIcon}>
                {serviciosAdicionales.atencion24hs ? '✅' : '⬜'}
              </Text>
              <Text style={styles.servicioText}>🕐 Atención 24hs</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.servicioItem}
              onPress={() => toggleServicio('diagnostico_avanzado')}
            >
              <Text style={styles.servicioIcon}>
                {serviciosAdicionales.diagnostico_avanzado ? '✅' : '⬜'}
              </Text>
              <Text style={styles.servicioText}>🔍 Diagnóstico Avanzado</Text>
            </TouchableOpacity>
          </View>

          <TouchableOpacity
            style={[styles.btnRegistrar, loading && styles.btnDisabled]}
            onPress={registrarMecanicoPlus}
            disabled={loading}
          >
            <Text style={styles.btnRegistrarText}>
              {loading ? '🔄 Creando cuenta Premium...' : '👑 Crear mi Cuenta RepuMovil Plus'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.btnVolver}
            onPress={() => router.push('/seleccionar-plan')}
          >
            <Text style={styles.btnVolverText}>← Volver a Planes</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#2C3E50',
  },
  content: {
    flex: 1,
  },
  header: {
    alignItems: 'center',
    paddingVertical: 30,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  logoText: {
    fontSize: 32,
    fontWeight: '900',
    color: 'white',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 2, height: 2 },
    textShadowRadius: 4,
  },
  logoRepu: {
    color: '#3498DB',
  },
  logoMovil: {
    color: '#E8F4FD',
  },
  logoPlus: {
    color: '#FFD700',
  },
  crownIcon: {
    fontSize: 30,
    marginLeft: 10,
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    lineHeight: 22,
    fontWeight: '700',
  },
  featureHighlight: {
    backgroundColor: 'rgba(255, 251, 240, 0.95)',
    marginHorizontal: 20,
    borderRadius: 15,
    padding: 20,
    marginBottom: 20,
    borderWidth: 2,
    borderColor: '#FFD700',
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: '#333',
    marginBottom: 15,
  },
  featureGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  featureItem: {
    fontSize: 12,
    color: '#3498DB',
    fontWeight: '600',
    width: '48%',
    marginBottom: 5,
  },
  formContainer: {
    backgroundColor: 'white',
    marginHorizontal: 20,
    borderRadius: 25,
    padding: 25,
    marginBottom: 30,
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 5 },
    shadowOpacity: 0.3,
    shadowRadius: 10,
    borderWidth: 3,
    borderColor: '#FFD700',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#333',
    marginBottom: 15,
    marginTop: 10,
  },
  input: {
    borderWidth: 2,
    borderColor: '#FFD700',
    borderRadius: 12,
    padding: 15,
    fontSize: 16,
    marginBottom: 15,
    backgroundColor: '#fff',
  },
  serviciosContainer: {
    marginBottom: 20,
  },
  servicioItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 15,
    backgroundColor: '#f8f9fa',
    borderRadius: 10,
    marginBottom: 10,
  },
  servicioIcon: {
    fontSize: 20,
    marginRight: 12,
  },
  servicioText: {
    fontSize: 16,
    color: '#333',
    fontWeight: '600',
  },
  btnRegistrar: {
    backgroundColor: '#FFD700',
    borderRadius: 12,
    padding: 18,
    alignItems: 'center',
    marginTop: 20,
    elevation: 3,
    shadowColor: '#FFD700',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  btnDisabled: {
    backgroundColor: '#ccc',
    elevation: 0,
    shadowOpacity: 0,
  },
  btnRegistrarText: {
    color: '#333',
    fontSize: 16,
    fontWeight: '900',
  },
  btnVolver: {
    alignItems: 'center',
    marginTop: 15,
    padding: 10,
  },
  btnVolverText: {
    color: '#666',
    fontSize: 16,
    fontWeight: '600',
  },
});
