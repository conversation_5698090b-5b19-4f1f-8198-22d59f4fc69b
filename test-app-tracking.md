# 📱 TESTING DE TRACKING GPS EN LA APP

## 🎯 **INSTRUCCIONES PARA PROBAR**

### **PASO 1: INICIAR LA APP**
```bash
cd RepuMovilExpo
npx expo start
```

### **PASO 2: VERIFICAR CONFIGURACIÓN**
- ✅ **Permisos de ubicación:** Configurados en app.json
- ✅ **Dependencias:** expo-location instalada
- ✅ **API URL:** Corregida a tracking.php
- ✅ **LocationService:** Integrado correctamente

### **PASO 3: QUÉ VERIFICAR EN EL DASHBOARD**

#### **🔍 INDICADORES VISUALES:**
1. **Notificaciones Push:**
   - 🔔 "Push Activo" (verde) = Funcionando
   - ⏳ "Configurando..." (amarillo) = Inicializando
   - 🔕 "Push Inactivo" (rojo) = Error

2. **Tracking GPS:**
   - 📍 "GPS Activo" (verde) = Tracking funcionando
   - ❌ "GPS Error" (rojo) = Error de permisos/ubicación
   - 📍 "GPS Inactivo" (gris) = No iniciado

#### **🎯 FUNCIONALIDADES A PROBAR:**

1. **Inicio Automático:**
   - Cambiar estado a "Disponible"
   - Debe iniciar tracking GPS automáticamente
   - Verificar que aparezca "GPS Activo"

2. **Precisión GPS:**
   - Debe mostrar "±Xm" de precisión
   - Valores típicos: 3-10 metros

3. **Envío al Servidor:**
   - Verificar logs en consola de Expo
   - Debe mostrar: "📤 Ubicación enviada al servidor correctamente"

4. **Control Manual:**
   - Tocar el botón GPS para iniciar/detener
   - Debe cambiar entre "GPS Activo" e "GPS Inactivo"

### **PASO 4: LOGS A VERIFICAR**

#### **🔍 EN CONSOLA DE EXPO:**
```
📍 Inicializando servicio de ubicación...
✅ Servicio de ubicación inicializado
🚀 Iniciando tracking GPS...
✅ Tracking GPS iniciado correctamente
📍 Nueva ubicación: {lat: -31.xxxx, lng: -68.xxxx}
📤 Ubicación enviada al servidor correctamente
```

#### **❌ ERRORES COMUNES:**
```
❌ Error inicializando ubicación: Permisos denegados
❌ Error enviando ubicación al servidor: Network error
❌ No se pudo iniciar el tracking GPS
```

### **PASO 5: VERIFICAR EN EL BACKEND**

#### **🌐 PANEL DE TRACKING:**
- URL: `http://localhost/mechanical-workshop/public/test-tracking.php`
- Debe mostrar el delivery activo con ubicación
- Verificar que se actualice en tiempo real

#### **📊 API DIRECTA:**
```powershell
# Ver deliveries activos
Invoke-WebRequest -Uri "http://localhost/mechanical-workshop/public/api/tracking.php?action=get_active_deliveries"

# Ver ubicación específica
Invoke-WebRequest -Uri "http://localhost/mechanical-workshop/public/api/tracking.php?action=get_delivery_location&delivery_id=1"
```

### **PASO 6: TESTING EN DISPOSITIVO FÍSICO**

#### **⚠️ IMPORTANTE:**
- **Simulador iOS/Android:** GPS limitado, puede no funcionar
- **Dispositivo físico:** Necesario para testing real
- **Ubicación exterior:** Mejor precisión GPS

#### **📱 COMANDOS PARA DISPOSITIVO:**
```bash
# Para Android
npx expo start --android

# Para iOS
npx expo start --ios

# Para dispositivo físico
npx expo start --tunnel
```

### **PASO 7: TROUBLESHOOTING**

#### **🔧 PROBLEMA: "GPS Error"**
**Soluciones:**
1. Verificar permisos de ubicación en configuración del dispositivo
2. Usar dispositivo físico en lugar de simulador
3. Estar en exterior para mejor señal GPS
4. Reiniciar la app y aceptar permisos

#### **🔧 PROBLEMA: "Error enviando ubicación al servidor"**
**Soluciones:**
1. Verificar que XAMPP esté ejecutándose
2. Verificar URL de la API (debe ser tracking.php)
3. Verificar conexión a internet
4. Revisar logs del servidor Apache

#### **🔧 PROBLEMA: "No se inicia automáticamente"**
**Soluciones:**
1. Cambiar estado del delivery a "Disponible"
2. Verificar que no haya errores en consola
3. Reiniciar la app
4. Verificar permisos de ubicación

### **PASO 8: MÉTRICAS DE ÉXITO**

#### **✅ CRITERIOS DE APROBACIÓN:**
- [ ] Indicador GPS muestra "GPS Activo"
- [ ] Precisión mostrada (±Xm)
- [ ] Logs de ubicación en consola
- [ ] Ubicación visible en panel backend
- [ ] Inicio/detención manual funciona
- [ ] Inicio automático al cambiar estado

#### **📊 RENDIMIENTO ESPERADO:**
- **Tiempo de inicio:** < 10 segundos
- **Precisión GPS:** 3-10 metros
- **Frecuencia de envío:** Cada 3 segundos
- **Respuesta del servidor:** < 1 segundo

---

## 🎉 **RESULTADO ESPERADO**

### **✅ SI TODO FUNCIONA:**
```
📱 App iniciada
📍 GPS Activo ±5m
🔔 Push Activo
🚀 Tracking en tiempo real
📊 Datos en backend
```

### **🚀 PRÓXIMO PASO:**
Una vez verificado que el tracking GPS funciona correctamente, proceder con:
**PASO 3: Sistema de aceptación/rechazo de pedidos**

---

## 💪 **¡TESTING COMPLETO!**

**¡HERMANO, CON ESTAS PRUEBAS VAS A VERIFICAR QUE EL TRACKING GPS ESTÉ FUNCIONANDO AL 100%!** 🔥

**Es como hacer la revisión técnica del auto antes de la carrera - hay que asegurar que todo esté perfecto!** ⚽🏆🎯
