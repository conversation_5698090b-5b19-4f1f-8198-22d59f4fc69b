<?php
session_start();
require_once 'config.php';

// Verificar si está logueado
if (!isset($_SESSION['logged_in']) || !$_SESSION['logged_in']) {
    header('Location: login.php');
    exit();
}

try {
    $pdo = connectDB();
    $user_id = $_SESSION['user_id'];
    
    // Obtener datos del usuario
    $stmt = $pdo->prepare("
        SELECT u.*, dp.nombre_completo, dp.telefono, v.tipo_vehiculo 
        FROM usuarios u
        LEFT JOIN datos_personales dp ON u.id = dp.usuario_id
        LEFT JOIN vehiculos v ON u.id = v.usuario_id
        WHERE u.id = ?
    ");
    $stmt->execute([$user_id]);
    $usuario = $stmt->fetch();
    
    // Obtener pedidos asignados al repartidor
    $stmt = $pdo->prepare("
        SELECT * FROM pedidos 
        WHERE repartidor_id = ? AND estado IN ('asignado', 'en_camino')
        ORDER BY fecha_pedido DESC
    ");
    $stmt->execute([$user_id]);
    $pedidos_activos = $stmt->fetchAll();
    
} catch (Exception $e) {
    $error_message = "Error al cargar datos: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mapa de Entregas - RepuMovil Delivery</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-orange: #FF6B35;
            --secondary-orange: #F7931E;
            --primary-red: #E53E3E;
            --secondary-red: #FC8181;
            --gradient-main: linear-gradient(135deg, #FF6B35 0%, #E53E3E 50%, #F7931E 100%);
            --white: #ffffff;
            --dark: #2D3748;
            --success: #48BB78;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .header {
            background: var(--gradient-main);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 1rem;
            font-size: 1.5rem;
            font-weight: 800;
        }

        .back-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            color: white;
            text-decoration: none;
        }

        .main-container {
            display: flex;
            height: calc(100vh - 80px);
            gap: 20px;
            padding: 20px;
        }

        .map-section {
            flex: 2;
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            position: relative;
        }

        #deliveryMap {
            width: 100%;
            height: 100%;
        }

        .map-overlay {
            position: absolute;
            top: 20px;
            left: 20px;
            right: 20px;
            z-index: 1000;
        }

        .status-bar {
            background: rgba(255, 255, 255, 0.95);
            padding: 15px;
            border-radius: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .online-toggle {
            display: flex;
            align-items: center;
            gap: 10px;
            background: var(--success);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            border: none;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .online-toggle.offline {
            background: #757575;
        }

        .gps-info {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 0.9rem;
            color: #666;
        }

        .map-controls {
            position: absolute;
            bottom: 20px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 1000;
        }

        .control-btn {
            width: 50px;
            height: 50px;
            background: white;
            border: none;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
            color: var(--primary-orange);
        }

        .control-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        .control-btn.active {
            background: var(--primary-orange);
            color: white;
        }

        .sidebar {
            flex: 1;
            background: white;
            border-radius: 20px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow-y: auto;
            max-height: calc(100vh - 120px);
        }

        .sidebar-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--dark);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .pedido-activo {
            background: linear-gradient(135deg, #E8F5E8, #F0FFF0);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 5px solid var(--success);
        }

        .pedido-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .pedido-id {
            font-size: 1.1rem;
            font-weight: 700;
            color: var(--dark);
        }

        .estado-badge {
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .estado-asignado {
            background: #E3F2FD;
            color: #1976D2;
        }

        .estado-en_camino {
            background: #FFF3E0;
            color: #F57C00;
        }

        .cliente-info {
            margin-bottom: 15px;
        }

        .cliente-nombre {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--dark);
            margin-bottom: 5px;
        }

        .cliente-direccion {
            color: #666;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .pedido-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 10px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 10px;
        }

        .stat-value {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--primary-orange);
        }

        .stat-label {
            font-size: 0.8rem;
            color: #666;
            text-transform: uppercase;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .action-btn {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
        }

        .btn-call {
            background: #E8F5E8;
            color: var(--success);
        }

        .btn-message {
            background: #FFF3E0;
            color: var(--primary-orange);
        }

        .btn-primary {
            background: var(--gradient-main);
            color: white;
            grid-column: 1 / -1;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 107, 53, 0.3);
        }

        .no-pedidos {
            text-align: center;
            padding: 40px 20px;
            color: #666;
        }

        .no-pedidos i {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.5;
        }

        @media (max-width: 768px) {
            .main-container {
                flex-direction: column;
                height: auto;
            }
            
            .map-section {
                height: 400px;
            }
            
            .sidebar {
                max-height: none;
            }

            .map-overlay {
                position: relative;
                top: 0;
                left: 0;
                right: 0;
                margin: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-motorcycle"></i>
                <span>RepuMovil <strong>Delivery</strong></span>
            </div>
            <a href="dashboard.php" class="back-btn">
                <i class="fas fa-arrow-left"></i>
                Volver al Dashboard
            </a>
        </div>
    </header>

    <div class="main-container">
        <!-- Sección del Mapa -->
        <div class="map-section">
            <div id="deliveryMap"></div>
            
            <!-- Overlay del mapa -->
            <div class="map-overlay">
                <div class="status-bar">
                    <button class="online-toggle" id="onlineToggle">
                        <i class="fas fa-circle"></i>
                        <span id="statusText">EN LÍNEA</span>
                    </button>
                    <div class="gps-info">
                        <i class="fas fa-satellite-dish"></i>
                        <span id="gpsAccuracy">GPS: Buscando...</span>
                    </div>
                </div>
            </div>

            <!-- Controles del mapa -->
            <div class="map-controls">
                <button class="control-btn" id="centerBtn" title="Mi ubicación">
                    <i class="fas fa-crosshairs"></i>
                </button>
                <button class="control-btn" id="fitBtn" title="Ajustar vista">
                    <i class="fas fa-expand-arrows-alt"></i>
                </button>
                <button class="control-btn" id="refreshBtn" title="Actualizar">
                    <i class="fas fa-sync-alt"></i>
                </button>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="sidebar">
            <h3 class="sidebar-title">
                <i class="fas fa-route"></i>
                Mi Entrega Activa
            </h3>

            <?php if (empty($pedidos_activos)): ?>
                <div class="no-pedidos">
                    <i class="fas fa-motorcycle"></i>
                    <h4>No hay entregas asignadas</h4>
                    <p>Cuando tengas un pedido asignado aparecerá aquí con la ruta en el mapa</p>
                </div>
            <?php else: ?>
                <?php $pedido = $pedidos_activos[0]; // Tomar el primer pedido activo ?>
                <div class="pedido-activo">
                    <div class="pedido-header">
                        <span class="pedido-id">Pedido #<?php echo $pedido['id']; ?></span>
                        <span class="estado-badge estado-<?php echo $pedido['estado']; ?>">
                            <?php echo ucfirst(str_replace('_', ' ', $pedido['estado'])); ?>
                        </span>
                    </div>

                    <div class="cliente-info">
                        <div class="cliente-nombre">Cliente Ejemplo</div>
                        <div class="cliente-direccion">
                            Av. Libertador 1234, San Juan<br>
                            <i class="fas fa-phone"></i> +54 9 ************
                        </div>
                    </div>

                    <div class="pedido-stats">
                        <div class="stat-item">
                            <div class="stat-value" id="routeDistance">-</div>
                            <div class="stat-label">Distancia</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="routeTime">-</div>
                            <div class="stat-label">Tiempo Est.</div>
                        </div>
                    </div>

                    <div class="action-buttons">
                        <button class="action-btn btn-call" onclick="llamarCliente()">
                            <i class="fas fa-phone"></i>
                            Llamar
                        </button>
                        <button class="action-btn btn-message" onclick="enviarMensaje()">
                            <i class="fas fa-comment"></i>
                            Mensaje
                        </button>
                    </div>

                    <?php if ($pedido['estado'] === 'asignado'): ?>
                        <button class="action-btn btn-primary" onclick="marcarEnCamino()">
                            <i class="fas fa-route"></i>
                            Iniciar Entrega
                        </button>
                    <?php else: ?>
                        <button class="action-btn btn-primary" onclick="completarEntrega()">
                            <i class="fas fa-check-circle"></i>
                            Completar Entrega
                        </button>
                    <?php endif; ?>
                </div>
            <?php endif; ?>

            <!-- Información adicional -->
            <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 10px;">
                <h4 style="margin-bottom: 10px; color: var(--dark);">
                    <i class="fas fa-info-circle"></i> Información
                </h4>
                <p style="font-size: 0.9rem; color: #666; line-height: 1.4;">
                    El mapa muestra tu ubicación actual y la ruta hacia el cliente. 
                    Mantén la app abierta para tracking en tiempo real.
                </p>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../js/GoogleMapsService.js"></script>
    <script>
        let mapService;
        let isOnline = false;
        let currentPedido = <?php echo !empty($pedidos_activos) ? json_encode($pedidos_activos[0]) : 'null'; ?>;

        // Coordenadas de ejemplo (en producción vendrían de la base de datos)
        const locations = {
            cliente: { lat: -31.5375, lng: -68.5364 },
            proveedor: { lat: -31.5365, lng: -68.5374 }
        };

        // Inicializar mapa
        function initMap() {
            mapService = new GoogleMapsService();
            mapService.initMap('deliveryMap', {
                zoom: 15,
                center: { lat: -31.5375, lng: -68.5364 }
            }).then(() => {
                console.log('🗺️ Mapa de delivery inicializado');
                if (currentPedido) {
                    loadPedidoRoute();
                }
                getCurrentLocation();
            });
        }

        // Obtener ubicación actual
        async function getCurrentLocation() {
            try {
                const location = await mapService.getCurrentLocation();
                document.getElementById('gpsAccuracy').textContent = 
                    `GPS: ${Math.round(location.accuracy)}m`;
                
                if (currentPedido) {
                    updateRouteInfo();
                }
            } catch (error) {
                console.error('Error obteniendo ubicación:', error);
                document.getElementById('gpsAccuracy').textContent = 'GPS: Error';
            }
        }

        // Cargar ruta del pedido
        function loadPedidoRoute() {
            mapService.clearMarkers();
            
            // Marker del cliente
            mapService.createCustomMarker(
                locations.cliente,
                'cliente',
                'Cliente - Destino de entrega'
            );

            // Obtener ubicación actual y calcular ruta
            mapService.getCurrentLocation().then(location => {
                // Marker del repartidor (ubicación actual)
                mapService.createCustomMarker(
                    { lat: location.lat, lng: location.lng },
                    'repartidor',
                    'Tu ubicación'
                );

                // Calcular ruta
                return mapService.calculateRoute(
                    { lat: location.lat, lng: location.lng },
                    locations.cliente
                );
            }).then(routeInfo => {
                updateRouteInfo(routeInfo);
                mapService.fitBounds();
            }).catch(error => {
                console.error('Error cargando ruta:', error);
            });
        }

        // Actualizar información de ruta
        function updateRouteInfo(routeInfo = null) {
            if (routeInfo) {
                document.getElementById('routeDistance').textContent = routeInfo.distance;
                document.getElementById('routeTime').textContent = routeInfo.duration;
            }
        }

        // Toggle estado online/offline
        document.getElementById('onlineToggle').addEventListener('click', function() {
            isOnline = !isOnline;
            const btn = this;
            const statusText = document.getElementById('statusText');
            
            if (isOnline) {
                btn.classList.remove('offline');
                statusText.textContent = 'EN LÍNEA';
                mapService.startLocationTracking(getCurrentLocation);
            } else {
                btn.classList.add('offline');
                statusText.textContent = 'DESCONECTADO';
                mapService.stopLocationTracking();
            }
        });

        // Controles del mapa
        document.getElementById('centerBtn').addEventListener('click', getCurrentLocation);
        document.getElementById('fitBtn').addEventListener('click', () => mapService.fitBounds());
        document.getElementById('refreshBtn').addEventListener('click', () => {
            if (currentPedido) loadPedidoRoute();
        });

        // Funciones de acción
        function llamarCliente() {
            alert('📞 Función de llamada próximamente disponible');
        }

        function enviarMensaje() {
            alert('💬 Función de mensajes próximamente disponible');
        }

        function marcarEnCamino() {
            if (confirm('¿Confirmas que estás en camino hacia el cliente?')) {
                alert('✅ Marcado como "En Camino". El cliente ha sido notificado.');
                // Aquí harías la actualización en la base de datos
            }
        }

        function completarEntrega() {
            if (confirm('¿Has completado la entrega exitosamente?')) {
                alert('🎉 ¡Entrega completada! Excelente trabajo.');
                // Aquí harías la actualización en la base de datos
            }
        }
    </script>

    <!-- Google Maps API REAL para Delivery -->
    <script async defer
        src="https://maps.googleapis.com/maps/api/js?key=AIzaSyDeF7xLqA58aAm5Gou7Nux3A7_jwGnnVxg&callback=initMap&libraries=geometry">
    </script>

    <script>
        function initMap() {
            console.log('🏍️ Inicializando Google Maps REAL para delivery...');

            // Configuración inicial para San Juan, Argentina
            const sanJuanCenter = { lat: -31.5375, lng: -68.5364 };

            // Crear mapa real de Google
            const map = new google.maps.Map(document.getElementById('deliveryMap'), {
                zoom: 15,
                center: sanJuanCenter,
                mapTypeId: google.maps.MapTypeId.ROADMAP,
                styles: [
                    {
                        featureType: "poi",
                        elementType: "labels",
                        stylers: [{ visibility: "off" }]
                    }
                ]
            });

            // Ubicación actual del repartidor (simulada)
            let repartidorLocation = { lat: -31.5375, lng: -68.5364 };

            // Destino del cliente
            const clienteLocation = { lat: -31.5350, lng: -68.5400 };

            // Marker del repartidor
            const repartidorMarker = new google.maps.Marker({
                position: repartidorLocation,
                map: map,
                title: 'Tu ubicación',
                icon: {
                    url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                        <svg width="40" height="40" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="20" cy="20" r="18" fill="#4CAF50" stroke="white" stroke-width="3"/>
                            <text x="20" y="28" text-anchor="middle" font-size="16">🏍️</text>
                        </svg>
                    `),
                    scaledSize: new google.maps.Size(40, 40),
                    anchor: new google.maps.Point(20, 20)
                }
            });

            // Marker del cliente
            const clienteMarker = new google.maps.Marker({
                position: clienteLocation,
                map: map,
                title: 'Destino - Cliente',
                icon: {
                    url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                        <svg width="40" height="40" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="20" cy="20" r="18" fill="#FF6B35" stroke="white" stroke-width="3"/>
                            <text x="20" y="28" text-anchor="middle" font-size="16">📍</text>
                        </svg>
                    `),
                    scaledSize: new google.maps.Size(40, 40),
                    anchor: new google.maps.Point(20, 20)
                }
            });

            // Servicio de direcciones
            const directionsService = new google.maps.DirectionsService();
            const directionsRenderer = new google.maps.DirectionsRenderer({
                polylineOptions: {
                    strokeColor: '#4CAF50',
                    strokeWeight: 5,
                    strokeOpacity: 0.8
                },
                suppressMarkers: true // Usar nuestros markers personalizados
            });
            directionsRenderer.setMap(map);

            // Calcular ruta inicial
            function calculateRoute() {
                directionsService.route({
                    origin: repartidorLocation,
                    destination: clienteLocation,
                    travelMode: google.maps.TravelMode.DRIVING,
                    avoidTolls: true,
                    language: 'es'
                }, (result, status) => {
                    if (status === 'OK') {
                        directionsRenderer.setDirections(result);

                        // Actualizar información de la ruta
                        const route = result.routes[0];
                        const leg = route.legs[0];

                        console.log(`📏 Distancia: ${leg.distance.text}`);
                        console.log(`⏱️ Tiempo: ${leg.duration.text}`);
                    }
                });
            }

            // Calcular ruta inicial
            calculateRoute();

            // Simular movimiento del repartidor
            function simularMovimiento() {
                setInterval(() => {
                    // Mover ligeramente hacia el destino
                    const latDiff = (clienteLocation.lat - repartidorLocation.lat) * 0.1;
                    const lngDiff = (clienteLocation.lng - repartidorLocation.lng) * 0.1;

                    repartidorLocation.lat += latDiff + (Math.random() - 0.5) * 0.0005;
                    repartidorLocation.lng += lngDiff + (Math.random() - 0.5) * 0.0005;

                    // Actualizar marker
                    repartidorMarker.setPosition(repartidorLocation);

                    // Recalcular ruta cada 30 segundos
                    if (Math.random() < 0.1) {
                        calculateRoute();
                    }

                    console.log('📍 Ubicación actualizada');
                }, 3000);
            }

            // Iniciar simulación de movimiento
            simularMovimiento();
        }

        function completarEntregaDemo() {
            if (confirm('¿Has completado la entrega exitosamente?')) {
                alert('🎉 ¡Entrega completada! Excelente trabajo repartidor.');
            }
        }

        // Simular toggle online
        document.addEventListener('DOMContentLoaded', function() {
            const onlineToggle = document.getElementById('onlineToggle');
            if (onlineToggle) {
                onlineToggle.addEventListener('click', function() {
                    const isOnline = this.classList.contains('offline');
                    if (isOnline) {
                        this.classList.remove('offline');
                        document.getElementById('statusText').textContent = 'EN LÍNEA';
                        document.getElementById('gpsAccuracy').textContent = 'GPS: 5m';
                    } else {
                        this.classList.add('offline');
                        document.getElementById('statusText').textContent = 'DESCONECTADO';
                        document.getElementById('gpsAccuracy').textContent = 'GPS: Inactivo';
                    }
                });
            }
        });
    </script>
</body>
</html>
