<?php
session_start();
require_once 'config.php';

// Verificar si está logueado
if (!isset($_SESSION['logged_in']) || !$_SESSION['logged_in']) {
    header('Location: login.php');
    exit();
}

try {
    $pdo = connectDB();
    $user_id = $_SESSION['user_id'];
    
    // Obtener datos del usuario
    $stmt = $pdo->prepare("
        SELECT u.*, dp.nombre_completo, dp.telefono, v.tipo_vehiculo 
        FROM usuarios u
        LEFT JOIN datos_personales dp ON u.id = dp.usuario_id
        LEFT JOIN vehiculos v ON u.id = v.usuario_id
        WHERE u.id = ?
    ");
    $stmt->execute([$user_id]);
    $usuario = $stmt->fetch();
    
    // Obtener estadísticas
    $stmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total_pedidos,
            SUM(CASE WHEN estado = 'entregado' THEN 1 ELSE 0 END) as pedidos_entregados,
            SUM(CASE WHEN estado = 'entregado' THEN comision_repartidor ELSE 0 END) as ganancias_totales,
            AVG(CASE WHEN estado = 'entregado' AND calificacion_cliente IS NOT NULL THEN calificacion_cliente END) as calificacion_promedio
        FROM pedidos 
        WHERE repartidor_id = ?
    ");
    $stmt->execute([$user_id]);
    $stats = $stmt->fetch();
    
    // Obtener pedidos recientes
    $stmt = $pdo->prepare("
        SELECT * FROM pedidos 
        WHERE repartidor_id = ? 
        ORDER BY fecha_pedido DESC 
        LIMIT 5
    ");
    $stmt->execute([$user_id]);
    $pedidos_recientes = $stmt->fetchAll();
    
} catch (Exception $e) {
    $error_message = "Error al cargar datos: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - RepuMovil Delivery</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700;800&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-orange: #FF6B35;
            --secondary-orange: #F7931E;
            --primary-red: #E53E3E;
            --secondary-red: #FC8181;
            --gradient-main: linear-gradient(135deg, #FF6B35 0%, #E53E3E 50%, #F7931E 100%);
            --white: #ffffff;
            --dark: #2D3748;
            --light-gray: #F7FAFC;
            --success: #48BB78;
            --warning: #ED8936;
            --box-shadow: 0 10px 30px rgba(229, 62, 62, 0.2);
            --transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        /* Header */
        .header {
            background: var(--gradient-main);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 1rem;
            font-size: 1.5rem;
            font-weight: 800;
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10px);
        }

        .user-menu {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .user-info {
            text-align: right;
        }

        .user-name {
            font-weight: 600;
        }

        .user-status {
            font-size: 0.8rem;
            opacity: 0.9;
        }

        .btn-logout {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            cursor: pointer;
            transition: var(--transition);
            backdrop-filter: blur(10px);
        }

        .btn-logout:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        /* Main Content */
        .main-content {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }

        .welcome-section {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .welcome-title {
            font-size: 2rem;
            color: var(--dark);
            margin-bottom: 0.5rem;
        }

        .welcome-subtitle {
            color: #666;
            font-size: 1.1rem;
        }

        /* Stats Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-main);
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: white;
        }

        .stat-icon.pedidos {
            background: linear-gradient(45deg, #4299E1, #63B3ED);
        }

        .stat-icon.entregados {
            background: linear-gradient(45deg, #48BB78, #68D391);
        }

        .stat-icon.ganancias {
            background: linear-gradient(45deg, #ED8936, #F6AD55);
        }

        .stat-icon.calificacion {
            background: linear-gradient(45deg, #9F7AEA, #B794F6);
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 800;
            color: var(--dark);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #666;
            font-weight: 500;
        }

        /* Status Badge */
        .status-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-pendiente {
            background: #FED7D7;
            color: #C53030;
        }

        .status-activo {
            background: #C6F6D5;
            color: #2F855A;
        }

        .status-suspendido {
            background: #FEEBC8;
            color: #C05621;
        }

        /* Recent Orders */
        .recent-orders {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--dark);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .orders-list {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .order-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            background: var(--light-gray);
            border-radius: 10px;
            transition: var(--transition);
        }

        .order-item:hover {
            background: #E2E8F0;
        }

        .order-info {
            flex: 1;
        }

        .order-id {
            font-weight: 600;
            color: var(--dark);
        }

        .order-details {
            font-size: 0.9rem;
            color: #666;
            margin-top: 0.2rem;
        }

        .order-amount {
            font-weight: 700;
            color: var(--primary-red);
            font-size: 1.1rem;
        }

        .empty-state {
            text-align: center;
            padding: 2rem;
            color: #666;
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        /* Quick Actions */
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .action-btn {
            background: var(--gradient-main);
            color: white;
            padding: 1rem;
            border-radius: 15px;
            text-decoration: none;
            text-align: center;
            font-weight: 600;
            transition: var(--transition);
            box-shadow: 0 8px 25px rgba(229, 62, 62, 0.2);
        }

        .action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(229, 62, 62, 0.3);
            color: white;
            text-decoration: none;
        }

        .action-btn i {
            display: block;
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
        }

        @media (max-width: 768px) {
            .header-content {
                padding: 0 1rem;
                flex-direction: column;
                gap: 1rem;
            }

            .main-content {
                padding: 0 1rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .order-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }

            .quick-actions {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <div class="logo-icon">
                    <i class="fas fa-motorcycle"></i>
                </div>
                <span>RepuMovil <strong>Delivery</strong></span>
            </div>
            
            <div class="user-menu">
                <div class="user-info">
                    <div class="user-name"><?php echo htmlspecialchars($usuario['nombre_completo'] ?? 'Usuario'); ?></div>
                    <div class="user-status">
                        <span class="status-badge status-<?php echo $usuario['estado']; ?>">
                            <?php echo ucfirst($usuario['estado']); ?>
                        </span>
                    </div>
                </div>
                <a href="logout.php" class="btn-logout">
                    <i class="fas fa-sign-out-alt"></i> Salir
                </a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Welcome Section -->
        <div class="welcome-section">
            <h1 class="welcome-title">¡Bienvenido, <?php echo htmlspecialchars(explode(' ', $usuario['nombre_completo'] ?? 'Usuario')[0]); ?>!</h1>
            <p class="welcome-subtitle">
                <?php if ($usuario['estado'] === 'pendiente'): ?>
                    Tu solicitud está siendo revisada. Te contactaremos pronto.
                <?php elseif ($usuario['estado'] === 'activo'): ?>
                    Estás listo para empezar a generar ingresos con RepuMovil Delivery
                <?php else: ?>
                    Contactá soporte para más información sobre tu cuenta
                <?php endif; ?>
            </p>
        </div>

        <!-- Quick Actions -->
        <?php if ($usuario['estado'] === 'activo'): ?>
        <div class="quick-actions">
            <a href="pedidos-disponibles.php" class="action-btn">
                <i class="fas fa-search"></i>
                Ver Pedidos Disponibles
            </a>
            <a href="mis-pedidos.php" class="action-btn">
                <i class="fas fa-clipboard-list"></i>
                Mis Pedidos
            </a>
            <a href="ganancias.php" class="action-btn">
                <i class="fas fa-dollar-sign"></i>
                Mis Ganancias
            </a>
            <a href="perfil.php" class="action-btn">
                <i class="fas fa-user-cog"></i>
                Mi Perfil
            </a>
        </div>
        <?php endif; ?>

        <!-- Stats Grid -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon pedidos">
                        <i class="fas fa-box"></i>
                    </div>
                </div>
                <div class="stat-value"><?php echo $stats['total_pedidos'] ?? 0; ?></div>
                <div class="stat-label">Total Pedidos</div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon entregados">
                        <i class="fas fa-check-circle"></i>
                    </div>
                </div>
                <div class="stat-value"><?php echo $stats['pedidos_entregados'] ?? 0; ?></div>
                <div class="stat-label">Pedidos Entregados</div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon ganancias">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                </div>
                <div class="stat-value">$<?php echo number_format($stats['ganancias_totales'] ?? 0, 0, ',', '.'); ?></div>
                <div class="stat-label">Ganancias Totales</div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon calificacion">
                        <i class="fas fa-star"></i>
                    </div>
                </div>
                <div class="stat-value"><?php echo number_format($stats['calificacion_promedio'] ?? 0, 1); ?></div>
                <div class="stat-label">Calificación Promedio</div>
            </div>
        </div>

        <!-- Recent Orders -->
        <div class="recent-orders">
            <h2 class="section-title">
                <i class="fas fa-clock"></i>
                Pedidos Recientes
            </h2>

            <?php if (empty($pedidos_recientes)): ?>
                <div class="empty-state">
                    <i class="fas fa-inbox"></i>
                    <h3>No hay pedidos aún</h3>
                    <p>Cuando tengas pedidos asignados, aparecerán aquí</p>
                </div>
            <?php else: ?>
                <div class="orders-list">
                    <?php foreach ($pedidos_recientes as $pedido): ?>
                        <div class="order-item">
                            <div class="order-info">
                                <div class="order-id">Pedido #<?php echo str_pad($pedido['id'], 6, '0', STR_PAD_LEFT); ?></div>
                                <div class="order-details">
                                    <?php echo htmlspecialchars($pedido['direccion_destino']); ?> • 
                                    <?php echo date('d/m/Y H:i', strtotime($pedido['fecha_pedido'])); ?>
                                </div>
                            </div>
                            <div class="order-amount">
                                $<?php echo number_format($pedido['comision_repartidor'], 0, ',', '.'); ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </main>
</body>
</html>
