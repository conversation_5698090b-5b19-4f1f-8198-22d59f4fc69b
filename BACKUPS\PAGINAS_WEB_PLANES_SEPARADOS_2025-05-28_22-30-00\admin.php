<?php
// Mostrar todos los errores para depuración
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Incluir archivo de configuración de la base de datos
require_once 'db_config.php';

// Iniciar sesión
session_start();

// Verificar si el usuario está autenticado y es administrador
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: login.php');
    exit;
}

// Obtener información del usuario actual
$userId = $_SESSION['user_id'];
$username = $_SESSION['username'];

// Conectar a la base de datos
try {
    $conn = connectDB();
    
    // Obtener estadísticas básicas
    $userCount = $conn->query("SELECT COUNT(*) FROM users")->fetchColumn();
    $workshopCount = $conn->query("SELECT COUNT(*) FROM workshops")->fetchColumn();
    $supplierCount = $conn->query("SELECT COUNT(*) FROM suppliers")->fetchColumn();
    
    // Obtener lista de usuarios
    $stmt = $conn->query("SELECT u.id, u.username, u.email, u.status, u.created_at, u.last_login, r.name as role_name 
                         FROM users u 
                         JOIN roles r ON u.role_id = r.id 
                         ORDER BY u.created_at DESC");
    $users = $stmt->fetchAll();
    
} catch (PDOException $e) {
    $error = "Error de base de datos: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Panel de Administración - Repumóvil</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #343a40;
            color: white;
            padding-top: 20px;
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.75);
        }
        .sidebar .nav-link:hover {
            color: rgba(255, 255, 255, 1);
        }
        .sidebar .nav-link.active {
            color: white;
            font-weight: bold;
        }
        .main-content {
            padding: 20px;
        }
        .logo-container {
            text-align: center;
            margin-bottom: 20px;
        }
        .logo-container img {
            max-width: 80%;
            height: auto;
        }
        .admin-panel-text {
            text-align: center;
            font-size: 14px;
            margin-top: 5px;
            color: rgba(255, 255, 255, 0.7);
        }
        .card-counter {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            padding: 20px;
            border-radius: 5px;
            color: white;
        }
        .card-counter i {
            font-size: 4em;
            opacity: 0.4;
        }
        .card-counter .count-numbers {
            position: absolute;
            right: 35px;
            top: 20px;
            font-size: 32px;
            display: block;
        }
        .card-counter .count-name {
            position: absolute;
            right: 35px;
            top: 65px;
            font-style: italic;
            text-transform: capitalize;
            opacity: 0.7;
            display: block;
        }
        .bg-info {
            background-color: #17a2b8;
        }
        .bg-success {
            background-color: #28a745;
        }
        .bg-warning {
            background-color: #ffc107;
        }
        .table-responsive {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2 sidebar">
                <div class="logo-container">
                    <img src="logo.png" alt="Repumóvil Logo">
                    <p class="admin-panel-text">Panel de Administración</p>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link active" href="admin.php">
                            <i class="fas fa-tachometer-alt mr-2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="admin_users.php">
                            <i class="fas fa-users mr-2"></i> Usuarios
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="admin_workshops.php">
                            <i class="fas fa-tools mr-2"></i> Talleres
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="admin_suppliers.php">
                            <i class="fas fa-truck mr-2"></i> Proveedores
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="admin_settings.php">
                            <i class="fas fa-cog mr-2"></i> Configuración
                        </a>
                    </li>
                    <li class="nav-item mt-5">
                        <a class="nav-link" href="logout.php">
                            <i class="fas fa-sign-out-alt mr-2"></i> Cerrar Sesión
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-10 main-content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>Dashboard</h2>
                    <div>
                        <span class="mr-2">Bienvenido, <?php echo htmlspecialchars($username); ?></span>
                        <a href="logout.php" class="btn btn-sm btn-outline-danger">
                            <i class="fas fa-sign-out-alt"></i> Salir
                        </a>
                    </div>
                </div>
                
                <?php if (isset($error)): ?>
                    <div class="alert alert-danger" role="alert">
                        <?php echo $error; ?>
                    </div>
                <?php endif; ?>
                
                <!-- Statistics Cards -->
                <div class="row">
                    <div class="col-md-4">
                        <div class="card-counter bg-info">
                            <i class="fas fa-users"></i>
                            <span class="count-numbers"><?php echo $userCount; ?></span>
                            <span class="count-name">Usuarios</span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card-counter bg-success">
                            <i class="fas fa-tools"></i>
                            <span class="count-numbers"><?php echo $workshopCount; ?></span>
                            <span class="count-name">Talleres</span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card-counter bg-warning">
                            <i class="fas fa-truck"></i>
                            <span class="count-numbers"><?php echo $supplierCount; ?></span>
                            <span class="count-name">Proveedores</span>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Users Table -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5>Usuarios Recientes</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Usuario</th>
                                        <th>Email</th>
                                        <th>Rol</th>
                                        <th>Estado</th>
                                        <th>Fecha Registro</th>
                                        <th>Último Acceso</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($users as $user): ?>
                                    <tr>
                                        <td><?php echo $user['id']; ?></td>
                                        <td><?php echo htmlspecialchars($user['username']); ?></td>
                                        <td><?php echo htmlspecialchars($user['email']); ?></td>
                                        <td><?php echo htmlspecialchars($user['role_name']); ?></td>
                                        <td>
                                            <?php if ($user['status'] == 'active'): ?>
                                                <span class="badge badge-success">Activo</span>
                                            <?php elseif ($user['status'] == 'inactive'): ?>
                                                <span class="badge badge-secondary">Inactivo</span>
                                            <?php else: ?>
                                                <span class="badge badge-danger">Suspendido</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo date('d/m/Y H:i', strtotime($user['created_at'])); ?></td>
                                        <td><?php echo $user['last_login'] ? date('d/m/Y H:i', strtotime($user['last_login'])) : 'Nunca'; ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>



