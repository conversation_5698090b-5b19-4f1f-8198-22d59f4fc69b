<!-- Selector de roles actualizado -->
<div class="form-group">
    <label for="role_id">Tipo de cuenta</label>
    <select class="form-control" id="role_id" name="role_id" required>
        <option value="">Seleccione un rol</option>
        <option value="2">Taller mecánico</option>
        <option value="3">Mecánico independiente</option>
        <option value="4">Usuario regular</option>
        <option value="5">Proveedor de repuestos</option>
    </select>
</div>

<!-- Campos dinámicos según el rol seleccionado -->
<div id="workshop_fields" style="display: none;">
    <div class="form-group">
        <label for="business_name">Nombre del taller</label>
        <input type="text" class="form-control" id="business_name" name="business_name">
    </div>
    <div class="form-group">
        <label for="address">Dirección</label>
        <input type="text" class="form-control" id="address" name="address">
    </div>
    <div class="form-group">
        <label for="phone">Teléfono</label>
        <input type="text" class="form-control" id="phone" name="phone">
    </div>
    <div class="form-group">
        <label for="description">Descripción</label>
        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
    </div>
</div>

<div id="mechanic_fields" style="display: none;">
    <div class="form-group">
        <label for="mechanic_name">Nombre completo</label>
        <input type="text" class="form-control" id="mechanic_name" name="mechanic_name">
    </div>
    <div class="form-group">
        <label for="specialties">Especialidades</label>
        <input type="text" class="form-control" id="specialties" name="specialties" placeholder="Ej: Frenos, Suspensión, Motor">
    </div>
    <div class="form-group">
        <label for="experience">Experiencia</label>
        <input type="text" class="form-control" id="experience" name="experience" placeholder="Ej: 5 años">
    </div>
    <div class="form-group">
        <label for="mechanic_address">Dirección</label>
        <input type="text" class="form-control" id="mechanic_address" name="address">
    </div>
    <div class="form-group">
        <label for="mechanic_phone">Teléfono</label>
        <input type="text" class="form-control" id="mechanic_phone" name="phone">
    </div>
    <div class="form-group">
        <label for="mechanic_description">Descripción</label>
        <textarea class="form-control" id="mechanic_description" name="description" rows="3"></textarea>
    </div>
</div>

<div id="person_fields" style="display: none;">
    <div class="form-group">
        <label for="first_name">Nombre</label>
        <input type="text" class="form-control" id="first_name" name="first_name">
    </div>
    <div class="form-group">
        <label for="last_name">Apellido</label>
        <input type="text" class="form-control" id="last_name" name="last_name">
    </div>
    <div class="form-group">
        <label for="person_phone">Teléfono</label>
        <input type="text" class="form-control" id="person_phone" name="phone">
    </div>
    <div class="form-group">
        <label for="person_address">Dirección</label>
        <input type="text" class="form-control" id="person_address" name="address">
    </div>
</div>

<div id="supplier_fields" style="display: none;">
    <div class="form-group">
        <label for="supplier_name">Nombre de la empresa</label>
        <input type="text" class="form-control" id="supplier_name" name="business_name">
    </div>
    <div class="form-group">
        <label for="supplier_address">Dirección</label>
        <input type="text" class="form-control" id="supplier_address" name="address">
    </div>
    <div class="form-group">
        <label for="supplier_phone">Teléfono</label>
        <input type="text" class="form-control" id="supplier_phone" name="phone">
    </div>
    <div class="form-group">
        <label for="supplier_description">Descripción</label>
        <textarea class="form-control" id="supplier_description" name="description" rows="3"></textarea>
    </div>
</div>

<!-- JavaScript para mostrar/ocultar campos según el rol seleccionado -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const roleSelect = document.getElementById('role_id');
    const workshopFields = document.getElementById('workshop_fields');
    const mechanicFields = document.getElementById('mechanic_fields');
    const personFields = document.getElementById('person_fields');
    const supplierFields = document.getElementById('supplier_fields');
    
    roleSelect.addEventListener('change', function() {
        // Ocultar todos los campos específicos
        workshopFields.style.display = 'none';
        mechanicFields.style.display = 'none';
        personFields.style.display = 'none';
        supplierFields.style.display = 'none';
        
        // Mostrar campos según el rol seleccionado
        const selectedRole = this.value;
        if (selectedRole === '2') {
            workshopFields.style.display = 'block';
        } else if (selectedRole === '3') {
            mechanicFields.style.display = 'block';
        } else if (selectedRole === '4') {
            personFields.style.display = 'block';
        } else if (selectedRole === '5') {
            supplierFields.style.display = 'block';
        }
    });
});
</script>