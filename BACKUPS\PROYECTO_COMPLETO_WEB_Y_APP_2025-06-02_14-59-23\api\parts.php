<?php
// api/parts.php - Funciones para gestión de repuestos

/**
 * Busca repuestos según criterios
 * @param array $params Parámetros de búsqueda
 * @return array Resultados de la búsqueda
 */
function searchParts($params) {
    $conn = connectDB();
    
    // Construir consulta base
    $query = "SELECT p.*, s.name as supplier_name 
              FROM parts p 
              JOIN suppliers s ON p.supplier_id = s.id 
              WHERE 1=1";
    
    $queryParams = [];
    
    // Filtrar por nombre/descripción
    if (isset($params['q']) && !empty($params['q'])) {
        $query .= " AND (p.name LIKE :query OR p.description LIKE :query)";
        $queryParams['query'] = '%' . $params['q'] . '%';
    }
    
    // Filtrar por categoría
    if (isset($params['category']) && !empty($params['category'])) {
        $query .= " AND p.category = :category";
        $queryParams['category'] = $params['category'];
    }
    
    // Filtrar por marca de vehículo
    if (isset($params['vehicle_brand']) && !empty($params['vehicle_brand'])) {
        $query .= " AND p.vehicle_brand = :vehicle_brand";
        $queryParams['vehicle_brand'] = $params['vehicle_brand'];
    }
    
    // Filtrar por modelo de vehículo
    if (isset($params['vehicle_model']) && !empty($params['vehicle_model'])) {
        $query .= " AND p.vehicle_model = :vehicle_model";
        $queryParams['vehicle_model'] = $params['vehicle_model'];
    }
    
    // Filtrar por año de vehículo
    if (isset($params['vehicle_year']) && !empty($params['vehicle_year'])) {
        $query .= " AND p.vehicle_year = :vehicle_year";
        $queryParams['vehicle_year'] = $params['vehicle_year'];
    }
    
    // Filtrar por proveedor
    if (isset($params['supplier_id']) && !empty($params['supplier_id'])) {
        $query .= " AND p.supplier_id = :supplier_id";
        $queryParams['supplier_id'] = $params['supplier_id'];
    }
    
    // Ordenar resultados
    $query .= " ORDER BY p.created_at DESC";
    
    // Limitar resultados
    $limit = isset($params['limit']) ? intval($params['limit']) : 50;
    $query .= " LIMIT :limit";
    $queryParams['limit'] = $limit;
    
    $stmt = $conn->prepare($query);
    
    // Bind de parámetros
    foreach ($queryParams as $key => $value) {
        if ($key === 'limit') {
            $stmt->bindValue(':' . $key, $value, PDO::PARAM_INT);
        } else {
            $stmt->bindValue(':' . $key, $value, PDO::PARAM_STR);
        }
    }
    
    $stmt->execute();
    $parts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    return [
        'status' => 'success',
        'parts' => $parts
    ];
}

/**
 * Obtiene información de un repuesto específico
 * @param int $id ID del repuesto
 * @return array Datos del repuesto
 */
function getPart($id) {
    $conn = connectDB();
    
    $query = "SELECT p.*, s.name as supplier_name, s.location as supplier_location, s.phone as supplier_phone
              FROM parts p 
              JOIN suppliers s ON p.supplier_id = s.id 
              WHERE p.id = :id";
    
    $stmt = $conn->prepare($query);
    $stmt->execute(['id' => $id]);
    $part = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$part) {
        return [
            'status' => 'error',
            'message' => 'Repuesto no encontrado'
        ];
    }
    
    // Obtener imágenes del repuesto (si existe una tabla para esto)
    $query = "SELECT * FROM part_images WHERE part_id = :part_id";
    $stmt = $conn->prepare($query);
    $stmt->execute(['part_id' => $id]);
    $images = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $part['images'] = $images;
    
    return [
        'status' => 'success',
        'part' => $part
    ];
}

/**
 * Agrega un nuevo repuesto
 * @param array $data Datos del repuesto
 * @return array Resultado de la operación
 */
function addPart($data) {
    global $current_user_id;
    $conn = connectDB();
    
    // Verificar que el usuario es un proveedor
    $stmt = $conn->prepare("SELECT s.id FROM suppliers s JOIN users u ON s.user_id = u.id WHERE u.id = :user_id");
    $stmt->execute(['user_id' => $current_user_id]);
    $supplier = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$supplier) {
        return [
            'status' => 'error',
            'message' => 'Solo los proveedores pueden agregar repuestos'
        ];
    }
    
    $supplierId = $supplier['id'];
    
    // Validar campos requeridos
    $requiredFields = ['name', 'price', 'category', 'vehicle_brand', 'vehicle_model', 'vehicle_year'];
    foreach ($requiredFields as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            return [
                'status' => 'error',
                'message' => "El campo '$field' es requerido"
            ];
        }
    }
    
    // Insertar repuesto
    $stmt = $conn->prepare("INSERT INTO parts (
        supplier_id, name, description, price, stock, category,
        vehicle_brand, vehicle_model, vehicle_year, condition_type
    ) VALUES (
        :supplier_id, :name, :description, :price, :stock, :category,
        :vehicle_brand, :vehicle_model, :vehicle_year, :condition_type
    )");
    
    $result = $stmt->execute([
        'supplier_id' => $supplierId,
        'name' => $data['name'],
        'description' => $data['description'] ?? '',
        'price' => $data['price'],
        'stock' => $data['stock'] ?? 1,
        'category' => $data['category'],
        'vehicle_brand' => $data['vehicle_brand'],
        'vehicle_model' => $data['vehicle_model'],
        'vehicle_year' => $data['vehicle_year'],
        'condition_type' => $data['condition_type'] ?? 'new'
    ]);
    
    if (!$result) {
        return [
            'status' => 'error',
            'message' => 'Error al agregar el repuesto'
        ];
    }
    
    $partId = $conn->lastInsertId();
    
    // Procesar imágenes si se proporcionaron
    if (isset($data['images']) && is_array($data['images'])) {
        foreach ($data['images'] as $imageUrl) {
            $stmt = $conn->prepare("INSERT INTO part_images (part_id, image_url) VALUES (:part_id, :image_url)");
            $stmt->execute([
                'part_id' => $partId,
                'image_url' => $imageUrl
            ]);
        }
    }
    
    return [
        'status' => 'success',
        'message' => 'Repuesto agregado correctamente',
        'part_id' => $partId
    ];
}

/**
 * Actualiza un repuesto existente
 * @param int $id ID del repuesto
 * @param array $data Datos a actualizar
 * @return array Resultado de la operación
 */
function updatePart($id, $data) {
    global $current_user_id;
    $conn = connectDB();
    
    // Verificar que el repuesto pertenece al proveedor actual
    $stmt = $conn->prepare("
        SELECT p.* FROM parts p 
        JOIN suppliers s ON p.supplier_id = s.id 
        WHERE p.id = :id AND s.user_id = :user_id
    ");
    $stmt->execute([
        'id' => $id,
        'user_id' => $current_user_id
    ]);
    $part = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$part) {
        return [
            'status' => 'error',
            'message' => 'No tienes permiso para actualizar este repuesto'
        ];
    }
    
    // Campos permitidos para actualizar
    $allowedFields = [
        'name', 'description', 'price', 'stock', 'category',
        'vehicle_brand', 'vehicle_model', 'vehicle_year', 'condition_type'
    ];
    
    $updateFields = [];
    $params = ['id' => $id];
    
    foreach ($allowedFields as $field) {
        if (isset($data[$field])) {
            $updateFields[] = "$field = :$field";
            $params[$field] = $data[$field];
        }
    }
    
    if (empty($updateFields)) {
        return [
            'status' => 'error',
            'message' => 'No se proporcionaron campos para actualizar'
        ];
    }
    
    $updateQuery = "UPDATE parts SET " . implode(', ', $updateFields) . " WHERE id = :id";
    $stmt = $conn->prepare($updateQuery);
    
    if ($stmt->execute($params)) {
        return [
            'status' => 'success',
            'message' => 'Repuesto actualizado correctamente'
        ];
    } else {
        return [
            'status' => 'error',
            'message' => 'Error al actualizar el repuesto'
        ];
    }
}