<?php
// Configuración de CORS para permitir solicitudes desde la aplicación móvil
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header("Content-Type: application/json; charset=UTF-8");

// Responder a las solicitudes OPTIONS (preflight)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Verificar que sea una solicitud GET
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Método no permitido']);
    exit;
}

// Incluir archivo de configuración de la base de datos
require_once '../db_config.php';

try {
    // Crear conexión a la base de datos
    $conn = new PDO("mysql:host=$servername;dbname=$dbname", $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Consulta para obtener usuarios con información básica (sin contraseñas)
    $stmt = $conn->prepare("
        SELECT u.id, u.username, u.email, u.role_id, u.created_at, u.status,
               CASE 
                   WHEN u.role_id = 2 THEN w.business_name
                   WHEN u.role_id = 3 THEN m.mechanic_name
                   WHEN u.role_id = 4 THEN CONCAT(p.first_name, ' ', p.last_name)
                   WHEN u.role_id = 5 THEN s.business_name
                   ELSE u.username
               END AS display_name
        FROM users u
        LEFT JOIN workshops w ON u.id = w.user_id AND u.role_id = 2
        LEFT JOIN mechanics m ON u.id = m.user_id AND u.role_id = 3
        LEFT JOIN persons p ON u.id = p.user_id AND u.role_id = 4
        LEFT JOIN suppliers s ON u.id = s.user_id AND u.role_id = 5
        ORDER BY u.created_at DESC
    ");
    $stmt->execute();
    
    // Obtener resultados
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Devolver respuesta exitosa
    echo json_encode([
        'success' => true,
        'users' => $users
    ]);
    
} catch(PDOException $e) {
    // Manejar errores de base de datos
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error al obtener usuarios: ' . $e->getMessage()
    ]);
}
