// Configuración para Google Maps API - RepuMovil
export const GOOGLE_MAPS_CONFIG = {
  // Modo desarrollo - sin API Key real
  DEVELOPMENT_MODE: true,
  API_KEY: 'AIzaSyDeF7xLqA58aAm5Gou7Nux3A7_jwGnnVxg',

  // URLs de la API
  GEOCODING_URL: 'https://maps.googleapis.com/maps/api/geocode/json',
  PLACES_URL: 'https://maps.googleapis.com/maps/api/place/autocomplete/json',

  // Configuración por defecto para San Juan, Argentina
  DEFAULT_LOCATION: {
    lat: -31.5375, // San Juan
    lng: -68.5364,
  },

  // Configuración de búsqueda
  SEARCH_CONFIG: {
    language: 'es',
    region: 'ar',
    components: 'country:ar', // Restringir a Argentina
  },

  // Configuración de mapas para RepuMovil
  MAPS_CONFIG: {
    DEFAULT_REGION: {
      latitude: -31.5375,
      longitude: -68.5364,
      latitudeDelta: 0.0922,
      longitudeDelta: 0.0421,
    },

    // Configuración de markers
    MARKER_COLORS: {
      repartidor: '#4CAF50',
      cliente: '#FF6B35',
      proveedor: '#2196F3',
    },

    // Configuración de rutas
    ROUTE_CONFIG: {
      strokeColor: '#4CAF50',
      strokeWidth: 4,
      strokeOpacity: 0.8,
    },

    // Ubicaciones simuladas para desarrollo
    MOCK_LOCATIONS: {
      sanJuan: { latitude: -31.5375, longitude: -68.5364 },
      cliente1: { latitude: -31.5400, longitude: -68.5300 },
      cliente2: { latitude: -31.5350, longitude: -68.5400 },
      proveedor1: { latitude: -31.5380, longitude: -68.5350 },
    },
  }
};

// Función para geocodificar una dirección
export const geocodeAddress = async (address: string) => {
  try {
    const url = `${GOOGLE_MAPS_CONFIG.GEOCODING_URL}?address=${encodeURIComponent(address)}&key=${GOOGLE_MAPS_CONFIG.API_KEY}&language=${GOOGLE_MAPS_CONFIG.SEARCH_CONFIG.language}&region=${GOOGLE_MAPS_CONFIG.SEARCH_CONFIG.region}`;
    
    const response = await fetch(url);
    const data = await response.json();
    
    if (data.status === 'OK' && data.results.length > 0) {
      return {
        success: true,
        address: data.results[0].formatted_address,
        coordinates: {
          lat: data.results[0].geometry.location.lat,
          lng: data.results[0].geometry.location.lng,
        },
        placeId: data.results[0].place_id,
      };
    } else {
      return {
        success: false,
        error: 'No se encontró la dirección',
      };
    }
  } catch (error) {
    return {
      success: false,
      error: 'Error al buscar la dirección',
    };
  }
};

// Función para obtener sugerencias de direcciones
export const getAddressSuggestions = async (input: string) => {
  try {
    const url = `${GOOGLE_MAPS_CONFIG.PLACES_URL}?input=${encodeURIComponent(input)}&key=${GOOGLE_MAPS_CONFIG.API_KEY}&language=${GOOGLE_MAPS_CONFIG.SEARCH_CONFIG.language}&components=${GOOGLE_MAPS_CONFIG.SEARCH_CONFIG.components}`;
    
    const response = await fetch(url);
    const data = await response.json();
    
    if (data.status === 'OK') {
      return {
        success: true,
        suggestions: data.predictions.map((prediction: any) => ({
          description: prediction.description,
          placeId: prediction.place_id,
        })),
      };
    } else {
      return {
        success: false,
        error: 'No se encontraron sugerencias',
      };
    }
  } catch (error) {
    return {
      success: false,
      error: 'Error al obtener sugerencias',
    };
  }
};

// Función para generar URL de Google Maps
export const generateMapsUrl = (address: string) => {
  const encodedAddress = encodeURIComponent(address);
  return `https://www.google.com/maps/search/?api=1&query=${encodedAddress}`;
};

// Función para abrir Google Maps en el navegador/app
export const openInGoogleMaps = (address: string) => {
  const url = generateMapsUrl(address);
  // En React Native, usarías Linking.openURL(url)
  console.log('Abrir en Google Maps:', url);
  return url;
};
