<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RepuMovil - Reportes y Análisis 📊</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary-color: #FF6B35;
            --secondary-color: #FFA500;
            --dark-color: #2c3e50;
            --light-color: #f8f9fa;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --purple-color: #6f42c1;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: '<PERSON><PERSON><PERSON>', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
            color: white;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .title {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .nav-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 30px;
        }

        .nav-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .nav-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .nav-btn.active {
            background: white;
            color: var(--primary-color);
        }

        .report-filters {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            color: var(--dark-color);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .filters-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 20px;
            text-align: center;
        }

        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
        }

        .filter-label {
            font-weight: bold;
            margin-bottom: 8px;
            color: var(--primary-color);
        }

        .filter-input {
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 0.9rem;
        }

        .filter-input:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .generate-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .generate-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .generate-btn:hover {
            background: #e55a2b;
            transform: translateY(-2px);
        }

        .generate-btn.secondary {
            background: var(--info-color);
        }

        .generate-btn.secondary:hover {
            background: #138496;
        }

        .reports-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .report-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            color: var(--dark-color);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .report-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .report-title {
            font-size: 1.2rem;
            font-weight: bold;
            color: var(--primary-color);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .export-btn {
            background: var(--success-color);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 6px;
            font-size: 0.8rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .export-btn:hover {
            background: #218838;
        }

        .chart-container {
            position: relative;
            height: 300px;
            margin-bottom: 20px;
        }

        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
        }

        .summary-item {
            text-align: center;
            padding: 15px;
            background: var(--light-color);
            border-radius: 8px;
        }

        .summary-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 5px;
        }

        .summary-label {
            font-size: 0.8rem;
            color: #666;
        }

        .insights-panel {
            background: white;
            border-radius: 15px;
            padding: 25px;
            color: var(--dark-color);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 30px;
        }

        .insights-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 20px;
            text-align: center;
        }

        .insights-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .insight-item {
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid var(--primary-color);
        }

        .insight-positive {
            background: #d4edda;
            border-left-color: var(--success-color);
        }

        .insight-warning {
            background: #fff3cd;
            border-left-color: var(--warning-color);
        }

        .insight-info {
            background: #d1ecf1;
            border-left-color: var(--info-color);
        }

        .insight-icon {
            font-size: 1.5rem;
            margin-bottom: 10px;
        }

        .insight-title {
            font-weight: bold;
            margin-bottom: 8px;
        }

        .insight-description {
            font-size: 0.9rem;
            color: #666;
            line-height: 1.4;
        }

        .quick-actions {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .actions-title {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 15px;
            text-align: center;
        }

        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .action-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 15px 20px;
            border-radius: 10px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .action-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        @media (max-width: 768px) {
            .reports-grid {
                grid-template-columns: 1fr;
            }
            
            .filters-grid {
                grid-template-columns: 1fr;
            }
            
            .generate-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">📊📈</div>
            <h1 class="title">Reportes y Análisis</h1>
            <p class="subtitle">Análisis completo del rendimiento del sistema RepuMovil</p>
        </div>

        <!-- Navegación -->
        <div class="nav-buttons">
            <a href="admin-dashboard.php" class="nav-btn">
                <i class="fas fa-chart-line"></i>
                Dashboard
            </a>
            <a href="admin-usuarios.php" class="nav-btn">
                <i class="fas fa-users"></i>
                Usuarios
            </a>
            <button class="nav-btn active">
                <i class="fas fa-file-alt"></i>
                Reportes
            </button>
            <a href="admin-configuracion.php" class="nav-btn">
                <i class="fas fa-cog"></i>
                Configuración
            </a>
        </div>

        <!-- Filtros de Reportes -->
        <div class="report-filters">
            <div class="filters-title">
                <i class="fas fa-filter"></i>
                Configurar Reporte
            </div>
            
            <div class="filters-grid">
                <div class="filter-group">
                    <label class="filter-label">Período</label>
                    <select class="filter-input" id="period-filter">
                        <option value="today">Hoy</option>
                        <option value="week">Esta Semana</option>
                        <option value="month" selected>Este Mes</option>
                        <option value="quarter">Este Trimestre</option>
                        <option value="year">Este Año</option>
                        <option value="custom">Personalizado</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label class="filter-label">Fecha Inicio</label>
                    <input type="date" class="filter-input" id="start-date">
                </div>
                
                <div class="filter-group">
                    <label class="filter-label">Fecha Fin</label>
                    <input type="date" class="filter-input" id="end-date">
                </div>
                
                <div class="filter-group">
                    <label class="filter-label">Tipo de Reporte</label>
                    <select class="filter-input" id="report-type">
                        <option value="general">General</option>
                        <option value="deliveries">Deliveries</option>
                        <option value="pedidos">Pedidos</option>
                        <option value="calificaciones">Calificaciones</option>
                        <option value="financiero">Financiero</option>
                    </select>
                </div>
            </div>
            
            <div class="generate-buttons">
                <button class="generate-btn" onclick="generarReporte()">
                    <i class="fas fa-chart-bar"></i>
                    Generar Reporte
                </button>
                <button class="generate-btn secondary" onclick="exportarPDF()">
                    <i class="fas fa-file-pdf"></i>
                    Exportar PDF
                </button>
                <button class="generate-btn secondary" onclick="exportarExcel()">
                    <i class="fas fa-file-excel"></i>
                    Exportar Excel
                </button>
            </div>
        </div>

        <!-- Grid de Reportes -->
        <div class="reports-grid">
            <!-- Reporte de Rendimiento -->
            <div class="report-card">
                <div class="report-header">
                    <div class="report-title">
                        <i class="fas fa-tachometer-alt"></i>
                        Rendimiento General
                    </div>
                    <button class="export-btn" onclick="exportarReporte('rendimiento')">
                        <i class="fas fa-download"></i>
                        Exportar
                    </button>
                </div>
                
                <div class="chart-container">
                    <canvas id="rendimientoChart"></canvas>
                </div>
                
                <div class="summary-stats">
                    <div class="summary-item">
                        <div class="summary-number">1,247</div>
                        <div class="summary-label">Pedidos</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-number">94.2%</div>
                        <div class="summary-label">Éxito</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-number">18.5</div>
                        <div class="summary-label">Tiempo Prom.</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-number">4.8</div>
                        <div class="summary-label">Calificación</div>
                    </div>
                </div>
            </div>

            <!-- Reporte de Deliveries -->
            <div class="report-card">
                <div class="report-header">
                    <div class="report-title">
                        <i class="fas fa-motorcycle"></i>
                        Análisis de Deliveries
                    </div>
                    <button class="export-btn" onclick="exportarReporte('deliveries')">
                        <i class="fas fa-download"></i>
                        Exportar
                    </button>
                </div>
                
                <div class="chart-container">
                    <canvas id="deliveriesChart"></canvas>
                </div>
                
                <div class="summary-stats">
                    <div class="summary-item">
                        <div class="summary-number">24</div>
                        <div class="summary-label">Activos</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-number">156</div>
                        <div class="summary-label">Entregas</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-number">92%</div>
                        <div class="summary-label">Disponibilidad</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-number">4.8</div>
                        <div class="summary-label">Rating Prom.</div>
                    </div>
                </div>
            </div>

            <!-- Reporte Financiero -->
            <div class="report-card">
                <div class="report-header">
                    <div class="report-title">
                        <i class="fas fa-dollar-sign"></i>
                        Análisis Financiero
                    </div>
                    <button class="export-btn" onclick="exportarReporte('financiero')">
                        <i class="fas fa-download"></i>
                        Exportar
                    </button>
                </div>
                
                <div class="chart-container">
                    <canvas id="financieroChart"></canvas>
                </div>
                
                <div class="summary-stats">
                    <div class="summary-item">
                        <div class="summary-number">$89K</div>
                        <div class="summary-label">Ingresos</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-number">+18%</div>
                        <div class="summary-label">Crecimiento</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-number">15%</div>
                        <div class="summary-label">Comisión</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-number">$2.8K</div>
                        <div class="summary-label">Hoy</div>
                    </div>
                </div>
            </div>

            <!-- Reporte de Calificaciones -->
            <div class="report-card">
                <div class="report-header">
                    <div class="report-title">
                        <i class="fas fa-star"></i>
                        Satisfacción del Cliente
                    </div>
                    <button class="export-btn" onclick="exportarReporte('calificaciones')">
                        <i class="fas fa-download"></i>
                        Exportar
                    </button>
                </div>
                
                <div class="chart-container">
                    <canvas id="calificacionesChart"></canvas>
                </div>
                
                <div class="summary-stats">
                    <div class="summary-item">
                        <div class="summary-number">4.8</div>
                        <div class="summary-label">Promedio</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-number">89</div>
                        <div class="summary-label">Este Mes</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-number">78%</div>
                        <div class="summary-label">5 Estrellas</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-number">65%</div>
                        <div class="summary-label">Respuestas</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Panel de Insights -->
        <div class="insights-panel">
            <div class="insights-title">
                <i class="fas fa-lightbulb"></i>
                Insights y Recomendaciones
            </div>
            
            <div class="insights-grid">
                <div class="insight-item insight-positive">
                    <div class="insight-icon">📈</div>
                    <div class="insight-title">Crecimiento Sostenido</div>
                    <div class="insight-description">
                        Los pedidos han aumentado un 18% este mes. La demanda está creciendo consistentemente, especialmente en horarios pico (10-12h y 15-17h).
                    </div>
                </div>
                
                <div class="insight-item insight-warning">
                    <div class="insight-icon">⚠️</div>
                    <div class="insight-title">Zona de Oportunidad</div>
                    <div class="insight-description">
                        3 deliveries tienen calificaciones por debajo de 4.0. Considera programas de capacitación o incentivos para mejorar el servicio.
                    </div>
                </div>
                
                <div class="insight-item insight-info">
                    <div class="insight-icon">💡</div>
                    <div class="insight-title">Optimización de Rutas</div>
                    <div class="insight-description">
                        El tiempo promedio de entrega se redujo 2.1 minutos. La implementación del tracking en tiempo real está mejorando la eficiencia.
                    </div>
                </div>
            </div>
        </div>

        <!-- Acciones Rápidas -->
        <div class="quick-actions">
            <div class="actions-title">
                <i class="fas fa-bolt"></i>
                Acciones Rápidas
            </div>
            <div class="actions-grid">
                <button class="action-btn" onclick="programarReporte()">
                    <i class="fas fa-calendar"></i>
                    Programar Reporte
                </button>
                <button class="action-btn" onclick="configurarAlertas()">
                    <i class="fas fa-bell"></i>
                    Configurar Alertas
                </button>
                <button class="action-btn" onclick="exportarDatos()">
                    <i class="fas fa-database"></i>
                    Exportar Datos
                </button>
                <button class="action-btn" onclick="compartirReporte()">
                    <i class="fas fa-share"></i>
                    Compartir Reporte
                </button>
            </div>
        </div>
    </div>

    <script>
        // PASO 10: Variables globales para gráficos
        let rendimientoChart, deliveriesChart, financieroChart, calificacionesChart;

        // PASO 10: Inicializar página
        document.addEventListener('DOMContentLoaded', function() {
            initCharts();
            setDefaultDates();
        });

        // PASO 10: Configurar fechas por defecto
        function setDefaultDates() {
            const today = new Date();
            const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
            
            document.getElementById('start-date').value = firstDay.toISOString().split('T')[0];
            document.getElementById('end-date').value = today.toISOString().split('T')[0];
        }

        // PASO 10: Inicializar gráficos
        function initCharts() {
            // Gráfico de Rendimiento
            const ctxRendimiento = document.getElementById('rendimientoChart').getContext('2d');
            rendimientoChart = new Chart(ctxRendimiento, {
                type: 'line',
                data: {
                    labels: ['Sem 1', 'Sem 2', 'Sem 3', 'Sem 4'],
                    datasets: [{
                        label: 'Pedidos Completados',
                        data: [280, 320, 350, 297],
                        borderColor: '#FF6B35',
                        backgroundColor: 'rgba(255, 107, 53, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // Gráfico de Deliveries
            const ctxDeliveries = document.getElementById('deliveriesChart').getContext('2d');
            deliveriesChart = new Chart(ctxDeliveries, {
                type: 'bar',
                data: {
                    labels: ['Activos', 'Ocupados', 'Desconectados'],
                    datasets: [{
                        data: [18, 6, 3],
                        backgroundColor: ['#28a745', '#ffc107', '#6c757d'],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // Gráfico Financiero
            const ctxFinanciero = document.getElementById('financieroChart').getContext('2d');
            financieroChart = new Chart(ctxFinanciero, {
                type: 'line',
                data: {
                    labels: ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun'],
                    datasets: [{
                        label: 'Ingresos ($)',
                        data: [65000, 72000, 68000, 81000, 85000, 89000],
                        borderColor: '#6f42c1',
                        backgroundColor: 'rgba(111, 66, 193, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // Gráfico de Calificaciones
            const ctxCalificaciones = document.getElementById('calificacionesChart').getContext('2d');
            calificacionesChart = new Chart(ctxCalificaciones, {
                type: 'doughnut',
                data: {
                    labels: ['5⭐', '4⭐', '3⭐', '2⭐', '1⭐'],
                    datasets: [{
                        data: [69, 18, 8, 3, 2],
                        backgroundColor: [
                            '#28a745',
                            '#17a2b8',
                            '#ffc107',
                            '#fd7e14',
                            '#dc3545'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        // PASO 10: Funciones de reportes
        function generarReporte() {
            const periodo = document.getElementById('period-filter').value;
            const tipo = document.getElementById('report-type').value;
            const fechaInicio = document.getElementById('start-date').value;
            const fechaFin = document.getElementById('end-date').value;

            alert(`📊 Generando Reporte\n\nTipo: ${tipo.toUpperCase()}\nPeríodo: ${periodo}\nFecha inicio: ${fechaInicio}\nFecha fin: ${fechaFin}\n\nProcesando datos...`);
            
            // Simular generación
            setTimeout(() => {
                alert('✅ Reporte generado exitosamente!\n\nEl reporte se ha actualizado con los nuevos parámetros.');
            }, 2000);
        }

        function exportarPDF() {
            alert('📄 Exportando a PDF\n\nGenerando documento PDF con:\n• Gráficos y métricas\n• Tablas de datos\n• Insights y recomendaciones\n• Formato profesional');
        }

        function exportarExcel() {
            alert('📊 Exportando a Excel\n\nGenerando archivo Excel con:\n• Datos en múltiples hojas\n• Gráficos interactivos\n• Tablas dinámicas\n• Formato para análisis');
        }

        function exportarReporte(tipo) {
            alert(`📤 Exportando Reporte: ${tipo.toUpperCase()}\n\nFormatos disponibles:\n• PDF para presentaciones\n• Excel para análisis\n• CSV para datos raw\n• PNG para gráficos`);
        }

        function programarReporte() {
            alert('📅 Programar Reporte Automático\n\nConfigurar:\n• Frecuencia (diario, semanal, mensual)\n• Destinatarios por email\n• Formato de entrega\n• Métricas incluidas');
        }

        function configurarAlertas() {
            alert('🔔 Configurar Alertas\n\nTipos de alertas:\n• Caída en calificaciones\n• Aumento en tiempos de entrega\n• Deliveries inactivos\n• Picos de demanda');
        }

        function exportarDatos() {
            alert('💾 Exportar Base de Datos\n\nExportar:\n• Datos de pedidos\n• Información de usuarios\n• Métricas históricas\n• Logs del sistema');
        }

        function compartirReporte() {
            alert('🔗 Compartir Reporte\n\nOpciones:\n• Generar enlace público\n• Enviar por email\n• Integrar en dashboard\n• Programar envíos');
        }
    </script>
</body>
</html>
