-- Base de datos para RepuMovil Delivery
CREATE DATABASE IF NOT EXISTS repumovil_delivery CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE repumovil_delivery;

-- Tabla de usuarios (repartidores)
CREATE TABLE usuarios (
    id INT PRIMARY KEY AUTO_INCREMENT,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    estado ENUM('pendiente', 'activo', 'suspendido', 'rechazado') DEFAULT 'pendiente',
    fecha_registro TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ultimo_acceso TIMESTAMP NULL,
    verificado BOOLEAN DEFAULT FALSE,
    INDEX idx_email (email),
    INDEX idx_estado (estado)
);

-- Tabla de datos personales
CREATE TABLE datos_personales (
    id INT PRIMARY KEY AUTO_INCREMENT,
    usuario_id INT NOT NULL,
    nombre_completo VARCHAR(150) NOT NULL,
    dni VARCHAR(20) NOT NULL UNIQUE,
    fecha_nacimiento DATE NOT NULL,
    telefono VARCHAR(20) NOT NULL,
    direccion_completa TEXT NOT NULL,
    fecha_creacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (usuario_id) REFERENCES usuarios(id) ON DELETE CASCADE,
    INDEX idx_dni (dni),
    INDEX idx_usuario (usuario_id)
);

-- Tabla de datos de pago
CREATE TABLE datos_pago (
    id INT PRIMARY KEY AUTO_INCREMENT,
    usuario_id INT NOT NULL,
    cbu_alias VARCHAR(50) NOT NULL,
    cuil_cuit VARCHAR(15) NULL,
    banco VARCHAR(100) NULL,
    tipo_cuenta ENUM('caja_ahorro', 'cuenta_corriente') DEFAULT 'caja_ahorro',
    fecha_creacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (usuario_id) REFERENCES usuarios(id) ON DELETE CASCADE,
    INDEX idx_usuario (usuario_id)
);

-- Tabla de vehículos
CREATE TABLE vehiculos (
    id INT PRIMARY KEY AUTO_INCREMENT,
    usuario_id INT NOT NULL,
    tipo_vehiculo ENUM('bicicleta', 'moto', 'auto') NOT NULL,
    marca VARCHAR(50) NULL,
    modelo VARCHAR(50) NULL,
    patente VARCHAR(10) NULL,
    licencia_conducir VARCHAR(20) NULL,
    vencimiento_licencia DATE NULL,
    seguro_vigente BOOLEAN DEFAULT FALSE,
    vencimiento_seguro DATE NULL,
    fecha_creacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (usuario_id) REFERENCES usuarios(id) ON DELETE CASCADE,
    INDEX idx_usuario (usuario_id),
    INDEX idx_tipo (tipo_vehiculo)
);

-- Tabla de documentos/fotos
CREATE TABLE documentos (
    id INT PRIMARY KEY AUTO_INCREMENT,
    usuario_id INT NOT NULL,
    tipo_documento ENUM('dni_frente', 'dni_dorso', 'licencia', 'selfie_dni', 'seguro', 'antecedentes') NOT NULL,
    nombre_archivo VARCHAR(255) NOT NULL,
    ruta_archivo VARCHAR(500) NOT NULL,
    tamaño_archivo INT NOT NULL,
    tipo_mime VARCHAR(100) NOT NULL,
    verificado BOOLEAN DEFAULT FALSE,
    fecha_subida TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (usuario_id) REFERENCES usuarios(id) ON DELETE CASCADE,
    INDEX idx_usuario (usuario_id),
    INDEX idx_tipo (tipo_documento)
);

-- Tabla de declaraciones juradas
CREATE TABLE declaraciones_juradas (
    id INT PRIMARY KEY AUTO_INCREMENT,
    usuario_id INT NOT NULL,
    acepta_terminos BOOLEAN NOT NULL DEFAULT FALSE,
    acepta_responsabilidad BOOLEAN NOT NULL DEFAULT FALSE,
    acepta_datos_personales BOOLEAN NOT NULL DEFAULT FALSE,
    ip_registro VARCHAR(45) NOT NULL,
    user_agent TEXT NOT NULL,
    fecha_aceptacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (usuario_id) REFERENCES usuarios(id) ON DELETE CASCADE,
    INDEX idx_usuario (usuario_id)
);

-- Tabla de pedidos
CREATE TABLE pedidos (
    id INT PRIMARY KEY AUTO_INCREMENT,
    repartidor_id INT NULL,
    cliente_nombre VARCHAR(100) NOT NULL,
    cliente_telefono VARCHAR(20) NOT NULL,
    direccion_origen TEXT NOT NULL,
    direccion_destino TEXT NOT NULL,
    descripcion_pedido TEXT NOT NULL,
    valor_pedido DECIMAL(10,2) NOT NULL,
    comision_repartidor DECIMAL(10,2) NOT NULL,
    estado ENUM('pendiente', 'asignado', 'en_camino', 'entregado', 'cancelado') DEFAULT 'pendiente',
    fecha_pedido TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    fecha_asignacion TIMESTAMP NULL,
    fecha_entrega TIMESTAMP NULL,
    calificacion_cliente INT NULL CHECK (calificacion_cliente BETWEEN 1 AND 5),
    comentario_cliente TEXT NULL,
    FOREIGN KEY (repartidor_id) REFERENCES usuarios(id) ON DELETE SET NULL,
    INDEX idx_repartidor (repartidor_id),
    INDEX idx_estado (estado),
    INDEX idx_fecha (fecha_pedido)
);

-- Tabla de ganancias
CREATE TABLE ganancias (
    id INT PRIMARY KEY AUTO_INCREMENT,
    repartidor_id INT NOT NULL,
    pedido_id INT NOT NULL,
    monto DECIMAL(10,2) NOT NULL,
    fecha_ganancia TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    pagado BOOLEAN DEFAULT FALSE,
    fecha_pago TIMESTAMP NULL,
    FOREIGN KEY (repartidor_id) REFERENCES usuarios(id) ON DELETE CASCADE,
    FOREIGN KEY (pedido_id) REFERENCES pedidos(id) ON DELETE CASCADE,
    INDEX idx_repartidor (repartidor_id),
    INDEX idx_pagado (pagado)
);

-- Tabla de ubicaciones en tiempo real
CREATE TABLE ubicaciones (
    id INT PRIMARY KEY AUTO_INCREMENT,
    repartidor_id INT NOT NULL,
    latitud DECIMAL(10, 8) NOT NULL,
    longitud DECIMAL(11, 8) NOT NULL,
    disponible BOOLEAN DEFAULT TRUE,
    fecha_actualizacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (repartidor_id) REFERENCES usuarios(id) ON DELETE CASCADE,
    INDEX idx_repartidor (repartidor_id),
    INDEX idx_disponible (disponible)
);

-- Tabla de calificaciones
CREATE TABLE calificaciones (
    id INT PRIMARY KEY AUTO_INCREMENT,
    repartidor_id INT NOT NULL,
    pedido_id INT NOT NULL,
    calificacion INT NOT NULL CHECK (calificacion BETWEEN 1 AND 5),
    comentario TEXT NULL,
    fecha_calificacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (repartidor_id) REFERENCES usuarios(id) ON DELETE CASCADE,
    FOREIGN KEY (pedido_id) REFERENCES pedidos(id) ON DELETE CASCADE,
    INDEX idx_repartidor (repartidor_id)
);

-- Insertar usuario admin por defecto
INSERT INTO usuarios (email, password, estado, verificado) VALUES 
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'activo', TRUE);

-- Crear directorios para uploads (esto se hará en PHP)
-- uploads/dni/
-- uploads/licencias/
-- uploads/selfies/
-- uploads/seguros/
-- uploads/antecedentes/
