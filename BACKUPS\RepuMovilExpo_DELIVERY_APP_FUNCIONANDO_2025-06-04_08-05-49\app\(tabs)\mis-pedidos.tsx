import React, { useState, useEffect } from 'react';
import {
  SafeAreaView,
  ScrollView,
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';

interface PedidoItem {
  nombre: string;
  cantidad: number;
  precio_unitario: number;
}

interface Pedido {
  id: number;
  estado: 'pendiente' | 'asignado' | 'en_camino' | 'entregado';
  created_at: string;
  total: number;
  direccion_entrega: string;
  repartidor_nombre?: string;
  items: PedidoItem[];
}

export default function MisPedidosScreen() {
  const router = useRouter();
  const [pedidos, setPedidos] = useState<Pedido[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadPedidos();
  }, []);

  const loadPedidos = async () => {
    try {
      setLoading(true);
      // Simular carga de pedidos
      // En producción, aquí iría la llamada a la API
      setTimeout(() => {
        setPedidos([
          {
            id: 1001,
            estado: 'en_camino',
            created_at: '2025-01-02T14:30:00Z',
            total: 13500,
            direccion_entrega: 'Av. Libertador 1234, San Juan Centro',
            repartidor_nombre: 'Juan Pérez',
            items: [
              { nombre: 'Filtro de Aceite Bosch', cantidad: 2, precio_unitario: 2500 },
              { nombre: 'Pastillas de Freno Brembo', cantidad: 1, precio_unitario: 8500 }
            ]
          },
          {
            id: 1000,
            estado: 'entregado',
            created_at: '2025-01-01T10:15:00Z',
            total: 5200,
            direccion_entrega: 'Calle San Martín 567, Rivadavia',
            repartidor_nombre: 'María González',
            items: [
              { nombre: 'Aceite Motor 5W30', cantidad: 1, precio_unitario: 5200 }
            ]
          },
          {
            id: 999,
            estado: 'asignado',
            created_at: '2025-01-02T16:45:00Z',
            total: 3400,
            direccion_entrega: 'Barrio Los Olivos, Chimbas',
            repartidor_nombre: 'Carlos Rodríguez',
            items: [
              { nombre: 'Bujías NGK', cantidad: 4, precio_unitario: 850 }
            ]
          }
        ]);
        setLoading(false);
      }, 1000);
    } catch (error) {
      console.error('Error loading pedidos:', error);
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadPedidos();
    setRefreshing(false);
  };

  const getEstadoInfo = (estado: string) => {
    const estados = {
      'pendiente': { text: '⏳ Pendiente', color: '#ffc107', bgColor: '#fff3cd' },
      'asignado': { text: '🚀 Asignado', color: '#28a745', bgColor: '#d4edda' },
      'en_camino': { text: '🏍️ En Camino', color: '#007bff', bgColor: '#cce7ff' },
      'entregado': { text: '✅ Entregado', color: '#17a2b8', bgColor: '#d1ecf1' }
    };
    return estados[estado as keyof typeof estados] || { text: estado, color: '#666', bgColor: '#f8f9fa' };
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('es-AR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const verDetalle = (pedido: Pedido) => {
    const itemsText = pedido.items.map(item => 
      `• ${item.nombre} (x${item.cantidad}) - $${item.precio_unitario.toFixed(2)}`
    ).join('\n');

    Alert.alert(
      `📦 Pedido #${pedido.id}`,
      `📅 Fecha: ${formatDate(pedido.created_at)}\n📍 Dirección: ${pedido.direccion_entrega}\n🏍️ Repartidor: ${pedido.repartidor_nombre || 'Asignando...'}\n\n📋 Productos:\n${itemsText}\n\n💰 Total: $${pedido.total.toFixed(2)}`,
      [{ text: 'Cerrar' }]
    );
  };

  const rastrearPedido = (pedidoId: number) => {
    Alert.alert(
      '🗺️ Rastreo en Tiempo Real',
      `Próximamente podrás ver la ubicación del repartidor en tiempo real para el pedido #${pedidoId}`,
      [
        { text: 'Cerrar' },
        { text: 'Ver Mapa', onPress: () => router.push('/tracking-mapa') }
      ]
    );
  };

  const calificarPedido = (pedidoId: number) => {
    Alert.alert(
      '⭐ Calificar Entrega',
      'Calificá tu experiencia con RepuMovil:',
      [
        { text: 'Cancelar', style: 'cancel' },
        { text: '⭐ 1 Estrella', onPress: () => enviarCalificacion(pedidoId, 1) },
        { text: '⭐⭐ 2 Estrellas', onPress: () => enviarCalificacion(pedidoId, 2) },
        { text: '⭐⭐⭐ 3 Estrellas', onPress: () => enviarCalificacion(pedidoId, 3) },
        { text: '⭐⭐⭐⭐ 4 Estrellas', onPress: () => enviarCalificacion(pedidoId, 4) },
        { text: '⭐⭐⭐⭐⭐ 5 Estrellas', onPress: () => enviarCalificacion(pedidoId, 5) }
      ]
    );
  };

  const enviarCalificacion = (pedidoId: number, rating: number) => {
    Alert.alert(
      '¡Gracias! 🙏',
      `Tu calificación de ${rating} estrella${rating > 1 ? 's' : ''} nos ayuda a mejorar el servicio.`,
      [{ text: 'Cerrar' }]
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#FF6B35" />
          <Text style={styles.loadingText}>Cargando tus pedidos...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="white" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Mis Pedidos 📦</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView 
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} colors={['#FF6B35']} />
        }
      >
        {/* Motivational Header */}
        <View style={styles.motivationalHeader}>
          <Text style={styles.motivationalText}>
            "SEGUÍ TUS PEDIDOS EN TIEMPO REAL"
          </Text>
        </View>

        {pedidos.length === 0 ? (
          <View style={styles.emptyState}>
            <Ionicons name="receipt-outline" size={80} color="#FF6B35" />
            <Text style={styles.emptyTitle}>No tenés pedidos aún</Text>
            <Text style={styles.emptySubtitle}>¡Empezá a comprar repuestos para tu taller!</Text>
            <TouchableOpacity style={styles.shopButton} onPress={() => router.push('/catalogo')}>
              <Ionicons name="search" size={20} color="white" />
              <Text style={styles.shopButtonText}>Explorar Catálogo</Text>
            </TouchableOpacity>
          </View>
        ) : (
          pedidos.map(pedido => {
            const estadoInfo = getEstadoInfo(pedido.estado);
            
            return (
              <View key={pedido.id} style={styles.pedidoCard}>
                {/* Header */}
                <View style={styles.pedidoHeader}>
                  <Text style={styles.pedidoId}>
                    <Ionicons name="receipt" size={16} color="#2c3e50" />
                    {' '}Pedido #{pedido.id}
                  </Text>
                  <View style={[styles.estadoBadge, { backgroundColor: estadoInfo.bgColor }]}>
                    <Text style={[styles.estadoText, { color: estadoInfo.color }]}>
                      {estadoInfo.text}
                    </Text>
                  </View>
                </View>

                {/* Info Grid */}
                <View style={styles.infoGrid}>
                  <View style={styles.infoItem}>
                    <Ionicons name="calendar" size={16} color="#FF6B35" />
                    <Text style={styles.infoText}>{formatDate(pedido.created_at)}</Text>
                  </View>
                  <View style={styles.infoItem}>
                    <Ionicons name="cash" size={16} color="#FF6B35" />
                    <Text style={styles.infoText}>${pedido.total.toFixed(2)}</Text>
                  </View>
                  <View style={styles.infoItem}>
                    <Ionicons name="location" size={16} color="#FF6B35" />
                    <Text style={styles.infoText} numberOfLines={1}>{pedido.direccion_entrega}</Text>
                  </View>
                  <View style={styles.infoItem}>
                    <Ionicons name="bicycle" size={16} color="#FF6B35" />
                    <Text style={styles.infoText}>{pedido.repartidor_nombre || 'Asignando...'}</Text>
                  </View>
                </View>

                {/* Items Preview */}
                <View style={styles.itemsPreview}>
                  <Text style={styles.itemsTitle}>
                    <Ionicons name="list" size={14} color="#666" />
                    {' '}Productos ({pedido.items.length})
                  </Text>
                  <View style={styles.itemsList}>
                    {pedido.items.slice(0, 2).map((item, index) => (
                      <Text key={index} style={styles.itemText}>
                        • {item.nombre} (x{item.cantidad})
                      </Text>
                    ))}
                    {pedido.items.length > 2 && (
                      <Text style={styles.moreItems}>
                        +{pedido.items.length - 2} producto{pedido.items.length - 2 > 1 ? 's' : ''} más
                      </Text>
                    )}
                  </View>
                </View>

                {/* Actions */}
                <View style={styles.actions}>
                  <TouchableOpacity 
                    style={styles.actionButton}
                    onPress={() => verDetalle(pedido)}
                  >
                    <Ionicons name="eye" size={16} color="#FF6B35" />
                    <Text style={styles.actionText}>Ver Detalle</Text>
                  </TouchableOpacity>

                  {(pedido.estado === 'asignado' || pedido.estado === 'en_camino') && (
                    <TouchableOpacity 
                      style={[styles.actionButton, styles.trackButton]}
                      onPress={() => rastrearPedido(pedido.id)}
                    >
                      <Ionicons name="navigate" size={16} color="white" />
                      <Text style={[styles.actionText, { color: 'white' }]}>Rastrear</Text>
                    </TouchableOpacity>
                  )}

                  {pedido.estado === 'entregado' && (
                    <TouchableOpacity 
                      style={[styles.actionButton, styles.rateButton]}
                      onPress={() => calificarPedido(pedido.id)}
                    >
                      <Ionicons name="star" size={16} color="white" />
                      <Text style={[styles.actionText, { color: 'white' }]}>Calificar</Text>
                    </TouchableOpacity>
                  )}
                </View>
              </View>
            );
          })
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f7fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#FF6B35',
    paddingHorizontal: 20,
    paddingVertical: 15,
    paddingTop: 50,
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
  },
  placeholder: {
    width: 34,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  motivationalHeader: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    marginBottom: 20,
    alignItems: 'center',
  },
  motivationalText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    textAlign: 'center',
  },
  emptyState: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 40,
    alignItems: 'center',
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginTop: 20,
    marginBottom: 10,
  },
  emptySubtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 30,
  },
  shopButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FF6B35',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  shopButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  pedidoCard: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  pedidoHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
    paddingBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  pedidoId: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  estadoBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
  },
  estadoText: {
    fontSize: 12,
    fontWeight: 'bold',
    textTransform: 'uppercase',
  },
  infoGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 15,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '50%',
    marginBottom: 8,
    gap: 8,
  },
  infoText: {
    fontSize: 14,
    color: '#666',
    flex: 1,
  },
  itemsPreview: {
    backgroundColor: '#f8f9fa',
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
  },
  itemsTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#666',
    marginBottom: 8,
  },
  itemsList: {
    gap: 4,
  },
  itemText: {
    fontSize: 13,
    color: '#666',
  },
  moreItems: {
    fontSize: 13,
    color: '#FF6B35',
    fontStyle: 'italic',
  },
  actions: {
    flexDirection: 'row',
    gap: 10,
    justifyContent: 'flex-end',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#FF6B35',
    gap: 4,
  },
  trackButton: {
    backgroundColor: '#007bff',
    borderColor: '#007bff',
  },
  rateButton: {
    backgroundColor: '#28a745',
    borderColor: '#28a745',
  },
  actionText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#FF6B35',
  },
});
