<?php
// Mostrar todos los errores
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Configuración de la base de datos
$host = 'localhost';
$dbname = 'autoconnect_db';
$username = 'root';
$password = '';

echo "<h1>Reparación de Usuario de Taller</h1>";

try {
    // Conectar a la base de datos
    $conn = new PDO("mysql:host=$host", $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Verificar si existe la base de datos
    $stmt = $conn->query("SELECT COUNT(*) FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '$dbname'");
    $dbExists = (bool)$stmt->fetchColumn();
    
    if (!$dbExists) {
        echo "<p>La base de datos '$dbname' no existe. Creándola...</p>";
        $conn->exec("CREATE DATABASE IF NOT EXISTS $dbname CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "<p style='color: green;'>Base de datos '$dbname' creada exitosamente.</p>";
    }
    
    // Conectar a la base de datos específica
    $conn = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p>Conexión a la base de datos exitosa.</p>";
    
    // Verificar si existe la tabla de roles
    $stmt = $conn->query("SHOW TABLES LIKE 'roles'");
    if ($stmt->rowCount() == 0) {
        echo "<p>Creando tabla de roles...</p>";
        $conn->exec("CREATE TABLE roles (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(50) NOT NULL,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");
        
        // Insertar roles básicos
        $conn->exec("INSERT INTO roles (id, name, description) VALUES 
            (1, 'root', 'Administrador del sistema'),
            (2, 'root', 'Taller mecánico'),
            (3, 'root', 'Proveedor de repuestos')");
        
        echo "<p style='color: green;'>Tabla de roles creada e inicializada.</p>";
    } else {
        echo "<p>Tabla de roles ya existe.</p>";
        
        // Verificar si existen los roles necesarios
        $stmt = $conn->query("SELECT * FROM roles WHERE name = 'workshop'");
        if ($stmt->rowCount() == 0) {
            echo "<p>Creando rol de taller...</p>";
            $conn->exec("INSERT INTO roles (id, name, description) VALUES 
                (2, 'root', 'Taller mecánico')");
            echo "<p style='color: green;'>Rol de taller creado.</p>";
        }
    }
    
    // Verificar si existe la tabla de usuarios
    $stmt = $conn->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() == 0) {
        echo "<p>Creando tabla de usuarios...</p>";
        $conn->exec("CREATE TABLE users (
            id INT PRIMARY KEY AUTO_INCREMENT,
            username VARCHAR(50) NOT NULL UNIQUE,
            email VARCHAR(100) NOT NULL UNIQUE,
            password VARCHAR(255) NOT NULL,
            role_id INT NOT NULL,
            status ENUM('active', 'root', 'suspended') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_login TIMESTAMP NULL,
            FOREIGN KEY (role_id) REFERENCES roles(id)
        )");
        
        echo "<p style='color: green;'>Tabla de usuarios creada.</p>";
    } else {
        echo "<p>Tabla de usuarios ya existe.</p>";
    }
    
    // Verificar si existe la tabla de talleres
    $stmt = $conn->query("SHOW TABLES LIKE 'workshops'");
    if ($stmt->rowCount() == 0) {
        echo "<p>Creando tabla de talleres...</p>";
        $conn->exec("CREATE TABLE workshops (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT NOT NULL UNIQUE,
            name VARCHAR(100) NOT NULL,
            location VARCHAR(255),
            phone VARCHAR(20),
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id)
        )");
        
        echo "<p style='color: green;'>Tabla de talleres creada.</p>";
    } else {
        echo "<p>Tabla de talleres ya existe.</p>";
    }
    
    // Verificar si existe el usuario de taller
    $stmt = $conn->query("SELECT * FROM users WHERE username = 'taller1'");
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        echo "<p>Usuario 'taller1' no encontrado. Creándolo...</p>";
        
        // Crear usuario de taller
        $workshopPassword = password_hash('taller123', PASSWORD_DEFAULT);
        $stmt = $conn->prepare("INSERT INTO users (username, email, password, role_id, status) VALUES 
            ('taller1', '<EMAIL>', :password, 2, 'active')");
        
        if ($stmt->execute(['password' => $workshopPassword])) {
            $workshopUserId = $conn->lastInsertId();
            echo "<p style='color: green;'>Usuario de taller creado exitosamente.</p>";
            
            // Insertar datos del taller
            $stmt = $conn->prepare("INSERT INTO workshops (user_id, name, location, phone, description) VALUES 
                (:user_id, 'Taller Mecánico Ejemplo', 'Calle Ejemplo 123, Ciudad', '************', 
                'Taller especializado en reparación de motores y sistemas de frenos')");
            
            if ($stmt->execute(['user_id' => $workshopUserId])) {
                echo "<p style='color: green;'>Datos del taller creados exitosamente.</p>";
            } else {
                echo "<p style='color: red;'>Error al crear datos del taller.</p>";
            }
        } else {
            echo "<p style='color: red;'>Error al crear usuario de taller.</p>";
        }
    } else {
        echo "<p>Usuario 'taller1' encontrado (ID: {$user['id']}).</p>";
        
        // Verificar si el usuario está activo
        if ($user['status'] !== 'active') {
            echo "<p>El usuario no está activo. Activándolo...</p>";
            $stmt = $conn->prepare("UPDATE users SET status = 'active' WHERE id = :id");
            if ($stmt->execute(['id' => $user['id']])) {
                echo "<p style='color: green;'>Usuario activado exitosamente.</p>";
            }
        } else {
            echo "<p>El usuario ya está activo.</p>";
        }
        
        // Verificar si tiene el rol correcto
        if ($user['role_id'] != 2) {
            echo "<p>El usuario tiene un rol incorrecto. Corrigiendo...</p>";
            $stmt = $conn->prepare("UPDATE users SET role_id = 2 WHERE id = :id");
            if ($stmt->execute(['id' => $user['id']])) {
                echo "<p style='color: green;'>Rol de usuario corregido exitosamente.</p>";
            }
        }
        
        // Verificar si existen datos del taller
        $stmt = $conn->prepare("SELECT * FROM workshops WHERE user_id = :user_id");
        $stmt->execute(['user_id' => $user['id']]);
        $workshop = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$workshop) {
            echo "<p>No se encontraron datos del taller para este usuario. Creándolos...</p>";
            $stmt = $conn->prepare("INSERT INTO workshops (user_id, name, location, phone, description) VALUES 
                (:user_id, 'Taller Mecánico Ejemplo', 'Calle Ejemplo 123, Ciudad', '************', 
                'Taller especializado en reparación de motores y sistemas de frenos')");
            
            if ($stmt->execute(['user_id' => $user['id']])) {
                echo "<p style='color: green;'>Datos del taller creados exitosamente.</p>";
            } else {
                echo "<p style='color: red;'>Error al crear datos del taller.</p>";
            }
        } else {
            echo "<p>Datos del taller encontrados.</p>";
        }
    }
    
    // Verificar la función de autenticación
    echo "<p>Probando autenticación...</p>";
    
    // Obtener el usuario de la base de datos
    $stmt = $conn->prepare("SELECT u.*, r.name as role_name 
                           FROM users u 
                           JOIN roles r ON u.role_id = r.id 
                           WHERE u.username = :username AND u.status = 'active'");
    $stmt->execute(['username' => 'taller1']);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user) {
        // Verificar la contraseña
        if (password_verify('taller123', $user['password'])) {
            echo "<p style='color: green;'>¡Autenticación exitosa! El usuario y contraseña son correctos.</p>";
        } else {
            echo "<p style='color: red;'>Error de autenticación: La contraseña es incorrecta.</p>";
            
            // Actualizar la contraseña
            echo "<p>Actualizando contraseña...</p>";
            $newPassword = password_hash('taller123', PASSWORD_DEFAULT);
            $stmt = $conn->prepare("UPDATE users SET password = :password WHERE username = 'taller1'");
            if ($stmt->execute(['password' => $newPassword])) {
                echo "<p style='color: green;'>Contraseña actualizada exitosamente.</p>";
            }
        }
    } else {
        echo "<p style='color: red;'>Error de autenticación: No se pudo encontrar el usuario activo.</p>";
    }
    
    echo "<hr>";
    echo "<h2>Resumen</h2>";
    echo "<p>Se ha verificado y reparado la configuración del usuario de taller.</p>";
    echo "<p><strong>Credenciales para iniciar sesión:</strong></p>";
    echo "<ul>";
    echo "<li>Usuario: taller1</li>";
    echo "<li>Contraseña: taller123</li>";
    echo "</ul>";
    
    echo "<p><a href='login_simple.php' style='display: inline-block; padding: 10px 20px; background-color: #4CAF50; color: white; text-decoration: none; border-radius: 5px;'>Ir a la página de inicio de sesión</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Error de base de datos: " . $e->getMessage() . "</p>";
}
?>