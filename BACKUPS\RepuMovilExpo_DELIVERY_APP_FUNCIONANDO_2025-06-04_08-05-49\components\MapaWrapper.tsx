import React from 'react';
import { Platform } from 'react-native';

// Importar el componente seguro por defecto
import MapaComponentSafe from './MapaComponentSafe';

// Tipos para compatibilidad
interface Coordinates {
  latitude: number;
  longitude: number;
}

interface LocationData {
  coords: Coordinates;
  timestamp: number;
  accuracy?: number;
}

interface MapaWrapperProps {
  clienteLocation?: Coordinates;
  pedidoId?: string;
  onLocationUpdate?: (location: LocationData) => void;
  showRoute?: boolean;
}

/**
 * Wrapper que siempre usa el componente seguro
 * Evita problemas de importación con react-native-maps
 */
const MapaWrapper: React.FC<MapaWrapperProps> = (props) => {
  // Siempre usar el componente seguro
  return <MapaComponentSafe {...props} />;
};

export default MapaWrapper;
export type { Coordinates, LocationData, MapaWrapperProps };
