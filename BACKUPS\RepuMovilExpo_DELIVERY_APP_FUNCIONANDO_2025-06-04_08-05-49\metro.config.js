const { getDefaultConfig } = require('expo/metro-config');

/** @type {import('expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname);

// Resolver para evitar problemas con react-native-maps en web
config.resolver.platforms = ['ios', 'android', 'native', 'web'];

// Alias para componentes problemáticos
config.resolver.alias = {
  'react-native-maps': require.resolve('./components/MapaComponentSafe.tsx'),
};

// Excluir módulos problemáticos en web
config.resolver.blockList = [
  /node_modules\/react-native-maps\/.*\/codegenNativeCommands/,
];

module.exports = config;
