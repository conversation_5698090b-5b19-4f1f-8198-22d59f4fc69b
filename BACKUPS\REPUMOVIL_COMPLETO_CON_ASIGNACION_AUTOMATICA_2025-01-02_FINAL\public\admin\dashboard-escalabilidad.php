<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Dashboard de Escalabilidad - RepuMovil</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .dashboard {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .metric-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            transition: transform 0.3s ease;
        }
        
        .metric-card:hover {
            transform: translateY(-5px);
        }
        
        .metric-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .metric-icon {
            font-size: 2rem;
            margin-right: 15px;
        }
        
        .metric-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
        }
        
        .metric-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: #4CAF50;
            margin-bottom: 10px;
        }
        
        .metric-subtitle {
            color: #666;
            font-size: 0.9rem;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-good { background: #4CAF50; }
        .status-warning { background: #FF9800; }
        .status-critical { background: #F44336; }
        
        .charts-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .chart-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        
        .chart-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 20px;
            color: #333;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #45a049);
            transition: width 0.3s ease;
        }
        
        .alerts-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        
        .alert-item {
            display: flex;
            align-items: center;
            padding: 15px;
            margin: 10px 0;
            border-radius: 10px;
            background: #f8f9fa;
        }
        
        .alert-icon {
            font-size: 1.5rem;
            margin-right: 15px;
        }
        
        .refresh-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: #4CAF50;
            color: white;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
        }
        
        .refresh-btn:hover {
            background: #45a049;
            transform: scale(1.1);
        }
        
        @media (max-width: 768px) {
            .charts-section {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <div class="header">
            <h1>🚀 Dashboard de Escalabilidad RepuMovil</h1>
            <p>Monitoreo en tiempo real - San Juan, Argentina</p>
        </div>
        
        <!-- Métricas Principales -->
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-header">
                    <span class="metric-icon">📦</span>
                    <span class="metric-title">Pedidos Hoy</span>
                </div>
                <div class="metric-value" id="pedidos-hoy">47</div>
                <div class="metric-subtitle">
                    <span class="status-indicator status-good"></span>
                    +23% vs ayer
                </div>
            </div>
            
            <div class="metric-card">
                <div class="metric-header">
                    <span class="metric-icon">🏍️</span>
                    <span class="metric-title">Repartidores Activos</span>
                </div>
                <div class="metric-value" id="repartidores-activos">8</div>
                <div class="metric-subtitle">
                    <span class="status-indicator status-good"></span>
                    de 12 registrados
                </div>
            </div>
            
            <div class="metric-card">
                <div class="metric-header">
                    <span class="metric-icon">⚡</span>
                    <span class="metric-title">Tiempo Asignación</span>
                </div>
                <div class="metric-value" id="tiempo-asignacion">18s</div>
                <div class="metric-subtitle">
                    <span class="status-indicator status-good"></span>
                    Objetivo: < 30s
                </div>
            </div>
            
            <div class="metric-card">
                <div class="metric-header">
                    <span class="metric-icon">💰</span>
                    <span class="metric-title">Ingresos Hoy</span>
                </div>
                <div class="metric-value" id="ingresos-hoy">$12,450</div>
                <div class="metric-subtitle">
                    <span class="status-indicator status-good"></span>
                    +18% vs ayer
                </div>
            </div>
            
            <div class="metric-card">
                <div class="metric-header">
                    <span class="metric-icon">🎯</span>
                    <span class="metric-title">Éxito Asignación</span>
                </div>
                <div class="metric-value" id="exito-asignacion">97%</div>
                <div class="metric-subtitle">
                    <span class="status-indicator status-good"></span>
                    Objetivo: > 95%
                </div>
            </div>
            
            <div class="metric-card">
                <div class="metric-header">
                    <span class="metric-icon">🌐</span>
                    <span class="metric-title">Cobertura</span>
                </div>
                <div class="metric-value" id="cobertura">85%</div>
                <div class="metric-subtitle">
                    <span class="status-indicator status-warning"></span>
                    San Juan Centro
                </div>
            </div>
        </div>
        
        <!-- Gráficos -->
        <div class="charts-section">
            <div class="chart-card">
                <div class="chart-title">📈 Capacidad del Sistema</div>
                <div>
                    <p>Pedidos Actuales vs Capacidad Máxima</p>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 47%"></div>
                    </div>
                    <small>47 de 100 pedidos/día (Capacidad actual)</small>
                </div>
                
                <div style="margin-top: 20px;">
                    <p>Repartidores Utilizados</p>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 67%"></div>
                    </div>
                    <small>8 de 12 repartidores activos</small>
                </div>
                
                <div style="margin-top: 20px;">
                    <p>Performance del Servidor</p>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 23%"></div>
                    </div>
                    <small>CPU: 23% | RAM: 45% | Disco: 12%</small>
                </div>
            </div>
            
            <div class="chart-card">
                <div class="chart-title">🎯 Métricas de Calidad</div>
                <div>
                    <p>Tiempo de Respuesta API</p>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 15%"></div>
                    </div>
                    <small>150ms promedio (Objetivo: < 200ms)</small>
                </div>
                
                <div style="margin-top: 20px;">
                    <p>Satisfacción Clientes</p>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 92%"></div>
                    </div>
                    <small>4.6/5 estrellas promedio</small>
                </div>
                
                <div style="margin-top: 20px;">
                    <p>Uptime del Sistema</p>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 99%"></div>
                    </div>
                    <small>99.8% disponibilidad (30 días)</small>
                </div>
            </div>
        </div>
        
        <!-- Alertas y Notificaciones -->
        <div class="alerts-section">
            <h3>🔔 Alertas del Sistema</h3>
            
            <div class="alert-item">
                <span class="alert-icon">✅</span>
                <div>
                    <strong>Sistema funcionando correctamente</strong><br>
                    <small>Todas las métricas dentro de los parámetros normales</small>
                </div>
            </div>
            
            <div class="alert-item">
                <span class="alert-icon">⚠️</span>
                <div>
                    <strong>Zona Rivadavia con baja cobertura</strong><br>
                    <small>Solo 2 repartidores activos en la zona. Considerar reclutamiento.</small>
                </div>
            </div>
            
            <div class="alert-item">
                <span class="alert-icon">📈</span>
                <div>
                    <strong>Pico de demanda detectado</strong><br>
                    <small>Incremento del 35% en pedidos entre 18:00-20:00. Sistema manejando correctamente.</small>
                </div>
            </div>
        </div>
    </div>
    
    <button class="refresh-btn" onclick="actualizarDatos()">🔄</button>
    
    <script>
        // Actualizar datos en tiempo real
        function actualizarDatos() {
            // Simular actualización de métricas
            const pedidos = Math.floor(Math.random() * 20) + 40;
            const repartidores = Math.floor(Math.random() * 4) + 6;
            const tiempo = Math.floor(Math.random() * 15) + 10;
            const ingresos = Math.floor(Math.random() * 5000) + 10000;
            
            document.getElementById('pedidos-hoy').textContent = pedidos;
            document.getElementById('repartidores-activos').textContent = repartidores;
            document.getElementById('tiempo-asignacion').textContent = tiempo + 's';
            document.getElementById('ingresos-hoy').textContent = '$' + ingresos.toLocaleString();
            
            console.log('📊 Datos actualizados:', new Date().toLocaleTimeString());
        }
        
        // Actualizar cada 30 segundos
        setInterval(actualizarDatos, 30000);
        
        // Animación inicial
        window.addEventListener('load', function() {
            const cards = document.querySelectorAll('.metric-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'all 0.5s ease';
                    
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 100);
            });
        });
    </script>
</body>
</html>
