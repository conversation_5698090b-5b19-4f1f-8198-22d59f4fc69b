import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';

const COLORS = {
  primary: '#FF6B35',
  red: '#E53E3E',
  white: '#FFFFFF',
  dark: '#2D3748',
  lightGray: '#F7FAFC',
};

const MapaScreen = () => {
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Mapa de Entregas</Text>
      </View>
      
      <View style={styles.mapPlaceholder}>
        <MaterialIcons name="map" size={80} color="#ccc" />
        <Text style={styles.placeholderText}>Mapa en desarrollo</Text>
        <Text style={styles.placeholderSubtext}>
          Aquí verás tu ubicación y los pedidos cercanos
        </Text>
      </View>
      
      <View style={styles.controls}>
        <TouchableOpacity style={styles.controlButton}>
          <MaterialIcons name="my-location" size={24} color={COLORS.white} />
          <Text style={styles.controlText}>Mi Ubicación</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.controlButton}>
          <MaterialIcons name="navigation" size={24} color={COLORS.white} />
          <Text style={styles.controlText}>Navegar</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.lightGray,
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 20,
    backgroundColor: COLORS.white,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '800',
    color: COLORS.dark,
  },
  mapPlaceholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
  },
  placeholderText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#999',
    marginTop: 20,
  },
  placeholderSubtext: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginTop: 10,
    paddingHorizontal: 40,
  },
  controls: {
    flexDirection: 'row',
    padding: 20,
    gap: 15,
  },
  controlButton: {
    flex: 1,
    backgroundColor: COLORS.red,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 15,
    borderRadius: 10,
  },
  controlText: {
    color: COLORS.white,
    fontWeight: '600',
    marginLeft: 8,
  },
});

export default MapaScreen;
