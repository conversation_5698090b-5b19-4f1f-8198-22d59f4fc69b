<?php
session_start();
require_once 'config.php';

// Verificar si es admin (simplificado para demo)
$is_admin = isset($_GET['admin']) && $_GET['admin'] === 'true';

try {
    $pdo = connectDB();
    
    // Obtener todos los repartidores registrados
    $stmt = $pdo->prepare("
        SELECT 
            u.id,
            u.email,
            u.estado,
            u.fecha_registro,
            dp.nombre_completo,
            dp.dni,
            dp.telefono,
            v.tipo_vehiculo,
            v.marca,
            v.modelo,
            COUNT(d.id) as documentos_subidos
        FROM usuarios u
        LEFT JOIN datos_personales dp ON u.id = dp.usuario_id
        LEFT JOIN vehiculos v ON u.id = v.usuario_id
        LEFT JOIN documentos d ON u.id = d.usuario_id
        WHERE u.email != '<EMAIL>'
        GROUP BY u.id
        ORDER BY u.fecha_registro DESC
    ");
    $stmt->execute();
    $repartidores = $stmt->fetchAll();
    
    // Estadísticas
    $stmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total_usuarios,
            SUM(CASE WHEN estado = 'pendiente' THEN 1 ELSE 0 END) as pendientes,
            SUM(CASE WHEN estado = 'activo' THEN 1 ELSE 0 END) as activos,
            SUM(CASE WHEN estado = 'suspendido' THEN 1 ELSE 0 END) as suspendidos
        FROM usuarios 
        WHERE email != '<EMAIL>'
    ");
    $stmt->execute();
    $stats = $stmt->fetch();
    
} catch (Exception $e) {
    $error_message = "Error al cargar datos: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel - RepuMovil Delivery</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700;800&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-orange: #FF6B35;
            --secondary-orange: #F7931E;
            --primary-red: #E53E3E;
            --secondary-red: #FC8181;
            --gradient-main: linear-gradient(135deg, #FF6B35 0%, #E53E3E 50%, #F7931E 100%);
            --white: #ffffff;
            --dark: #2D3748;
            --light-gray: #F7FAFC;
            --success: #48BB78;
            --warning: #ED8936;
            --danger: #E53E3E;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .header {
            background: var(--gradient-main);
            color: white;
            padding: 2rem 0;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .container {
            max-width: 1400px;
            margin: 2rem auto;
            padding: 0 2rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-main);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 1.5rem;
            color: white;
        }

        .stat-icon.total { background: linear-gradient(45deg, #4299E1, #63B3ED); }
        .stat-icon.pendientes { background: linear-gradient(45deg, #ED8936, #F6AD55); }
        .stat-icon.activos { background: linear-gradient(45deg, #48BB78, #68D391); }
        .stat-icon.suspendidos { background: linear-gradient(45deg, #E53E3E, #FC8181); }

        .stat-value {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--dark);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #666;
            font-weight: 600;
        }

        .table-container {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow-x: auto;
        }

        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .table-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--dark);
        }

        .search-box {
            padding: 0.5rem 1rem;
            border: 2px solid #e2e8f0;
            border-radius: 25px;
            font-size: 0.9rem;
            width: 250px;
        }

        .search-box:focus {
            outline: none;
            border-color: var(--primary-red);
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        th, td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }

        th {
            background: var(--light-gray);
            font-weight: 600;
            color: var(--dark);
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        td {
            font-size: 0.9rem;
        }

        .status-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-pendiente {
            background: #FED7D7;
            color: #C53030;
        }

        .status-activo {
            background: #C6F6D5;
            color: #2F855A;
        }

        .status-suspendido {
            background: #FEEBC8;
            color: #C05621;
        }

        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }

        .btn {
            padding: 0.4rem 0.8rem;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.8rem;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.3rem;
            transition: all 0.3s ease;
        }

        .btn-view {
            background: #4299E1;
            color: white;
        }

        .btn-approve {
            background: var(--success);
            color: white;
        }

        .btn-reject {
            background: var(--danger);
            color: white;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #666;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .back-link {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            color: white;
            text-decoration: none;
            font-weight: 600;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }

        .back-link:hover {
            transform: translateX(-5px);
            color: #FFE5D9;
        }

        @media (max-width: 768px) {
            .container {
                padding: 0 1rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .table-header {
                flex-direction: column;
                gap: 1rem;
            }

            .search-box {
                width: 100%;
            }

            table {
                font-size: 0.8rem;
            }

            th, td {
                padding: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <a href="index.php" class="back-link">
            <i class="fas fa-arrow-left"></i> Volver al inicio
        </a>
        <h1><i class="fas fa-shield-alt"></i> Panel de Administración</h1>
        <p>RepuMovil Delivery - Gestión de Repartidores</p>
    </div>

    <div class="container">
        <!-- Estadísticas -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon total">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-value"><?php echo $stats['total_usuarios'] ?? 0; ?></div>
                <div class="stat-label">Total Repartidores</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon pendientes">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-value"><?php echo $stats['pendientes'] ?? 0; ?></div>
                <div class="stat-label">Pendientes</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon activos">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-value"><?php echo $stats['activos'] ?? 0; ?></div>
                <div class="stat-label">Activos</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon suspendidos">
                    <i class="fas fa-ban"></i>
                </div>
                <div class="stat-value"><?php echo $stats['suspendidos'] ?? 0; ?></div>
                <div class="stat-label">Suspendidos</div>
            </div>
        </div>

        <!-- Tabla de repartidores -->
        <div class="table-container">
            <div class="table-header">
                <h2 class="table-title">
                    <i class="fas fa-motorcycle"></i> Repartidores Registrados
                </h2>
                <input type="text" class="search-box" placeholder="Buscar repartidor..." id="searchInput">
            </div>

            <?php if (empty($repartidores)): ?>
                <div class="empty-state">
                    <i class="fas fa-inbox"></i>
                    <h3>No hay repartidores registrados</h3>
                    <p>Los nuevos registros aparecerán aquí</p>
                </div>
            <?php else: ?>
                <table id="repartidoresTable">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Nombre Completo</th>
                            <th>Email</th>
                            <th>DNI</th>
                            <th>Teléfono</th>
                            <th>Vehículo</th>
                            <th>Documentos</th>
                            <th>Estado</th>
                            <th>Fecha Registro</th>
                            <th>Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($repartidores as $repartidor): ?>
                            <tr>
                                <td>#<?php echo str_pad($repartidor['id'], 4, '0', STR_PAD_LEFT); ?></td>
                                <td><?php echo htmlspecialchars($repartidor['nombre_completo'] ?? 'N/A'); ?></td>
                                <td><?php echo htmlspecialchars($repartidor['email']); ?></td>
                                <td><?php echo htmlspecialchars($repartidor['dni'] ?? 'N/A'); ?></td>
                                <td><?php echo htmlspecialchars($repartidor['telefono'] ?? 'N/A'); ?></td>
                                <td>
                                    <?php if ($repartidor['tipo_vehiculo']): ?>
                                        <i class="fas fa-<?php echo $repartidor['tipo_vehiculo'] === 'bicicleta' ? 'bicycle' : ($repartidor['tipo_vehiculo'] === 'moto' ? 'motorcycle' : 'car'); ?>"></i>
                                        <?php echo ucfirst($repartidor['tipo_vehiculo']); ?>
                                        <?php if ($repartidor['marca']): ?>
                                            <br><small><?php echo htmlspecialchars($repartidor['marca'] . ' ' . $repartidor['modelo']); ?></small>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        N/A
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="status-badge <?php echo $repartidor['documentos_subidos'] > 0 ? 'status-activo' : 'status-pendiente'; ?>">
                                        <?php echo $repartidor['documentos_subidos']; ?> docs
                                    </span>
                                </td>
                                <td>
                                    <span class="status-badge status-<?php echo $repartidor['estado']; ?>">
                                        <?php echo ucfirst($repartidor['estado']); ?>
                                    </span>
                                </td>
                                <td><?php echo date('d/m/Y H:i', strtotime($repartidor['fecha_registro'])); ?></td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="ver-repartidor.php?id=<?php echo $repartidor['id']; ?>" class="btn btn-view">
                                            <i class="fas fa-eye"></i> Ver
                                        </a>
                                        <?php if ($repartidor['estado'] === 'pendiente'): ?>
                                            <button class="btn btn-approve" onclick="cambiarEstado(<?php echo $repartidor['id']; ?>, 'activo')">
                                                <i class="fas fa-check"></i> Aprobar
                                            </button>
                                            <button class="btn btn-reject" onclick="cambiarEstado(<?php echo $repartidor['id']; ?>, 'rechazado')">
                                                <i class="fas fa-times"></i> Rechazar
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php endif; ?>
        </div>
    </div>

    <script>
        // Búsqueda en tiempo real
        document.getElementById('searchInput').addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const table = document.getElementById('repartidoresTable');
            const rows = table.getElementsByTagName('tr');

            for (let i = 1; i < rows.length; i++) {
                const row = rows[i];
                const text = row.textContent.toLowerCase();
                
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            }
        });

        // Cambiar estado de repartidor
        function cambiarEstado(id, nuevoEstado) {
            if (confirm(`¿Estás seguro de cambiar el estado a "${nuevoEstado}"?`)) {
                // Aquí iría la llamada AJAX para cambiar el estado
                alert(`Estado cambiado a "${nuevoEstado}" para el repartidor #${id}`);
                location.reload();
            }
        }
    </script>
</body>
</html>
