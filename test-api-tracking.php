<?php
// PASO 2: Script de testing para API de tracking GPS
// Pruebas automáticas de todos los endpoints

echo "🧪 TESTING COMPLETO DE API TRACKING GPS\n";
echo "=====================================\n\n";

$baseUrl = 'http://localhost/mechanical-workshop/public/api/tracking.php';
$testResults = [];

/**
 * Función para hacer requests HTTP
 */
function makeRequest($url, $method = 'GET', $data = null) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    if ($method === 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        if ($data) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        }
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'success' => !$error && $httpCode === 200,
        'http_code' => $httpCode,
        'response' => $response,
        'error' => $error
    ];
}

/**
 * Función para mostrar resultado de test
 */
function showTestResult($testName, $result, $details = '') {
    $status = $result['success'] ? '✅ PASS' : '❌ FAIL';
    echo sprintf("%-40s %s\n", $testName, $status);
    
    if (!$result['success']) {
        echo "   Error: " . ($result['error'] ?: "HTTP {$result['http_code']}") . "\n";
    }
    
    if ($details) {
        echo "   $details\n";
    }
    
    echo "\n";
    return $result['success'];
}

// TEST 1: Obtener deliveries activos
echo "1️⃣ PROBANDO GET DELIVERIES ACTIVOS\n";
echo "-----------------------------------\n";
$result1 = makeRequest($baseUrl . '?action=get_active_deliveries');
$success1 = showTestResult('GET /api/tracking.php?action=get_active_deliveries', $result1);

if ($success1) {
    $data1 = json_decode($result1['response'], true);
    if ($data1 && $data1['success']) {
        $totalActivos = count($data1['data']['activos']);
        $totalInactivos = count($data1['data']['inactivos']);
        echo "   📊 Deliveries activos: $totalActivos\n";
        echo "   😴 Deliveries inactivos: $totalInactivos\n";
        echo "   🎯 Total: " . ($totalActivos + $totalInactivos) . "\n\n";
    }
}

// TEST 2: Actualizar ubicación
echo "2️⃣ PROBANDO POST ACTUALIZAR UBICACIÓN\n";
echo "-------------------------------------\n";
$locationData = [
    'action' => 'update_location',
    'delivery_id' => 1,
    'latitude' => -31.5375,
    'longitude' => -68.5364,
    'accuracy' => 5.0,
    'speed' => 25.5,
    'heading' => 45.0,
    'timestamp' => time() * 1000
];

$result2 = makeRequest($baseUrl, 'POST', $locationData);
$success2 = showTestResult('POST /api/tracking.php (update_location)', $result2);

if ($success2) {
    $data2 = json_decode($result2['response'], true);
    if ($data2 && $data2['success']) {
        echo "   📍 Ubicación actualizada para delivery {$data2['data']['delivery_id']}\n";
        echo "   🎯 Coordenadas: {$data2['data']['latitude']}, {$data2['data']['longitude']}\n";
        echo "   📏 Precisión: ±{$data2['data']['accuracy']}m\n\n";
    }
}

// TEST 3: Obtener ubicación específica
echo "3️⃣ PROBANDO GET UBICACIÓN ESPECÍFICA\n";
echo "------------------------------------\n";
$result3 = makeRequest($baseUrl . '?action=get_delivery_location&delivery_id=1');
$success3 = showTestResult('GET /api/tracking.php?action=get_delivery_location', $result3);

if ($success3) {
    $data3 = json_decode($result3['response'], true);
    if ($data3 && $data3['success']) {
        echo "   👤 Delivery: {$data3['data']['delivery_name']}\n";
        echo "   📞 Teléfono: {$data3['data']['delivery_phone']}\n";
        echo "   📍 Ubicación: {$data3['data']['latitude']}, {$data3['data']['longitude']}\n";
        echo "   ⏰ Última actualización: {$data3['data']['ultima_actualizacion']}\n";
        echo "   🕐 Minutos desde actualización: {$data3['data']['minutos_desde_actualizacion']}\n\n";
    }
}

// TEST 4: Obtener historial de tracking
echo "4️⃣ PROBANDO GET HISTORIAL DE TRACKING\n";
echo "-------------------------------------\n";
$result4 = makeRequest($baseUrl . '?action=get_tracking_history&delivery_id=1&limite=10');
$success4 = showTestResult('GET /api/tracking.php?action=get_tracking_history', $result4);

if ($success4) {
    $data4 = json_decode($result4['response'], true);
    if ($data4 && $data4['success']) {
        $historial = $data4['data']['historial'];
        $estadisticas = $data4['data']['estadisticas'];
        
        echo "   📋 Puntos en historial: " . count($historial) . "\n";
        echo "   📊 Total puntos GPS: {$estadisticas['total_puntos']}\n";
        echo "   🛣️ Distancia total: {$estadisticas['distancia_total']} km\n";
        echo "   🚗 Velocidad promedio: {$estadisticas['velocidad_promedio']} km/h\n";
        echo "   ⏱️ Tiempo total: {$estadisticas['tiempo_total']} min\n\n";
    }
}

// TEST 5: Simular múltiples ubicaciones
echo "5️⃣ PROBANDO SIMULACIÓN DE MOVIMIENTO\n";
echo "------------------------------------\n";
$baseLatitude = -31.5375;
$baseLongitude = -68.5364;
$simulationSuccess = 0;
$simulationTotal = 5;

for ($i = 0; $i < $simulationTotal; $i++) {
    // Simular movimiento aleatorio pequeño
    $lat = $baseLatitude + (rand(-100, 100) / 100000); // ±0.001 grados
    $lng = $baseLongitude + (rand(-100, 100) / 100000);
    
    $moveData = [
        'action' => 'update_location',
        'delivery_id' => 1,
        'latitude' => $lat,
        'longitude' => $lng,
        'accuracy' => rand(3, 10),
        'speed' => rand(10, 50),
        'heading' => rand(0, 360),
        'timestamp' => time() * 1000
    ];
    
    $moveResult = makeRequest($baseUrl, 'POST', $moveData);
    if ($moveResult['success']) {
        $simulationSuccess++;
        echo "   📍 Punto " . ($i + 1) . ": $lat, $lng ✅\n";
    } else {
        echo "   📍 Punto " . ($i + 1) . ": $lat, $lng ❌\n";
    }
    
    usleep(500000); // Esperar 0.5 segundos entre puntos
}

echo "\n   🎯 Simulación completada: $simulationSuccess/$simulationTotal puntos enviados\n\n";

// TEST 6: Verificar datos en base de datos
echo "6️⃣ PROBANDO VERIFICACIÓN DE BASE DE DATOS\n";
echo "-----------------------------------------\n";

try {
    $pdo = new PDO("mysql:host=localhost;dbname=mechanical_workshop;charset=utf8", 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Contar ubicaciones actuales
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM delivery_current_location");
    $currentLocations = $stmt->fetch()['total'];
    
    // Contar historial de ubicaciones
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM delivery_locations");
    $historyLocations = $stmt->fetch()['total'];
    
    // Contar sesiones activas
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM tracking_sessions WHERE activa = 1");
    $activeSessions = $stmt->fetch()['total'];
    
    echo "   ✅ Conexión a base de datos: OK\n";
    echo "   📍 Ubicaciones actuales: $currentLocations\n";
    echo "   📋 Historial de ubicaciones: $historyLocations\n";
    echo "   🔄 Sesiones activas: $activeSessions\n\n";
    
} catch (Exception $e) {
    echo "   ❌ Error de base de datos: " . $e->getMessage() . "\n\n";
}

// RESUMEN FINAL
echo "🏆 RESUMEN DE PRUEBAS\n";
echo "====================\n";
$totalTests = 6;
$passedTests = 0;

if ($success1) $passedTests++;
if ($success2) $passedTests++;
if ($success3) $passedTests++;
if ($success4) $passedTests++;
if ($simulationSuccess > 0) $passedTests++;
if (isset($currentLocations)) $passedTests++;

echo "✅ Pruebas exitosas: $passedTests/$totalTests\n";
echo "📊 Porcentaje de éxito: " . round(($passedTests / $totalTests) * 100, 1) . "%\n\n";

if ($passedTests === $totalTests) {
    echo "🎉 ¡TODAS LAS PRUEBAS PASARON! EL TRACKING GPS ESTÁ FUNCIONANDO PERFECTAMENTE! 🔥\n";
} else {
    echo "⚠️ Algunas pruebas fallaron. Revisar configuración.\n";
}

echo "\n🚀 PRÓXIMO PASO: Probar en la app React Native\n";
echo "📱 Comando: npx expo start\n";
echo "🎯 Verificar: Dashboard delivery → Indicador GPS\n\n";
?>
