<?php
session_start();
require_once 'config.php';

// Log de logout si hay usuario logueado
if (isset($_SESSION['user_id'])) {
    logActivity("Logout exitoso", $_SESSION['user_id']);
}

// Destruir todas las variables de sesión
$_SESSION = array();

// Destruir la cookie de sesión si existe
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// Destruir la sesión
session_destroy();

// Redirigir al login
header('Location: login.php');
exit();
?>
