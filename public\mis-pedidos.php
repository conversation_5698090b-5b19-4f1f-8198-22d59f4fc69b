<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RepuMovil - Mis Pedidos 📦</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #FF6B35;
            --secondary-color: #FFA500;
            --dark-color: #2c3e50;
            --light-color: #f8f9fa;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        /* Header - <PERSON><PERSON> estilo RepuMovil */
        .header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1.5rem;
            font-weight: bold;
        }

        .nav-buttons {
            display: flex;
            gap: 10px;
        }

        .nav-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .nav-btn:hover {
            background: rgba(255,255,255,0.3);
            color: white;
            text-decoration: none;
        }

        /* Main Content */
        .main-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        /* Page Header */
        .page-header {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            text-align: center;
        }

        .page-title {
            color: var(--dark-color);
            font-size: 2rem;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .page-subtitle {
            color: #666;
            font-size: 1.1rem;
        }

        /* Pedidos Grid */
        .pedidos-grid {
            display: grid;
            gap: 20px;
        }

        .pedido-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .pedido-card:hover {
            transform: translateY(-5px);
        }

        .pedido-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid var(--light-color);
        }

        .pedido-id {
            font-size: 1.2rem;
            font-weight: bold;
            color: var(--dark-color);
        }

        .pedido-estado {
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: bold;
            text-transform: uppercase;
        }

        .estado-pendiente {
            background: #fff3cd;
            color: #856404;
        }

        .estado-asignado {
            background: #d4edda;
            color: #155724;
        }

        .estado-en-camino {
            background: #cce7ff;
            color: #004085;
        }

        .estado-entregado {
            background: #d1ecf1;
            color: #0c5460;
        }

        .pedido-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .info-item {
            display: flex;
            align-items: center;
            gap: 10px;
            color: #666;
        }

        .info-item i {
            color: var(--primary-color);
            width: 20px;
        }

        .pedido-items {
            margin-bottom: 20px;
        }

        .items-title {
            font-size: 1.1rem;
            font-weight: bold;
            color: var(--dark-color);
            margin-bottom: 10px;
        }

        .item-list {
            background: var(--light-color);
            border-radius: 10px;
            padding: 15px;
        }

        .pedido-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }

        .action-btn {
            padding: 8px 15px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9rem;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-info {
            background: var(--info-color);
            color: white;
        }

        .action-btn:hover {
            opacity: 0.9;
            text-decoration: none;
            color: white;
        }

        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 50px;
            color: #666;
        }

        .empty-state i {
            font-size: 5rem;
            color: var(--primary-color);
            margin-bottom: 20px;
        }

        .empty-state h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
        }

        .shop-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-top: 20px;
        }

        .shop-btn:hover {
            background: #e55a2b;
            color: white;
            text-decoration: none;
        }

        /* Loading */
        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }

        .loading i {
            font-size: 3rem;
            color: var(--primary-color);
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .pedido-info {
                grid-template-columns: 1fr;
            }

            .pedido-header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .pedido-actions {
                justify-content: center;
                flex-wrap: wrap;
            }

            .header-content {
                flex-direction: column;
                gap: 15px;
            }

            .nav-buttons {
                flex-wrap: wrap;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-wrench"></i>
                <span>RepuMovil</span>
            </div>
            <div class="nav-buttons">
                <a href="dashboard-taller.php" class="nav-btn">
                    <i class="fas fa-home"></i> Dashboard
                </a>
                <a href="catalogo-repuestos.php" class="nav-btn">
                    <i class="fas fa-search"></i> Catálogo
                </a>
                <a href="carrito.php" class="nav-btn">
                    <i class="fas fa-shopping-cart"></i> Carrito
                </a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Page Header -->
        <section class="page-header">
            <h1 class="page-title">
                <i class="fas fa-list-alt"></i>
                Mis Pedidos
                <span style="font-size: 1.5rem;">📦</span>
            </h1>
            <p class="page-subtitle">"SEGUÍ TUS PEDIDOS EN TIEMPO REAL"</p>
        </section>

        <!-- Pedidos Content -->
        <div class="pedidos-grid" id="pedidosContent">
            <div class="loading">
                <i class="fas fa-spinner"></i>
                <p>Cargando tus pedidos...</p>
            </div>
        </div>
    </main>

    <script>
        // Verificar usuario logueado
        const currentUser = JSON.parse(localStorage.getItem('repumovil_user') || 'null');
        if (!currentUser) {
            window.location.href = 'login-dinamico.php';
        }

        // Cargar pedidos al iniciar
        loadPedidos();

        async function loadPedidos() {
            try {
                const response = await fetch(`../api/pedidos.php?user_id=${currentUser.id}`);
                const result = await response.json();

                if (result.success) {
                    displayPedidos(result.data);
                } else {
                    showError('Error al cargar pedidos: ' + result.error);
                }
            } catch (error) {
                showError('Error de conexión: ' + error.message);
            }
        }

        function displayPedidos(pedidos) {
            const container = document.getElementById('pedidosContent');

            if (pedidos.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-box-open"></i>
                        <h3>No tenés pedidos aún</h3>
                        <p>¡Empezá a comprar repuestos para tu taller!</p>
                        <a href="catalogo-repuestos.php" class="shop-btn">
                            <i class="fas fa-search"></i> Explorar Catálogo
                        </a>
                    </div>
                `;
                return;
            }

            container.innerHTML = pedidos.map(pedido => `
                <div class="pedido-card">
                    <div class="pedido-header">
                        <div class="pedido-id">
                            <i class="fas fa-hashtag"></i>
                            Pedido ${pedido.id}
                        </div>
                        <div class="pedido-estado estado-${pedido.estado}">
                            ${getEstadoText(pedido.estado)}
                        </div>
                    </div>
                    
                    <div class="pedido-info">
                        <div class="info-item">
                            <i class="fas fa-calendar"></i>
                            <span>${formatDate(pedido.created_at)}</span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-dollar-sign"></i>
                            <span>$${parseFloat(pedido.total).toFixed(2)}</span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>${pedido.direccion_entrega}</span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-truck"></i>
                            <span>${pedido.repartidor_nombre || 'Asignando delivery...'}</span>
                        </div>
                    </div>
                    
                    <div class="pedido-items">
                        <div class="items-title">
                            <i class="fas fa-list"></i>
                            Productos (${pedido.items ? pedido.items.length : 0})
                        </div>
                        <div class="item-list">
                            ${pedido.items ? pedido.items.map(item => 
                                `• ${item.nombre} (x${item.cantidad}) - $${parseFloat(item.precio_unitario).toFixed(2)}`
                            ).join('<br>') : 'Cargando productos...'}
                        </div>
                    </div>
                    
                    <div class="pedido-actions">
                        ${getActionButtons(pedido)}
                    </div>
                </div>
            `).join('');
        }

        function getEstadoText(estado) {
            const estados = {
                'pendiente': '⏳ Pendiente',
                'asignado': '🚀 Asignado',
                'en_camino': '🏍️ En Camino',
                'entregado': '✅ Entregado'
            };
            return estados[estado] || estado;
        }

        function getActionButtons(pedido) {
            let buttons = '';
            
            if (pedido.estado === 'asignado' || pedido.estado === 'en_camino') {
                buttons += `<a href="tracking-pedido.php?id=${pedido.id}" class="action-btn btn-info">
                    <i class="fas fa-map-marked-alt"></i> Rastrear
                </a>`;
            }
            
            if (pedido.estado === 'entregado') {
                buttons += `<button class="action-btn btn-success" onclick="calificarPedido(${pedido.id})">
                    <i class="fas fa-star"></i> Calificar
                </button>`;
            }
            
            buttons += `<button class="action-btn btn-primary" onclick="verDetalle(${pedido.id})">
                <i class="fas fa-eye"></i> Ver Detalle
            </button>`;
            
            return buttons;
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('es-AR', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        function verDetalle(pedidoId) {
            alert(`Ver detalle del pedido #${pedidoId}\n\n(Próximamente: modal con detalle completo)`);
        }

        function calificarPedido(pedidoId) {
            const rating = prompt('Calificá tu experiencia (1-5 estrellas):');
            if (rating && rating >= 1 && rating <= 5) {
                alert(`¡Gracias por tu calificación de ${rating} estrellas! 🌟`);
            }
        }

        function showError(message) {
            document.getElementById('pedidosContent').innerHTML = `
                <div style="text-align: center; padding: 50px; color: var(--danger-color);">
                    <i class="fas fa-exclamation-triangle" style="font-size: 3rem; margin-bottom: 20px;"></i>
                    <p>${message}</p>
                    <button onclick="loadPedidos()" style="background: var(--primary-color); color: white; border: none; padding: 10px 20px; border-radius: 5px; margin-top: 15px; cursor: pointer;">
                        Reintentar
                    </button>
                </div>
            `;
        }
    </script>
</body>
</html>
