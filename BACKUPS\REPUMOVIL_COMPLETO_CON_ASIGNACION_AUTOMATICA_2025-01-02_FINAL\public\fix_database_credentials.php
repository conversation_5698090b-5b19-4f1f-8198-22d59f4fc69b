<?php
// Mostrar todos los errores
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Credenciales correctas
$correctHost = 'localhost';
$correctUsername = 'root';
$correctPassword = '';
$correctDbname = 'autoconnect_db';

echo "<h1>Corrección de credenciales de base de datos</h1>";

// Función para verificar la conexión a la base de datos
function testConnection($host, $username, $password, $dbname) {
    try {
        $conn = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return true;
    } catch(PDOException $e) {
        return false;
    }
}

// Verificar la conexión con las credenciales correctas
if (testConnection($correctHost, $correctUsername, $correctPassword, $correctDbname)) {
    echo "<p>✅ Conexión exitosa con las credenciales correctas.</p>";
} else {
    echo "<p>❌ No se pudo conectar con las credenciales correctas. Verifica la configuración de MySQL.</p>";
    exit;
}

// Archivos PHP a verificar
$phpFiles = glob('*.php');
$phpFiles = array_merge($phpFiles, glob('../src/*.php'));

echo "<h2>Verificando archivos PHP:</h2>";
echo "<ul>";

foreach ($phpFiles as $file) {
    echo "<li>$file: ";
    
    // Leer el contenido del archivo
    $content = file_get_contents($file);
    
    // Buscar credenciales incorrectas
    $foundIncorrect = false;
    
    // Buscar patrones comunes de credenciales de base de datos
    if (preg_match('/[\'"](?!root)[a-zA-Z0-9]+[\'"](?=\s*,\s*[\'"][^\'"]*[\'"](?:\s*,\s*[\'"]YES[\'"]|\s*\)))/i', $content)) {
        echo "⚠️ Encontrado usuario incorrecto. ";
        $foundIncorrect = true;
        
        // Reemplazar cualquier nombre de usuario por 'root'
        $content = preg_replace('/([\'"])(?!root)[a-zA-Z0-9]+([\'"])(?=\s*,\s*[\'"][^\'"]*[\'"](?:\s*,\s*[\'"]YES[\'"]|\s*\)))/i', '$1root$2', $content);
    }
    
    if (strpos($content, "''") !== false || strpos($content, '""') !== false) {
        echo "⚠️ Encontrada contraseña ''. ";
        $foundIncorrect = true;
        
        // Reemplazar '' por ''
        $content = str_replace("''", "''", $content);
        $content = str_replace('""', '""', $content);
    }
    
    // Buscar variables de conexión a la base de datos
    if (preg_match('/\$(?:username|user|dbuser|db_user)\s*=\s*[\'"](?!root)[^\'"]+[\'"]/i', $content)) {
        echo "⚠️ Encontrada variable de usuario incorrecta. ";
        $foundIncorrect = true;
        
        // Reemplazar cualquier variable de usuario por 'root'
        $content = preg_replace('/(\$(?:username|user|dbuser|db_user)\s*=\s*[\'"])(?!root)[^\'"]+([\'"])/i', '$1root$2', $content);
    }
    
    if (preg_match('/\$(?:password|pass|dbpass|db_pass)\s*=\s*[\'"][^\'"]*[\'"]/i', $content)) {
        echo "⚠️ Encontrada variable de contraseña. ";
        $foundIncorrect = true;
        
        // Reemplazar cualquier variable de contraseña por ''
        $content = preg_replace('/(\$(?:password|pass|dbpass|db_pass)\s*=\s*[\'"])[^\'"]*([\'"])/i', '$1$2', $content);
    }
    
    // Buscar en la clase Database
    if (strpos($content, 'class Database') !== false) {
        echo "⚠️ Encontrada clase Database. ";
        $foundIncorrect = true;
        
        // Reemplazar propiedades de la clase Database
        $content = preg_replace('/private\s+\$username\s*=\s*[\'"][^\'"]+[\'"]/i', 'private $username = \'root\'', $content);
        $content = preg_replace('/private\s+\$password\s*=\s*[\'"][^\'"]*[\'"]/i', 'private $password = \'\'', $content);
    }
    
    // Si se encontraron credenciales incorrectas, guardar el archivo corregido
    if ($foundIncorrect) {
        file_put_contents($file, $content);
        echo "✅ Corregido.";
    } else {
        echo "✅ OK.";
    }
    
    echo "</li>";
}

echo "</ul>";

// Crear un archivo de configuración central
echo "<h2>Creando archivo de configuración central:</h2>";
$configContent = <<<EOT
<?php
// Configuración de la base de datos
\$config = [
    'db' => [
        'host' => 'localhost',
        'name' => 'autoconnect_db',
        'user' => 'root',
        'pass' => '',
        'charset' => 'utf8mb4'
    ],
    'app' => [
        'name' => 'AutoConnect',
        'url' => 'http://localhost/mechanical-workshop',
        'version' => '1.0.0',
        'debug' => true
    ]
];

// Mostrar errores en modo debug
if (\$config['app']['debug']) {
    ini_set('display_errors', 1);
    ini_set('display_startup_errors', 1);
    error_reporting(E_ALL);
} else {
    ini_set('display_errors', 0);
    ini_set('display_startup_errors', 0);
    error_reporting(0);
}
?>
EOT;

file_put_contents('../src/config.php', $configContent);
echo "<p>✅ Archivo de configuración central creado en ../src/config.php</p>";

// Modificar el archivo admin.php específicamente
if (file_exists('admin.php')) {
    $adminContent = file_get_contents('admin.php');
    
    // Reemplazar la sección de configuración de la base de datos
    $adminContent = preg_replace(
        '/\/\/ Configuración de la conexión a la base de datos.*?function connectDB\(\) {.*?}/s',
        "// Configuración de la conexión a la base de datos
\$host = 'localhost';
\$username = 'root';
\$password = '';
\$dbname = 'autoconnect_db';

// Función para conectar a la base de datos
function connectDB() {
    global \$host, \$username, \$password, \$dbname;
    
    try {
        \$conn = new PDO(\"mysql:host=\$host;dbname=\$dbname\", \$username, \$password);
        \$conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return \$conn;
    } catch(PDOException \$e) {
        die(\"Error de conexión: \" . \$e->getMessage());
    }
}",
        $adminContent
    );
    
    file_put_contents('admin.php', $adminContent);
    echo "<p>✅ Archivo admin.php corregido específicamente</p>";
}

echo "<p>Proceso completado. Ahora deberías poder crear usuarios correctamente.</p>";
echo "<p><a href='admin.php'>Volver a la página de administración</a></p>";
?>
