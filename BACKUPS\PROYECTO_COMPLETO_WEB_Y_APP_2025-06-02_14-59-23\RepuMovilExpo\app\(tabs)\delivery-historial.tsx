import React, { useState } from 'react';
import {
  SafeAreaView,
  StatusBar,
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  FlatList,
} from 'react-native';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';

const { width } = Dimensions.get('window');

interface EntregaHistorial {
  id: string;
  fecha: string;
  hora: string;
  cliente: string;
  direccion: string;
  tipo: 'repuestos' | 'servicio';
  estado: 'completado' | 'cancelado';
  ganancia: string;
  distancia: string;
  tiempo_total: string;
  calificacion: number;
}

export default function DeliveryHistorial() {
  const router = useRouter();
  const [filtroSeleccionado, setFiltroSeleccionado] = useState<'todos' | 'completados' | 'cancelados'>('todos');

  // Datos de ejemplo del historial
  const historialEntregas: EntregaHistorial[] = [
    {
      id: '1',
      fecha: '2024-01-15',
      hora: '14:30',
      cliente: 'Tall<PERSON> Rodríguez',
      direccion: 'Av. San Martín 1234',
      tipo: 'repuestos',
      estado: 'completado',
      ganancia: '$240',
      distancia: '2.3 km',
      tiempo_total: '18 min',
      calificacion: 5
    },
    {
      id: '2',
      fecha: '2024-01-15',
      hora: '11:15',
      cliente: 'Mecánico López',
      direccion: 'Calle Rivadavia 567',
      tipo: 'repuestos',
      estado: 'completado',
      ganancia: '$160',
      distancia: '1.8 km',
      tiempo_total: '15 min',
      calificacion: 4
    },
    {
      id: '3',
      fecha: '2024-01-14',
      hora: '16:45',
      cliente: 'AutoService Plus',
      direccion: 'Ruta 40 Km 15',
      tipo: 'servicio',
      estado: 'cancelado',
      ganancia: '$0',
      distancia: '4.1 km',
      tiempo_total: '0 min',
      calificacion: 0
    },
    {
      id: '4',
      fecha: '2024-01-14',
      hora: '09:20',
      cliente: 'Taller Central',
      direccion: 'Av. Libertador 890',
      tipo: 'repuestos',
      estado: 'completado',
      ganancia: '$320',
      distancia: '3.2 km',
      tiempo_total: '22 min',
      calificacion: 5
    },
    {
      id: '5',
      fecha: '2024-01-13',
      hora: '13:10',
      cliente: 'Mecánica Express',
      direccion: 'Calle Mitre 456',
      tipo: 'servicio',
      estado: 'completado',
      ganancia: '$180',
      distancia: '1.5 km',
      tiempo_total: '12 min',
      calificacion: 4
    }
  ];

  const entregasFiltradas = historialEntregas.filter(entrega => {
    if (filtroSeleccionado === 'todos') return true;
    return entrega.estado === filtroSeleccionado;
  });

  const estadisticas = {
    total_entregas: historialEntregas.filter(e => e.estado === 'completado').length,
    total_ganancias: historialEntregas
      .filter(e => e.estado === 'completado')
      .reduce((sum, e) => sum + parseInt(e.ganancia.replace('$', '')), 0),
    promedio_calificacion: historialEntregas
      .filter(e => e.estado === 'completado' && e.calificacion > 0)
      .reduce((sum, e, _, arr) => sum + e.calificacion / arr.length, 0),
    entregas_canceladas: historialEntregas.filter(e => e.estado === 'cancelado').length
  };

  const renderEstrellas = (calificacion: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Text key={i} style={styles.estrella}>
        {i < calificacion ? '⭐' : '☆'}
      </Text>
    ));
  };

  const renderEntrega = ({ item }: { item: EntregaHistorial }) => (
    <View style={styles.entregaCard}>
      <View style={styles.entregaHeader}>
        <View style={styles.fechaContainer}>
          <Text style={styles.fecha}>{item.fecha}</Text>
          <Text style={styles.hora}>{item.hora}</Text>
        </View>
        <View style={[
          styles.estadoBadge,
          item.estado === 'completado' ? styles.estadoCompletado : styles.estadoCancelado
        ]}>
          <Text style={[
            styles.estadoText,
            item.estado === 'completado' ? styles.estadoTextCompletado : styles.estadoTextCancelado
          ]}>
            {item.estado === 'completado' ? '✅ Completado' : '❌ Cancelado'}
          </Text>
        </View>
      </View>

      <Text style={styles.cliente}>{item.cliente}</Text>
      <Text style={styles.direccion}>📍 {item.direccion}</Text>

      <View style={styles.tipoContainer}>
        <Text style={styles.tipoText}>
          {item.tipo === 'repuestos' ? '🔧 Repuestos' : '⚙️ Servicio'}
        </Text>
      </View>

      <View style={styles.detallesContainer}>
        <View style={styles.detalle}>
          <Text style={styles.detalleIcon}>💰</Text>
          <Text style={styles.detalleText}>{item.ganancia}</Text>
        </View>
        <View style={styles.detalle}>
          <Text style={styles.detalleIcon}>📏</Text>
          <Text style={styles.detalleText}>{item.distancia}</Text>
        </View>
        <View style={styles.detalle}>
          <Text style={styles.detalleIcon}>⏱️</Text>
          <Text style={styles.detalleText}>{item.tiempo_total}</Text>
        </View>
      </View>

      {item.estado === 'completado' && item.calificacion > 0 && (
        <View style={styles.calificacionContainer}>
          <Text style={styles.calificacionLabel}>Calificación del cliente:</Text>
          <View style={styles.estrellas}>
            {renderEstrellas(item.calificacion)}
          </View>
        </View>
      )}
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#FF6B35" />
      
      {/* Header */}
      <LinearGradient
        colors={['#FF6B35', '#E53E3E']}
        style={styles.header}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <Text style={styles.backButtonText}>← Volver</Text>
        </TouchableOpacity>
        
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>📊 Historial de Entregas</Text>
          <Text style={styles.headerSubtitle}>Revisa todas tus entregas realizadas</Text>
        </View>
      </LinearGradient>

      {/* Estadísticas Resumen */}
      <View style={styles.statsContainer}>
        <View style={styles.statsGrid}>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{estadisticas.total_entregas}</Text>
            <Text style={styles.statLabel}>Completadas</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>${estadisticas.total_ganancias}</Text>
            <Text style={styles.statLabel}>Total Ganado</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>⭐ {estadisticas.promedio_calificacion.toFixed(1)}</Text>
            <Text style={styles.statLabel}>Promedio</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{estadisticas.entregas_canceladas}</Text>
            <Text style={styles.statLabel}>Canceladas</Text>
          </View>
        </View>
      </View>

      {/* Filtros */}
      <View style={styles.filtrosContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {[
            { key: 'todos', label: 'Todas', icon: '📋' },
            { key: 'completados', label: 'Completadas', icon: '✅' },
            { key: 'cancelados', label: 'Canceladas', icon: '❌' }
          ].map((filtro) => (
            <TouchableOpacity
              key={filtro.key}
              style={[
                styles.filtroButton,
                filtroSeleccionado === filtro.key && styles.filtroButtonActive
              ]}
              onPress={() => setFiltroSeleccionado(filtro.key as any)}
            >
              <Text style={[
                styles.filtroText,
                filtroSeleccionado === filtro.key && styles.filtroTextActive
              ]}>
                {filtro.icon} {filtro.label}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Lista de Entregas */}
      <FlatList
        data={entregasFiltradas}
        renderItem={renderEntrega}
        keyExtractor={(item) => item.id}
        style={styles.lista}
        contentContainerStyle={styles.listaContent}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyIcon}>📭</Text>
            <Text style={styles.emptyText}>No hay entregas para mostrar</Text>
            <Text style={styles.emptySubtext}>
              {filtroSeleccionado === 'todos' 
                ? 'Aún no has realizado ninguna entrega'
                : `No tienes entregas ${filtroSeleccionado}`
              }
            </Text>
          </View>
        }
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    paddingTop: 20,
    paddingBottom: 25,
    paddingHorizontal: 20,
  },
  backButton: {
    alignSelf: 'flex-start',
    marginBottom: 15,
  },
  backButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  headerContent: {
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '800',
    color: 'white',
    marginBottom: 5,
  },
  headerSubtitle: {
    fontSize: 16,
    color: 'white',
    opacity: 0.9,
    textAlign: 'center',
  },
  statsContainer: {
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statCard: {
    backgroundColor: 'white',
    width: (width - 60) / 4,
    padding: 15,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  statNumber: {
    fontSize: 16,
    fontWeight: '800',
    color: '#FF6B35',
    marginBottom: 5,
  },
  statLabel: {
    fontSize: 10,
    color: '#666',
    fontWeight: '500',
    textAlign: 'center',
  },
  filtrosContainer: {
    paddingHorizontal: 20,
    marginBottom: 15,
  },
  filtroButton: {
    backgroundColor: 'white',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
    marginRight: 10,
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  filtroButtonActive: {
    backgroundColor: '#FF6B35',
    borderColor: '#FF6B35',
  },
  filtroText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '600',
  },
  filtroTextActive: {
    color: 'white',
  },
  lista: {
    flex: 1,
  },
  listaContent: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  entregaCard: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  entregaHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  fechaContainer: {
    alignItems: 'flex-start',
  },
  fecha: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2D3748',
  },
  hora: {
    fontSize: 12,
    color: '#666',
  },
  estadoBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
  },
  estadoCompletado: {
    backgroundColor: '#E8F5E8',
  },
  estadoCancelado: {
    backgroundColor: '#FFEBEE',
  },
  estadoText: {
    fontSize: 12,
    fontWeight: '600',
  },
  estadoTextCompletado: {
    color: '#2E7D32',
  },
  estadoTextCancelado: {
    color: '#C62828',
  },
  cliente: {
    fontSize: 16,
    fontWeight: '700',
    color: '#2D3748',
    marginBottom: 5,
  },
  direccion: {
    fontSize: 14,
    color: '#666',
    marginBottom: 10,
  },
  tipoContainer: {
    alignSelf: 'flex-start',
    backgroundColor: '#E3F2FD',
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    marginBottom: 15,
  },
  tipoText: {
    fontSize: 12,
    color: '#1976D2',
    fontWeight: '600',
  },
  detallesContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 15,
  },
  detalle: {
    alignItems: 'center',
  },
  detalleIcon: {
    fontSize: 16,
    marginBottom: 5,
  },
  detalleText: {
    fontSize: 12,
    color: '#666',
    fontWeight: '600',
  },
  calificacionContainer: {
    alignItems: 'center',
    paddingTop: 15,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  calificacionLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 5,
  },
  estrellas: {
    flexDirection: 'row',
  },
  estrella: {
    fontSize: 16,
    marginHorizontal: 2,
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyIcon: {
    fontSize: 60,
    marginBottom: 20,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2D3748',
    marginBottom: 10,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
});
