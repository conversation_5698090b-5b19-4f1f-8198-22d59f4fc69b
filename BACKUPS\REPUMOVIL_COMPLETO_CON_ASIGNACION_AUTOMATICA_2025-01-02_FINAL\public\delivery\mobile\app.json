{"expo": {"name": "RepuMovil Delivery", "slug": "repumovil-delivery", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#FF6B35"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.repumovil.delivery"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#FF6B35"}, "package": "com.repumovil.delivery", "permissions": ["ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION", "CAMERA", "READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE"]}, "web": {"favicon": "./assets/favicon.png"}, "extra": {"apiUrl": "http://localhost/mechanical-workshop/public/delivery/api"}, "plugins": ["expo-location", "expo-image-picker", "expo-document-picker"]}}