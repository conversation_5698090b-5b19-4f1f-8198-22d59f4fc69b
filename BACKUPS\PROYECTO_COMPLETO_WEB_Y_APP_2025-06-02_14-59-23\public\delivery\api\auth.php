<?php
/**
 * API de Autenticación para RepuMovil Delivery
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Manejar preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once '../config.php';

/**
 * Función para enviar respuesta JSON
 */
function sendResponse($success, $message, $data = null, $code = 200) {
    http_response_code($code);
    echo json_encode([
        'success' => $success,
        'message' => $message,
        'data' => $data,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    exit();
}

/**
 * Función para validar entrada JSON
 */
function getJsonInput() {
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        sendResponse(false, 'JSON inválido', null, 400);
    }
    
    return $data;
}

try {
    $pdo = connectDB();
    $method = $_SERVER['REQUEST_METHOD'];
    $action = $_GET['action'] ?? '';

    switch ($method) {
        case 'POST':
            switch ($action) {
                case 'login':
                    handleLogin($pdo);
                    break;
                case 'register':
                    handleRegister($pdo);
                    break;
                case 'verify-email':
                    handleVerifyEmail($pdo);
                    break;
                case 'forgot-password':
                    handleForgotPassword($pdo);
                    break;
                default:
                    sendResponse(false, 'Acción no válida', null, 400);
            }
            break;
        
        case 'GET':
            switch ($action) {
                case 'profile':
                    handleGetProfile($pdo);
                    break;
                default:
                    sendResponse(false, 'Acción no válida', null, 400);
            }
            break;
        
        default:
            sendResponse(false, 'Método no permitido', null, 405);
    }

} catch (Exception $e) {
    logActivity("Error en API auth: " . $e->getMessage());
    sendResponse(false, 'Error interno del servidor', null, 500);
}

/**
 * Manejar login de usuario
 */
function handleLogin($pdo) {
    $data = getJsonInput();
    
    if (!isset($data['email']) || !isset($data['password'])) {
        sendResponse(false, 'Email y contraseña son requeridos', null, 400);
    }
    
    $email = filter_var($data['email'], FILTER_VALIDATE_EMAIL);
    if (!$email) {
        sendResponse(false, 'Email inválido', null, 400);
    }
    
    try {
        // Buscar usuario
        $stmt = $pdo->prepare("
            SELECT u.*, dp.nombre_completo, dp.telefono 
            FROM usuarios u 
            LEFT JOIN datos_personales dp ON u.id = dp.usuario_id 
            WHERE u.email = ?
        ");
        $stmt->execute([$email]);
        $user = $stmt->fetch();
        
        if (!$user || !password_verify($data['password'], $user['password'])) {
            logActivity("Intento de login fallido para email: $email");
            sendResponse(false, 'Credenciales incorrectas', null, 401);
        }
        
        if ($user['estado'] !== 'activo') {
            $mensaje = match($user['estado']) {
                'pendiente' => 'Tu cuenta está pendiente de verificación',
                'suspendido' => 'Tu cuenta ha sido suspendida',
                'rechazado' => 'Tu solicitud ha sido rechazada',
                default => 'Tu cuenta no está activa'
            };
            sendResponse(false, $mensaje, null, 403);
        }
        
        // Actualizar último acceso
        $stmt = $pdo->prepare("UPDATE usuarios SET ultimo_acceso = NOW() WHERE id = ?");
        $stmt->execute([$user['id']]);
        
        // Generar token de sesión
        $token = generarToken();
        
        // Preparar datos de respuesta
        $userData = [
            'id' => $user['id'],
            'email' => $user['email'],
            'nombre_completo' => $user['nombre_completo'],
            'telefono' => $user['telefono'],
            'estado' => $user['estado'],
            'verificado' => (bool)$user['verificado'],
            'fecha_registro' => $user['fecha_registro'],
            'token' => $token
        ];
        
        logActivity("Login exitoso", $user['id']);
        sendResponse(true, 'Login exitoso', $userData);
        
    } catch (PDOException $e) {
        logActivity("Error en login: " . $e->getMessage());
        sendResponse(false, 'Error en el servidor', null, 500);
    }
}

/**
 * Manejar registro de usuario
 */
function handleRegister($pdo) {
    $data = getJsonInput();
    
    // Validar campos requeridos
    $required_fields = ['email', 'password', 'nombre_completo', 'dni', 'telefono', 'direccion'];
    foreach ($required_fields as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            sendResponse(false, "El campo $field es requerido", null, 400);
        }
    }
    
    // Validar email
    $email = filter_var($data['email'], FILTER_VALIDATE_EMAIL);
    if (!$email) {
        sendResponse(false, 'Email inválido', null, 400);
    }
    
    // Validar DNI
    if (!validarDNI($data['dni'])) {
        sendResponse(false, 'DNI inválido', null, 400);
    }
    
    // Validar contraseña
    if (strlen($data['password']) < 6) {
        sendResponse(false, 'La contraseña debe tener al menos 6 caracteres', null, 400);
    }
    
    try {
        $pdo->beginTransaction();
        
        // Verificar si el email ya existe
        $stmt = $pdo->prepare("SELECT id FROM usuarios WHERE email = ?");
        $stmt->execute([$email]);
        if ($stmt->fetch()) {
            sendResponse(false, 'El email ya está registrado', null, 409);
        }
        
        // Verificar si el DNI ya existe
        $stmt = $pdo->prepare("SELECT id FROM datos_personales WHERE dni = ?");
        $stmt->execute([$data['dni']]);
        if ($stmt->fetch()) {
            sendResponse(false, 'El DNI ya está registrado', null, 409);
        }
        
        // Crear usuario
        $hashedPassword = password_hash($data['password'], PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("
            INSERT INTO usuarios (email, password, estado, fecha_registro) 
            VALUES (?, ?, 'pendiente', NOW())
        ");
        $stmt->execute([$email, $hashedPassword]);
        $userId = $pdo->lastInsertId();
        
        // Crear datos personales
        $stmt = $pdo->prepare("
            INSERT INTO datos_personales (usuario_id, nombre_completo, dni, fecha_nacimiento, telefono, direccion_completa) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            $userId,
            $data['nombre_completo'],
            $data['dni'],
            $data['fecha_nacimiento'] ?? '1990-01-01',
            $data['telefono'],
            $data['direccion']
        ]);
        
        // Crear datos de pago si se proporcionaron
        if (isset($data['cbu_alias']) && !empty($data['cbu_alias'])) {
            $stmt = $pdo->prepare("
                INSERT INTO datos_pago (usuario_id, cbu_alias, cuil_cuit) 
                VALUES (?, ?, ?)
            ");
            $stmt->execute([
                $userId,
                $data['cbu_alias'],
                $data['cuil_cuit'] ?? null
            ]);
        }
        
        // Crear datos de vehículo si se proporcionaron
        if (isset($data['tipo_vehiculo']) && !empty($data['tipo_vehiculo'])) {
            $stmt = $pdo->prepare("
                INSERT INTO vehiculos (usuario_id, tipo_vehiculo, marca, modelo, patente, licencia_conducir) 
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $userId,
                $data['tipo_vehiculo'],
                $data['marca'] ?? null,
                $data['modelo'] ?? null,
                $data['patente'] ?? null,
                $data['licencia'] ?? null
            ]);
        }
        
        // Crear declaración jurada
        if (isset($data['acepta_terminos']) && $data['acepta_terminos']) {
            $stmt = $pdo->prepare("
                INSERT INTO declaraciones_juradas (usuario_id, acepta_terminos, acepta_responsabilidad, acepta_datos_personales, ip_registro, user_agent) 
                VALUES (?, TRUE, TRUE, TRUE, ?, ?)
            ");
            $stmt->execute([
                $userId,
                obtenerIP(),
                $_SERVER['HTTP_USER_AGENT'] ?? 'Mobile App'
            ]);
        }
        
        $pdo->commit();
        
        logActivity("Registro exitoso", $userId);
        sendResponse(true, 'Registro exitoso. Tu cuenta está pendiente de verificación.', [
            'user_id' => $userId,
            'email' => $email,
            'estado' => 'pendiente'
        ]);
        
    } catch (PDOException $e) {
        $pdo->rollBack();
        logActivity("Error en registro: " . $e->getMessage());
        sendResponse(false, 'Error en el servidor', null, 500);
    }
}

/**
 * Obtener perfil de usuario
 */
function handleGetProfile($pdo) {
    $headers = getallheaders();
    $token = $headers['Authorization'] ?? '';
    
    if (empty($token)) {
        sendResponse(false, 'Token de autorización requerido', null, 401);
    }
    
    // Por ahora, simulamos la validación del token
    // En producción, aquí validarías el JWT o token de sesión
    $userId = $_GET['user_id'] ?? null;
    
    if (!$userId) {
        sendResponse(false, 'ID de usuario requerido', null, 400);
    }
    
    try {
        $stmt = $pdo->prepare("
            SELECT u.*, dp.*, dpago.*, v.*, 
                   COUNT(p.id) as total_entregas,
                   AVG(c.calificacion) as calificacion_promedio
            FROM usuarios u
            LEFT JOIN datos_personales dp ON u.id = dp.usuario_id
            LEFT JOIN datos_pago dpago ON u.id = dpago.usuario_id
            LEFT JOIN vehiculos v ON u.id = v.usuario_id
            LEFT JOIN pedidos p ON u.id = p.repartidor_id AND p.estado = 'entregado'
            LEFT JOIN calificaciones c ON u.id = c.repartidor_id
            WHERE u.id = ?
            GROUP BY u.id
        ");
        $stmt->execute([$userId]);
        $profile = $stmt->fetch();
        
        if (!$profile) {
            sendResponse(false, 'Usuario no encontrado', null, 404);
        }
        
        // Limpiar datos sensibles
        unset($profile['password']);
        
        sendResponse(true, 'Perfil obtenido exitosamente', $profile);
        
    } catch (PDOException $e) {
        logActivity("Error obteniendo perfil: " . $e->getMessage());
        sendResponse(false, 'Error en el servidor', null, 500);
    }
}

/**
 * Manejar verificación de email (placeholder)
 */
function handleVerifyEmail($pdo) {
    sendResponse(false, 'Función en desarrollo', null, 501);
}

/**
 * Manejar recuperación de contraseña (placeholder)
 */
function handleForgotPassword($pdo) {
    sendResponse(false, 'Función en desarrollo', null, 501);
}
?>
