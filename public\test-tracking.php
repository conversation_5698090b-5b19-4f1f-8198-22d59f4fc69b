<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RepuMovil - Test Tracking GPS 📍</title>
    <style>
        :root {
            --primary-color: #FF6B35;
            --secondary-color: #FFA500;
            --dark-color: #2c3e50;
            --light-color: #f8f9fa;
            --success-color: #28a745;
            --danger-color: #dc3545;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
            color: white;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .title {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .panel {
            background: white;
            color: var(--dark-color);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .panel h3 {
            color: var(--primary-color);
            margin-bottom: 20px;
            font-size: 1.3rem;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn:hover {
            background: #e55a2b;
            transform: translateY(-2px);
        }

        .btn-success {
            background: var(--success-color);
        }

        .btn-success:hover {
            background: #218838;
        }

        .btn-danger {
            background: var(--danger-color);
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .delivery-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            border-left: 4px solid var(--primary-color);
        }

        .delivery-name {
            font-weight: bold;
            font-size: 1.1rem;
            margin-bottom: 5px;
        }

        .delivery-status {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 10px;
        }

        .location-info {
            background: rgba(255, 107, 53, 0.1);
            padding: 10px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 0.9rem;
        }

        .map-container {
            height: 400px;
            border-radius: 10px;
            overflow: hidden;
            margin-top: 15px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .stat-card {
            background: rgba(255, 107, 53, 0.1);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        .stat-label {
            font-size: 0.9rem;
            color: #666;
            margin-top: 5px;
        }

        .log-container {
            background: #2c3e50;
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 0.9rem;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 15px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-label {
            display: block;
            font-weight: bold;
            margin-bottom: 5px;
            color: var(--primary-color);
        }

        .form-input, .form-select {
            width: 100%;
            padding: 10px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 1rem;
        }

        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-active { background: var(--success-color); }
        .status-inactive { background: var(--danger-color); }
        .status-warning { background: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">📍 Test Tracking GPS</h1>
            <p class="subtitle">Panel de pruebas para tracking en tiempo real de RepuMovil</p>
        </div>

        <!-- Panel de deliveries activos -->
        <div class="panel">
            <h3>🚚 Deliveries Activos</h3>
            <div id="deliveries-container">
                <p>Cargando deliveries...</p>
            </div>
            <button class="btn btn-success" onclick="cargarDeliveries()">🔄 Actualizar Deliveries</button>
        </div>

        <div class="grid">
            <!-- Panel de simulación de ubicaciones -->
            <div class="panel">
                <h3>🎯 Simular Ubicación</h3>
                <div class="form-group">
                    <label class="form-label">Delivery:</label>
                    <select class="form-select" id="delivery-select">
                        <option value="1">Juan Pérez (ID: 1)</option>
                        <option value="2">María González (ID: 2)</option>
                        <option value="3">Carlos Rodríguez (ID: 3)</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Latitud:</label>
                    <input type="number" class="form-input" id="latitude" value="-31.5375" step="0.000001">
                </div>
                
                <div class="form-group">
                    <label class="form-label">Longitud:</label>
                    <input type="number" class="form-input" id="longitude" value="-68.5364" step="0.000001">
                </div>
                
                <div class="form-group">
                    <label class="form-label">Precisión (metros):</label>
                    <input type="number" class="form-input" id="accuracy" value="5" min="1" max="100">
                </div>
                
                <button class="btn" onclick="enviarUbicacion()">📤 Enviar Ubicación</button>
                <button class="btn btn-success" onclick="iniciarSimulacion()">🎮 Iniciar Simulación</button>
                <button class="btn btn-danger" onclick="detenerSimulacion()">⏹️ Detener</button>
            </div>

            <!-- Panel de estadísticas -->
            <div class="panel">
                <h3>📊 Estadísticas de Tracking</h3>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number" id="total-deliveries">0</div>
                        <div class="stat-label">Deliveries Activos</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="total-ubicaciones">0</div>
                        <div class="stat-label">Ubicaciones Hoy</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="precision-promedio">0m</div>
                        <div class="stat-label">Precisión Promedio</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="velocidad-promedio">0</div>
                        <div class="stat-label">Velocidad Promedio</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Panel de historial -->
        <div class="panel">
            <h3>📋 Historial de Tracking</h3>
            <div class="form-group">
                <label class="form-label">Delivery para ver historial:</label>
                <select class="form-select" id="historial-delivery">
                    <option value="1">Juan Pérez (ID: 1)</option>
                    <option value="2">María González (ID: 2)</option>
                    <option value="3">Carlos Rodríguez (ID: 3)</option>
                </select>
                <button class="btn" onclick="cargarHistorial()" style="margin-top: 10px;">📋 Ver Historial</button>
            </div>
            <div id="historial-container"></div>
        </div>

        <!-- Panel de log en tiempo real -->
        <div class="panel">
            <h3>📝 Log en Tiempo Real</h3>
            <button class="btn btn-danger" onclick="limpiarLog()">🗑️ Limpiar Log</button>
            <div id="log-container" class="log-container">
                <div>📍 Sistema de tracking inicializado...</div>
            </div>
        </div>
    </div>

    <script>
        let simulacionActiva = false;
        let intervalSimulacion = null;

        // PASO 2: Cargar deliveries activos
        async function cargarDeliveries() {
            try {
                agregarLog('🔄 Cargando deliveries activos...');
                
                const response = await fetch('api/tracking.php?action=get_active_deliveries');
                const result = await response.json();
                
                const container = document.getElementById('deliveries-container');
                
                if (result.success) {
                    const { activos, inactivos } = result.data;
                    
                    container.innerHTML = '';
                    
                    // Mostrar deliveries activos
                    if (activos.length > 0) {
                        activos.forEach(delivery => {
                            const card = document.createElement('div');
                            card.className = 'delivery-card';
                            card.innerHTML = `
                                <div class="delivery-name">
                                    <span class="status-indicator status-active"></span>
                                    ${delivery.nombre} (ID: ${delivery.id})
                                </div>
                                <div class="delivery-status">
                                    📞 ${delivery.telefono || 'N/A'} • 
                                    ⏰ Actualizado hace ${delivery.minutos_inactivo || 0} min
                                </div>
                                ${delivery.latitude ? `
                                    <div class="location-info">
                                        📍 Lat: ${parseFloat(delivery.latitude).toFixed(6)}<br>
                                        📍 Lng: ${parseFloat(delivery.longitude).toFixed(6)}<br>
                                        🎯 Precisión: ±${delivery.accuracy || 'N/A'}m<br>
                                        🚗 Velocidad: ${delivery.speed ? (delivery.speed * 3.6).toFixed(1) + ' km/h' : 'N/A'}
                                    </div>
                                ` : '<div class="location-info">❌ Sin ubicación</div>'}
                            `;
                            container.appendChild(card);
                        });
                    }
                    
                    // Mostrar deliveries inactivos
                    if (inactivos.length > 0) {
                        const inactivosTitle = document.createElement('h4');
                        inactivosTitle.textContent = '😴 Deliveries Inactivos';
                        inactivosTitle.style.marginTop = '20px';
                        inactivosTitle.style.color = '#666';
                        container.appendChild(inactivosTitle);
                        
                        inactivos.forEach(delivery => {
                            const card = document.createElement('div');
                            card.className = 'delivery-card';
                            card.style.opacity = '0.6';
                            card.innerHTML = `
                                <div class="delivery-name">
                                    <span class="status-indicator status-inactive"></span>
                                    ${delivery.nombre} (ID: ${delivery.id})
                                </div>
                                <div class="delivery-status">
                                    📞 ${delivery.telefono || 'N/A'} • 
                                    ${delivery.minutos_inactivo ? `⏰ Inactivo ${delivery.minutos_inactivo} min` : '❌ Sin ubicación'}
                                </div>
                            `;
                            container.appendChild(card);
                        });
                    }
                    
                    // Actualizar estadísticas
                    document.getElementById('total-deliveries').textContent = activos.length;
                    
                    agregarLog(`✅ ${activos.length} deliveries activos, ${inactivos.length} inactivos`);
                } else {
                    container.innerHTML = '<p>❌ Error cargando deliveries</p>';
                    agregarLog(`❌ Error: ${result.message}`);
                }
            } catch (error) {
                console.error('Error:', error);
                agregarLog(`❌ Error de conexión: ${error.message}`);
            }
        }

        // PASO 2: Enviar ubicación manual
        async function enviarUbicacion() {
            try {
                const deliveryId = document.getElementById('delivery-select').value;
                const latitude = parseFloat(document.getElementById('latitude').value);
                const longitude = parseFloat(document.getElementById('longitude').value);
                const accuracy = parseFloat(document.getElementById('accuracy').value);
                
                agregarLog(`📤 Enviando ubicación para delivery ${deliveryId}...`);
                
                const response = await fetch('api/tracking.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'update_location',
                        delivery_id: parseInt(deliveryId),
                        latitude: latitude,
                        longitude: longitude,
                        accuracy: accuracy,
                        speed: Math.random() * 50, // Velocidad aleatoria para testing
                        heading: Math.random() * 360, // Dirección aleatoria
                        timestamp: Date.now()
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    agregarLog(`✅ Ubicación enviada: ${latitude.toFixed(6)}, ${longitude.toFixed(6)}`);
                    cargarDeliveries(); // Actualizar la lista
                } else {
                    agregarLog(`❌ Error enviando ubicación: ${result.message}`);
                }
            } catch (error) {
                agregarLog(`❌ Error: ${error.message}`);
            }
        }

        // PASO 2: Iniciar simulación de movimiento
        function iniciarSimulacion() {
            if (simulacionActiva) return;
            
            simulacionActiva = true;
            agregarLog('🎮 Iniciando simulación de movimiento...');
            
            let lat = parseFloat(document.getElementById('latitude').value);
            let lng = parseFloat(document.getElementById('longitude').value);
            
            intervalSimulacion = setInterval(async () => {
                // Simular movimiento aleatorio
                lat += (Math.random() - 0.5) * 0.0001; // Movimiento pequeño
                lng += (Math.random() - 0.5) * 0.0001;
                
                // Actualizar campos
                document.getElementById('latitude').value = lat.toFixed(6);
                document.getElementById('longitude').value = lng.toFixed(6);
                
                // Enviar ubicación
                await enviarUbicacion();
            }, 3000); // Cada 3 segundos
        }

        // PASO 2: Detener simulación
        function detenerSimulacion() {
            if (!simulacionActiva) return;
            
            simulacionActiva = false;
            if (intervalSimulacion) {
                clearInterval(intervalSimulacion);
                intervalSimulacion = null;
            }
            agregarLog('⏹️ Simulación detenida');
        }

        // PASO 2: Cargar historial de tracking
        async function cargarHistorial() {
            try {
                const deliveryId = document.getElementById('historial-delivery').value;
                agregarLog(`📋 Cargando historial para delivery ${deliveryId}...`);
                
                const response = await fetch(`api/tracking.php?action=get_tracking_history&delivery_id=${deliveryId}&limite=20`);
                const result = await response.json();
                
                const container = document.getElementById('historial-container');
                
                if (result.success && result.data.historial.length > 0) {
                    const { historial, estadisticas } = result.data;
                    
                    container.innerHTML = `
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-number">${estadisticas.total_puntos}</div>
                                <div class="stat-label">Puntos GPS</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">${estadisticas.distancia_total}km</div>
                                <div class="stat-label">Distancia Total</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">${estadisticas.velocidad_promedio}km/h</div>
                                <div class="stat-label">Velocidad Promedio</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">${estadisticas.tiempo_total}min</div>
                                <div class="stat-label">Tiempo Total</div>
                            </div>
                        </div>
                        <div class="log-container" style="max-height: 200px;">
                            ${historial.map(punto => `
                                <div>
                                    📍 ${new Date(punto.fecha_registro).toLocaleTimeString()} - 
                                    Lat: ${parseFloat(punto.latitude).toFixed(6)}, 
                                    Lng: ${parseFloat(punto.longitude).toFixed(6)}, 
                                    Precisión: ±${punto.accuracy || 'N/A'}m
                                </div>
                            `).join('')}
                        </div>
                    `;
                    
                    agregarLog(`✅ Historial cargado: ${historial.length} puntos`);
                } else {
                    container.innerHTML = '<p>❌ No hay historial disponible</p>';
                    agregarLog('❌ No se encontró historial');
                }
            } catch (error) {
                agregarLog(`❌ Error cargando historial: ${error.message}`);
            }
        }

        // PASO 2: Agregar mensaje al log
        function agregarLog(mensaje) {
            const logContainer = document.getElementById('log-container');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.textContent = `[${timestamp}] ${mensaje}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // PASO 2: Limpiar log
        function limpiarLog() {
            document.getElementById('log-container').innerHTML = '<div>📍 Log limpiado...</div>';
        }

        // PASO 2: Inicializar al cargar la página
        document.addEventListener('DOMContentLoaded', function() {
            cargarDeliveries();
            
            // Auto-actualizar cada 30 segundos
            setInterval(cargarDeliveries, 30000);
        });
    </script>
</body>
</html>
