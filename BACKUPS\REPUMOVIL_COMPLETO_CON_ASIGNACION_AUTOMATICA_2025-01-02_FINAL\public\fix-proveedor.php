<?php
echo "<h1>🔧 Arreglando Proveedor de Prueba</h1>";

// Configuración de base de datos
$host = 'localhost';
$dbname = 'mechanical_workshop';
$username = 'root';
$password = '';

try {
    echo "<p>📡 Conectando a la base de datos...</p>";
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p>✅ Conexión exitosa!</p>";

    // Verificar estructura de la tabla users
    echo "<p>🔍 Verificando estructura de tabla users...</p>";
    $stmt = $pdo->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "<p>📋 Columnas encontradas: " . implode(', ', $columns) . "</p>";

    // Eliminar usuario si existe
    echo "<p>🗑️ Eliminando usuario existente (si existe)...</p>";
    $stmt = $pdo->prepare("DELETE FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    echo "<p>✅ Usuario eliminado (si existía)</p>";

    // Crear nuevo usuario proveedor
    echo "<p>👤 Creando nuevo proveedor...</p>";
    $password_hash = password_hash('123456', PASSWORD_DEFAULT);
    
    // Usar solo las columnas que sabemos que existen
    $stmt = $pdo->prepare("
        INSERT INTO users (nombre, email, password, telefono, user_type) 
        VALUES (?, ?, ?, ?, ?)
    ");
    
    $result = $stmt->execute([
        'Proveedor Test',
        '<EMAIL>', 
        $password_hash,
        '+54 9 ************',
        'proveedor_repuestos'
    ]);
    
    if ($result) {
        echo "<p>🎉 ¡Proveedor creado exitosamente!</p>";
        
        // Verificar que se creó correctamente
        $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
        $stmt->execute(['<EMAIL>']);
        $user = $stmt->fetch();
        
        if ($user) {
            echo "<div style='background: #e8f5e8; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
            echo "<h2>✅ PROVEEDOR CREADO CORRECTAMENTE</h2>";
            echo "<p><strong>📧 Email:</strong> <EMAIL></p>";
            echo "<p><strong>🔑 Contraseña:</strong> 123456</p>";
            echo "<p><strong>👤 Tipo:</strong> " . $user['user_type'] . "</p>";
            echo "<p><strong>📱 Teléfono:</strong> " . $user['telefono'] . "</p>";
            echo "<p><strong>🆔 ID:</strong> " . $user['id'] . "</p>";
            echo "</div>";
            
            echo "<div style='text-align: center; margin: 30px 0;'>";
            echo "<a href='login-dinamico.php' style='background: #4CAF50; color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; font-size: 18px; font-weight: bold;'>";
            echo "🔐 IR AL LOGIN AHORA";
            echo "</a>";
            echo "</div>";
        } else {
            echo "<p>❌ Error: Usuario no encontrado después de crearlo</p>";
        }
    } else {
        echo "<p>❌ Error al crear el usuario</p>";
    }
    
} catch (PDOException $e) {
    echo "<div style='background: #ffebee; padding: 20px; border-radius: 10px; color: #d32f2f;'>";
    echo "<h2>❌ ERROR DE BASE DE DATOS</h2>";
    echo "<p><strong>Mensaje:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Código:</strong> " . $e->getCode() . "</p>";
    echo "</div>";
    
    echo "<h3>🔧 Posibles soluciones:</h3>";
    echo "<ol>";
    echo "<li>Verificar que XAMPP esté corriendo</li>";
    echo "<li>Verificar que la base de datos 'mechanical_workshop' exista</li>";
    echo "<li>Verificar que la tabla 'users' exista</li>";
    echo "<li>Ejecutar el script de creación de base de datos</li>";
    echo "</ol>";
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Proveedor - RepuMovil</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        
        h1 {
            color: #4CAF50;
            text-align: center;
            margin-bottom: 30px;
        }
        
        p {
            background: white;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #4CAF50;
        }
        
        ol, ul {
            background: white;
            padding: 20px;
            border-radius: 5px;
        }
    </style>
</head>
<body>
</body>
</html>
