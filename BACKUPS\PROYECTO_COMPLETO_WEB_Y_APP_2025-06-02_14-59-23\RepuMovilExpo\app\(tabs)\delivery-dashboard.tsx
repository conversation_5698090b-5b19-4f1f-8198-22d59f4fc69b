import React, { useState } from 'react';
import {
  SafeAreaView,
  StatusBar,
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert,
  Dimensions,
  Modal,
} from 'react-native';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';

const { width } = Dimensions.get('window');

interface Pedido {
  id: string;
  cliente: string;
  direccion: string;
  distancia: string;
  pago: string;
  tiempo_estimado: string;
  tipo: 'repuestos' | 'servicio';
  estado: 'disponible' | 'en_curso' | 'completado';
  ganancia: string;
}

export default function DeliveryDashboard() {
  const router = useRouter();
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedPedido, setSelectedPedido] = useState<Pedido | null>(null);
  const [estadoRepartidor, setEstadoRepartidor] = useState<'disponible' | 'ocupado' | 'desconectado'>('disponible');

  // Datos de ejemplo
  const pedidosDisponibles: Pedido[] = [
    {
      id: '1',
      cliente: 'Taller Rodríguez',
      direccion: 'Av. San Martín 1234, Centro',
      distancia: '2.3 km',
      pago: '$1,200',
      tiempo_estimado: '15 min',
      tipo: 'repuestos',
      estado: 'disponible',
      ganancia: '$240'
    },
    {
      id: '2',
      cliente: 'Mecánico López',
      direccion: 'Calle Rivadavia 567, Barrio Norte',
      distancia: '1.8 km',
      pago: '$800',
      tiempo_estimado: '12 min',
      tipo: 'repuestos',
      estado: 'disponible',
      ganancia: '$160'
    },
    {
      id: '3',
      cliente: 'AutoService Plus',
      direccion: 'Ruta 40 Km 15, Zona Industrial',
      distancia: '4.1 km',
      pago: '$2,500',
      tiempo_estimado: '25 min',
      tipo: 'servicio',
      estado: 'disponible',
      ganancia: '$500'
    }
  ];

  const estadisticasHoy = {
    entregas: 3,
    ganancias: '$900',
    tiempo_activo: '4h 30m',
    calificacion: 4.8
  };

  const toggleEstado = () => {
    if (estadoRepartidor === 'disponible') {
      setEstadoRepartidor('desconectado');
    } else {
      setEstadoRepartidor('disponible');
    }
  };

  const aceptarPedido = (pedido: Pedido) => {
    setSelectedPedido(pedido);
    setModalVisible(true);
  };

  const confirmarPedido = () => {
    if (selectedPedido) {
      Alert.alert(
        '🎉 ¡Pedido Aceptado!',
        `Has aceptado el pedido para ${selectedPedido.cliente}. Dirígete al punto de recogida.`,
        [
          {
            text: 'Ver Ruta',
            onPress: () => {
              setModalVisible(false);
              setEstadoRepartidor('ocupado');
              // Aquí se abriría el mapa con la ruta
              Alert.alert('🗺️ Navegación', 'Abriendo mapa con la ruta...');
            }
          }
        ]
      );
    }
  };

  const getEstadoColor = () => {
    switch (estadoRepartidor) {
      case 'disponible': return '#4CAF50';
      case 'ocupado': return '#FF9800';
      case 'desconectado': return '#9E9E9E';
      default: return '#9E9E9E';
    }
  };

  const getEstadoText = () => {
    switch (estadoRepartidor) {
      case 'disponible': return 'Disponible';
      case 'ocupado': return 'En Entrega';
      case 'desconectado': return 'Desconectado';
      default: return 'Desconectado';
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#FF6B35" />
      
      {/* Header */}
      <LinearGradient
        colors={['#FF6B35', '#E53E3E']}
        style={styles.header}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={styles.headerContent}>
          <View style={styles.logoContainer}>
            <Text style={styles.logoIcon}>🏍️</Text>
          </View>
          <Text style={styles.logoText}>
            <Text style={styles.logoRepu}>Repu</Text>
            <Text style={styles.logoMovil}>Movil</Text>
          </Text>
          <Text style={styles.logoSubtitle}>DELIVERY</Text>
          
          {/* Estado del repartidor */}
          <TouchableOpacity style={styles.estadoContainer} onPress={toggleEstado}>
            <View style={[styles.estadoIndicator, { backgroundColor: getEstadoColor() }]} />
            <Text style={styles.estadoText}>{getEstadoText()}</Text>
          </TouchableOpacity>
        </View>
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Estadísticas del día */}
        <View style={styles.statsContainer}>
          <Text style={styles.sectionTitle}>📊 Resumen de Hoy</Text>
          <View style={styles.statsGrid}>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>{estadisticasHoy.entregas}</Text>
              <Text style={styles.statLabel}>Entregas</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>{estadisticasHoy.ganancias}</Text>
              <Text style={styles.statLabel}>Ganancias</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>{estadisticasHoy.tiempo_activo}</Text>
              <Text style={styles.statLabel}>Tiempo Activo</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>⭐ {estadisticasHoy.calificacion}</Text>
              <Text style={styles.statLabel}>Calificación</Text>
            </View>
          </View>
        </View>

        {/* Pedidos disponibles */}
        <View style={styles.pedidosSection}>
          <Text style={styles.sectionTitle}>
            🚚 Pedidos Disponibles ({pedidosDisponibles.length})
          </Text>
          
          {estadoRepartidor === 'desconectado' ? (
            <View style={styles.desconectadoMessage}>
              <Text style={styles.desconectadoIcon}>😴</Text>
              <Text style={styles.desconectadoText}>
                Estás desconectado. Actívate para ver pedidos disponibles.
              </Text>
              <TouchableOpacity style={styles.activarButton} onPress={toggleEstado}>
                <Text style={styles.activarButtonText}>🚀 Activarme</Text>
              </TouchableOpacity>
            </View>
          ) : estadoRepartidor === 'ocupado' ? (
            <View style={styles.ocupadoMessage}>
              <Text style={styles.ocupadoIcon}>🚚</Text>
              <Text style={styles.ocupadoText}>
                Tienes una entrega en curso. Complétala para ver más pedidos.
              </Text>
            </View>
          ) : (
            pedidosDisponibles.map((pedido) => (
              <View key={pedido.id} style={styles.pedidoCard}>
                <View style={styles.pedidoHeader}>
                  <Text style={styles.pedidoCliente}>{pedido.cliente}</Text>
                  <View style={styles.tipoBadge}>
                    <Text style={styles.tipoText}>
                      {pedido.tipo === 'repuestos' ? '🔧 Repuestos' : '⚙️ Servicio'}
                    </Text>
                  </View>
                </View>
                
                <Text style={styles.pedidoDireccion}>📍 {pedido.direccion}</Text>
                
                <View style={styles.pedidoDetails}>
                  <View style={styles.detailItem}>
                    <Text style={styles.detailIcon}>📏</Text>
                    <Text style={styles.detailText}>{pedido.distancia}</Text>
                  </View>
                  <View style={styles.detailItem}>
                    <Text style={styles.detailIcon}>⏱️</Text>
                    <Text style={styles.detailText}>{pedido.tiempo_estimado}</Text>
                  </View>
                  <View style={styles.detailItem}>
                    <Text style={styles.detailIcon}>💰</Text>
                    <Text style={styles.detailText}>{pedido.ganancia}</Text>
                  </View>
                </View>

                <TouchableOpacity
                  style={styles.aceptarButton}
                  onPress={() => aceptarPedido(pedido)}
                >
                  <Text style={styles.aceptarButtonText}>✅ Aceptar Pedido</Text>
                </TouchableOpacity>
              </View>
            ))
          )}
        </View>

        {/* Acciones rápidas */}
        <View style={styles.accionesSection}>
          <Text style={styles.sectionTitle}>⚡ Acciones Rápidas</Text>
          
          <View style={styles.accionesGrid}>
            <TouchableOpacity
              style={styles.accionCard}
              onPress={() => router.push('/delivery-mapa')}
            >
              <Text style={styles.accionIcon}>🗺️</Text>
              <Text style={styles.accionText}>Mapa</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.accionCard}
              onPress={() => router.push('/delivery-historial')}
            >
              <Text style={styles.accionIcon}>📊</Text>
              <Text style={styles.accionText}>Historial</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.accionCard}
              onPress={() => router.push('/delivery-ganancias')}
            >
              <Text style={styles.accionIcon}>💰</Text>
              <Text style={styles.accionText}>Ganancias</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.accionCard}
              onPress={() => router.push('/delivery-perfil')}
            >
              <Text style={styles.accionIcon}>⚙️</Text>
              <Text style={styles.accionText}>Perfil</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>

      {/* Modal de confirmación */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>🚚 Confirmar Pedido</Text>
            
            {selectedPedido && (
              <>
                <Text style={styles.modalCliente}>{selectedPedido.cliente}</Text>
                <Text style={styles.modalDireccion}>📍 {selectedPedido.direccion}</Text>
                
                <View style={styles.modalDetails}>
                  <Text style={styles.modalDetailText}>💰 Ganancia: {selectedPedido.ganancia}</Text>
                  <Text style={styles.modalDetailText}>📏 Distancia: {selectedPedido.distancia}</Text>
                  <Text style={styles.modalDetailText}>⏱️ Tiempo: {selectedPedido.tiempo_estimado}</Text>
                </View>

                <View style={styles.modalButtons}>
                  <TouchableOpacity
                    style={styles.cancelButton}
                    onPress={() => setModalVisible(false)}
                  >
                    <Text style={styles.cancelButtonText}>❌ Cancelar</Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity
                    style={styles.confirmButton}
                    onPress={confirmarPedido}
                  >
                    <Text style={styles.confirmButtonText}>✅ Confirmar</Text>
                  </TouchableOpacity>
                </View>
              </>
            )}
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    paddingTop: 20,
    paddingBottom: 25,
    paddingHorizontal: 20,
  },
  headerContent: {
    alignItems: 'center',
  },
  logoContainer: {
    width: 60,
    height: 60,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
  },
  logoIcon: {
    fontSize: 30,
  },
  logoText: {
    fontSize: 28,
    fontWeight: '800',
    color: 'white',
    marginBottom: 5,
  },
  logoRepu: {
    color: '#ffffff',
  },
  logoMovil: {
    color: '#FFF3E0',
  },
  logoSubtitle: {
    fontSize: 14,
    fontWeight: '600',
    color: 'white',
    letterSpacing: 1.5,
    marginBottom: 15,
  },
  estadoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
  },
  estadoIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  estadoText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  statsContainer: {
    marginTop: 20,
    marginBottom: 25,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#2D3748',
    marginBottom: 15,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statCard: {
    backgroundColor: 'white',
    width: (width - 60) / 2,
    padding: 20,
    borderRadius: 15,
    alignItems: 'center',
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  statNumber: {
    fontSize: 20,
    fontWeight: '800',
    color: '#FF6B35',
    marginBottom: 5,
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
  },
  pedidosSection: {
    marginBottom: 25,
  },
  desconectadoMessage: {
    backgroundColor: 'white',
    padding: 30,
    borderRadius: 15,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  desconectadoIcon: {
    fontSize: 50,
    marginBottom: 15,
  },
  desconectadoText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 22,
  },
  activarButton: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 25,
    paddingVertical: 12,
    borderRadius: 25,
  },
  activarButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  ocupadoMessage: {
    backgroundColor: 'white',
    padding: 30,
    borderRadius: 15,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  ocupadoIcon: {
    fontSize: 50,
    marginBottom: 15,
  },
  ocupadoText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 22,
  },
  pedidoCard: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 15,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  pedidoHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  pedidoCliente: {
    fontSize: 16,
    fontWeight: '700',
    color: '#2D3748',
    flex: 1,
  },
  tipoBadge: {
    backgroundColor: '#E3F2FD',
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
  },
  tipoText: {
    fontSize: 12,
    color: '#1976D2',
    fontWeight: '600',
  },
  pedidoDireccion: {
    fontSize: 14,
    color: '#666',
    marginBottom: 15,
    lineHeight: 20,
  },
  pedidoDetails: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 15,
  },
  detailItem: {
    alignItems: 'center',
  },
  detailIcon: {
    fontSize: 16,
    marginBottom: 5,
  },
  detailText: {
    fontSize: 12,
    color: '#666',
    fontWeight: '600',
  },
  aceptarButton: {
    backgroundColor: '#4CAF50',
    paddingVertical: 12,
    borderRadius: 10,
    alignItems: 'center',
  },
  aceptarButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '700',
  },
  accionesSection: {
    marginBottom: 30,
  },
  accionesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  accionCard: {
    backgroundColor: 'white',
    width: (width - 60) / 2,
    padding: 20,
    borderRadius: 15,
    alignItems: 'center',
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  accionIcon: {
    fontSize: 30,
    marginBottom: 10,
  },
  accionText: {
    fontSize: 14,
    color: '#2D3748',
    fontWeight: '600',
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: 'white',
    margin: 20,
    borderRadius: 20,
    padding: 25,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#2D3748',
    marginBottom: 15,
  },
  modalCliente: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FF6B35',
    marginBottom: 10,
  },
  modalDireccion: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 20,
  },
  modalDetails: {
    alignItems: 'center',
    marginBottom: 25,
  },
  modalDetailText: {
    fontSize: 14,
    color: '#2D3748',
    marginBottom: 5,
    fontWeight: '500',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  cancelButton: {
    backgroundColor: '#f8f9fa',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 10,
    flex: 1,
    marginRight: 10,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  cancelButtonText: {
    color: '#666',
    fontSize: 14,
    fontWeight: '600',
  },
  confirmButton: {
    backgroundColor: '#4CAF50',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 10,
    flex: 1,
    marginLeft: 10,
    alignItems: 'center',
  },
  confirmButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '700',
  },
});
