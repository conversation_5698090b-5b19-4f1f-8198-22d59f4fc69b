import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';

const COLORS = {
  primary: '#FF6B35',
  red: '#E53E3E',
  white: '#FFFFFF',
  dark: '#2D3748',
  lightGray: '#F7FAFC',
};

const PerfilScreen = ({ navigation }) => {
  const handleLogout = () => {
    Alert.alert(
      'Cerrar Sesión',
      '¿Estás seguro que querés salir?',
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Salir',
          style: 'destructive',
          onPress: async () => {
            await AsyncStorage.clear();
            navigation.reset({
              index: 0,
              routes: [{ name: 'Login' }],
            });
          },
        },
      ]
    );
  };

  const ProfileOption = ({ icon, title, subtitle, onPress, showArrow = true }) => (
    <TouchableOpacity style={styles.optionCard} onPress={onPress}>
      <View style={styles.optionLeft}>
        <View style={styles.optionIcon}>
          <MaterialIcons name={icon} size={24} color={COLORS.red} />
        </View>
        <View style={styles.optionText}>
          <Text style={styles.optionTitle}>{title}</Text>
          {subtitle && <Text style={styles.optionSubtitle}>{subtitle}</Text>}
        </View>
      </View>
      {showArrow && (
        <MaterialIcons name="chevron-right" size={24} color="#ccc" />
      )}
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Mi Perfil</Text>
      </View>

      <ScrollView style={styles.content}>
        {/* User Info */}
        <View style={styles.userCard}>
          <View style={styles.avatar}>
            <MaterialIcons name="person" size={40} color={COLORS.white} />
          </View>
          <View style={styles.userInfo}>
            <Text style={styles.userName}>Juan Pérez</Text>
            <Text style={styles.userEmail}><EMAIL></Text>
            <View style={styles.statusBadge}>
              <MaterialIcons name="verified" size={16} color={COLORS.primary} />
              <Text style={styles.statusText}>Verificado</Text>
            </View>
          </View>
        </View>

        {/* Profile Options */}
        <View style={styles.optionsContainer}>
          <Text style={styles.sectionTitle}>Información Personal</Text>
          
          <ProfileOption
            icon="person-outline"
            title="Datos Personales"
            subtitle="Nombre, DNI, teléfono"
            onPress={() => Alert.alert('Info', 'Función en desarrollo')}
          />
          
          <ProfileOption
            icon="directions-car"
            title="Mi Vehículo"
            subtitle="Moto Honda CB 150"
            onPress={() => Alert.alert('Info', 'Función en desarrollo')}
          />
          
          <ProfileOption
            icon="account-balance"
            title="Datos de Pago"
            subtitle="CBU y información bancaria"
            onPress={() => Alert.alert('Info', 'Función en desarrollo')}
          />
        </View>

        <View style={styles.optionsContainer}>
          <Text style={styles.sectionTitle}>Configuración</Text>
          
          <ProfileOption
            icon="notifications-outline"
            title="Notificaciones"
            subtitle="Configurar alertas"
            onPress={() => Alert.alert('Info', 'Función en desarrollo')}
          />
          
          <ProfileOption
            icon="security"
            title="Seguridad"
            subtitle="Cambiar contraseña"
            onPress={() => Alert.alert('Info', 'Función en desarrollo')}
          />
          
          <ProfileOption
            icon="help-outline"
            title="Ayuda y Soporte"
            subtitle="Contactar soporte"
            onPress={() => Alert.alert('Info', 'Función en desarrollo')}
          />
        </View>

        <View style={styles.optionsContainer}>
          <Text style={styles.sectionTitle}>Aplicación</Text>
          
          <ProfileOption
            icon="info-outline"
            title="Acerca de"
            subtitle="Versión 1.0.0"
            onPress={() => Alert.alert('RepuMovil Delivery', 'Versión 1.0.0\nHecho con ❤️ en San Juan, Argentina')}
          />
          
          <ProfileOption
            icon="logout"
            title="Cerrar Sesión"
            onPress={handleLogout}
            showArrow={false}
          />
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.lightGray,
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 20,
    backgroundColor: COLORS.white,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '800',
    color: COLORS.dark,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  userCard: {
    backgroundColor: COLORS.white,
    borderRadius: 15,
    padding: 20,
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 30,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  avatar: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: COLORS.red,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 20,
    fontWeight: '800',
    color: COLORS.dark,
    marginBottom: 4,
  },
  userEmail: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#E6FFFA',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  statusText: {
    fontSize: 12,
    color: COLORS.primary,
    fontWeight: '600',
    marginLeft: 4,
  },
  optionsContainer: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: COLORS.dark,
    marginBottom: 15,
  },
  optionCard: {
    backgroundColor: COLORS.white,
    borderRadius: 12,
    padding: 15,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  optionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  optionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.lightGray,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  optionText: {
    flex: 1,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.dark,
    marginBottom: 2,
  },
  optionSubtitle: {
    fontSize: 13,
    color: '#666',
  },
});

export default PerfilScreen;
