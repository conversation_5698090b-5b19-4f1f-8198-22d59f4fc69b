<?php
/**
 * API de búsqueda SUPER SIMPLE para debug
 */

header('Content-Type: application/json');
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'db_config.php';

try {
    $pdo = connectDB();
    
    // Parámetros simples
    $query = isset($_GET['q']) ? trim($_GET['q']) : '';
    
    if (empty($query)) {
        echo json_encode([
            'success' => false,
            'message' => 'Parámetro q requerido',
            'data' => []
        ]);
        exit;
    }
    
    // Consulta SUPER SIMPLE
    $sql = "SELECT 
                id,
                nombre,
                descripcion,
                categoria,
                marca,
                modelo,
                precio,
                stock,
                codigo_producto
            FROM repuestos 
            WHERE estado = 'disponible' 
            AND stock > 0 
            AND nombre LIKE ?
            ORDER BY nombre ASC 
            LIMIT 10";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute(['%' . $query . '%']);
    $repuestos = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Formatear resultados
    $resultados = [];
    foreach ($repuestos as $repuesto) {
        $resultados[] = [
            'id' => (int)$repuesto['id'],
            'nombre' => $repuesto['nombre'],
            'descripcion' => $repuesto['descripcion'],
            'categoria' => $repuesto['categoria'],
            'marca' => $repuesto['marca'],
            'modelo' => $repuesto['modelo'],
            'precio' => (float)$repuesto['precio'],
            'stock' => (int)$repuesto['stock'],
            'codigo' => $repuesto['codigo_producto'],
            'precio_formateado' => '$' . number_format($repuesto['precio'], 2)
        ];
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Búsqueda exitosa',
        'data' => $resultados,
        'total' => count($resultados),
        'query' => $query
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage(),
        'data' => []
    ]);
}
?>
