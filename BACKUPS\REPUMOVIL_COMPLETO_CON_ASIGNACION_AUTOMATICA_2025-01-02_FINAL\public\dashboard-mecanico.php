<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RepuMovil - Dashboard Mecánico</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #2C3E50 0%, #3498DB 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .navbar-custom {
            background: linear-gradient(135deg, #34495E 0%, #2C3E50 100%);
            padding: 15px 0;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .logo-text {
            font-size: 28px;
            font-weight: 900;
            color: white;
        }

        .logo-repu {
            color: #3498DB;
        }

        .logo-movil {
            color: #E8F4FD;
        }

        .welcome-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 25px;
            margin: 20px 0;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .welcome-title {
            color: #2C3E50;
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .welcome-subtitle {
            color: #7F8C8D;
            font-size: 16px;
            margin-bottom: 0;
        }

        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .stat-number {
            font-size: 32px;
            font-weight: 900;
            color: #3498DB;
            margin-bottom: 8px;
        }

        .stat-label {
            color: #7F8C8D;
            font-size: 14px;
            font-weight: 600;
        }

        .functions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .function-card {
            background: white;
            border-radius: 20px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }

        .function-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
            border-color: #3498DB;
        }

        .function-icon {
            font-size: 48px;
            color: #3498DB;
            margin-bottom: 15px;
        }

        .function-title {
            font-size: 18px;
            font-weight: 700;
            color: #2C3E50;
            margin-bottom: 10px;
        }

        .function-description {
            color: #7F8C8D;
            font-size: 14px;
            line-height: 1.5;
        }

        .container-custom {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .modal-custom .modal-content {
            border-radius: 20px;
            border: none;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
        }

        .modal-custom .modal-header {
            background: linear-gradient(135deg, #3498DB 0%, #2C3E50 100%);
            color: white;
            border-radius: 20px 20px 0 0;
            padding: 20px 25px;
        }

        .modal-custom .modal-title {
            font-weight: 700;
            font-size: 20px;
        }

        .btn-close-white {
            filter: brightness(0) invert(1);
        }

        .trabajo-item {
            background: #F8F9FA;
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 15px;
            border-left: 4px solid #3498DB;
        }

        .trabajo-cliente {
            font-weight: 700;
            color: #2C3E50;
            font-size: 16px;
        }

        .trabajo-vehiculo {
            color: #7F8C8D;
            font-size: 14px;
            margin: 5px 0;
        }

        .trabajo-hora {
            color: #3498DB;
            font-weight: 600;
            font-size: 14px;
        }

        .btn-whatsapp {
            background: #25D366;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 8px 15px;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .btn-whatsapp:hover {
            background: #128C7E;
            color: white;
        }

        .ingreso-display {
            background: linear-gradient(135deg, #27AE60 0%, #2ECC71 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            margin: 20px 0;
        }

        .ingreso-amount {
            font-size: 36px;
            font-weight: 900;
            margin-bottom: 5px;
        }

        .ingreso-label {
            font-size: 16px;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-custom">
        <div class="container-custom">
            <div class="d-flex justify-content-between align-items-center w-100">
                <div class="logo-text">
                    <i class="fas fa-wrench me-2"></i>
                    <span class="logo-repu">Repu</span><span class="logo-movil">Movil</span>
                </div>
                <div class="d-flex align-items-center">
                    <span class="text-white me-3">👋 Hola, Mecánico</span>
                    <button class="btn btn-outline-light btn-sm">
                        <i class="fas fa-sign-out-alt"></i> Salir
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-custom">
        <!-- Welcome Section -->
        <div class="welcome-section">
            <h2 class="welcome-title">🔧 Tu Taller Móvil Profesional</h2>
            <p class="welcome-subtitle">Gestiona tus trabajos, clientes y herramientas desde cualquier lugar</p>
            <div class="slogan-section mt-3">
                <h3 style="color: #3498DB; font-weight: 900; font-size: 20px;">
                    TU TALLER VA DONDE VOS VAS, <span style="color: #E74C3C; font-size: 22px;">NEEEÑO</span>
                </h3>
            </div>
        </div>

        <!-- Stats -->
        <div class="stats-container">
            <div class="stat-card">
                <div class="stat-number">3</div>
                <div class="stat-label">Trabajos Hoy</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">$12,500</div>
                <div class="stat-label">Ingresos del Día</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">4.8</div>
                <div class="stat-label">Calificación</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">15</div>
                <div class="stat-label">Clientes Activos</div>
            </div>
        </div>

        <!-- Functions Grid -->
        <div class="functions-grid">
            <!-- Trabajos del Día -->
            <div class="function-card" onclick="abrirTrabajos()">
                <div class="function-icon">📋</div>
                <h3 class="function-title">Trabajos del Día</h3>
                <p class="function-description">Gestiona tus trabajos pendientes y programa tu día</p>
            </div>

            <!-- Contacto Rápido -->
            <div class="function-card" onclick="abrirContactos()">
                <div class="function-icon">📱</div>
                <h3 class="function-title">Contacto Rápido</h3>
                <p class="function-description">WhatsApp y llamadas directas con tus clientes</p>
            </div>

            <!-- Ubicaciones -->
            <div class="function-card" onclick="abrirUbicaciones()">
                <div class="function-icon">🗺️</div>
                <h3 class="function-title">Mis Ubicaciones</h3>
                <p class="function-description">Trabajos a domicilio y navegación GPS</p>
            </div>

            <!-- Ingresos Diarios -->
            <div class="function-card" onclick="abrirIngresos()">
                <div class="function-icon">💰</div>
                <h3 class="function-title">Ingresos Diarios</h3>
                <p class="function-description">Control simple de tus ganancias del día</p>
            </div>

            <!-- Mi Reputación -->
            <div class="function-card" onclick="abrirReputacion()">
                <div class="function-icon">⭐</div>
                <h3 class="function-title">Mi Reputación</h3>
                <p class="function-description">Calificaciones y comentarios de clientes</p>
            </div>

            <!-- Mis Herramientas -->
            <div class="function-card" onclick="abrirHerramientas()">
                <div class="function-icon">🔧</div>
                <h3 class="function-title">Mis Herramientas</h3>
                <p class="function-description">Lista de herramientas y recordatorios</p>
            </div>

            <!-- Fotos de Trabajos -->
            <div class="function-card" onclick="abrirFotos()">
                <div class="function-icon">📸</div>
                <h3 class="function-title">Fotos de Trabajos</h3>
                <p class="function-description">Antes/después y evidencia para clientes</p>
            </div>

            <!-- Pedido de Repuestos -->
            <div class="function-card" onclick="abrirRepuestos()">
                <div class="function-icon">🛒</div>
                <h3 class="function-title">Pedido de Repuestos</h3>
                <p class="function-description">Solicita repuestos para tus trabajos</p>
            </div>
        </div>
    </div>

    <!-- Modal Trabajos del Día -->
    <div class="modal fade modal-custom" id="modalTrabajos" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">📋 Trabajos del Día</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="trabajo-item">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <div class="trabajo-cliente">Juan Pérez</div>
                                <div class="trabajo-vehiculo">Ford Focus 2018 - ABC123</div>
                                <div class="trabajo-hora">🕐 09:00 AM - Cambio de aceite</div>
                            </div>
                            <button class="btn btn-whatsapp">
                                <i class="fab fa-whatsapp"></i> WhatsApp
                            </button>
                        </div>
                    </div>

                    <div class="trabajo-item">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <div class="trabajo-cliente">María García</div>
                                <div class="trabajo-vehiculo">Toyota Corolla 2020 - XYZ789</div>
                                <div class="trabajo-hora">🕐 11:30 AM - Revisión frenos</div>
                            </div>
                            <button class="btn btn-whatsapp">
                                <i class="fab fa-whatsapp"></i> WhatsApp
                            </button>
                        </div>
                    </div>

                    <div class="trabajo-item">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <div class="trabajo-cliente">Carlos López</div>
                                <div class="trabajo-vehiculo">Chevrolet Onix 2019 - DEF456</div>
                                <div class="trabajo-hora">🕐 02:00 PM - Diagnóstico eléctrico</div>
                            </div>
                            <button class="btn btn-whatsapp">
                                <i class="fab fa-whatsapp"></i> WhatsApp
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Ingresos -->
    <div class="modal fade modal-custom" id="modalIngresos" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">💰 Ingresos del Día</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="ingreso-display">
                        <div class="ingreso-amount">$12,500</div>
                        <div class="ingreso-label">Total del Día</div>
                    </div>

                    <div class="row">
                        <div class="col-6">
                            <div class="text-center p-3">
                                <h6>Trabajos Completados</h6>
                                <div class="h4 text-primary">3</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center p-3">
                                <h6>Promedio por Trabajo</h6>
                                <div class="h4 text-success">$4,167</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function abrirTrabajos() {
            new bootstrap.Modal(document.getElementById('modalTrabajos')).show();
        }

        function abrirContactos() {
            alert('📱 Contacto Rápido\n\n• WhatsApp directo con clientes\n• Llamadas rápidas\n• Lista de contactos frecuentes');
        }

        function abrirUbicaciones() {
            alert('🗺️ Mis Ubicaciones\n\n• Trabajos a domicilio\n• Navegación GPS\n• Rutas optimizadas');
        }

        function abrirIngresos() {
            new bootstrap.Modal(document.getElementById('modalIngresos')).show();
        }

        function abrirReputacion() {
            alert('⭐ Mi Reputación\n\n• Calificación: 4.8/5\n• 25 reseñas positivas\n• "Excelente mecánico, muy profesional"');
        }

        function abrirHerramientas() {
            alert('🔧 Mis Herramientas\n\n• Llaves combinadas ✅\n• Multímetro ✅\n• Scanner OBD ✅\n• Compresor ⚠️ (mantenimiento)');
        }

        function abrirFotos() {
            alert('📸 Fotos de Trabajos\n\n• Galería de antes/después\n• Evidencia para clientes\n• Portfolio de trabajos');
        }

        function abrirRepuestos() {
            alert('🛒 Pedido de Repuestos\n\n• Buscar repuestos\n• Agregar al carrito\n• Solicitar cotización');
        }
    </script>
</body>
</html>
