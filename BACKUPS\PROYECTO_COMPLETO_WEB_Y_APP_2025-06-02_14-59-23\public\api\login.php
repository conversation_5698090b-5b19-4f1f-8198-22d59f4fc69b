<?php
// API para login de usuarios
session_start();

header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header("Content-Type: application/json; charset=UTF-8");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Método no permitido']);
    exit;
}

require_once '../db_config.php';

try {
    // Conectar usando la función del db_config
    $pdo = connectDB();

    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';
    $remember = isset($_POST['remember']);

    // Validaciones básicas
    if (empty($email) || empty($password)) {
        throw new Exception('Email y contraseña son requeridos');
    }

    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('Email no válido');
    }

    // Verificar si es admin primero
    if ($email === '<EMAIL>' && $password === 'admin123') {
        // Login directo para admin
        $_SESSION['user_id'] = 1;
        $_SESSION['user_email'] = '<EMAIL>';
        $_SESSION['user_type'] = 'admin';
        $_SESSION['user_name'] = 'Administrador';
        $_SESSION['role'] = 'admin';
        $_SESSION['logged_in'] = true;

        echo json_encode([
            'success' => true,
            'message' => 'Inicio de sesión exitoso',
            'user' => [
                'id' => 1,
                'email' => '<EMAIL>',
                'user_type' => 'admin',
                'nombre' => 'Administrador',
                'status' => 'active'
            ],
            'redirect' => 'admin.php'
        ]);
        exit;
    }

    // Buscar usuario en la base de datos (estructura del admin)
    $stmt = $pdo->prepare("
        SELECT u.id, u.username, u.email, u.password, u.status, u.created_at, u.last_login,
               r.name as role_name, r.description as role_description
        FROM users u
        LEFT JOIN roles r ON u.role_id = r.id
        WHERE u.email = ?
    ");
    $stmt->execute([$email]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$user) {
        throw new Exception('Email o contraseña incorrectos');
    }

    // Verificar contraseña
    if (!password_verify($password, $user['password'])) {
        throw new Exception('Email o contraseña incorrectos');
    }

    // Verificar estado de la cuenta
    if ($user['status'] === 'inactive') {
        throw new Exception('Tu cuenta está inactiva. Contacta al soporte.');
    }

    // Obtener datos adicionales según el rol (estructura del admin)
    $additional_data = [];
    $user_type = '';

    // Mapear roles a tipos de usuario basado en role_name
    switch ($user['role_name']) {
        case 'admin':
            $user_type = 'admin';
            break;

        case 'taller':
            // Verificar si tiene registro en workshops
            $stmt = $pdo->prepare("SELECT name, address, phone, description FROM workshops WHERE user_id = ?");
            $stmt->execute([$user['id']]);
            $workshop_data = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($workshop_data) {
                $user_type = 'taller_mecanico';
                $additional_data = $workshop_data;
            } else {
                $user_type = 'usuario_regular'; // Fallback si no tiene workshop
            }
            break;

        case 'proveedor':
            // Verificar si tiene registro en suppliers
            $stmt = $pdo->prepare("SELECT name, address, phone, description FROM suppliers WHERE user_id = ?");
            $stmt->execute([$user['id']]);
            $supplier_data = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($supplier_data) {
                $user_type = 'proveedor_repuestos';
                $additional_data = $supplier_data;
            } else {
                $user_type = 'usuario_regular'; // Fallback si no tiene supplier
            }
            break;

        case 'mecanico':
            // Verificar si tiene registro en mechanics
            $stmt = $pdo->prepare("SELECT name, specialties, experience, address, phone, description FROM mechanics WHERE user_id = ?");
            $stmt->execute([$user['id']]);
            $mechanic_data = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($mechanic_data) {
                $user_type = 'mecanico_independiente';
                $additional_data = $mechanic_data;
            } else {
                $user_type = 'usuario_regular'; // Fallback si no tiene mechanic
            }
            break;

        case 'usuario':
        default:
            $user_type = 'usuario_regular';
            break;
    }

    // Actualizar último login
    $stmt = $pdo->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
    $stmt->execute([$user['id']]);

    // Crear sesión
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['user_email'] = $user['email'];
    $_SESSION['user_type'] = $user_type;
    $_SESSION['user_name'] = $user['username'];
    $_SESSION['logged_in'] = true;

    // Configurar cookie si se seleccionó "recordar"
    if ($remember) {
        $token = bin2hex(random_bytes(32));

        // Guardar token en la base de datos (crear tabla remember_tokens si no existe)
        try {
            $stmt = $pdo->prepare("
                INSERT INTO remember_tokens (user_id, token, expires_at)
                VALUES (?, ?, DATE_ADD(NOW(), INTERVAL 30 DAY))
                ON DUPLICATE KEY UPDATE
                token = VALUES(token), expires_at = VALUES(expires_at)
            ");
            $stmt->execute([$user['id'], hash('sha256', $token)]);

            // Configurar cookie
            setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/', '', false, true);
        } catch (PDOException $e) {
            // Si la tabla no existe, la creamos
            $pdo->exec("
                CREATE TABLE IF NOT EXISTS remember_tokens (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    token VARCHAR(255) NOT NULL,
                    expires_at TIMESTAMP NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                    UNIQUE KEY unique_user (user_id)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
        }
    }

    // Respuesta exitosa
    echo json_encode([
        'success' => true,
        'message' => 'Inicio de sesión exitoso',
        'user' => [
            'id' => $user['id'],
            'email' => $user['email'],
            'user_type' => $user_type,
            'username' => $user['username'],
            'role_name' => $user['role_name'],
            'status' => $user['status'],
            'member_since' => $user['created_at'],
            'additional_data' => $additional_data
        ],
        'redirect' => getDashboardUrl($user_type, $additional_data)
    ]);

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

// Función para determinar la URL del dashboard según el tipo de usuario y plan
function getDashboardUrl($user_type, $additional_data = []) {
    switch ($user_type) {
        case 'taller_mecanico':
            // Verificar si tiene plan plus o común
            $plan_type = $additional_data['plan_type'] ?? 'comun';
            return $plan_type === 'plus' ? 'dashboard-taller-plus.php' : 'dashboard-taller-comun.php';

        case 'proveedor_repuestos':
            // Redirigir al dashboard específico de proveedor
            return 'dashboard-proveedor.php';

        case 'mecanico_independiente':
            // Redirigir al dashboard específico de mecánico independiente
            return 'dashboard-mecanico.php';

        case 'usuario_regular':
            // Redirigir al dashboard de usuario común
            return 'dashboard-usuario.php';

        default:
            // Por defecto, dashboard de usuario
            return 'dashboard-usuario.php';
    }
}
?>
