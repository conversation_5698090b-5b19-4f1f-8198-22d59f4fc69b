<?php
/**
 * Sistema de Asignación Automática de Pedidos - RepuMovil
 * Asigna automáticamente repartidores cuando se crea una orden
 */

require_once '../config/database.php';
require_once '../config/auth.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

/**
 * Algoritmo de asignación inteligente
 * Considera: distancia, disponibilidad, calificación, carga de trabajo
 */
function asignarRepartidorAutomatico($pedidoId, $direccionOrigen, $direccionDestino) {
    global $pdo;
    
    try {
        // 1. Buscar repartidores disponibles
        $stmt = $pdo->prepare("
            SELECT u.id, u.nombre, u.telefono, u.email,
                   r.estado, r.calificacion_promedio, r.entregas_completadas,
                   r.latitud_actual, r.longitud_actual,
                   COUNT(p.id) as pedidos_activos
            FROM usuarios u
            JOIN repartidores r ON u.id = r.user_id
            LEFT JOIN pedidos p ON r.user_id = p.repartidor_id 
                AND p.estado IN ('asignado', 'en_camino')
            WHERE u.tipo_usuario = 'delivery' 
                AND r.estado = 'disponible'
                AND r.activo = 1
            GROUP BY u.id
            HAVING pedidos_activos < 3
            ORDER BY 
                r.calificacion_promedio DESC,
                pedidos_activos ASC,
                r.entregas_completadas DESC
            LIMIT 10
        ");
        $stmt->execute();
        $repartidores = $stmt->fetchAll();
        
        if (empty($repartidores)) {
            return ['success' => false, 'message' => 'No hay repartidores disponibles'];
        }
        
        // 2. Calcular distancias y seleccionar el mejor
        $mejorRepartidor = null;
        $menorDistancia = PHP_FLOAT_MAX;
        
        // Coordenadas de ejemplo para San Juan (en producción usar geocoding real)
        $coordenadasOrigen = obtenerCoordenadas($direccionOrigen);
        
        foreach ($repartidores as $repartidor) {
            if ($repartidor['latitud_actual'] && $repartidor['longitud_actual']) {
                $distancia = calcularDistancia(
                    $coordenadasOrigen['lat'], $coordenadasOrigen['lng'],
                    $repartidor['latitud_actual'], $repartidor['longitud_actual']
                );
                
                // Factor de puntuación: distancia + calificación + carga
                $puntuacion = $distancia - ($repartidor['calificacion_promedio'] * 0.5) + ($repartidor['pedidos_activos'] * 2);
                
                if ($puntuacion < $menorDistancia) {
                    $menorDistancia = $puntuacion;
                    $mejorRepartidor = $repartidor;
                    $mejorRepartidor['distancia_km'] = round($distancia, 2);
                }
            }
        }
        
        if (!$mejorRepartidor) {
            // Fallback: asignar al primer repartidor disponible
            $mejorRepartidor = $repartidores[0];
            $mejorRepartidor['distancia_km'] = 2.5; // Distancia estimada
        }
        
        // 3. Asignar el pedido
        $pdo->beginTransaction();
        
        $stmt = $pdo->prepare("
            UPDATE pedidos 
            SET repartidor_id = ?, estado = 'asignado', fecha_asignacion = NOW() 
            WHERE id = ?
        ");
        $stmt->execute([$mejorRepartidor['id'], $pedidoId]);
        
        // 4. Actualizar estado del repartidor
        $stmt = $pdo->prepare("
            UPDATE repartidores 
            SET estado = 'ocupado', ultimo_pedido_asignado = NOW() 
            WHERE user_id = ?
        ");
        $stmt->execute([$mejorRepartidor['id']]);
        
        // 5. Registrar en log de asignaciones
        $stmt = $pdo->prepare("
            INSERT INTO asignaciones_log (pedido_id, repartidor_id, metodo_asignacion, distancia_km, fecha_asignacion)
            VALUES (?, ?, 'automatico', ?, NOW())
        ");
        $stmt->execute([$pedidoId, $mejorRepartidor['id'], $mejorRepartidor['distancia_km']]);
        
        $pdo->commit();
        
        // 6. Enviar notificación al repartidor (simulado)
        enviarNotificacionRepartidor($mejorRepartidor['id'], $pedidoId);
        
        return [
            'success' => true,
            'message' => 'Pedido asignado automáticamente',
            'repartidor' => [
                'id' => $mejorRepartidor['id'],
                'nombre' => $mejorRepartidor['nombre'],
                'telefono' => $mejorRepartidor['telefono'],
                'calificacion' => $mejorRepartidor['calificacion_promedio'],
                'distancia_km' => $mejorRepartidor['distancia_km'],
                'tiempo_estimado' => round($mejorRepartidor['distancia_km'] * 3) . ' min'
            ]
        ];
        
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollback();
        }
        return ['success' => false, 'message' => 'Error en asignación: ' . $e->getMessage()];
    }
}

/**
 * Calcular distancia entre dos puntos (Haversine)
 */
function calcularDistancia($lat1, $lon1, $lat2, $lon2) {
    $R = 6371; // Radio de la Tierra en km
    $dLat = deg2rad($lat2 - $lat1);
    $dLon = deg2rad($lon2 - $lon1);
    
    $a = sin($dLat/2) * sin($dLat/2) + cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * sin($dLon/2) * sin($dLon/2);
    $c = 2 * atan2(sqrt($a), sqrt(1-$a));
    $distancia = $R * $c;
    
    return $distancia;
}

/**
 * Obtener coordenadas de una dirección (simulado)
 */
function obtenerCoordenadas($direccion) {
    // En producción, usar Google Geocoding API
    // Por ahora, coordenadas de San Juan Centro
    return [
        'lat' => -31.5375 + (rand(-100, 100) / 10000), // Variación pequeña
        'lng' => -68.5364 + (rand(-100, 100) / 10000)
    ];
}

/**
 * Enviar notificación al repartidor
 */
function enviarNotificacionRepartidor($repartidorId, $pedidoId) {
    // Aquí se integraría con Firebase, OneSignal, etc.
    error_log("📱 Notificación enviada - Repartidor: $repartidorId, Pedido: $pedidoId");
    return true;
}

/**
 * API Endpoints
 */
$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'POST':
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (isset($input['action'])) {
            switch ($input['action']) {
                case 'asignar_automatico':
                    $pedidoId = $input['pedido_id'];
                    $direccionOrigen = $input['direccion_origen'];
                    $direccionDestino = $input['direccion_destino'];
                    
                    $resultado = asignarRepartidorAutomatico($pedidoId, $direccionOrigen, $direccionDestino);
                    echo json_encode($resultado);
                    break;
                    
                case 'reasignar_pedido':
                    // Reasignar si el repartidor actual no puede
                    $pedidoId = $input['pedido_id'];
                    $motivo = $input['motivo'] ?? 'Reasignación solicitada';
                    
                    // Liberar repartidor actual
                    $stmt = $pdo->prepare("UPDATE pedidos SET repartidor_id = NULL, estado = 'pendiente' WHERE id = ?");
                    $stmt->execute([$pedidoId]);
                    
                    // Obtener datos del pedido
                    $stmt = $pdo->prepare("SELECT direccion_origen, direccion_destino FROM pedidos WHERE id = ?");
                    $stmt->execute([$pedidoId]);
                    $pedido = $stmt->fetch();
                    
                    $resultado = asignarRepartidorAutomatico($pedidoId, $pedido['direccion_origen'], $pedido['direccion_destino']);
                    echo json_encode($resultado);
                    break;
                    
                default:
                    echo json_encode(['success' => false, 'message' => 'Acción no válida']);
            }
        }
        break;
        
    case 'GET':
        // Estadísticas de asignación
        $stmt = $pdo->query("
            SELECT 
                COUNT(*) as total_asignaciones,
                AVG(distancia_km) as distancia_promedio,
                COUNT(CASE WHEN metodo_asignacion = 'automatico' THEN 1 END) as asignaciones_automaticas
            FROM asignaciones_log 
            WHERE DATE(fecha_asignacion) = CURDATE()
        ");
        $stats = $stmt->fetch();
        
        echo json_encode([
            'success' => true,
            'estadisticas' => $stats
        ]);
        break;
        
    default:
        echo json_encode(['success' => false, 'message' => 'Método no permitido']);
}
?>
