<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registro RepuMovil Plus - Taller</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .container-form {
            max-width: 700px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .card-custom {
            background: white;
            border-radius: 25px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            border: 3px solid #FFD700;
            overflow: hidden;
        }
        
        .card-header-custom {
            background: linear-gradient(135deg, #FF6B35 0%, #FFA500 100%, #FFD700 100%);
            color: white;
            padding: 30px;
            text-align: center;
            border: none;
            position: relative;
            overflow: hidden;
        }
        
        .card-header-custom::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 215, 0, 0.2), transparent);
            animation: shine 3s infinite;
        }
        
        @keyframes shine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }
        
        .logo-title {
            font-size: 36px;
            font-weight: 900;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }
        
        .logo-repu {
            color: white;
        }
        
        .logo-movil {
            color: #FFE4B5;
        }
        
        .plus-text {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 900;
            margin-left: 10px;
        }
        
        .plan-subtitle {
            font-size: 18px;
            opacity: 0.9;
            margin-bottom: 0;
            position: relative;
            z-index: 1;
        }
        
        .crown-icon {
            position: absolute;
            top: 15px;
            right: 20px;
            font-size: 30px;
            color: #FFD700;
            z-index: 1;
        }
        
        .form-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        
        .form-control, .form-select {
            border-radius: 12px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #FFD700;
            box-shadow: 0 0 0 0.2rem rgba(255, 215, 0, 0.25);
        }
        
        .btn-register {
            background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
            border: none;
            border-radius: 12px;
            padding: 18px 35px;
            color: #333;
            font-weight: 900;
            font-size: 18px;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn-register:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(255, 215, 0, 0.4);
            color: #333;
        }
        
        .back-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid white;
            color: white;
            padding: 10px 20px;
            border-radius: 10px;
            text-decoration: none;
            transition: all 0.3s ease;
            display: inline-block;
            margin-bottom: 20px;
        }
        
        .back-btn:hover {
            background: white;
            color: #667eea;
        }
        
        .maps-btn {
            background: #4285f4;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 8px 15px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .maps-btn:hover {
            background: #3367d6;
            color: white;
        }
        
        .feature-highlight {
            background: linear-gradient(135deg, #fff9e6 0%, #fffbf0 100%);
            border-left: 4px solid #FFD700;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 25px;
            border: 2px solid #FFD700;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 0;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
        }
        
        .feature-list li {
            padding: 5px 0;
            color: #FF6B35;
            font-weight: 600;
            font-size: 14px;
        }
        
        .feature-list li i {
            margin-right: 8px;
            color: #FFD700;
        }
        
        @media (max-width: 768px) {
            .feature-list {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container-form">
        <a href="seleccionar-plan-taller.php" class="back-btn">
            <i class="fas fa-arrow-left me-2"></i>Volver a Planes
        </a>

        <div class="card card-custom">
            <div class="card-header-custom">
                <i class="fas fa-crown crown-icon"></i>
                <h1 class="logo-title">
                    <i class="fas fa-tools me-2"></i>
                    <span class="logo-repu">Repu</span><span class="logo-movil">Movil</span>
                    <span class="plus-text">Plus</span>
                </h1>
                <p class="plan-subtitle">La solución completa para tu taller, desde repuestos hasta administración</p>
            </div>
            
            <div class="card-body p-4">
                <!-- Características del Plan Plus -->
                <div class="feature-highlight">
                    <h6 class="fw-bold mb-3">✨ Tu plan Premium incluye:</h6>
                    <ul class="feature-list">
                        <li><i class="fas fa-star"></i>Todo lo de RepuMovil</li>
                        <li><i class="fas fa-star"></i>Gestión de clientes</li>
                        <li><i class="fas fa-star"></i>Calendario de turnos</li>
                        <li><i class="fas fa-star"></i>Órdenes de trabajo</li>
                        <li><i class="fas fa-star"></i>Reportes avanzados</li>
                        <li><i class="fas fa-star"></i>Inventario herramientas</li>
                        <li><i class="fas fa-star"></i>Dashboard completo</li>
                        <li><i class="fas fa-star"></i>WhatsApp integrado</li>
                    </ul>
                </div>

                <form id="registroForm" method="POST" action="api/registro-taller-plus.php">
                    <input type="hidden" name="user_type" value="taller_mecanico">
                    <input type="hidden" name="plan_type" value="plus">
                    
                    <!-- Datos Básicos -->
                    <div class="mb-3">
                        <label for="nombre" class="form-label">Nombre Completo</label>
                        <input type="text" class="form-control" id="nombre" name="nombre" required>
                    </div>

                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>

                    <div class="mb-3">
                        <label for="telefono" class="form-label">Teléfono</label>
                        <input type="tel" class="form-control" id="telefono" name="telefono" required>
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label">Contraseña</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>

                    <!-- Datos del Taller -->
                    <hr class="my-4">
                    <h5 class="mb-3">🏆 Datos del Taller Premium</h5>

                    <div class="mb-3">
                        <label for="username" class="form-label">Nombre del Taller</label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>

                    <div class="mb-3">
                        <label for="direccion" class="form-label">Dirección del Taller</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="direccion" name="direccion" required>
                            <button type="button" class="btn maps-btn" onclick="abrirGoogleMaps()">
                                <i class="fas fa-map-marker-alt me-1"></i>Maps
                            </button>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="rubro_principal" class="form-label">Rubro Principal</label>
                        <select class="form-select" id="rubro_principal" name="rubro_principal" required>
                            <option value="">Selecciona tu especialidad</option>
                            <option value="mecanica_general">Mecánica General</option>
                            <option value="electricidad_automotriz">Electricidad Automotriz</option>
                            <option value="chapa_pintura">Chapa y Pintura</option>
                            <option value="neumaticos">Neumáticos</option>
                            <option value="frenos">Frenos</option>
                            <option value="suspension">Suspensión</option>
                            <option value="aire_acondicionado">Aire Acondicionado</option>
                            <option value="gnc">GNC</option>
                            <option value="diesel">Diesel</option>
                            <option value="transmision">Transmisión</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="datos_fiscales" class="form-label">Datos Fiscales (CUIT/DNI)</label>
                        <input type="text" class="form-control" id="datos_fiscales" name="datos_fiscales" required>
                    </div>

                    <!-- Campos adicionales Plus -->
                    <div class="mb-3">
                        <label for="capacidad_clientes" class="form-label">Capacidad de Clientes por Día</label>
                        <select class="form-select" id="capacidad_clientes" name="capacidad_clientes">
                            <option value="1-5">1-5 clientes</option>
                            <option value="6-10">6-10 clientes</option>
                            <option value="11-20">11-20 clientes</option>
                            <option value="21+">Más de 20 clientes</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label for="servicios_adicionales" class="form-label">Servicios Adicionales</label>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="servicios[]" value="grua" id="grua">
                                    <label class="form-check-label" for="grua">🚛 Servicio de Grúa</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="servicios[]" value="domicilio" id="domicilio">
                                    <label class="form-check-label" for="domicilio">🏠 Servicio a Domicilio</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="servicios[]" value="24hs" id="24hs">
                                    <label class="form-check-label" for="24hs">🕐 Atención 24hs</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="servicios[]" value="lavado" id="lavado">
                                    <label class="form-check-label" for="lavado">🚿 Lavado de Autos</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-register">
                        <i class="fas fa-crown me-2"></i>Crear mi Cuenta RepuMovil Plus
                    </button>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function abrirGoogleMaps() {
            const direccion = document.getElementById('direccion').value;
            if (direccion) {
                const url = `https://www.google.com/maps/search/${encodeURIComponent(direccion)}`;
                window.open(url, '_blank');
            } else {
                alert('Por favor, ingresa una dirección primero');
            }
        }

        document.getElementById('registroForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Validaciones
            const password = document.getElementById('password').value;
            if (password.length < 6) {
                alert('La contraseña debe tener al menos 6 caracteres');
                return;
            }

            // Enviar formulario
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creando cuenta Premium...';
            submitBtn.disabled = true;
            
            fetch(this.action, {
                method: 'POST',
                body: new FormData(this)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('¡Cuenta RepuMovil Plus creada exitosamente! 🎉\n\n✨ Bienvenido al plan Premium!\nRedirigiendo al login...');
                    setTimeout(() => {
                        window.location.href = 'login-dinamico.php';
                    }, 2000);
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                alert('Error de conexión. Intenta nuevamente.');
                console.error('Error:', error);
            })
            .finally(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });

        // Animación de entrada
        document.addEventListener('DOMContentLoaded', function() {
            const card = document.querySelector('.card-custom');
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            
            setTimeout(() => {
                card.style.transition = 'all 0.8s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 100);
        });
    </script>
</body>
</html>
