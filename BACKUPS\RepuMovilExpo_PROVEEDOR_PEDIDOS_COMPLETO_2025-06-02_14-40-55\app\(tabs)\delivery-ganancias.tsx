import React, { useState } from 'react';
import {
  SafeAreaView,
  StatusBar,
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Dimensions,
} from 'react-native';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';

const { width } = Dimensions.get('window');

interface GananciaDiaria {
  fecha: string;
  entregas: number;
  ganancia_bruta: number;
  comision_plataforma: number;
  ganancia_neta: number;
  tiempo_activo: string;
}

export default function DeliveryGanancias() {
  const router = useRouter();
  const [periodoSeleccionado, setPeriodoSeleccionado] = useState<'semana' | 'mes' | 'año'>('semana');

  // Datos de ejemplo de ganancias
  const gananciasSemanales: GananciaDiaria[] = [
    {
      fecha: '2024-01-15',
      entregas: 8,
      ganancia_bruta: 1200,
      comision_plataforma: 120,
      ganancia_neta: 1080,
      tiempo_activo: '6h 30m'
    },
    {
      fecha: '2024-01-14',
      entregas: 6,
      ganancia_bruta: 900,
      comision_plataforma: 90,
      ganancia_neta: 810,
      tiempo_activo: '5h 15m'
    },
    {
      fecha: '2024-01-13',
      entregas: 10,
      ganancia_bruta: 1500,
      comision_plataforma: 150,
      ganancia_neta: 1350,
      tiempo_activo: '7h 45m'
    },
    {
      fecha: '2024-01-12',
      entregas: 4,
      ganancia_bruta: 600,
      comision_plataforma: 60,
      ganancia_neta: 540,
      tiempo_activo: '3h 20m'
    },
    {
      fecha: '2024-01-11',
      entregas: 7,
      ganancia_bruta: 1050,
      comision_plataforma: 105,
      ganancia_neta: 945,
      tiempo_activo: '5h 50m'
    },
    {
      fecha: '2024-01-10',
      entregas: 9,
      ganancia_bruta: 1350,
      comision_plataforma: 135,
      ganancia_neta: 1215,
      tiempo_activo: '6h 40m'
    },
    {
      fecha: '2024-01-09',
      entregas: 5,
      ganancia_bruta: 750,
      comision_plataforma: 75,
      ganancia_neta: 675,
      tiempo_activo: '4h 10m'
    }
  ];

  const calcularEstadisticas = () => {
    const totalEntregas = gananciasSemanales.reduce((sum, dia) => sum + dia.entregas, 0);
    const totalBruto = gananciasSemanales.reduce((sum, dia) => sum + dia.ganancia_bruta, 0);
    const totalComision = gananciasSemanales.reduce((sum, dia) => sum + dia.comision_plataforma, 0);
    const totalNeto = gananciasSemanales.reduce((sum, dia) => sum + dia.ganancia_neta, 0);
    const promedioEntregas = totalEntregas / gananciasSemanales.length;
    const promedioDiario = totalNeto / gananciasSemanales.length;

    return {
      totalEntregas,
      totalBruto,
      totalComision,
      totalNeto,
      promedioEntregas: Math.round(promedioEntregas * 10) / 10,
      promedioDiario: Math.round(promedioDiario)
    };
  };

  const estadisticas = calcularEstadisticas();

  const formatearFecha = (fecha: string) => {
    const date = new Date(fecha);
    const dias = ['Dom', 'Lun', 'Mar', 'Mié', 'Jue', 'Vie', 'Sáb'];
    const meses = ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun', 'Jul', 'Ago', 'Sep', 'Oct', 'Nov', 'Dic'];
    
    return {
      dia: dias[date.getDay()],
      fecha: `${date.getDate()} ${meses[date.getMonth()]}`
    };
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#FF6B35" />
      
      {/* Header */}
      <LinearGradient
        colors={['#FF6B35', '#E53E3E']}
        style={styles.header}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <Text style={styles.backButtonText}>← Volver</Text>
        </TouchableOpacity>
        
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>💰 Mis Ganancias</Text>
          <Text style={styles.headerSubtitle}>Detalle de tus ingresos como repartidor</Text>
        </View>
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Resumen Total */}
        <View style={styles.resumenContainer}>
          <LinearGradient
            colors={['#4CAF50', '#45A049']}
            style={styles.resumenCard}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <Text style={styles.resumenLabel}>Ganancia Neta Total</Text>
            <Text style={styles.resumenMonto}>${estadisticas.totalNeto}</Text>
            <Text style={styles.resumenPeriodo}>Últimos 7 días</Text>
          </LinearGradient>
        </View>

        {/* Filtros de Período */}
        <View style={styles.filtrosContainer}>
          <Text style={styles.filtrosTitle}>📅 Período</Text>
          <View style={styles.filtrosButtons}>
            {[
              { key: 'semana', label: 'Esta Semana' },
              { key: 'mes', label: 'Este Mes' },
              { key: 'año', label: 'Este Año' }
            ].map((periodo) => (
              <TouchableOpacity
                key={periodo.key}
                style={[
                  styles.filtroButton,
                  periodoSeleccionado === periodo.key && styles.filtroButtonActive
                ]}
                onPress={() => setPeriodoSeleccionado(periodo.key as any)}
              >
                <Text style={[
                  styles.filtroText,
                  periodoSeleccionado === periodo.key && styles.filtroTextActive
                ]}>
                  {periodo.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Estadísticas Generales */}
        <View style={styles.statsContainer}>
          <Text style={styles.sectionTitle}>📊 Estadísticas Generales</Text>
          <View style={styles.statsGrid}>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>{estadisticas.totalEntregas}</Text>
              <Text style={styles.statLabel}>Total Entregas</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>{estadisticas.promedioEntregas}</Text>
              <Text style={styles.statLabel}>Promedio/Día</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>${estadisticas.promedioDiario}</Text>
              <Text style={styles.statLabel}>Ganancia/Día</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>10%</Text>
              <Text style={styles.statLabel}>Comisión</Text>
            </View>
          </View>
        </View>

        {/* Desglose Financiero */}
        <View style={styles.desgloseContainer}>
          <Text style={styles.sectionTitle}>💳 Desglose Financiero</Text>
          
          <View style={styles.desgloseCard}>
            <View style={styles.desgloseItem}>
              <Text style={styles.desgloseLabel}>💵 Ganancia Bruta</Text>
              <Text style={styles.desgloseMonto}>${estadisticas.totalBruto}</Text>
            </View>
            
            <View style={styles.desgloseSeparator} />
            
            <View style={styles.desgloseItem}>
              <Text style={styles.desgloseLabel}>📱 Comisión Plataforma (10%)</Text>
              <Text style={[styles.desgloseMonto, styles.desgloseComision]}>-${estadisticas.totalComision}</Text>
            </View>
            
            <View style={styles.desgloseSeparator} />
            
            <View style={styles.desgloseItem}>
              <Text style={[styles.desgloseLabel, styles.desgloseTotal]}>💰 Ganancia Neta</Text>
              <Text style={[styles.desgloseMonto, styles.desgloseMontoTotal]}>${estadisticas.totalNeto}</Text>
            </View>
          </View>
        </View>

        {/* Detalle Diario */}
        <View style={styles.detalleDiarioContainer}>
          <Text style={styles.sectionTitle}>📅 Detalle Diario</Text>
          
          {gananciasSemanales.map((dia, index) => {
            const fechaFormateada = formatearFecha(dia.fecha);
            return (
              <View key={index} style={styles.diaCard}>
                <View style={styles.diaHeader}>
                  <View style={styles.fechaInfo}>
                    <Text style={styles.diaSemana}>{fechaFormateada.dia}</Text>
                    <Text style={styles.fechaDia}>{fechaFormateada.fecha}</Text>
                  </View>
                  <View style={styles.gananciaInfo}>
                    <Text style={styles.gananciaNeta}>${dia.ganancia_neta}</Text>
                    <Text style={styles.entregasCount}>{dia.entregas} entregas</Text>
                  </View>
                </View>
                
                <View style={styles.diaDetalles}>
                  <View style={styles.diaDetalle}>
                    <Text style={styles.diaDetalleLabel}>Bruto:</Text>
                    <Text style={styles.diaDetalleValor}>${dia.ganancia_bruta}</Text>
                  </View>
                  <View style={styles.diaDetalle}>
                    <Text style={styles.diaDetalleLabel}>Comisión:</Text>
                    <Text style={styles.diaDetalleValor}>-${dia.comision_plataforma}</Text>
                  </View>
                  <View style={styles.diaDetalle}>
                    <Text style={styles.diaDetalleLabel}>Tiempo:</Text>
                    <Text style={styles.diaDetalleValor}>{dia.tiempo_activo}</Text>
                  </View>
                </View>
              </View>
            );
          })}
        </View>

        {/* Información de Pagos */}
        <View style={styles.pagosContainer}>
          <Text style={styles.sectionTitle}>🏦 Información de Pagos</Text>
          
          <View style={styles.pagosCard}>
            <View style={styles.pagoInfo}>
              <Text style={styles.pagoIcon}>📅</Text>
              <View style={styles.pagoTexto}>
                <Text style={styles.pagoTitulo}>Próximo Pago</Text>
                <Text style={styles.pagoDescripcion}>Viernes 19 de Enero, 2024</Text>
              </View>
            </View>
            
            <View style={styles.pagoInfo}>
              <Text style={styles.pagoIcon}>💳</Text>
              <View style={styles.pagoTexto}>
                <Text style={styles.pagoTitulo}>Método de Pago</Text>
                <Text style={styles.pagoDescripcion}>Transferencia Bancaria</Text>
              </View>
            </View>
            
            <View style={styles.pagoInfo}>
              <Text style={styles.pagoIcon}>🕐</Text>
              <View style={styles.pagoTexto}>
                <Text style={styles.pagoTitulo}>Frecuencia</Text>
                <Text style={styles.pagoDescripcion}>Pagos semanales automáticos</Text>
              </View>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    paddingTop: 20,
    paddingBottom: 25,
    paddingHorizontal: 20,
  },
  backButton: {
    alignSelf: 'flex-start',
    marginBottom: 15,
  },
  backButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  headerContent: {
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '800',
    color: 'white',
    marginBottom: 5,
  },
  headerSubtitle: {
    fontSize: 16,
    color: 'white',
    opacity: 0.9,
    textAlign: 'center',
  },
  content: {
    flex: 1,
  },
  resumenContainer: {
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  resumenCard: {
    borderRadius: 20,
    padding: 25,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 10,
    elevation: 5,
  },
  resumenLabel: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 10,
  },
  resumenMonto: {
    color: 'white',
    fontSize: 36,
    fontWeight: '800',
    marginBottom: 5,
  },
  resumenPeriodo: {
    color: 'white',
    fontSize: 14,
    opacity: 0.9,
  },
  filtrosContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  filtrosTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#2D3748',
    marginBottom: 15,
  },
  filtrosButtons: {
    flexDirection: 'row',
    gap: 10,
  },
  filtroButton: {
    flex: 1,
    backgroundColor: 'white',
    paddingVertical: 12,
    borderRadius: 10,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  filtroButtonActive: {
    backgroundColor: '#FF6B35',
    borderColor: '#FF6B35',
  },
  filtroText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '600',
  },
  filtroTextActive: {
    color: 'white',
  },
  statsContainer: {
    paddingHorizontal: 20,
    marginBottom: 25,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#2D3748',
    marginBottom: 15,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statCard: {
    backgroundColor: 'white',
    width: (width - 60) / 4,
    padding: 15,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  statNumber: {
    fontSize: 16,
    fontWeight: '800',
    color: '#FF6B35',
    marginBottom: 5,
  },
  statLabel: {
    fontSize: 10,
    color: '#666',
    fontWeight: '500',
    textAlign: 'center',
  },
  desgloseContainer: {
    paddingHorizontal: 20,
    marginBottom: 25,
  },
  desgloseCard: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  desgloseItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
  },
  desgloseLabel: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  desgloseMonto: {
    fontSize: 16,
    color: '#2D3748',
    fontWeight: '600',
  },
  desgloseComision: {
    color: '#E53E3E',
  },
  desgloseTotal: {
    fontSize: 16,
    color: '#2D3748',
    fontWeight: '700',
  },
  desgloseMontoTotal: {
    fontSize: 18,
    color: '#4CAF50',
    fontWeight: '800',
  },
  desgloseSeparator: {
    height: 1,
    backgroundColor: '#f0f0f0',
    marginVertical: 5,
  },
  detalleDiarioContainer: {
    paddingHorizontal: 20,
    marginBottom: 25,
  },
  diaCard: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  diaHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  fechaInfo: {
    alignItems: 'flex-start',
  },
  diaSemana: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FF6B35',
  },
  fechaDia: {
    fontSize: 12,
    color: '#666',
  },
  gananciaInfo: {
    alignItems: 'flex-end',
  },
  gananciaNeta: {
    fontSize: 18,
    fontWeight: '800',
    color: '#4CAF50',
  },
  entregasCount: {
    fontSize: 12,
    color: '#666',
  },
  diaDetalles: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  diaDetalle: {
    alignItems: 'center',
  },
  diaDetalleLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 2,
  },
  diaDetalleValor: {
    fontSize: 12,
    fontWeight: '600',
    color: '#2D3748',
  },
  pagosContainer: {
    paddingHorizontal: 20,
    marginBottom: 30,
  },
  pagosCard: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  pagoInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  pagoIcon: {
    fontSize: 24,
    marginRight: 15,
  },
  pagoTexto: {
    flex: 1,
  },
  pagoTitulo: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2D3748',
    marginBottom: 2,
  },
  pagoDescripcion: {
    fontSize: 12,
    color: '#666',
  },
});
