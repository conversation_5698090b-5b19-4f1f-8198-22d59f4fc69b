<?php
// Mostrar todos los errores para depuración
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Incluir archivo de configuración de la base de datos
require_once 'db_config.php';

echo "<h1>Prueba de Conexión a la Base de Datos</h1>";

try {
    // Intentar conectar a la base de datos
    $conn = connectDB();
    
    echo "<div style='color: green; font-weight: bold;'>✅ Conexión exitosa a la base de datos</div>";
    
    // Verificar tablas existentes
    $tables = $conn->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<h2>Tablas en la base de datos:</h2>";
    echo "<ul>";
    foreach ($tables as $table) {
        echo "<li>{$table}</li>";
    }
    echo "</ul>";
    
    // Verificar usuarios existentes
    if (in_array('users', $tables)) {
        $users = $conn->query("SELECT id, username, email, role_id FROM users")->fetchAll();
        
        echo "<h2>Usuarios existentes:</h2>";
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>ID</th><th>Usuario</th><th>Email</th><th>Rol ID</th></tr>";
        
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>{$user['id']}</td>";
            echo "<td>{$user['username']}</td>";
            echo "<td>{$user['email']}</td>";
            echo "<td>{$user['role_id']}</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    }
    
    // Verificar roles existentes
    if (in_array('roles', $tables)) {
        $roles = $conn->query("SELECT id, name, description FROM roles")->fetchAll();
        
        echo "<h2>Roles existentes:</h2>";
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>ID</th><th>Nombre</th><th>Descripción</th></tr>";
        
        foreach ($roles as $role) {
            echo "<tr>";
            echo "<td>{$role['id']}</td>";
            echo "<td>{$role['name']}</td>";
            echo "<td>{$role['description']}</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    }
    
    // Información del servidor MySQL
    $serverInfo = $conn->getAttribute(PDO::ATTR_SERVER_INFO);
    $serverVersion = $conn->getAttribute(PDO::ATTR_SERVER_VERSION);
    
    echo "<h2>Información del servidor MySQL:</h2>";
    echo "<ul>";
    echo "<li><strong>Versión:</strong> {$serverVersion}</li>";
    if ($serverInfo) {
        echo "<li><strong>Información adicional:</strong> {$serverInfo}</li>";
    }
    echo "</ul>";
    
    // Información de la conexión PDO
    echo "<h2>Información de la conexión PDO:</h2>";
    echo "<ul>";
    echo "<li><strong>Driver:</strong> " . $conn->getAttribute(PDO::ATTR_DRIVER_NAME) . "</li>";
    echo "<li><strong>Versión del cliente:</strong> " . $conn->getAttribute(PDO::ATTR_CLIENT_VERSION) . "</li>";
    echo "<li><strong>Modo de error:</strong> " . $conn->getAttribute(PDO::ATTR_ERRMODE) . "</li>";
    echo "</ul>";
    
    echo "<p><a href='login.php'>Ir a la página de inicio de sesión</a></p>";
    
} catch (PDOException $e) {
    echo "<div style='color: red; font-weight: bold;'>❌ Error de conexión: " . $e->getMessage() . "</div>";
    
    // Mostrar información de configuración (sin mostrar la contraseña completa)
    global $db_config;
    echo "<h2>Configuración actual:</h2>";
    echo "<ul>";
    echo "<li><strong>Host:</strong> " . $db_config['host'] . "</li>";
    echo "<li><strong>Base de datos:</strong> " . $db_config['dbname'] . "</li>";
    echo "<li><strong>Usuario:</strong> " . $db_config['username'] . "</li>";
    echo "<li><strong>Contraseña:</strong> " . (empty($db_config['password']) ? "<em>vacía</em>" : "<em>configurada</em>") . "</li>";
    echo "</ul>";
    
    echo "<h2>Posibles soluciones:</h2>";
    echo "<ol>";
    echo "<li>Verifica que el servidor MySQL esté en ejecución.</li>";
    echo "<li>Comprueba que las credenciales en el archivo <code>db_config.php</code> sean correctas.</li>";
    echo "<li>Si usas XAMPP/WAMP, asegúrate de que el servicio MySQL esté iniciado.</li>";
    echo "<li>Intenta crear manualmente la base de datos <code>{$db_config['dbname']}</code> desde phpMyAdmin.</li>";
    echo "</ol>";
}
?>