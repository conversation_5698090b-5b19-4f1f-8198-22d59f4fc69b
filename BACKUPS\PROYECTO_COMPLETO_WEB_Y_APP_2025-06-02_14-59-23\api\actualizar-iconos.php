<?php
/**
 * Script para actualizar los iconos de las categorías
 */

header('Content-Type: application/json');
require_once 'db_config.php';

try {
    $pdo = connectDB();
    
    // Actualizar iconos uno por uno
    $updates = [
        ['Motor y Combustible', 'fas fa-engine'],
        ['Frenos y Suspensión', 'fas fa-car-brake'],
        ['Sistema Eléctrico', 'fas fa-bolt'],
        ['Filtros y Aceites', 'fas fa-filter'],
        ['Neumáticos y Llantas', 'fas fa-tire'],
        ['Carrocería y Cristales', 'fas fa-car-side'],
        ['Climatización', 'fas fa-temperature-low'],
        ['Transmisión', 'fas fa-cogs']
    ];
    
    $updated = 0;
    foreach ($updates as $update) {
        $sql = "UPDATE categorias SET icono = ? WHERE nombre = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$update[1], $update[0]]);
        $updated += $stmt->rowCount();
    }
    
    echo json_encode([
        'success' => true,
        'message' => "Iconos actualizados: $updated categorías",
        'data' => $updates
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ]);
}
?>
