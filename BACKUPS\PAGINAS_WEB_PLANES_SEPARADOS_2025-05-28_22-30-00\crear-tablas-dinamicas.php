<?php
// Crear tablas para el sistema de registro dinámico
require_once 'db_config.php';

try {
    echo "<h2>🔧 Creando tablas para RepuMovil...</h2>";

    // Tabla principal de usuarios (actualizada)
    $sql_users = "
    CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        email VARCHAR(255) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        user_type ENUM('usuario_regular', 'taller_mecanico', 'mecanico_independiente', 'proveedor_repuestos') NOT NULL,
        nombre VARCHAR(255) NOT NULL,
        telefono VARCHAR(20),
        email_verified BOOLEAN DEFAULT FALSE,
        sms_verified BOOLEAN DEFAULT FALSE,
        status ENUM('active', 'inactive', 'pending') DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";

    $pdo->exec($sql_users);
    echo "✅ Tabla 'users' creada/actualizada<br>";

    // Tabla para talleres mecánicos con soporte para planes
    $sql_talleres = "
    CREATE TABLE IF NOT EXISTS talleres_mecanicos (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        username VARCHAR(100) UNIQUE,
        direccion TEXT,
        coordenadas_lat DECIMAL(10, 8),
        coordenadas_lng DECIMAL(11, 8),
        rubro_principal VARCHAR(100),
        datos_fiscales TEXT,
        plan_type ENUM('comun', 'plus') DEFAULT 'comun',
        capacidad_clientes VARCHAR(20),
        servicios_adicionales TEXT,
        verificado BOOLEAN DEFAULT FALSE,
        calificacion DECIMAL(3,2) DEFAULT 0.00,
        total_trabajos INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_ubicacion (coordenadas_lat, coordenadas_lng),
        INDEX idx_rubro (rubro_principal),
        INDEX idx_plan (plan_type)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";

    $pdo->exec($sql_talleres);
    echo "✅ Tabla 'talleres_mecanicos' creada<br>";

    // Tabla para proveedores de repuestos
    $sql_proveedores = "
    CREATE TABLE IF NOT EXISTS proveedores_repuestos (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        ubicacion_local TEXT,
        coordenadas_lat DECIMAL(10, 8),
        coordenadas_lng DECIMAL(11, 8),
        zonas_entrega TEXT,
        datos_contacto TEXT,
        horarios_atencion TEXT,
        verificado BOOLEAN DEFAULT FALSE,
        calificacion DECIMAL(3,2) DEFAULT 0.00,
        total_ventas INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_ubicacion (coordenadas_lat, coordenadas_lng)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";

    $pdo->exec($sql_proveedores);
    echo "✅ Tabla 'proveedores_repuestos' creada<br>";

    // Tabla para mecánicos independientes
    $sql_mecanicos = "
    CREATE TABLE IF NOT EXISTS mecanicos_independientes (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        especialidades TEXT,
        experiencia_anos INT,
        descripcion TEXT,
        zona_trabajo TEXT,
        precio_hora DECIMAL(10,2),
        disponible BOOLEAN DEFAULT TRUE,
        calificacion DECIMAL(3,2) DEFAULT 0.00,
        total_trabajos INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";

    $pdo->exec($sql_mecanicos);
    echo "✅ Tabla 'mecanicos_independientes' creada<br>";

    // Tabla para logs de SMS
    $sql_sms_logs = "
    CREATE TABLE IF NOT EXISTS sms_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT,
        telefono VARCHAR(20) NOT NULL,
        mensaje TEXT NOT NULL,
        tipo ENUM('confirmacion', 'verificacion', 'notificacion') NOT NULL,
        estado ENUM('enviado', 'entregado', 'fallido') DEFAULT 'enviado',
        proveedor VARCHAR(50),
        external_id VARCHAR(100),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_telefono (telefono),
        INDEX idx_fecha (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";

    $pdo->exec($sql_sms_logs);
    echo "✅ Tabla 'sms_logs' creada<br>";

    // Tabla para geocodificación de direcciones
    $sql_geocoding = "
    CREATE TABLE IF NOT EXISTS geocoding_cache (
        id INT AUTO_INCREMENT PRIMARY KEY,
        direccion_original TEXT NOT NULL,
        direccion_formateada TEXT,
        coordenadas_lat DECIMAL(10, 8),
        coordenadas_lng DECIMAL(11, 8),
        place_id VARCHAR(255),
        pais VARCHAR(100),
        ciudad VARCHAR(100),
        provincia VARCHAR(100),
        codigo_postal VARCHAR(20),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_coordenadas (coordenadas_lat, coordenadas_lng),
        INDEX idx_lugar (ciudad, provincia)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";

    $pdo->exec($sql_geocoding);
    echo "✅ Tabla 'geocoding_cache' creada<br>";

    // Insertar datos de prueba
    echo "<br><h3>📊 Insertando datos de prueba...</h3>";

    // Usuario de prueba - Taller
    $stmt = $pdo->prepare("
        INSERT IGNORE INTO users (email, password, user_type, nombre, telefono, status)
        VALUES (?, ?, ?, ?, ?, ?)
    ");
    $stmt->execute([
        '<EMAIL>',
        password_hash('123456', PASSWORD_DEFAULT),
        'taller_mecanico',
        'Taller Central',
        '+54 9 11 1234-5678',
        'active'
    ]);

    if ($stmt->rowCount() > 0) {
        $taller_user_id = $pdo->lastInsertId();

        $stmt = $pdo->prepare("
            INSERT IGNORE INTO talleres_mecanicos
            (user_id, username, direccion, rubro_principal, datos_fiscales)
            VALUES (?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            $taller_user_id,
            'taller_central',
            'Av. Corrientes 1234, CABA',
            'autos',
            'CUIT: 20-12345678-9'
        ]);
        echo "✅ Taller de prueba creado<br>";
    }

    // Usuario de prueba - Proveedor
    $stmt = $pdo->prepare("
        INSERT IGNORE INTO users (email, password, user_type, nombre, telefono, status)
        VALUES (?, ?, ?, ?, ?, ?)
    ");
    $stmt->execute([
        '<EMAIL>',
        password_hash('123456', PASSWORD_DEFAULT),
        'proveedor_repuestos',
        'Repuestos del Centro',
        '+54 9 11 8765-4321',
        'active'
    ]);

    if ($stmt->rowCount() > 0) {
        $proveedor_user_id = $pdo->lastInsertId();

        $stmt = $pdo->prepare("
            INSERT IGNORE INTO proveedores_repuestos
            (user_id, ubicacion_local, zonas_entrega, datos_contacto, horarios_atencion)
            VALUES (?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            $proveedor_user_id,
            'Av. San Martín 567, CABA',
            'Centro, Palermo, Belgrano, Villa Crespo',
            'WhatsApp: +54 9 11 8765-4321\nInstagram: @repuestoscentro',
            'Lunes a Viernes: 8:00 - 18:00\nSábados: 8:00 - 13:00\nDomingos: Cerrado'
        ]);
        echo "✅ Proveedor de prueba creado<br>";
    }

    echo "<br><h2>🎉 ¡Base de datos configurada exitosamente!</h2>";
    echo "<p><strong>Usuarios de prueba creados:</strong></p>";
    echo "<ul>";
    echo "<li><strong>Taller:</strong> <EMAIL> / 123456</li>";
    echo "<li><strong>Proveedor:</strong> <EMAIL> / 123456</li>";
    echo "</ul>";

} catch (PDOException $e) {
    echo "<div style='color: red;'>";
    echo "<h3>❌ Error al crear las tablas:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
