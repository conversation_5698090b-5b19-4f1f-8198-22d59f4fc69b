<?php
// Iniciar sesión
session_start();

// Incluir archivos necesarios
require_once '../src/User.php';
require_once '../src/UserManager.php';

// Crear instancia del gestor de usuarios
$userManager = new Src\UserManager();

// Variables para mensajes
$error = '';
$success = '';

// Procesar el formulario cuando se envía
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verificar si es login o registro
    if (isset($_POST['action'])) {
        
        // Proceso de login
        if ($_POST['action'] === 'login') {
            $email = filter_input(INPUT_POST, 'email', FILTER_SANITIZE_EMAIL);
            $password = $_POST['password'];
            $userType = $_POST['user_type'];
            
            // Validar campos
            if (empty($email) || empty($password) || empty($userType)) {
                $error = 'Por favor, complete todos los campos.';
            } else {
                // Intentar autenticar al usuario
                $user = $userManager->authenticateUser($email, $password, $userType);
                
                if ($user) {
                    // Guardar información del usuario en la sesión
                    $_SESSION['user_id'] = $user->getId();
                    $_SESSION['user_name'] = $user->getName();
                    $_SESSION['user_email'] = $user->getEmail();
                    $_SESSION['user_type'] = $user->getUserType();
                    
                    // Redirigir según el tipo de usuario
                    if ($userType === 'workshop') {
                        header('Location: dashboard-workshop.php');
                    } else {
                        header('Location: dashboard-supplier.php');
                    }
                    exit;
                } else {
                    $error = 'Credenciales incorrectas. Por favor, intente nuevamente.';
                }
            }
        }
        
        // Proceso de registro
        if ($_POST['action'] === 'register') {
            $name = filter_input(INPUT_POST, 'name', FILTER_SANITIZE_STRING);
            $email = filter_input(INPUT_POST, 'email', FILTER_SANITIZE_EMAIL);
            $password = $_POST['password'];
            $confirmPassword = $_POST['confirm_password'];
            $userType = $_POST['user_type'];
            
            // Validar campos
            if (empty($name) || empty($email) || empty($password) || empty($confirmPassword) || empty($userType)) {
                $error = 'Por favor, complete todos los campos.';
            } elseif ($password !== $confirmPassword) {
                $error = 'Las contraseñas no coinciden.';
            } elseif (strlen($password) < 6) {
                $error = 'La contraseña debe tener al menos 6 caracteres.';
            } else {
                // Verificar si el correo ya está registrado
                if ($userManager->emailExists($email)) {
                    $error = 'Este correo electrónico ya está registrado.';
                } else {
                    // Crear nuevo usuario
                    $userId = $userManager->createUser($name, $email, $password, $userType);
                    
                    if ($userId) {
                        $success = 'Registro exitoso. Ahora puede iniciar sesión.';
                    } else {
                        $error = 'Error al registrar el usuario. Por favor, intente nuevamente.';
                    }
                }
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Iniciar Sesión - AutoConnect</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <style>
        .auth-container {
            max-width: 500px;
            margin: 50px auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: var(--box-shadow);
            overflow: hidden;
        }
        
        .auth-tabs {
            display: flex;
        }
        
        .auth-tab {
            flex: 1;
            text-align: center;
            padding: 15px;
            background-color: #f4f4f4;
            cursor: pointer;
            font-weight: bold;
            transition: var(--transition);
        }
        
        .auth-tab.active {
            background-color: var(--primary-color);
            color: white;
        }
        
        .auth-content {
            padding: 30px;
        }
        
        .auth-form {
            display: none;
        }
        
        .auth-form.active {
            display: block;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-family: inherit;
            font-size: 16px;
        }
        
        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: var(--primary-color);
        }
        
        .btn-full {
            width: 100%;
            padding: 12px;
            font-size: 16px;
        }
        
        .error-message {
            background-color: #ffebee;
            color: #c62828;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .success-message {
            background-color: #e8f5e9;
            color: #2e7d32;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .auth-footer {
            text-align: center;
            margin-top: 20px;
            font-size: 14px;
            color: #666;
        }
    </style>
</head>
<body>
    <header>
        <div class="logo-container">
            <i class="fas fa-cogs logo-icon"></i>
            <h1>AutoConnect</h1>
        </div>
        <p>La plataforma que conecta talleres mecánicos con proveedores de repuestos</p>
        <nav>
            <ul>
                <li><a href="index.php">Inicio</a></li>
                <li><a href="index.php#talleres">Talleres</a></li>
                <li><a href="index.php#proveedores">Proveedores</a></li>
                <li><a href="index.php#como-funciona">Cómo Funciona</a></li>
                <li><a href="index.php#contacto">Contacto</a></li>
            </ul>
        </nav>
    </header>

    <div class="auth-container">
        <div class="auth-tabs">
            <div class="auth-tab active" id="login-tab">Iniciar Sesión</div>
            <div class="auth-tab" id="register-tab">Registrarse</div>
        </div>
        
        <div class="auth-content">
            <?php if (!empty($error)): ?>
                <div class="error-message"><?php echo $error; ?></div>
            <?php endif; ?>
            
            <?php if (!empty($success)): ?>
                <div class="success-message"><?php echo $success; ?></div>
            <?php endif; ?>
            
            <!-- Formulario de Login -->
            <form class="auth-form active" id="login-form" method="post" action="login.php">
                <input type="hidden" name="action" value="login">
                
                <div class="form-group">
                    <label for="login-email">Correo Electrónico</label>
                    <input type="email" id="login-email" name="email" required>
                </div>
                
                <div class="form-group">
                    <label for="login-password">Contraseña</label>
                    <input type="password" id="login-password" name="password" required>
                </div>
                
                <div class="form-group">
                    <label for="login-user-type">Tipo de Usuario</label>
                    <select id="login-user-type" name="user_type" required>
                        <option value="">Seleccione...</option>
                        <option value="workshop">Taller Mecánico</option>
                        <option value="supplier">Proveedor de Repuestos</option>
                    </select>
                </div>
                
                <button type="submit" class="btn btn-primary btn-full">Iniciar Sesión</button>
            </form>
            
            <!-- Formulario de Registro -->
            <form class="auth-form" id="register-form" method="post" action="login.php">
                <input type="hidden" name="action" value="register">
                
                <div class="form-group">
                    <label for="register-name">Nombre Completo / Empresa</label>
                    <input type="text" id="register-name" name="name" required>
                </div>
                
                <div class="form-group">
                    <label for="register-email">Correo Electrónico</label>
                    <input type="email" id="register-email" name="email" required>
                </div>
                
                <div class="form-group">
                    <label for="register-password">Contraseña</label>
                    <input type="password" id="register-password" name="password" required>
                </div>
                
                <div class="form-group">
                    <label for="register-confirm-password">Confirmar Contraseña</label>
                    <input type="password" id="register-confirm-password" name="confirm_password" required>
                </div>
                
                <div class="form-group">
                    <label for="register-user-type">Tipo de Usuario</label>
                    <select id="register-user-type" name="user_type" required>
                        <option value="">Seleccione...</option>
                        <option value="workshop">Taller Mecánico</option>
                        <option value="supplier">Proveedor de Repuestos</option>
                    </select>
                </div>
                
                <button type="submit" class="btn btn-primary btn-full">Registrarse</button>
            </form>
            
            <div class="auth-footer">
                <p>Al registrarse, acepta nuestros <a href="#">Términos y Condiciones</a> y <a href="#">Política de Privacidad</a>.</p>
            </div>
        </div>
    </div>

    <footer>
        <div class="footer-content">
            <div class="footer-logo">
                <i class="fas fa-cogs logo-icon"></i>
                <h3>AutoConnect</h3>
            </div>
            <div class="footer-links">
                <h4>Enlaces Rápidos</h4>
                <ul>
                    <li><a href="index.php#talleres">Talleres</a></li>
                    <li><a href="index.php#proveedores">Proveedores</a></li>
                    <li><a href="index.php#como-funciona">Cómo Funciona</a></li>
                    <li><a href="index.php#contacto">Contacto</a></li>
                </ul>
            </div>
            <div class="footer-social">
                <h4>Síguenos</h4>
                <div class="social-icons">
                    <a href="#"><i class="fab fa-facebook"></i></a>
                    <a href="#"><i class="fab fa-twitter"></i></a>
                    <a href="#"><i class="fab fa-instagram"></i></a>
                    <a href="#"><i class="fab fa-linkedin"></i></a>
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; 2023 AutoConnect. Todos los derechos reservados.</p>
        </div>
    </footer>

    <script>
        // Cambiar entre formularios de login y registro
        document.getElementById('login-tab').addEventListener('click', function() {
            document.getElementById('login-tab').classList.add('active');
            document.getElementById('register-tab').classList.remove('active');
            document.getElementById('login-form').classList.add('active');
            document.getElementById('register-form').classList.remove('active');
        });
        
        document.getElementById('register-tab').addEventListener('click', function() {
            document.getElementById('register-tab').classList.add('active');
            document.getElementById('login-tab').classList.remove('active');
            document.getElementById('register-form').classList.add('active');
            document.getElementById('login-form').classList.remove('active');
        });
    </script>
</body>
</html>
