import * as Location from 'expo-location';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Coordinates, LocationData } from './GoogleMapsService';

export interface LocationUpdate {
  repartidorId: string;
  coords: Coordinates;
  timestamp: number;
  accuracy?: number;
  heading?: number;
  speed?: number;
}

class LocationService {
  private static instance: LocationService;
  private watchId: Location.LocationSubscription | null = null;
  private isTracking: boolean = false;
  private lastKnownLocation: LocationData | null = null;
  private locationUpdateCallbacks: ((location: LocationData) => void)[] = [];

  private constructor() {}

  static getInstance(): LocationService {
    if (!LocationService.instance) {
      LocationService.instance = new LocationService();
    }
    return LocationService.instance;
  }

  // Verificar y solicitar permisos de ubicación
  async requestLocationPermissions(): Promise<boolean> {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      
      if (status !== 'granted') {
        console.log('Permiso de ubicación denegado');
        return false;
      }

      // También solicitar permisos de background si es necesario
      const backgroundStatus = await Location.requestBackgroundPermissionsAsync();
      console.log('Permisos de background:', backgroundStatus.status);

      return true;
    } catch (error) {
      console.error('Error solicitando permisos:', error);
      return false;
    }
  }

  // Obtener ubicación actual una sola vez
  async getCurrentLocation(): Promise<LocationData | null> {
    try {
      const hasPermission = await this.requestLocationPermissions();
      if (!hasPermission) return null;

      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
        maximumAge: 10000, // Usar ubicación de hasta 10 segundos
      });

      const locationData: LocationData = {
        coords: {
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
        },
        timestamp: location.timestamp,
        accuracy: location.coords.accuracy || undefined,
      };

      this.lastKnownLocation = locationData;
      await this.saveLocationToStorage(locationData);

      return locationData;
    } catch (error) {
      console.error('Error obteniendo ubicación actual:', error);
      return await this.getLastKnownLocation();
    }
  }

  // PASO 2: Iniciar seguimiento de ubicación en tiempo real con envío al servidor
  async startLocationTracking(
    deliveryId: number,
    pedidoId?: number,
    callback?: (location: LocationData) => void
  ): Promise<boolean> {
    try {
      if (this.isTracking) {
        console.log('🔄 Ya se está rastreando la ubicación');
        return true;
      }

      const hasPermission = await this.requestLocationPermissions();
      if (!hasPermission) return false;

      if (callback) {
        this.locationUpdateCallbacks.push(callback);
      }

      console.log(`🚀 Iniciando tracking GPS para delivery ${deliveryId}${pedidoId ? `, pedido ${pedidoId}` : ''}`);

      this.watchId = await Location.watchPositionAsync(
        {
          accuracy: Location.Accuracy.BestForNavigation, // PASO 2: Máxima precisión
          timeInterval: 3000, // PASO 2: Actualizar cada 3 segundos (más frecuente)
          distanceInterval: 5, // PASO 2: O cuando se mueva 5 metros (más sensible)
        },
        async (location) => {
          const locationData: LocationData = {
            coords: {
              latitude: location.coords.latitude,
              longitude: location.coords.longitude,
            },
            timestamp: location.timestamp,
            accuracy: location.coords.accuracy || undefined,
          };

          this.lastKnownLocation = locationData;
          await this.saveLocationToStorage(locationData);

          // PASO 2: Enviar ubicación al servidor en tiempo real
          await this.sendLocationToServer(deliveryId, locationData, pedidoId);

          // Notificar a todos los callbacks
          this.locationUpdateCallbacks.forEach(cb => cb(locationData));

          console.log('📍 Ubicación actualizada:', {
            lat: location.coords.latitude.toFixed(6),
            lng: location.coords.longitude.toFixed(6),
            accuracy: location.coords.accuracy?.toFixed(1),
            speed: location.coords.speed?.toFixed(1)
          });
        }
      );

      this.isTracking = true;
      console.log('✅ Tracking GPS iniciado correctamente');
      return true;
    } catch (error) {
      console.error('❌ Error iniciando seguimiento:', error);
      return false;
    }
  }

  // Detener seguimiento de ubicación
  stopLocationTracking(): void {
    if (this.watchId) {
      this.watchId.remove();
      this.watchId = null;
    }
    this.isTracking = false;
    this.locationUpdateCallbacks = [];
    console.log('Seguimiento de ubicación detenido');
  }

  // Agregar callback para actualizaciones de ubicación
  addLocationUpdateCallback(callback: (location: LocationData) => void): void {
    this.locationUpdateCallbacks.push(callback);
  }

  // Remover callback
  removeLocationUpdateCallback(callback: (location: LocationData) => void): void {
    this.locationUpdateCallbacks = this.locationUpdateCallbacks.filter(cb => cb !== callback);
  }

  // Obtener última ubicación conocida
  getLastKnownLocation(): Promise<LocationData | null> {
    if (this.lastKnownLocation) {
      return Promise.resolve(this.lastKnownLocation);
    }
    return this.loadLocationFromStorage();
  }

  // Guardar ubicación en AsyncStorage
  private async saveLocationToStorage(location: LocationData): Promise<void> {
    try {
      await AsyncStorage.setItem('lastKnownLocation', JSON.stringify(location));
    } catch (error) {
      console.error('Error guardando ubicación:', error);
    }
  }

  // Cargar ubicación desde AsyncStorage
  private async loadLocationFromStorage(): Promise<LocationData | null> {
    try {
      const locationStr = await AsyncStorage.getItem('lastKnownLocation');
      if (locationStr) {
        return JSON.parse(locationStr);
      }
    } catch (error) {
      console.error('Error cargando ubicación:', error);
    }
    return null;
  }

  // Verificar si el seguimiento está activo
  isLocationTracking(): boolean {
    return this.isTracking;
  }

  // Obtener distancia entre dos puntos
  calculateDistance(point1: Coordinates, point2: Coordinates): number {
    const R = 6371; // Radio de la Tierra en km
    const dLat = this.toRad(point2.latitude - point1.latitude);
    const dLon = this.toRad(point2.longitude - point1.longitude);
    
    const a = 
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRad(point1.latitude)) * Math.cos(this.toRad(point2.latitude)) *
      Math.sin(dLon / 2) * Math.sin(dLon / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;
    
    return Math.round(distance * 1000); // Retornar en metros
  }

  private toRad(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  // Simular ubicación para testing (solo en desarrollo)
  async simulateLocationUpdate(coords: Coordinates): Promise<void> {
    if (__DEV__) {
      const locationData: LocationData = {
        coords,
        timestamp: Date.now(),
        accuracy: 5,
      };

      this.lastKnownLocation = locationData;
      this.locationUpdateCallbacks.forEach(cb => cb(locationData));
    }
  }

  // PASO 2: Enviar ubicación al servidor en tiempo real
  private async sendLocationToServer(
    deliveryId: number,
    locationData: LocationData,
    pedidoId?: number
  ): Promise<boolean> {
    try {
      const payload = {
        action: 'update_location',
        delivery_id: deliveryId,
        pedido_id: pedidoId,
        latitude: locationData.coords.latitude,
        longitude: locationData.coords.longitude,
        accuracy: locationData.accuracy,
        timestamp: locationData.timestamp,
        speed: null, // Agregar si está disponible
        heading: null, // Agregar si está disponible
      };

      const response = await fetch('http://localhost/mechanical-workshop/public/api/tracking.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const result = await response.json();

      if (result.success) {
        console.log('📤 Ubicación enviada al servidor correctamente');
        return true;
      } else {
        console.error('❌ Error del servidor al guardar ubicación:', result.message);
        return false;
      }

    } catch (error) {
      console.error('❌ Error enviando ubicación al servidor:', error);
      return false;
    }
  }

  // PASO 2: Obtener ubicación actual y enviarla al servidor
  async getCurrentLocationAndSend(deliveryId: number, pedidoId?: number): Promise<LocationData | null> {
    try {
      const locationData = await this.getCurrentLocation();
      if (locationData) {
        await this.sendLocationToServer(deliveryId, locationData, pedidoId);
      }
      return locationData;
    } catch (error) {
      console.error('❌ Error obteniendo y enviando ubicación:', error);
      return null;
    }
  }

  // Limpiar datos almacenados
  async clearStoredLocation(): Promise<void> {
    try {
      await AsyncStorage.removeItem('lastKnownLocation');
      this.lastKnownLocation = null;
    } catch (error) {
      console.error('Error limpiando ubicación almacenada:', error);
    }
  }
}

export default LocationService;
