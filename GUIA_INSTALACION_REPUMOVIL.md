# 🛠️ GUÍA DE INSTALACIÓN Y CONFIGURACIÓN - REPUMOVIL

## 📋 **REQUISITOS DEL SISTEMA**

### **Servidor de Desarrollo:**
- **XAMPP 8.2+** (Apache, MySQL, PHP)
- **Node.js 18+**
- **Expo CLI**
- **Git**
- **Editor de código** (VS Code recomendado)

### **Servidor de Producción:**
- **Apache 2.4+**
- **PHP 8.0+**
- **MySQL 8.0+**
- **SSL Certificate**
- **2GB RAM mínimo**
- **10GB espacio en disco**

---

## 🚀 **INSTALACIÓN PASO A PASO**

### **PASO 1: CONFIGURAR ENTORNO DE DESARROLLO**

#### **1.1 Instalar XAMPP:**
1. <PERSON><PERSON><PERSON> desde: https://www.apachefriends.org/
2. Ejecutar instalador y seguir pasos
3. Iniciar Apache y MySQL desde el panel de control
4. Verificar que funcione en: http://localhost

#### **1.2 Instalar Node.js:**
1. <PERSON><PERSON><PERSON> desde: https://nodejs.org/
2. Instalar versión LTS (18+)
3. Verificar instalación:
```bash
node --version
npm --version
```

#### **1.3 Instalar Expo CLI:**
```bash
npm install -g @expo/cli
expo --version
```

### **PASO 2: CONFIGURAR BASE DE DATOS**

#### **2.1 Crear Base de Datos:**
1. Abrir phpMyAdmin: http://localhost/phpmyadmin
2. Crear nueva base de datos: `mechanical_workshop`
3. Configurar charset: `utf8_general_ci`

#### **2.2 Ejecutar Scripts SQL:**
```sql
-- Ejecutar en orden:
SOURCE db/01_usuarios.sql;
SOURCE db/02_notificaciones.sql;
SOURCE db/03_pedidos_basicos.sql;
SOURCE db/04_notificaciones_push.sql;
SOURCE db/05_tracking_gps.sql;
```

#### **2.3 Verificar Tablas Creadas:**
```sql
SHOW TABLES;
-- Deberías ver 15+ tablas
```

### **PASO 3: CONFIGURAR BACKEND**

#### **3.1 Ubicar Archivos:**
```
C:\xampp\htdocs\mechanical-workshop\
├── public/
│   ├── api/
│   │   ├── notifications.php
│   │   ├── tracking.php
│   │   └── pedidos-delivery.php
│   ├── test-notifications.php
│   ├── test-tracking.php
│   └── test-pedidos.php
└── db/
    ├── 01_usuarios.sql
    ├── 02_notificaciones.sql
    ├── 03_pedidos_basicos.sql
    ├── 04_notificaciones_push.sql
    └── 05_tracking_gps.sql
```

#### **3.2 Configurar Variables:**
Editar archivos PHP con configuración correcta:
```php
// En todos los archivos API
$host = 'localhost';
$dbname = 'mechanical_workshop';
$username = 'root';
$password = ''; // Tu password de MySQL
```

#### **3.3 Probar APIs:**
```bash
# Probar notificaciones
curl http://localhost/mechanical-workshop/public/api/notifications.php?action=get_tokens

# Probar tracking
curl http://localhost/mechanical-workshop/public/api/tracking.php?action=get_active_deliveries

# Probar pedidos
curl http://localhost/mechanical-workshop/public/api/pedidos-delivery.php?action=get_pedidos_disponibles&delivery_id=1
```

### **PASO 4: CONFIGURAR APP MÓVIL**

#### **4.1 Clonar/Crear Proyecto:**
```bash
# Si tienes el código:
cd RepuMovilExpo

# Si empiezas desde cero:
npx create-expo-app RepuMovilExpo --template tabs
cd RepuMovilExpo
```

#### **4.2 Instalar Dependencias:**
```bash
npx expo install expo-notifications expo-device expo-constants expo-location
npm install @expo/vector-icons expo-linear-gradient
```

#### **4.3 Configurar app.json:**
```json
{
  "expo": {
    "name": "RepuMovil",
    "slug": "repumovil",
    "version": "1.0.0",
    "platforms": ["ios", "android"],
    "permissions": [
      "NOTIFICATIONS",
      "ACCESS_FINE_LOCATION",
      "ACCESS_COARSE_LOCATION",
      "ACCESS_BACKGROUND_LOCATION"
    ],
    "android": {
      "permissions": [
        "android.permission.ACCESS_FINE_LOCATION",
        "android.permission.ACCESS_COARSE_LOCATION",
        "android.permission.ACCESS_BACKGROUND_LOCATION",
        "android.permission.RECEIVE_BOOT_COMPLETED",
        "android.permission.VIBRATE"
      ]
    },
    "ios": {
      "infoPlist": {
        "NSLocationWhenInUseUsageDescription": "RepuMovil necesita acceso a tu ubicación para mostrar tu posición a los clientes.",
        "NSLocationAlwaysAndWhenInUseUsageDescription": "RepuMovil necesita acceso a tu ubicación para el tracking en tiempo real."
      }
    }
  }
}
```

#### **4.4 Configurar URLs de API:**
```javascript
// En services/NotificationService.ts, LocationService.ts, etc.
const API_BASE_URL = 'http://localhost/mechanical-workshop/public/api';

// Para testing en dispositivo físico, usar IP local:
const API_BASE_URL = 'http://192.168.1.XXX/mechanical-workshop/public/api';
```

### **PASO 5: TESTING INICIAL**

#### **5.1 Probar Backend:**
```bash
# Ejecutar tests automáticos
cd C:\xampp\htdocs\mechanical-workshop
php test-api-tracking.php
php test-sistema-pedidos.php
```

#### **5.2 Probar App Móvil:**
```bash
cd RepuMovilExpo
npx expo start
```

#### **5.3 Verificar Funcionalidades:**
- ✅ Notificaciones push funcionando
- ✅ GPS tracking activo
- ✅ Modal de pedidos aparece
- ✅ APIs responden correctamente

---

## 🔧 **CONFIGURACIÓN AVANZADA**

### **Configurar HTTPS (Producción):**
```apache
# En .htaccess
RewriteEngine On
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
```

### **Optimizar Base de Datos:**
```sql
-- Índices adicionales para rendimiento
CREATE INDEX idx_pedidos_estado_fecha ON pedidos(estado, fecha_creacion);
CREATE INDEX idx_tracking_delivery_fecha ON delivery_locations(delivery_id, fecha_registro);
CREATE INDEX idx_notifications_fecha ON notificaciones_log(fecha_envio);
```

### **Configurar Backup Automático:**
```bash
# Script de backup diario
#!/bin/bash
mysqldump -u root -p mechanical_workshop > backup_$(date +%Y%m%d).sql
```

---

## 🧪 **HERRAMIENTAS DE TESTING**

### **Paneles Web de Testing:**
- **Notificaciones:** http://localhost/mechanical-workshop/public/test-notifications.php
- **Tracking GPS:** http://localhost/mechanical-workshop/public/test-tracking.php
- **Pedidos:** http://localhost/mechanical-workshop/public/test-pedidos.php
- **Monitor en vivo:** http://localhost/mechanical-workshop/public/monitor-tracking.php

### **Scripts de Testing Automático:**
```bash
# Testing completo del sistema
php test-api-tracking.php
php test-sistema-pedidos.php
php simulate-mobile-tracking.php
```

### **Testing en Dispositivos:**
```bash
# Para Android
npx expo start --android

# Para iOS
npx expo start --ios

# Para dispositivo físico (tunnel)
npx expo start --tunnel
```

---

## 🚨 **TROUBLESHOOTING**

### **Problemas Comunes:**

#### **Error: "Access denied for user 'root'"**
```sql
-- Solución: Configurar password de MySQL
ALTER USER 'root'@'localhost' IDENTIFIED BY 'tu_password';
FLUSH PRIVILEGES;
```

#### **Error: "CORS policy"**
```php
// Agregar en archivos PHP:
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
```

#### **Error: "Expo CLI not found"**
```bash
npm install -g @expo/cli
# O usar npx:
npx expo start
```

#### **Error: "Network request failed"**
```javascript
// Verificar URL en la app:
// Para emulador: http://localhost
// Para dispositivo físico: http://192.168.1.XXX
```

### **Logs de Debugging:**
```bash
# Logs de Apache
tail -f C:\xampp\apache\logs\error.log

# Logs de MySQL
tail -f C:\xampp\mysql\data\*.err

# Logs de Expo
npx expo start --clear
```

---

## 📊 **VERIFICACIÓN DE INSTALACIÓN**

### **Checklist Final:**

#### **Backend:**
- [ ] XAMPP funcionando (Apache + MySQL)
- [ ] Base de datos creada con todas las tablas
- [ ] APIs respondiendo correctamente
- [ ] Paneles de testing accesibles

#### **App Móvil:**
- [ ] Expo CLI instalado
- [ ] Dependencias instaladas
- [ ] App inicia sin errores
- [ ] Permisos configurados

#### **Funcionalidades:**
- [ ] Notificaciones push funcionando
- [ ] GPS tracking activo
- [ ] Modal de pedidos aparece
- [ ] Estados del delivery cambian

#### **Testing:**
- [ ] Scripts automáticos pasan
- [ ] Paneles web funcionan
- [ ] App se conecta al backend
- [ ] Datos se guardan en BD

---

## 🎯 **PRÓXIMOS PASOS**

### **Después de la Instalación:**
1. **Crear usuarios de prueba** en la base de datos
2. **Configurar datos de ejemplo** (clientes, repuestos)
3. **Probar flujo completo** de pedidos
4. **Configurar notificaciones** en dispositivos reales
5. **Optimizar rendimiento** según uso

### **Para Producción:**
1. **Configurar dominio** y SSL
2. **Migrar base de datos** a servidor
3. **Configurar backups** automáticos
4. **Implementar monitoreo** de logs
5. **Configurar CDN** para assets

---

## 📞 **SOPORTE DE INSTALACIÓN**

### **¿Necesitas ayuda?**
- **Email:** <EMAIL>
- **WhatsApp:** +54 264 XXX-XXXX
- **Horario:** Lunes a Viernes 9:00-18:00

### **Recursos Adicionales:**
- **Documentación técnica completa**
- **Videos tutoriales** (próximamente)
- **Foro de desarrolladores**
- **Base de conocimientos**

---

## ✅ **¡INSTALACIÓN COMPLETADA!**

**¡Felicitaciones!** 🎉 Has instalado exitosamente RepuMovil.

**El sistema está listo para:**
- ✅ Recibir y procesar pedidos
- ✅ Manejar deliveries en tiempo real
- ✅ Enviar notificaciones push
- ✅ Trackear ubicaciones GPS
- ✅ Escalar a producción

**¡Que tengas mucho éxito con RepuMovil!** 🚚📱💪
