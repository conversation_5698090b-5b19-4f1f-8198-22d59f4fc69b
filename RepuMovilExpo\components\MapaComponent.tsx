import React, { useState, useEffect, useRef } from 'react';
import { View, StyleSheet, Alert, Text, TouchableOpacity, Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import GoogleMapsService, { Coordinates, RouteInfo, LocationData } from '../services/GoogleMapsService';
import LocationService from '../services/LocationService';

// Importación condicional de React Native Maps
let MapView: any = null;
let Marker: any = null;
let Polyline: any = null;
let PROVIDER_GOOGLE: any = null;

try {
  const RNMaps = require('react-native-maps');
  MapView = RNMaps.default;
  Marker = RNMaps.Marker;
  Polyline = RNMaps.Polyline;
  PROVIDER_GOOGLE = RNMaps.PROVIDER_GOOGLE;
} catch (error) {
  console.log('React Native Maps no disponible, usando componente alternativo');
}



interface MapaComponentProps {
  clienteLocation?: Coordinates;
  pedidoId?: string;
  onLocationUpdate?: (location: LocationData) => void;
  showRoute?: boolean;
}

interface MarkerData {
  id: string;
  coordinate: Coordinates;
  title: string;
  description: string;
  type: 'repartidor' | 'cliente' | 'proveedor';
}

// Componente alternativo cuando React Native Maps no está disponible
const AlternativeMapView = ({ children, style, initialRegion }: any) => {
  return (
    <View style={[style, { backgroundColor: '#E8F5E8' }]}>
      <View style={{
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: 20,
      }}>
        <View style={{
          backgroundColor: 'white',
          padding: 30,
          borderRadius: 15,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
          elevation: 5,
          maxWidth: 350,
          width: '100%',
        }}>
          <Text style={{
            fontSize: 24,
            fontWeight: 'bold',
            color: '#4CAF50',
            textAlign: 'center',
            marginBottom: 15,
          }}>
            🗺️ RepuMovil Maps
          </Text>

          <Text style={{
            fontSize: 16,
            color: '#666',
            textAlign: 'center',
            marginBottom: 20,
          }}>
            Sistema de navegación y tracking
          </Text>

          <View style={{
            backgroundColor: '#f8f9fa',
            padding: 20,
            borderRadius: 10,
            marginBottom: 20,
          }}>
            <Text style={{
              fontSize: 14,
              color: '#4CAF50',
              fontWeight: 'bold',
              textAlign: 'center',
              marginBottom: 10,
            }}>
              📍 Ubicación Actual
            </Text>
            <Text style={{
              fontSize: 12,
              color: '#666',
              textAlign: 'center',
            }}>
              San Juan, Argentina{'\n'}
              Lat: {initialRegion?.latitude?.toFixed(4)}{'\n'}
              Lng: {initialRegion?.longitude?.toFixed(4)}
            </Text>
          </View>

          <View style={{
            flexDirection: 'row',
            justifyContent: 'space-around',
            marginBottom: 20,
          }}>
            <View style={{ alignItems: 'center' }}>
              <Text style={{ fontSize: 24, marginBottom: 5 }}>🏍️</Text>
              <Text style={{ fontSize: 12, color: '#666' }}>Repartidor</Text>
            </View>
            <View style={{ alignItems: 'center' }}>
              <Text style={{ fontSize: 24, marginBottom: 5 }}>📍</Text>
              <Text style={{ fontSize: 12, color: '#666' }}>Cliente</Text>
            </View>
            <View style={{ alignItems: 'center' }}>
              <Text style={{ fontSize: 24, marginBottom: 5 }}>🏪</Text>
              <Text style={{ fontSize: 12, color: '#666' }}>Proveedor</Text>
            </View>
          </View>

          <View style={{
            backgroundColor: '#4CAF50',
            padding: 15,
            borderRadius: 10,
          }}>
            <Text style={{
              color: 'white',
              fontWeight: 'bold',
              textAlign: 'center',
              marginBottom: 5,
            }}>
              🛣️ Ruta Activa
            </Text>
            <Text style={{
              color: 'white',
              fontSize: 12,
              textAlign: 'center',
            }}>
              Distancia: 2.5 km • Tiempo: 8 min
            </Text>
          </View>
        </View>

        {children}
      </View>
    </View>
  );
};



const MapaComponent: React.FC<MapaComponentProps> = ({
  clienteLocation,
  pedidoId,
  onLocationUpdate,
  showRoute = true,
}) => {
  const mapRef = useRef<MapView>(null);
  const [repartidorLocation, setRepartidorLocation] = useState<Coordinates | null>(null);
  const [markers, setMarkers] = useState<MarkerData[]>([]);
  const [routeCoordinates, setRouteCoordinates] = useState<Coordinates[]>([]);
  const [routeInfo, setRouteInfo] = useState<RouteInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isTracking, setIsTracking] = useState(false);

  const googleMapsService = GoogleMapsService.getInstance();
  const locationService = LocationService.getInstance();

  useEffect(() => {
    initializeMap();
    return () => {
      locationService.stopLocationTracking();
    };
  }, []);

  useEffect(() => {
    if (repartidorLocation && clienteLocation && showRoute) {
      calculateRoute();
    }
  }, [repartidorLocation, clienteLocation, showRoute]);

  const initializeMap = async () => {
    try {
      setIsLoading(true);

      // Obtener ubicación actual del repartidor
      const location = await locationService.getCurrentLocation();
      if (location) {
        setRepartidorLocation(location.coords);

        // Notificar actualización de ubicación
        if (onLocationUpdate) {
          onLocationUpdate(location);
        }
      }

      // Configurar markers iniciales
      updateMarkers(location?.coords, clienteLocation);



      setIsLoading(false);
    } catch (error) {
      console.error('Error inicializando mapa:', error);
      Alert.alert('Error', 'No se pudo cargar el mapa');
      setIsLoading(false);
    }
  };

  const updateMarkers = (repartidorCoords?: Coordinates, clienteCoords?: Coordinates) => {
    const newMarkers: MarkerData[] = [];

    // Marker del repartidor
    if (repartidorCoords) {
      newMarkers.push({
        id: 'repartidor',
        coordinate: repartidorCoords,
        title: 'Tu ubicación',
        description: 'Repartidor RepuMovil',
        type: 'repartidor',
      });
    }

    // Marker del cliente
    if (clienteCoords) {
      newMarkers.push({
        id: 'cliente',
        coordinate: clienteCoords,
        title: 'Cliente',
        description: 'Destino de entrega',
        type: 'cliente',
      });
    }

    setMarkers(newMarkers);
  };

  const calculateRoute = async () => {
    if (!repartidorLocation || !clienteLocation) return;

    try {
      const route = await googleMapsService.getRoute(repartidorLocation, clienteLocation);
      if (route) {
        setRouteInfo(route);
        const decodedCoordinates = googleMapsService.decodePolyline(route.polyline);
        setRouteCoordinates(decodedCoordinates);
      }
    } catch (error) {
      console.error('Error calculando ruta:', error);
    }
  };

  const startLocationTracking = async () => {
    const success = await locationService.startLocationTracking((location) => {
      setRepartidorLocation(location.coords);
      updateMarkers(location.coords, clienteLocation);
      
      if (onLocationUpdate) {
        onLocationUpdate(location);
      }
    });

    if (success) {
      setIsTracking(true);
    } else {
      Alert.alert('Error', 'No se pudo iniciar el seguimiento de ubicación');
    }
  };

  const stopLocationTracking = () => {
    locationService.stopLocationTracking();
    setIsTracking(false);
  };

  const centerOnUser = async () => {
    const location = await locationService.getCurrentLocation();
    if (location && mapRef.current) {
      mapRef.current.animateToRegion({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      }, 1000);
    }
  };

  const fitToMarkers = () => {
    if (markers.length > 0 && mapRef.current) {
      mapRef.current.fitToCoordinates(
        markers.map(marker => marker.coordinate),
        {
          edgePadding: { top: 50, right: 50, bottom: 50, left: 50 },
          animated: true,
        }
      );
    }
  };

  const getMarkerIcon = (type: string) => {
    switch (type) {
      case 'repartidor':
        return '🏍️';
      case 'cliente':
        return '📍';
      case 'proveedor':
        return '🏪';
      default:
        return '📍';
    }
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Cargando mapa...</Text>
      </View>
    );
  }

  // Determinar qué componente de mapa usar
  const MapComponent = MapView ? MapView : AlternativeMapView;
  const MarkerComponent = Marker || View;
  const PolylineComponent = Polyline || View;

  return (
    <View style={styles.container}>
      <MapComponent
        ref={mapRef}
        style={styles.map}
        provider={PROVIDER_GOOGLE}
        initialRegion={{
          latitude: repartidorLocation?.latitude || -31.5375,
          longitude: repartidorLocation?.longitude || -68.5364,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        }}
        showsUserLocation={false}
        showsMyLocationButton={false}
        showsTraffic={true}
        showsBuildings={true}
      >
        {/* Markers - solo si MapView está disponible */}
        {MapView && markers.map((marker) => (
          <MarkerComponent
            key={marker.id}
            coordinate={marker.coordinate}
            title={marker.title}
            description={marker.description}
          >
            <View style={[
              styles.markerContainer,
              marker.type === 'repartidor' ? styles.repartidorMarker : styles.clienteMarker
            ]}>
              <Text style={styles.markerText}>
                {getMarkerIcon(marker.type)}
              </Text>
            </View>
          </MarkerComponent>
        ))}

        {/* Ruta - solo si MapView está disponible */}
        {MapView && routeCoordinates.length > 0 && (
          <PolylineComponent
            coordinates={routeCoordinates}
            strokeColor="#4CAF50"
            strokeWidth={4}
            lineDashPattern={[1]}
          />
        )}
      </MapComponent>

      {/* Información de la ruta */}
      {routeInfo && (
        <View style={styles.routeInfo}>
          <Text style={styles.routeText}>
            📏 {routeInfo.distance} • ⏱️ {routeInfo.duration}
          </Text>
        </View>
      )}

      {/* Controles */}
      <View style={styles.controls}>
        <TouchableOpacity
          style={styles.controlButton}
          onPress={centerOnUser}
        >
          <Ionicons name="locate" size={24} color="#4CAF50" />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.controlButton}
          onPress={fitToMarkers}
        >
          <Ionicons name="resize" size={24} color="#4CAF50" />
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.controlButton,
            isTracking ? styles.trackingActive : null
          ]}
          onPress={isTracking ? stopLocationTracking : startLocationTracking}
        >
          <Ionicons 
            name={isTracking ? "pause" : "play"} 
            size={24} 
            color={isTracking ? "#FF6B35" : "#4CAF50"} 
          />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  map: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
    marginTop: 10,
  },
  markerContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: '#fff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  repartidorMarker: {
    backgroundColor: '#4CAF50',
  },
  clienteMarker: {
    backgroundColor: '#FF6B35',
  },
  markerText: {
    fontSize: 18,
  },
  routeInfo: {
    position: 'absolute',
    top: 50,
    left: 20,
    right: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    padding: 15,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
  routeText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    textAlign: 'center',
  },
  controls: {
    position: 'absolute',
    bottom: 30,
    right: 20,
    flexDirection: 'column',
    gap: 10,
  },
  controlButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
  trackingActive: {
    backgroundColor: 'rgba(255, 107, 53, 0.1)',
  },
});

export default MapaComponent;
