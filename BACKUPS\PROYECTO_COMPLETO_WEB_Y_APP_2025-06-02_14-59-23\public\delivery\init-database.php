<?php
require_once 'config.php';

echo "<h1>🚀 Inicializando Base de Datos RepuMovil Delivery</h1>";

try {
    // Conectar y crear base de datos
    $pdo = connectDB();
    
    echo "<h2>✅ Conexión exitosa a la base de datos</h2>";
    
    // Verificar que las tablas existen
    $tables = [
        'usuarios',
        'datos_personales', 
        'datos_pago',
        'vehiculos',
        'documentos',
        'declaraciones_juradas',
        'pedidos',
        'ganancias',
        'ubicaciones',
        'calificaciones'
    ];
    
    echo "<h2>📋 Verificando tablas:</h2>";
    
    foreach ($tables as $table) {
        $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        
        if ($stmt->fetch()) {
            echo "<p>✅ Tabla '$table' existe</p>";
        } else {
            echo "<p>❌ Tabla '$table' NO existe</p>";
        }
    }
    
    // Crear directorios de upload
    echo "<h2>📁 Creando directorios de upload:</h2>";
    
    $upload_dirs = [
        'uploads/',
        'uploads/dni/',
        'uploads/licencias/',
        'uploads/selfies/',
        'uploads/seguros/',
        'uploads/antecedentes/',
        'logs/'
    ];
    
    foreach ($upload_dirs as $dir) {
        $full_path = __DIR__ . '/' . $dir;
        
        if (!file_exists($full_path)) {
            if (mkdir($full_path, 0755, true)) {
                echo "<p>✅ Directorio '$dir' creado</p>";
            } else {
                echo "<p>❌ Error creando directorio '$dir'</p>";
            }
        } else {
            echo "<p>✅ Directorio '$dir' ya existe</p>";
        }
        
        // Crear archivo .htaccess para proteger uploads
        if (strpos($dir, 'uploads/') === 0) {
            $htaccess_path = $full_path . '.htaccess';
            if (!file_exists($htaccess_path)) {
                $htaccess_content = "# Proteger archivos subidos\n";
                $htaccess_content .= "Options -Indexes\n";
                $htaccess_content .= "Order deny,allow\n";
                $htaccess_content .= "Deny from all\n";
                $htaccess_content .= "\n# Permitir solo a scripts PHP autorizados\n";
                $htaccess_content .= "<Files ~ \"\\.(php)$\">\n";
                $htaccess_content .= "    Order allow,deny\n";
                $htaccess_content .= "    Deny from all\n";
                $htaccess_content .= "</Files>\n";
                
                file_put_contents($htaccess_path, $htaccess_content);
                echo "<p>🔒 Archivo .htaccess creado en '$dir'</p>";
            }
        }
    }
    
    // Verificar usuario admin
    echo "<h2>👤 Verificando usuario administrador:</h2>";
    
    $stmt = $pdo->prepare("SELECT * FROM usuarios WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    $admin = $stmt->fetch();
    
    if ($admin) {
        echo "<p>✅ Usuario admin existe</p>";
        echo "<p><strong>Email:</strong> <EMAIL></p>";
        echo "<p><strong>Contraseña:</strong> admin123 (cambiar después del primer login)</p>";
    } else {
        // Crear usuario admin
        $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("
            INSERT INTO usuarios (email, password, estado, verificado, fecha_registro) 
            VALUES (?, ?, 'activo', 1, NOW())
        ");
        
        if ($stmt->execute(['<EMAIL>', $admin_password])) {
            echo "<p>✅ Usuario admin creado</p>";
            echo "<p><strong>Email:</strong> <EMAIL></p>";
            echo "<p><strong>Contraseña:</strong> admin123</p>";
        } else {
            echo "<p>❌ Error creando usuario admin</p>";
        }
    }
    
    // Estadísticas de la base de datos
    echo "<h2>📊 Estadísticas actuales:</h2>";
    
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM $table");
            $stmt->execute();
            $count = $stmt->fetch()['total'];
            echo "<p>📋 <strong>$table:</strong> $count registros</p>";
        } catch (Exception $e) {
            echo "<p>❌ Error consultando tabla '$table': " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<h2>🎉 ¡Inicialización completada!</h2>";
    echo "<p><strong>Próximos pasos:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Acceder a: <a href='index.php'>RepuMovil Delivery</a></li>";
    echo "<li>✅ Registrar repartidores: <a href='registro.php'>Registro</a></li>";
    echo "<li>✅ Login: <a href='login.php'>Iniciar Sesión</a></li>";
    echo "<li>✅ Panel admin: <a href='admin.php'>Administración</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h2>❌ Error durante la inicialización:</h2>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<p><strong>Solución:</strong> Verificar que XAMPP esté ejecutándose y que la base de datos esté disponible.</p>";
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inicializar DB - RepuMovil Delivery</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background: linear-gradient(135deg, #FF6B35 0%, #E53E3E 50%, #F7931E 100%);
            min-height: 100vh;
            color: white;
        }
        
        h1, h2 {
            color: white;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        p, li {
            background: rgba(255, 255, 255, 0.1);
            padding: 8px 12px;
            margin: 5px 0;
            border-radius: 5px;
            backdrop-filter: blur(10px);
        }
        
        a {
            color: #FFE5D9;
            text-decoration: none;
            font-weight: bold;
        }
        
        a:hover {
            color: white;
            text-decoration: underline;
        }
        
        ul {
            list-style: none;
            padding: 0;
        }
        
        li {
            margin: 10px 0;
        }
    </style>
</head>
<body>
</body>
</html>
