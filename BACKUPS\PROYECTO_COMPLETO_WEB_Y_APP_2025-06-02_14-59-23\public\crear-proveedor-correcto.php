<?php
echo "<h1>🔧 Creando Proveedor en la Base Correcta</h1>";

// USAR LA MISMA CONFIGURACIÓN QUE EL ADMIN
require_once 'db_config.php';

try {
    echo "<p>📡 Conectando a la base de datos autoconnect_bd...</p>";
    $pdo = connectDB();
    echo "<p>✅ Conexión exitosa a autoconnect_bd!</p>";

    // Verificar estructura de la tabla users
    echo "<p>🔍 Verificando estructura de tabla users...</p>";
    $stmt = $pdo->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "<p>📋 Columnas encontradas: " . implode(', ', $columns) . "</p>";

    // Verificar roles disponibles
    echo "<p>🔍 Verificando roles disponibles...</p>";
    $stmt = $pdo->query("SELECT * FROM roles");
    $roles = $stmt->fetchAll();
    echo "<p>📋 Roles encontrados:</p>";
    foreach ($roles as $role) {
        echo "<p>- ID: {$role['id']}, Nombre: {$role['name']}, Descripción: {$role['description']}</p>";
    }

    // Eliminar usuario proveedor si existe
    echo "<p>🗑️ Eliminando usuario proveedor existente (si existe)...</p>";
    $stmt = $pdo->prepare("DELETE FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    echo "<p>✅ Usuario eliminado (si existía)</p>";

    // Crear nuevo usuario proveedor
    echo "<p>👤 Creando nuevo proveedor...</p>";
    $password_hash = password_hash('123456', PASSWORD_DEFAULT);
    
    // Usar role_id = 3 para proveedor (según la estructura que vimos)
    $stmt = $pdo->prepare("
        INSERT INTO users (username, email, password, role_id, status) 
        VALUES (?, ?, ?, ?, ?)
    ");
    
    $result = $stmt->execute([
        'proveedor_test',
        '<EMAIL>', 
        $password_hash,
        3, // role_id para proveedor
        'active'
    ]);
    
    if ($result) {
        $user_id = $pdo->lastInsertId();
        echo "<p>🎉 ¡Usuario proveedor creado exitosamente! ID: $user_id</p>";
        
        // Crear registro en tabla suppliers
        echo "<p>🏪 Creando registro de proveedor...</p>";
        $stmt = $pdo->prepare("
            INSERT INTO suppliers (user_id, name, address, phone, description) 
            VALUES (?, ?, ?, ?, ?)
        ");
        
        $supplier_result = $stmt->execute([
            $user_id,
            'Proveedor Test RepuMovil',
            'Calle Test 123, San Juan, Argentina',
            '+54 9 ************',
            'Proveedor de repuestos automotrices para testing'
        ]);
        
        if ($supplier_result) {
            echo "<p>🎉 ¡Registro de proveedor creado exitosamente!</p>";
        }
        
        // Verificar que se creó correctamente
        $stmt = $pdo->prepare("
            SELECT u.*, r.name as role_name, s.name as supplier_name 
            FROM users u 
            LEFT JOIN roles r ON u.role_id = r.id 
            LEFT JOIN suppliers s ON u.id = s.user_id 
            WHERE u.email = ?
        ");
        $stmt->execute(['<EMAIL>']);
        $user = $stmt->fetch();
        
        if ($user) {
            echo "<div style='background: #e8f5e8; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
            echo "<h2>✅ PROVEEDOR CREADO CORRECTAMENTE EN autoconnect_bd</h2>";
            echo "<p><strong>📧 Email:</strong> <EMAIL></p>";
            echo "<p><strong>🔑 Contraseña:</strong> 123456</p>";
            echo "<p><strong>👤 Username:</strong> " . $user['username'] . "</p>";
            echo "<p><strong>🎭 Rol:</strong> " . $user['role_name'] . " (ID: " . $user['role_id'] . ")</p>";
            echo "<p><strong>🏪 Nombre Proveedor:</strong> " . $user['supplier_name'] . "</p>";
            echo "<p><strong>📊 Estado:</strong> " . $user['status'] . "</p>";
            echo "<p><strong>🆔 User ID:</strong> " . $user['id'] . "</p>";
            echo "</div>";
            
            echo "<div style='text-align: center; margin: 30px 0;'>";
            echo "<a href='admin.php' style='background: #FF9800; color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; font-size: 18px; font-weight: bold; margin: 10px;'>";
            echo "👑 VER EN ADMIN";
            echo "</a>";
            echo "<a href='login-dinamico.php' style='background: #4CAF50; color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; font-size: 18px; font-weight: bold; margin: 10px;'>";
            echo "🔐 IR AL LOGIN";
            echo "</a>";
            echo "</div>";
        } else {
            echo "<p>❌ Error: Usuario no encontrado después de crearlo</p>";
        }
    } else {
        echo "<p>❌ Error al crear el usuario</p>";
    }
    
} catch (PDOException $e) {
    echo "<div style='background: #ffebee; padding: 20px; border-radius: 10px; color: #d32f2f;'>";
    echo "<h2>❌ ERROR DE BASE DE DATOS</h2>";
    echo "<p><strong>Mensaje:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Código:</strong> " . $e->getCode() . "</p>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crear Proveedor Correcto - RepuMovil</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        
        h1 {
            color: #4CAF50;
            text-align: center;
            margin-bottom: 30px;
        }
        
        p {
            background: white;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #4CAF50;
        }
    </style>
</head>
<body>
</body>
</html>
