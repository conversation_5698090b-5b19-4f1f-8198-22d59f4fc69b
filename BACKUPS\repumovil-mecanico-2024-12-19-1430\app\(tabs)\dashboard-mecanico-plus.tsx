import React, { useState } from 'react';
import {
  SafeAreaView,
  StatusBar,
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Modal,
  Alert,
  Dimensions,
} from 'react-native';

const { width } = Dimensions.get('window');

export default function DashboardMecanicoPlus() {
  const [modalVisible, setModalVisible] = useState(false);
  const [modalType, setModalType] = useState<'base_clientes' | 'agenda' | 'finanzas' | 'perfil' | 'whatsapp' | 'galeria' | 'zona' | 'diagnosticos' | 'red' | 'analisis' | 'capacitacion' | 'repuestos' | 'calificaciones'>('base_clientes');

  const abrirModal = (tipo: typeof modalType) => {
    setModalType(tipo);
    setModalVisible(true);
  };

  const mostrarFuncionPremium = (titulo: string, descripcion: string) => {
    Alert.alert(
      `✨ ${titulo}`,
      `PREMIUM:\n${descripcion}`,
      [{ text: 'OK' }]
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#1A237E" />

      {/* Header Premium */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <View style={styles.logoContainer}>
            <Text style={styles.logoText}>
              🛠️ <Text style={styles.logoRepu}>Repu</Text><Text style={styles.logoMovil}>Movil</Text>
              <Text style={styles.logoPlus}> Plus</Text>
            </Text>
            <Text style={styles.crownIcon}>👑</Text>
          </View>
          <Text style={styles.crownIcon}>👑</Text>
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Welcome Section */}
        <View style={styles.welcomeSection}>
          <Text style={styles.welcomeTitle}>👑 De Mecánico a Empresario</Text>
          <Text style={styles.welcomeSubtitle}>Gestiona tu negocio como un profesional con herramientas empresariales</Text>
          <View style={styles.sloganSection}>
            <Text style={styles.sloganText}>
              DE MECÁNICO A EMPRESARIO EN UN TOQUE, <Text style={styles.sloganHighlight}>NEEEÑO</Text>
            </Text>
          </View>
        </View>

        {/* Empresario Section */}
        <View style={styles.empresarioSection}>
          <Text style={styles.empresarioTitle}>🚀 Tu Transformación Empresarial</Text>
          <Text style={styles.empresarioSubtitle}>Herramientas que usan los grandes talleres, adaptadas para ti</Text>
        </View>

        {/* Stats */}
        <View style={styles.statsContainer}>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>12</Text>
            <Text style={styles.statLabel}>Pedidos Realizados</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>8</Text>
            <Text style={styles.statLabel}>Repuestos en Demanda</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>4.9</Text>
            <Text style={styles.statLabel}>Calificación Promedio</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>35</Text>
            <Text style={styles.statLabel}>Clientes Base</Text>
          </View>
        </View>

        {/* Functions Grid */}
        <View style={styles.functionsGrid}>
          {/* Pedido de Repuestos - BÁSICO */}
          <TouchableOpacity style={styles.functionCard} onPress={() => abrirModal('repuestos')}>
            <Text style={[styles.functionIcon, styles.functionIconBasic]}>📋</Text>
            <Text style={styles.functionTitle}>Pedido de Repuestos</Text>
            <Text style={styles.functionDescription}>Busca y solicita repuestos directamente a proveedores verificados</Text>
          </TouchableOpacity>

          {/* Calificaciones Recibidas - BÁSICO */}
          <TouchableOpacity style={styles.functionCard} onPress={() => abrirModal('calificaciones')}>
            <Text style={[styles.functionIcon, styles.functionIconBasic]}>⭐</Text>
            <Text style={styles.functionTitle}>Calificaciones Recibidas</Text>
            <Text style={styles.functionDescription}>Revisa las calificaciones y comentarios de tus clientes</Text>
          </TouchableOpacity>

          {/* Base de Clientes - PREMIUM */}
          <TouchableOpacity style={[styles.functionCard, styles.functionCardPremium]} onPress={() => abrirModal('base_clientes')}>
            <View style={styles.premiumBadge}>
              <Text style={styles.premiumBadgeText}>PREMIUM</Text>
            </View>
            <Text style={[styles.functionIcon, styles.functionIconPremium]}>👥</Text>
            <Text style={styles.functionTitle}>Base de Clientes</Text>
            <Text style={styles.functionDescription}>Historial completo y gestión avanzada</Text>
          </TouchableOpacity>

          {/* Agenda Inteligente - PREMIUM */}
          <TouchableOpacity style={[styles.functionCard, styles.functionCardPremium]} onPress={() => abrirModal('agenda')}>
            <View style={styles.premiumBadge}>
              <Text style={styles.premiumBadgeText}>PREMIUM</Text>
            </View>
            <Text style={[styles.functionIcon, styles.functionIconPremium]}>📅</Text>
            <Text style={styles.functionTitle}>Agenda Inteligente</Text>
            <Text style={styles.functionDescription}>Calendario profesional con turnos</Text>
          </TouchableOpacity>

          {/* Finanzas Empresariales - PREMIUM */}
          <TouchableOpacity style={[styles.functionCard, styles.functionCardPremium]} onPress={() => abrirModal('finanzas')}>
            <View style={styles.premiumBadge}>
              <Text style={styles.premiumBadgeText}>PREMIUM</Text>
            </View>
            <Text style={[styles.functionIcon, styles.functionIconPremium]}>💰</Text>
            <Text style={styles.functionTitle}>Finanzas Empresariales</Text>
            <Text style={styles.functionDescription}>Facturación y reportes avanzados</Text>
          </TouchableOpacity>

          {/* Perfil Profesional - PREMIUM */}
          <TouchableOpacity style={[styles.functionCard, styles.functionCardPremium]} onPress={() => abrirModal('perfil')}>
            <View style={styles.premiumBadge}>
              <Text style={styles.premiumBadgeText}>PREMIUM</Text>
            </View>
            <Text style={[styles.functionIcon, styles.functionIconPremium]}>🌟</Text>
            <Text style={styles.functionTitle}>Perfil Profesional</Text>
            <Text style={styles.functionDescription}>Portfolio y certificaciones</Text>
          </TouchableOpacity>

        </View>
      </ScrollView>

      {/* Modal Universal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                {modalType === 'base_clientes' && '👥 Base de Clientes Completa'}
                {modalType === 'agenda' && '📅 Agenda Inteligente'}
                {modalType === 'finanzas' && '💰 Finanzas Empresariales'}
                {modalType === 'perfil' && '🌟 Perfil Profesional'}
                {modalType === 'whatsapp' && '📱 WhatsApp Business'}
                {modalType === 'galeria' && '📸 Galería Profesional'}
                {modalType === 'zona' && '🗺️ Zona de Trabajo Premium'}
                {modalType === 'diagnosticos' && '📋 Diagnósticos Digitales'}
                {modalType === 'red' && '🤝 Red de Mecánicos'}
                {modalType === 'analisis' && '📈 Análisis de Crecimiento'}
                {modalType === 'capacitacion' && '🎓 Capacitación Continua'}
                {modalType === 'repuestos' && '🛒 Pedido de Repuestos'}
                {modalType === 'calificaciones' && '⭐ Calificaciones Recibidas'}
              </Text>
              <TouchableOpacity onPress={() => setModalVisible(false)}>
                <Text style={styles.closeButton}>✕</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.modalBody}>
              {modalType === 'base_clientes' && (
                <View style={styles.premiumFeature}>
                  <Text style={styles.premiumTitle}>✨ PREMIUM</Text>
                  <Text style={styles.premiumDescription}>
                    • Historial completo de cada cliente{'\n'}
                    • Vehículos y trabajos anteriores{'\n'}
                    • Recordatorios de mantenimiento{'\n'}
                    • "Nunca más olvides qué le hiciste a cada auto"
                  </Text>
                </View>
              )}

              {modalType === 'agenda' && (
                <View style={styles.premiumFeature}>
                  <Text style={styles.premiumTitle}>✨ PREMIUM</Text>
                  <Text style={styles.premiumDescription}>
                    • Calendario profesional con turnos{'\n'}
                    • Recordatorios automáticos{'\n'}
                    • Optimización de rutas{'\n'}
                    • "Organiza tu día como un profesional"
                  </Text>
                </View>
              )}

              {modalType === 'finanzas' && (
                <View style={styles.premiumFeature}>
                  <Text style={styles.premiumTitle}>✨ PREMIUM</Text>
                  <Text style={styles.premiumDescription}>
                    • Facturación automática{'\n'}
                    • Control de gastos{'\n'}
                    • Reportes de rentabilidad{'\n'}
                    • "Sabe exactamente cuánto ganas"
                  </Text>
                </View>
              )}

              {modalType === 'perfil' && (
                <View style={styles.premiumFeature}>
                  <Text style={styles.premiumTitle}>✨ PREMIUM</Text>
                  <Text style={styles.premiumDescription}>
                    • Portfolio de trabajos{'\n'}
                    • Certificaciones{'\n'}
                    • Especialidades destacadas{'\n'}
                    • "Muestra tu experiencia"
                  </Text>
                </View>
              )}

              {modalType === 'whatsapp' && (
                <View style={styles.premiumFeature}>
                  <Text style={styles.premiumTitle}>✨ PREMIUM</Text>
                  <Text style={styles.premiumDescription}>
                    • Mensajes automáticos{'\n'}
                    • Catálogo de servicios{'\n'}
                    • Estados profesionales{'\n'}
                    • "Comunícate como empresa"
                  </Text>
                </View>
              )}

              {modalType === 'galeria' && (
                <View style={styles.premiumFeature}>
                  <Text style={styles.premiumTitle}>✨ PREMIUM</Text>
                  <Text style={styles.premiumDescription}>
                    • Antes/después organizados{'\n'}
                    • Casos de éxito{'\n'}
                    • Compartir en redes{'\n'}
                    • "Tu trabajo habla por ti"
                  </Text>
                </View>
              )}

              {modalType === 'zona' && (
                <View style={styles.premiumFeature}>
                  <Text style={styles.premiumTitle}>✨ PREMIUM</Text>
                  <Text style={styles.premiumDescription}>
                    • Mapa de clientes{'\n'}
                    • Rutas optimizadas{'\n'}
                    • Clientes potenciales cercanos{'\n'}
                    • "Maximiza tu tiempo y combustible"
                  </Text>
                </View>
              )}

              {modalType === 'diagnosticos' && (
                <View style={styles.premiumFeature}>
                  <Text style={styles.premiumTitle}>✨ PREMIUM</Text>
                  <Text style={styles.premiumDescription}>
                    • Plantillas de diagnóstico{'\n'}
                    • Fotos organizadas{'\n'}
                    • Presupuestos automáticos{'\n'}
                    • "Diagnósticos como los grandes talleres"
                  </Text>
                </View>
              )}

              {modalType === 'red' && (
                <View style={styles.premiumFeature}>
                  <Text style={styles.premiumTitle}>✨ PREMIUM</Text>
                  <Text style={styles.premiumDescription}>
                    • Derivar trabajos complejos{'\n'}
                    • Colaboraciones{'\n'}
                    • Intercambio de conocimiento{'\n'}
                    • "Conecta con otros profesionales"
                  </Text>
                </View>
              )}

              {modalType === 'analisis' && (
                <View style={styles.premiumFeature}>
                  <Text style={styles.premiumTitle}>✨ PREMIUM</Text>
                  <Text style={styles.premiumDescription}>
                    • Tendencias de ingresos{'\n'}
                    • Servicios más rentables{'\n'}
                    • Sugerencias de mejora{'\n'}
                    • "Crece tu negocio con datos"
                  </Text>
                </View>
              )}

              {modalType === 'capacitacion' && (
                <View style={styles.premiumFeature}>
                  <Text style={styles.premiumTitle}>✨ PREMIUM</Text>
                  <Text style={styles.premiumDescription}>
                    • Cursos especializados{'\n'}
                    • Nuevas tecnologías{'\n'}
                    • Certificaciones{'\n'}
                    • "Mantente actualizado"
                  </Text>
                </View>
              )}

              {modalType === 'repuestos' && (
                <View style={styles.basicFeature}>
                  <Text style={styles.basicTitle}>🛒 Pedido de Repuestos</Text>
                  <Text style={styles.basicDescription}>
                    • Buscar repuestos{'\n'}
                    • Sistema changuito{'\n'}
                    • Solicitar cotización{'\n'}
                    • Entrega rápida
                  </Text>
                </View>
              )}

              {modalType === 'calificaciones' && (
                <View style={styles.basicFeature}>
                  <Text style={styles.basicTitle}>⭐ Calificaciones Recibidas</Text>
                  <View style={styles.calificacionHeader}>
                    <Text style={styles.calificacionScore}>4.9</Text>
                    <Text style={styles.calificacionStars}>⭐⭐⭐⭐⭐</Text>
                    <Text style={styles.calificacionTotal}>35 calificaciones</Text>
                  </View>
                  <View style={styles.comentarioItem}>
                    <Text style={styles.comentarioTexto}>"Excelente mecánico empresario, muy profesional"</Text>
                    <Text style={styles.comentarioAutor}>- Carlos Mendoza</Text>
                  </View>
                  <View style={styles.comentarioItem}>
                    <Text style={styles.comentarioTexto}>"Servicio premium, vale la pena cada peso"</Text>
                    <Text style={styles.comentarioAutor}>- Ana López</Text>
                  </View>
                </View>
              )}
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1A237E',
  },
  header: {
    backgroundColor: '#283593',
    paddingTop: 20,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoText: {
    fontSize: 24,
    fontWeight: '900',
    color: 'white',
  },
  logoRepu: {
    color: '#3F51B5',
  },
  logoMovil: {
    color: '#E8EAF6',
  },
  logoPlus: {
    color: '#FFD700',
  },
  crownIcon: {
    fontSize: 24,
    marginLeft: 8,
  },
  userGreeting: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    backgroundColor: '#f8f9fa',
    paddingHorizontal: 15,
  },
  welcomeSection: {
    backgroundColor: 'rgba(255, 251, 240, 0.95)',
    borderRadius: 25,
    padding: 25,
    margin: 20,
    alignItems: 'center',
    elevation: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 15,
    borderWidth: 3,
    borderColor: '#FFD700',
  },
  welcomeTitle: {
    fontSize: 20,
    fontWeight: '900',
    color: '#1A237E',
    textAlign: 'center',
    marginBottom: 10,
  },
  welcomeSubtitle: {
    fontSize: 14,
    color: '#5D4037',
    textAlign: 'center',
    marginBottom: 15,
    fontWeight: '600',
  },
  sloganSection: {
    marginTop: 10,
  },
  sloganText: {
    fontSize: 16,
    fontWeight: '900',
    color: '#1A237E',
    textAlign: 'center',
  },
  sloganHighlight: {
    color: '#E74C3C',
    fontSize: 18,
  },
  empresarioSection: {
    backgroundColor: '#FFD700',
    borderRadius: 20,
    padding: 20,
    marginHorizontal: 20,
    marginBottom: 20,
    alignItems: 'center',
    elevation: 10,
    shadowColor: '#FFD700',
    shadowOffset: { width: 0, height: 5 },
    shadowOpacity: 0.3,
    shadowRadius: 10,
  },
  empresarioTitle: {
    fontSize: 18,
    fontWeight: '900',
    color: '#1A237E',
    marginBottom: 5,
  },
  empresarioSubtitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1A237E',
    opacity: 0.9,
    textAlign: 'center',
  },
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  statCard: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    flexBasis: '48%',
    marginBottom: 10,
    alignItems: 'center',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  statNumber: {
    fontSize: 28,
    fontWeight: '900',
    color: '#1A237E',
    marginBottom: 8,
  },
  statLabel: {
    fontSize: 12,
    color: '#5D4037',
    textAlign: 'center',
    fontWeight: '600',
  },
  functionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingBottom: 30,
  },
  functionCard: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 20,
    flexBasis: '48%',
    marginBottom: 15,
    alignItems: 'center',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    position: 'relative',
  },
  functionCardPremium: {
    borderWidth: 3,
    borderColor: '#FFD700',
    backgroundColor: '#fffbf0',
  },
  premiumBadge: {
    position: 'absolute',
    top: 10,
    right: 10,
    backgroundColor: '#FFD700',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    zIndex: 1,
  },
  premiumBadgeText: {
    fontSize: 10,
    fontWeight: '700',
    color: '#1A237E',
  },
  functionIcon: {
    fontSize: 36,
    marginBottom: 10,
  },
  functionIconPremium: {
    color: '#FFD700',
  },
  functionIconBasic: {
    color: '#3F51B5',
  },
  functionTitle: {
    fontSize: 13,
    fontWeight: '700',
    color: '#1A237E',
    textAlign: 'center',
    marginBottom: 5,
  },
  functionDescription: {
    fontSize: 10,
    color: '#5D4037',
    textAlign: 'center',
    lineHeight: 14,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 25,
    width: width * 0.9,
    maxHeight: '70%',
    elevation: 20,
    borderWidth: 3,
    borderColor: '#FFD700',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#1A237E',
    borderTopLeftRadius: 22,
    borderTopRightRadius: 22,
  },
  modalTitle: {
    fontSize: 16,
    fontWeight: '900',
    color: 'white',
  },
  closeButton: {
    fontSize: 24,
    color: 'white',
    fontWeight: 'bold',
  },
  modalBody: {
    padding: 20,
  },
  premiumFeature: {
    alignItems: 'center',
  },
  premiumTitle: {
    fontSize: 20,
    fontWeight: '900',
    color: '#FFD700',
    marginBottom: 15,
    textAlign: 'center',
  },
  premiumDescription: {
    fontSize: 16,
    color: '#1A237E',
    lineHeight: 24,
    textAlign: 'center',
  },
  basicFeature: {
    alignItems: 'center',
  },
  basicTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#3F51B5',
    marginBottom: 15,
    textAlign: 'center',
  },
  basicDescription: {
    fontSize: 16,
    color: '#5D4037',
    lineHeight: 24,
    textAlign: 'center',
  },
  calificacionHeader: {
    alignItems: 'center',
    marginBottom: 15,
    backgroundColor: '#F8F9FA',
    borderRadius: 15,
    padding: 15,
    width: '100%',
  },
  calificacionScore: {
    fontSize: 36,
    fontWeight: '900',
    color: '#1A237E',
    marginBottom: 5,
  },
  calificacionStars: {
    fontSize: 20,
    marginBottom: 5,
  },
  calificacionTotal: {
    fontSize: 12,
    color: '#5D4037',
    fontWeight: '600',
  },
  comentarioItem: {
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
    padding: 15,
    marginBottom: 10,
    borderLeftWidth: 4,
    borderLeftColor: '#1A237E',
  },
  comentarioTexto: {
    fontSize: 14,
    color: '#2C3E50',
    fontStyle: 'italic',
    marginBottom: 5,
  },
  comentarioAutor: {
    fontSize: 12,
    color: '#7F8C8D',
    fontWeight: '600',
  },
});
