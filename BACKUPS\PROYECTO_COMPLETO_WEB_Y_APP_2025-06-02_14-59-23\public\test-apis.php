<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Test APIs RepuMovil - FASE 2</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .api-section {
            border: 2px solid #FF6B35;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .api-title {
            color: #FF6B35;
            font-size: 1.5em;
            margin-bottom: 10px;
        }
        button {
            background: #FF6B35;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #e55a2b;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            border-color: #28a745;
            background: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background: #f8d7da;
        }
        input, select {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 RepuMovil - Test APIs FASE 2</h1>
        <p><strong>PASO A PASO MOSTAZA MERLO</strong> ⚽🇦🇷</p>
        <p>Prueba todas las APIs del sistema RepuMovil</p>
    </div>

    <!-- API LOGIN -->
    <div class="api-section">
        <h2 class="api-title">🔐 API LOGIN</h2>
        <div>
            <input type="email" id="loginEmail" placeholder="Email" value="<EMAIL>">
            <input type="password" id="loginPassword" placeholder="Contraseña" value="taller123">
            <button onclick="testLogin()">Probar Login</button>
        </div>
        <div id="loginResult" class="result"></div>
    </div>

    <!-- API REPUESTOS -->
    <div class="api-section">
        <h2 class="api-title">🔧 API REPUESTOS</h2>
        <div>
            <input type="text" id="searchRepuestos" placeholder="Buscar repuestos">
            <select id="categoriaFilter">
                <option value="">Todas las categorías</option>
                <option value="Filtros">Filtros</option>
                <option value="Frenos">Frenos</option>
                <option value="Suspensión">Suspensión</option>
                <option value="Eléctrico">Eléctrico</option>
            </select>
            <button onclick="testRepuestos()">Listar Repuestos</button>
            <button onclick="testRepuestoById()">Buscar por ID (1)</button>
        </div>
        <div id="repuestosResult" class="result"></div>
    </div>

    <!-- API CARRITO -->
    <div class="api-section">
        <h2 class="api-title">🛒 API CARRITO</h2>
        <div>
            <input type="number" id="userId" placeholder="User ID" value="2">
            <input type="number" id="repuestoId" placeholder="Repuesto ID" value="1">
            <input type="number" id="cantidad" placeholder="Cantidad" value="1">
            <button onclick="testAgregarCarrito()">Agregar al Carrito</button>
            <button onclick="testVerCarrito()">Ver Carrito</button>
        </div>
        <div id="carritoResult" class="result"></div>
    </div>

    <!-- API PEDIDOS -->
    <div class="api-section">
        <h2 class="api-title">📦 API PEDIDOS</h2>
        <div>
            <input type="text" id="metodoPago" placeholder="Método de Pago" value="Efectivo">
            <input type="text" id="direccion" placeholder="Dirección" value="Calle Ejemplo 123">
            <input type="text" id="telefono" placeholder="Teléfono" value="************">
            <button onclick="testCrearPedido()">Crear Pedido</button>
            <button onclick="testVerPedidos()">Ver Pedidos</button>
        </div>
        <div id="pedidosResult" class="result"></div>
    </div>

    <script>
        let currentUserId = 2; // Usuario taller por defecto

        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                
                const data = await response.json();
                return { success: response.ok, data };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function testLogin() {
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;
            
            const result = await makeRequest('../api/login.php', {
                method: 'POST',
                body: JSON.stringify({ email, password })
            });
            
            const resultDiv = document.getElementById('loginResult');
            resultDiv.textContent = JSON.stringify(result, null, 2);
            resultDiv.className = 'result ' + (result.success ? 'success' : 'error');
            
            if (result.success && result.data.user) {
                currentUserId = result.data.user.id;
                document.getElementById('userId').value = currentUserId;
            }
        }

        async function testRepuestos() {
            const search = document.getElementById('searchRepuestos').value;
            const categoria = document.getElementById('categoriaFilter').value;
            
            let url = '../api/repuestos.php?';
            if (search) url += `search=${encodeURIComponent(search)}&`;
            if (categoria) url += `categoria=${encodeURIComponent(categoria)}&`;
            
            const result = await makeRequest(url);
            
            const resultDiv = document.getElementById('repuestosResult');
            resultDiv.textContent = JSON.stringify(result, null, 2);
            resultDiv.className = 'result ' + (result.success ? 'success' : 'error');
        }

        async function testRepuestoById() {
            const result = await makeRequest('../api/repuestos.php', {
                method: 'POST',
                body: JSON.stringify({ action: 'get_by_id', id: 1 })
            });
            
            const resultDiv = document.getElementById('repuestosResult');
            resultDiv.textContent = JSON.stringify(result, null, 2);
            resultDiv.className = 'result ' + (result.success ? 'success' : 'error');
        }

        async function testAgregarCarrito() {
            const userId = document.getElementById('userId').value;
            const repuestoId = document.getElementById('repuestoId').value;
            const cantidad = document.getElementById('cantidad').value;
            
            const result = await makeRequest('../api/carrito.php', {
                method: 'POST',
                body: JSON.stringify({ user_id: userId, repuesto_id: repuestoId, cantidad: parseInt(cantidad) })
            });
            
            const resultDiv = document.getElementById('carritoResult');
            resultDiv.textContent = JSON.stringify(result, null, 2);
            resultDiv.className = 'result ' + (result.success ? 'success' : 'error');
        }

        async function testVerCarrito() {
            const userId = document.getElementById('userId').value;
            
            const result = await makeRequest(`../api/carrito.php?user_id=${userId}`);
            
            const resultDiv = document.getElementById('carritoResult');
            resultDiv.textContent = JSON.stringify(result, null, 2);
            resultDiv.className = 'result ' + (result.success ? 'success' : 'error');
        }

        async function testCrearPedido() {
            const userId = document.getElementById('userId').value;
            const metodoPago = document.getElementById('metodoPago').value;
            const direccion = document.getElementById('direccion').value;
            const telefono = document.getElementById('telefono').value;
            
            const result = await makeRequest('../api/pedidos.php', {
                method: 'POST',
                body: JSON.stringify({
                    user_id: userId,
                    metodo_pago: metodoPago,
                    direccion_entrega: direccion,
                    telefono_contacto: telefono,
                    notas: 'Pedido de prueba desde test APIs'
                })
            });
            
            const resultDiv = document.getElementById('pedidosResult');
            resultDiv.textContent = JSON.stringify(result, null, 2);
            resultDiv.className = 'result ' + (result.success ? 'success' : 'error');
        }

        async function testVerPedidos() {
            const userId = document.getElementById('userId').value;
            
            const result = await makeRequest(`../api/pedidos.php?user_id=${userId}`);
            
            const resultDiv = document.getElementById('pedidosResult');
            resultDiv.textContent = JSON.stringify(result, null, 2);
            resultDiv.className = 'result ' + (result.success ? 'success' : 'error');
        }
    </script>
</body>
</html>
