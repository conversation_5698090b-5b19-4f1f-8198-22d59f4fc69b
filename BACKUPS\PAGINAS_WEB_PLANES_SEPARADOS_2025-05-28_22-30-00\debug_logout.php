<?php
// Mostrar todos los errores
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Depuración de cierre de sesión</h1>";

// Verificar si hay otros archivos de logout
echo "<h2>Archivos de cierre de sesión:</h2>";
$logoutFiles = [
    'logout.php',
    'logout_nuevo.php',
    'logout_reemplazo.php'
];

echo "<ul>";
foreach ($logoutFiles as $file) {
    if (file_exists($file)) {
        echo "<li>$file: ✅ Existe</li>";
        
        // Mostrar el contenido del archivo
        echo "<details>";
        echo "<summary>Ver contenido</summary>";
        echo "<pre>";
        highlight_file($file);
        echo "</pre>";
        echo "</details>";
    } else {
        echo "<li>$file: ❌ No existe</li>";
    }
}
echo "</ul>";

// Verificar si hay archivos .htaccess
echo "<h2>Verificación de .htaccess:</h2>";
$htaccessFiles = [
    '../.htaccess',
    '.htaccess'
];

echo "<ul>";
foreach ($htaccessFiles as $file) {
    if (file_exists($file)) {
        echo "<li>$file: ✅ Existe</li>";
        
        // Mostrar el contenido del archivo
        echo "<details>";
        echo "<summary>Ver contenido</summary>";
        echo "<pre>";
        readfile($file);
        echo "</pre>";
        echo "</details>";
    } else {
        echo "<li>$file: ❌ No existe</li>";
    }
}
echo "</ul>";

// Mostrar información del servidor
echo "<h2>Información del servidor:</h2>";
echo "<p>Servidor: " . $_SERVER['SERVER_SOFTWARE'] . "</p>";
echo "<p>Documento raíz: " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p>Ruta del script: " . $_SERVER['SCRIPT_FILENAME'] . "</p>";
echo "<p>URL actual: " . $_SERVER['REQUEST_URI'] . "</p>";

// Mostrar enlaces útiles
echo "<h2>Enlaces útiles:</h2>";
echo "<ul>";
echo "<li><a href='login_reemplazo.php'>Página de login</a></li>";
echo "<li><a href='index.php'>Página de inicio</a></li>";
echo "</ul>";

// Mostrar un enlace de prueba para cerrar sesión
echo "<h2>Prueba de cierre de sesión:</h2>";
echo "<p>Haz clic en el siguiente enlace para probar el cierre de sesión:</p>";
echo "<a href='logout.php'>Cerrar sesión</a>";
?>