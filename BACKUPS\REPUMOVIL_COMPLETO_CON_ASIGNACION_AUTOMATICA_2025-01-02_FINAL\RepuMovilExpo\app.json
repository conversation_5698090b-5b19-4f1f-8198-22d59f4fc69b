{"expo": {"name": "RepuMovilExpo", "slug": "RepuMovilExpo", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "repumovilexpo", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "infoPlist": {"NSLocationWhenInUseUsageDescription": "RepuMovil necesita acceso a tu ubicación para mostrar tu posición en el mapa y calcular rutas de entrega.", "NSLocationAlwaysAndWhenInUseUsageDescription": "RepuMovil necesita acceso a tu ubicación para rastrear entregas en tiempo real."}, "config": {"googleMapsApiKey": "AIzaSyDeF7xLqA58aAm5Gou7Nux3A7_jwGnnVxg"}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "permissions": ["ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION", "ACCESS_BACKGROUND_LOCATION"], "config": {"googleMaps": {"apiKey": "AIzaSyDeF7xLqA58aAm5Gou7Nux3A7_jwGnnVxg"}}}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], ["expo-location", {"locationAlwaysAndWhenInUsePermission": "RepuMovil necesita acceso a tu ubicación para mostrar tu posición en el mapa y calcular rutas de entrega.", "locationAlwaysPermission": "RepuMovil necesita acceso a tu ubicación para rastrear entregas en tiempo real.", "locationWhenInUsePermission": "RepuMovil necesita acceso a tu ubicación para mostrar tu posición en el mapa."}]], "experiments": {"typedRoutes": true}}}