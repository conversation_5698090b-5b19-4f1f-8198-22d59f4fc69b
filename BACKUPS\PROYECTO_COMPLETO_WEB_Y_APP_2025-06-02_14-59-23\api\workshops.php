<?php
// Mostrar todos los errores para depuración
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Iniciar sesión
session_start();

// Verificar si el usuario está autenticado y es administrador
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

// Incluir archivo de configuración de la base de datos
require_once '../db_config.php';

// Obtener lista de talleres
try {
    $conn = connectDB();
    $query = "SELECT w.*, u.username, u.email, u.status 
              FROM workshops w 
              JOIN users u ON w.user_id = u.id 
              ORDER BY w.id";
    $workshops = $conn->query($query)->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $error = 'Error al cargar talleres: ' . $e->getMessage();
    $workshops = [];
}

// Procesar acciones
$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $action = $_POST['action'];
        $workshopId = isset($_POST['workshop_id']) ? (int)$_POST['workshop_id'] : 0;
        $userId = isset($_POST['user_id']) ? (int)$_POST['user_id'] : 0;
        
        if ($action === 'activate' && $userId > 0) {
            try {
                $stmt = $conn->prepare("UPDATE users SET status = 'active' WHERE id = :id");
                $stmt->execute(['id' => $userId]);
                $message = 'Taller activado correctamente';
            } catch (PDOException $e) {
                $error = 'Error al activar taller: ' . $e->getMessage();
            }
        } else if ($action === 'deactivate' && $userId > 0) {
            try {
                $stmt = $conn->prepare("UPDATE users SET status = 'inactive' WHERE id = :id");
                $stmt->execute(['id' => $userId]);
                $message = 'Taller desactivado correctamente';
            } catch (PDOException $e) {
                $error = 'Error al desactivar taller: ' . $e->getMessage();
            }
        } else if ($action === 'delete' && $workshopId > 0) {
            try {
                $conn->beginTransaction();
                
                // Primero eliminamos el taller
                $stmt = $conn->prepare("DELETE FROM workshops WHERE id = :id");
                $stmt->execute(['id' => $workshopId]);
                
                // Luego eliminamos el usuario asociado
                if ($userId > 0) {
                    $stmt = $conn->prepare("DELETE FROM users WHERE id = :id");
                    $stmt->execute(['id' => $userId]);
                }
                
                $conn->commit();
                $message = 'Taller eliminado correctamente';
            } catch (PDOException $e) {
                $conn->rollBack();
                $error = 'Error al eliminar taller: ' . $e->getMessage();
            }
        }
        
        // Recargar la lista de talleres después de la acción
        try {
            $query = "SELECT w.*, u.username, u.email, u.status 
                      FROM workshops w 
                      JOIN users u ON w.user_id = u.id 
                      ORDER BY w.id";
            $workshops = $conn->query($query)->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            $error = 'Error al recargar talleres: ' . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestión de Talleres - Panel de Administración</title>
    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav id="sidebar" class="col-md-3 col-lg-2 d-md-block bg-dark sidebar">
                <div class="position-sticky">
                    <div class="sidebar-header">
                        <h3>AutoConnect</h3>
                        <p>Panel de Administración</p>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="index.php">
                                <i class="fas fa-tachometer-alt"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="users.php">
                                <i class="fas fa-users"></i> Usuarios
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="admin_workshops.php">
                                <i class="fas fa-tools"></i> Talleres
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="admin_suppliers.php">
                                <i class="fas fa-truck"></i> Proveedores
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="admin_mechanics.php">
                                <i class="fas fa-wrench"></i> Mecánicos
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="admin_requests.php">
                                <i class="fas fa-clipboard-list"></i> Solicitudes
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../logout.php">
                                <i class="fas fa-sign-out-alt"></i> Cerrar Sesión
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
            
            <!-- Contenido principal -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Gestión de Talleres</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="add_workshop.php" class="btn btn-sm btn-primary">
                            <i class="fas fa-plus"></i> Agregar Taller
                        </a>
                    </div>
                </div>
                
                <?php if (!empty($message)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <?php if (!empty($error)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $error; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <div class="card">
                    <div class="card-header">
                        <h5>Lista de Talleres</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Nombre del Taller</th>
                                        <th>Usuario</th>
                                        <th>Email</th>
                                        <th>Dirección</th>
                                        <th>Teléfono</th>
                                        <th>Estado</th>
                                        <th>Fecha Registro</th>
                                        <th>Acciones</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (!empty($workshops)): ?>
                                        <?php foreach ($workshops as $workshop): ?>
                                            <tr>
                                                <td><?php echo $workshop['id']; ?></td>
                                                <td><?php echo htmlspecialchars($workshop['name']); ?></td>
                                                <td><?php echo htmlspecialchars($workshop['username']); ?></td>
                                                <td><?php echo htmlspecialchars($workshop['email']); ?></td>
                                                <td><?php echo htmlspecialchars($workshop['address']); ?></td>
                                                <td><?php echo htmlspecialchars($workshop['phone']); ?></td>
                                                <td>
                                                    <?php if ($workshop['status'] == 'active'): ?>
                                                        <span class="badge bg-success">Activo</span>
                                                    <?php elseif ($workshop['status'] == 'inactive'): ?>
                                                        <span class="badge bg-warning">Inactivo</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-secondary"><?php echo ucfirst($workshop['status']); ?></span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo date('d/m/Y', strtotime($workshop['created_at'])); ?></td>
                                                <td>
                                                    <div class="btn-group">
                                                        <a href="view_workshop.php?id=<?php echo $workshop['id']; ?>" class="btn btn-sm btn-info">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <a href="edit_workshop.php?id=<?php echo $workshop['id']; ?>" class="btn btn-sm btn-primary">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        
                                                        <?php if ($workshop['status'] == 'active'): ?>
                                                            <form method="post" style="display: inline;">
                                                                <input type="hidden" name="action" value="deactivate">
                                                                <input type="hidden" name="user_id" value="<?php echo $workshop['user_id']; ?>">
                                                                <button type="submit" class="btn btn-sm btn-warning" onclick="return confirm('¿Está seguro de desactivar este taller?')">
                                                                    <i class="fas fa-ban"></i>
                                                                </button>
                                                            </form>
                                                        <?php else: ?>
                                                            <form method="post" style="display: inline;">
                                                                <input type="hidden" name="action" value="activate">
                                                                <input type="hidden" name="user_id" value="<?php echo $workshop['user_id']; ?>">
                                                                <button type="submit" class="btn btn-sm btn-success" onclick="return confirm('¿Está seguro de activar este taller?')">
                                                                    <i class="fas fa-check"></i>
                                                                </button>
                                                            </form>
                                                        <?php endif; ?>
                                                        
                                                        <form method="post" style="display: inline;">
                                                            <input type="hidden" name="action" value="delete">
                                                            <input type="hidden" name="workshop_id" value="<?php echo $workshop['id']; ?>">
                                                            <input type="hidden" name="user_id" value="<?php echo $workshop['user_id']; ?>">
                                                            <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('¿Está seguro de eliminar este taller? Esta acción no se puede deshacer.')">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </form>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="9" class="text-center">No hay talleres registrados</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <script src="../js/bootstrap.bundle.min.js"></script>
</body>
</html>
