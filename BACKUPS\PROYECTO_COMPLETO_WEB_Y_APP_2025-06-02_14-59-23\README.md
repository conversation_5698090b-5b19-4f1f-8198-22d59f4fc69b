# Mechanical Workshop Project

This project is a web application for a mechanical workshop, providing information about the services offered, contact details, and workshop specifics.

## Project Structure

```
mechanical-workshop
├── public
│   └── index.php
├── src
│   └── Workshop.php
├── composer.json
└── README.md
```

## Installation

1. Clone the repository:
   ```
   git clone <repository-url>
   ```
2. Navigate to the project directory:
   ```
   cd mechanical-workshop
   ```
3. Install dependencies using Composer:
   ```
   composer install
   ```

## Usage

- The main entry point of the application is located in `public/index.php`. You can access the application by navigating to `http://localhost/path-to-your-project/public/index.php` in your web browser.

## Features

- Displays services offered by the workshop.
- Provides contact information.
- Includes workshop details.

## Contributing

Feel free to submit issues or pull requests for improvements or bug fixes.

## License

This project is licensed under the MIT License.