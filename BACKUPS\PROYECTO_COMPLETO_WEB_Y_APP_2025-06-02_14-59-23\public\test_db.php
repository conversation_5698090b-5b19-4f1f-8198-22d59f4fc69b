<?php
// Mostrar todos los errores
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Incluir archivos necesarios
require_once '../src/Database.php';

try {
    // Intentar conectar a la base de datos
    $db = Src\Database::getInstance();
    $conn = $db->getConnection();
    
    echo "<h1>Conexión a la base de datos exitosa</h1>";
    
    // Verificar si existen las tablas
    $tables = ['roles', 'users', 'workshops', 'suppliers'];
    $existingTables = [];
    
    foreach ($tables as $table) {
        $query = "SHOW TABLES LIKE '$table'";
        $result = $db->query($query);
        
        if ($result && $result->rowCount() > 0) {
            $existingTables[] = $table;
        }
    }
    
    if (count($existingTables) === count($tables)) {
        echo "<p>Todas las tablas existen en la base de datos:</p>";
    } else {
        echo "<p>Faltan algunas tablas en la base de datos:</p>";
    }
    
    echo "<ul>";
    foreach ($tables as $table) {
        $exists = in_array($table, $existingTables);
        echo "<li>$table: " . ($exists ? "✅ Existe" : "❌ No existe") . "</li>";
    }
    echo "</ul>";
    
    // Verificar si existen usuarios
    $query = "SELECT COUNT(*) as count FROM users";
    $result = $db->single($query);
    
    if ($result) {
        $userCount = $result['count'];
        echo "<p>Hay $userCount usuarios en la base de datos.</p>";
        
        if ($userCount > 0) {
            // Mostrar usuarios
            $query = "SELECT u.*, r.name as role_name FROM users u JOIN roles r ON u.role_id = r.id";
            $users = $db->resultSet($query);
            
            echo "<h2>Usuarios registrados:</h2>";
            echo "<table border='1' cellpadding='5'>";
            echo "<tr><th>ID</th><th>Usuario</th><th>Email</th><th>Rol</th><th>Estado</th></tr>";
            
            foreach ($users as $user) {
                echo "<tr>";
                echo "<td>{$user['id']}</td>";
                echo "<td>{$user['username']}</td>";
                echo "<td>{$user['email']}</td>";
                echo "<td>{$user['role_name']}</td>";
                echo "<td>{$user['status']}</td>";
                echo "</tr>";
            }
            
            echo "</table>";
        }
    } else {
        echo "<p>No se pudo verificar la cantidad de usuarios.</p>";
    }
    
    echo "<p>Todo parece estar funcionando correctamente. Puedes <a href='login.php'>iniciar sesión</a> ahora.</p>";
    
} catch (Exception $e) {
    echo "<h1>Error de conexión a la base de datos</h1>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "<p>Es posible que necesites <a href='init_db.php'>inicializar la base de datos</a> primero.</p>";
}
?>
