<?php
session_start();
require_once 'db_config.php';

// Verificar si el usuario está logueado y es proveedor
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'proveedor_repuestos') {
    header('Location: login-dinamico.php');
    exit();
}

$user_id = $_SESSION['user_id'];
$user_name = $_SESSION['user_name'] ?? 'Proveedor';

// Procesar formulario de agregar repuesto
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'agregar') {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO repuestos (nombre, descripcion, categoria, marca, modelo, precio, stock, codigo_producto, supplier_id, supplier_name, estado)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'disponible')
        ");
        
        $stmt->execute([
            $_POST['nombre'],
            $_POST['descripcion'],
            $_POST['categoria'],
            $_POST['marca'],
            $_POST['modelo'],
            $_POST['precio'],
            $_POST['stock'],
            $_POST['codigo'],
            $user_id,
            $user_name
        ]);
        
        $success_message = "Repuesto agregado exitosamente!";
    } catch (PDOException $e) {
        $error_message = "Error al agregar repuesto: " . $e->getMessage();
    }
}

// Obtener repuestos del proveedor
$repuestos = [];
try {
    $stmt = $pdo->prepare("
        SELECT * FROM repuestos
        WHERE supplier_id = ?
        ORDER BY fecha_creacion DESC
    ");
    $stmt->execute([$user_id]);
    $repuestos = $stmt->fetchAll();
} catch (PDOException $e) {
    $error_message = "Error al cargar inventario: " . $e->getMessage();
}

// Obtener categorías disponibles
$categorias = [
    'Motor y Combustible',
    'Frenos y Suspensión',
    'Eléctrico y Encendido',
    'Transmisión',
    'Carrocería y Exterior',
    'Interior y Confort',
    'Neumáticos y Llantas',
    'Aceites y Lubricantes',
    'Herramientas',
    'Accesorios'
];
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mi Inventario - RepuMovil</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            min-height: 100vh;
        }

        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .logo h1 {
            color: white;
            font-size: 1.8rem;
            font-weight: 900;
        }

        .logo .repu { color: #FFE082; }
        .logo .movil { color: #E8F5E8; }

        .nav-links {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .nav-link {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            transition: background 0.3s ease;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .page-header {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .page-title {
            font-size: 1.8rem;
            color: #333;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .btn-primary {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: background 0.3s ease;
        }

        .btn-primary:hover {
            background: #45a049;
        }

        .form-section {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            display: none;
        }

        .form-section.active {
            display: block;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: #333;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            padding: 0.8rem;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #4CAF50;
        }

        .inventory-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .product-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
            border-left: 5px solid #4CAF50;
        }

        .product-card:hover {
            transform: translateY(-5px);
        }

        .product-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .product-name {
            font-size: 1.2rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 0.3rem;
        }

        .product-code {
            background: #f0f0f0;
            color: #666;
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
        }

        .product-info {
            margin-bottom: 1rem;
        }

        .product-info p {
            margin-bottom: 0.3rem;
            color: #666;
            font-size: 0.9rem;
        }

        .product-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .product-price {
            font-size: 1.3rem;
            font-weight: bold;
            color: #4CAF50;
        }

        .stock-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .stock-high { background: #E8F5E8; color: #4CAF50; }
        .stock-medium { background: #FFF3E0; color: #FF9800; }
        .stock-low { background: #FFEBEE; color: #F44336; }

        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }

        .alert-success {
            background: #E8F5E8;
            color: #4CAF50;
            border: 1px solid #4CAF50;
        }

        .alert-error {
            background: #FFEBEE;
            color: #F44336;
            border: 1px solid #F44336;
        }

        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #666;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .page-header {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .inventory-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="logo">
            <i class="fas fa-store" style="font-size: 2rem; color: #FFE082;"></i>
            <h1><span class="repu">Repu</span><span class="movil">Movil</span></h1>
        </div>
        <div class="nav-links">
            <a href="dashboard-proveedor.php" class="nav-link">
                <i class="fas fa-home"></i> Dashboard
            </a>
            <a href="logout.php" class="nav-link">
                <i class="fas fa-sign-out-alt"></i> Salir
            </a>
        </div>
    </div>

    <div class="container">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-boxes"></i>
                Mi Inventario
            </h1>
            <button class="btn-primary" onclick="toggleForm()">
                <i class="fas fa-plus"></i> Agregar Repuesto
            </button>
        </div>

        <!-- Alerts -->
        <?php if (isset($success_message)): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
            </div>
        <?php endif; ?>

        <?php if (isset($error_message)): ?>
            <div class="alert alert-error">
                <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
            </div>
        <?php endif; ?>

        <!-- Form Section -->
        <div id="formSection" class="form-section">
            <h2 style="margin-bottom: 1.5rem; color: #333;">
                <i class="fas fa-plus-circle"></i> Agregar Nuevo Repuesto
            </h2>
            
            <form method="POST">
                <input type="hidden" name="action" value="agregar">
                
                <div class="form-grid">
                    <div class="form-group">
                        <label for="nombre">Nombre del Repuesto *</label>
                        <input type="text" id="nombre" name="nombre" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="categoria">Categoría *</label>
                        <select id="categoria" name="categoria" required>
                            <option value="">Seleccionar categoría</option>
                            <?php foreach ($categorias as $categoria): ?>
                                <option value="<?php echo $categoria; ?>"><?php echo $categoria; ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="marca">Marca *</label>
                        <input type="text" id="marca" name="marca" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="modelo">Modelo/Aplicación</label>
                        <input type="text" id="modelo" name="modelo">
                    </div>
                    
                    <div class="form-group">
                        <label for="precio">Precio *</label>
                        <input type="number" id="precio" name="precio" step="0.01" min="0" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="stock">Stock Inicial *</label>
                        <input type="number" id="stock" name="stock" min="0" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="codigo">Código de Producto *</label>
                        <input type="text" id="codigo" name="codigo" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="descripcion">Descripción</label>
                    <textarea id="descripcion" name="descripcion" rows="3" placeholder="Descripción detallada del repuesto..."></textarea>
                </div>
                
                <div style="display: flex; gap: 1rem; margin-top: 1.5rem;">
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-save"></i> Guardar Repuesto
                    </button>
                    <button type="button" class="btn-primary" onclick="toggleForm()" style="background: #666;">
                        <i class="fas fa-times"></i> Cancelar
                    </button>
                </div>
            </form>
        </div>

        <!-- Inventory Grid -->
        <?php if (empty($repuestos)): ?>
            <div class="empty-state">
                <i class="fas fa-box-open"></i>
                <h3>No hay productos en tu inventario</h3>
                <p>Comienza agregando tu primer repuesto usando el botón "Agregar Repuesto"</p>
            </div>
        <?php else: ?>
            <div class="inventory-grid">
                <?php foreach ($repuestos as $repuesto): ?>
                    <div class="product-card">
                        <div class="product-header">
                            <div>
                                <div class="product-name"><?php echo htmlspecialchars($repuesto['nombre']); ?></div>
                                <div class="product-code"><?php echo htmlspecialchars($repuesto['codigo_producto']); ?></div>
                            </div>
                        </div>
                        
                        <div class="product-info">
                            <p><strong>Marca:</strong> <?php echo htmlspecialchars($repuesto['marca']); ?></p>
                            <p><strong>Categoría:</strong> <?php echo htmlspecialchars($repuesto['categoria']); ?></p>
                            <?php if ($repuesto['modelo']): ?>
                                <p><strong>Modelo:</strong> <?php echo htmlspecialchars($repuesto['modelo']); ?></p>
                            <?php endif; ?>
                            <?php if ($repuesto['descripcion']): ?>
                                <p><strong>Descripción:</strong> <?php echo htmlspecialchars(substr($repuesto['descripcion'], 0, 100)); ?><?php echo strlen($repuesto['descripcion']) > 100 ? '...' : ''; ?></p>
                            <?php endif; ?>
                        </div>
                        
                        <div class="product-footer">
                            <div class="product-price">$<?php echo number_format($repuesto['precio'], 0, ',', '.'); ?></div>
                            <div class="stock-badge <?php 
                                if ($repuesto['stock'] > 20) echo 'stock-high';
                                elseif ($repuesto['stock'] > 5) echo 'stock-medium';
                                else echo 'stock-low';
                            ?>">
                                Stock: <?php echo $repuesto['stock']; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>

    <script>
        function toggleForm() {
            const formSection = document.getElementById('formSection');
            formSection.classList.toggle('active');
            
            if (formSection.classList.contains('active')) {
                formSection.scrollIntoView({ behavior: 'smooth' });
            }
        }
    </script>
</body>
</html>
