<?php
session_start();
require_once 'db_config.php';

// Conectar a la base de datos
try {
    $pdo = connectDB();
} catch (Exception $e) {
    die("Error de conexión: " . $e->getMessage());
}

// Verificar si el usuario está logueado y es proveedor
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'proveedor_repuestos') {
    header('Location: login-dinamico.php');
    exit();
}

$user_id = $_SESSION['user_id'];
$user_name = $_SESSION['user_name'] ?? 'Proveedor';

// Crear directorio de imágenes si no existe
$upload_dir = 'uploads/repuestos/';
if (!file_exists($upload_dir)) {
    mkdir($upload_dir, 0777, true);
}

// Procesar formulario de agregar repuesto
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'agregar') {
    try {
        // Iniciar transacción
        $pdo->beginTransaction();

        // Insertar repuesto
        $stmt = $pdo->prepare("
            INSERT INTO repuestos (nombre, descripcion, categoria, marca, modelo, precio, stock, codigo_producto, supplier_id, supplier_name, estado)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'disponible')
        ");

        $stmt->execute([
            $_POST['nombre'],
            $_POST['descripcion'],
            $_POST['categoria'],
            $_POST['marca'],
            $_POST['modelo'],
            $_POST['precio'],
            $_POST['stock'],
            $_POST['codigo'],
            $user_id,
            $user_name
        ]);

        $repuesto_id = $pdo->lastInsertId();

        // Procesar imágenes subidas
        if (isset($_FILES['imagenes']) && !empty($_FILES['imagenes']['name'][0])) {
            $orden = 1;
            foreach ($_FILES['imagenes']['name'] as $key => $filename) {
                if ($_FILES['imagenes']['error'][$key] === UPLOAD_ERR_OK) {
                    $tmp_name = $_FILES['imagenes']['tmp_name'][$key];
                    $file_extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));

                    // Validar tipo de archivo
                    $allowed_types = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
                    if (in_array($file_extension, $allowed_types)) {
                        // Generar nombre único
                        $new_filename = 'repuesto_' . $repuesto_id . '_' . $orden . '_' . time() . '.' . $file_extension;
                        $upload_path = $upload_dir . $new_filename;

                        if (move_uploaded_file($tmp_name, $upload_path)) {
                            // Guardar en base de datos
                            $stmt_img = $pdo->prepare("
                                INSERT INTO repuesto_imagenes (repuesto_id, url_imagen, nombre_archivo, orden)
                                VALUES (?, ?, ?, ?)
                            ");
                            $stmt_img->execute([$repuesto_id, $upload_path, $new_filename, $orden]);
                            $orden++;
                        }
                    }
                }
            }
        }

        $pdo->commit();
        $success_message = "¡Repuesto agregado exitosamente con " . ($orden - 1) . " imagen(es)! 🎉";

    } catch (PDOException $e) {
        $pdo->rollBack();
        $error_message = "Error al agregar repuesto: " . $e->getMessage();
    }
}

// Obtener repuestos del proveedor
$repuestos = [];
try {
    // Primero verificar si la tabla existe
    $stmt = $pdo->query("SHOW TABLES LIKE 'repuestos'");
    if ($stmt->rowCount() == 0) {
        // Crear la tabla si no existe
        $pdo->exec("CREATE TABLE repuestos (
            id INT AUTO_INCREMENT PRIMARY KEY,
            supplier_id INT NOT NULL,
            supplier_name VARCHAR(255),
            nombre VARCHAR(255) NOT NULL,
            descripcion TEXT,
            categoria VARCHAR(100) NOT NULL,
            marca VARCHAR(100),
            modelo VARCHAR(100),
            precio DECIMAL(10,2) NOT NULL,
            stock INT DEFAULT 0,
            imagen_url VARCHAR(500),
            codigo_producto VARCHAR(100),
            estado ENUM('disponible', 'agotado', 'descontinuado') DEFAULT 'disponible',
            fecha_creacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
    }

    // Crear tabla de imágenes múltiples si no existe
    $stmt = $pdo->query("SHOW TABLES LIKE 'repuesto_imagenes'");
    if ($stmt->rowCount() == 0) {
        $pdo->exec("CREATE TABLE repuesto_imagenes (
            id INT AUTO_INCREMENT PRIMARY KEY,
            repuesto_id INT NOT NULL,
            url_imagen VARCHAR(500) NOT NULL,
            nombre_archivo VARCHAR(255),
            orden INT DEFAULT 1,
            descripcion VARCHAR(255),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (repuesto_id) REFERENCES repuestos(id) ON DELETE CASCADE,
            INDEX idx_repuesto_orden (repuesto_id, orden)
        )");
    }

    // Obtener repuestos con sus imágenes
    $stmt = $pdo->prepare("
        SELECT r.*,
               GROUP_CONCAT(
                   CONCAT(ri.id, '|', ri.url_imagen, '|', ri.orden)
                   ORDER BY ri.orden ASC
                   SEPARATOR ';'
               ) as imagenes
        FROM repuestos r
        LEFT JOIN repuesto_imagenes ri ON r.id = ri.repuesto_id
        WHERE r.supplier_id = ?
        GROUP BY r.id
        ORDER BY r.fecha_creacion DESC
    ");
    $stmt->execute([$user_id]);
    $repuestos_raw = $stmt->fetchAll();

    // Procesar imágenes para cada repuesto
    $repuestos = [];
    foreach ($repuestos_raw as $repuesto) {
        $repuesto['imagenes_array'] = [];
        if (!empty($repuesto['imagenes'])) {
            $imagenes_data = explode(';', $repuesto['imagenes']);
            foreach ($imagenes_data as $img_data) {
                $img_parts = explode('|', $img_data);
                if (count($img_parts) >= 3) {
                    $repuesto['imagenes_array'][] = [
                        'id' => $img_parts[0],
                        'url' => $img_parts[1],
                        'orden' => $img_parts[2]
                    ];
                }
            }
        }
        $repuestos[] = $repuesto;
    }
} catch (PDOException $e) {
    $error_message = "Error al cargar inventario: " . $e->getMessage();
}

// Obtener categorías disponibles
$categorias = [
    'Motor y Combustible',
    'Frenos y Suspensión',
    'Eléctrico y Encendido',
    'Transmisión',
    'Carrocería y Exterior',
    'Interior y Confort',
    'Neumáticos y Llantas',
    'Aceites y Lubricantes',
    'Herramientas',
    'Accesorios'
];
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mi Inventario - RepuMovil</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
            min-height: 100vh;
        }

        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .logo h1 {
            color: white;
            font-size: 1.8rem;
            font-weight: 900;
        }

        .logo .repu { color: #FFE082; }
        .logo .movil { color: #FFFFFF; }

        .nav-links {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .nav-link {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            transition: background 0.3s ease;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .page-header {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .page-title {
            font-size: 1.8rem;
            color: #333;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .btn-primary {
            background: #FF6B35;
            color: white;
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: background 0.3s ease;
        }

        .btn-primary:hover {
            background: #E55A2B;
        }

        .form-section {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            display: none;
        }

        .form-section.active {
            display: block;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: #333;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            padding: 0.8rem;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #FF6B35;
        }

        .inventory-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .product-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
            border-left: 5px solid #FF6B35;
        }

        .product-card:hover {
            transform: translateY(-5px);
        }

        .product-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .product-name {
            font-size: 1.2rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 0.3rem;
        }

        .product-code {
            background: #f0f0f0;
            color: #666;
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
        }

        .product-info {
            margin-bottom: 1rem;
        }

        .product-info p {
            margin-bottom: 0.3rem;
            color: #666;
            font-size: 0.9rem;
        }

        .product-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .product-price {
            font-size: 1.3rem;
            font-weight: bold;
            color: #FF6B35;
        }

        .stock-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .stock-high { background: #FFF4F0; color: #FF6B35; }
        .stock-medium { background: #FFF3E0; color: #F7931E; }
        .stock-low { background: #FFEBEE; color: #F44336; }

        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }

        .alert-success {
            background: #FFF4F0;
            color: #FF6B35;
            border: 1px solid #FF6B35;
        }

        .alert-error {
            background: #FFEBEE;
            color: #F44336;
            border: 1px solid #F44336;
        }

        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #666;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        /* Estilos para sistema de imágenes */
        .image-upload-section {
            margin-top: 1rem;
            padding: 1rem;
            border: 2px dashed #FF6B35;
            border-radius: 8px;
            background: #FFF4F0;
        }

        .image-upload-section h4 {
            color: #FF6B35;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .file-input-wrapper {
            position: relative;
            display: inline-block;
            cursor: pointer;
            width: 100%;
        }

        .file-input-wrapper input[type=file] {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }

        .file-input-display {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            background: white;
            transition: all 0.3s ease;
        }

        .file-input-display:hover {
            border-color: #FF6B35;
            background: #FFF4F0;
        }

        .file-input-display i {
            font-size: 2rem;
            color: #FF6B35;
        }

        .file-info {
            flex: 1;
        }

        .file-info h5 {
            margin: 0;
            color: #333;
        }

        .file-info p {
            margin: 0;
            color: #666;
            font-size: 0.9rem;
        }

        .image-preview {
            display: flex;
            gap: 1rem;
            margin-top: 1rem;
            flex-wrap: wrap;
        }

        .image-preview-item {
            position: relative;
            width: 100px;
            height: 100px;
            border-radius: 8px;
            overflow: hidden;
            border: 2px solid #FF6B35;
        }

        .image-preview-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .product-images {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }

        .product-image {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            overflow: hidden;
            border: 2px solid #FF6B35;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .product-image:hover {
            transform: scale(1.1);
        }

        .product-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .product-image.main {
            width: 80px;
            height: 80px;
            border-width: 3px;
        }

        .no-images {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 1.5rem;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .page-header {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .inventory-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="logo">
            <i class="fas fa-wrench" style="font-size: 2rem; color: #FFE082;"></i>
            <h1><span class="repu">Repu</span><span class="movil">Movil</span></h1>
        </div>
        <div class="nav-links">
            <a href="dashboard-proveedor.php" class="nav-link">
                <i class="fas fa-home"></i> Dashboard
            </a>
            <a href="logout.php" class="nav-link">
                <i class="fas fa-sign-out-alt"></i> Salir
            </a>
        </div>
    </div>

    <div class="container">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-boxes"></i>
                Mi Inventario
            </h1>
            <button class="btn-primary" onclick="toggleForm()">
                <i class="fas fa-plus"></i> Agregar Repuesto
            </button>
        </div>

        <!-- Alerts -->
        <?php if (isset($success_message)): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
            </div>
        <?php endif; ?>

        <?php if (isset($error_message)): ?>
            <div class="alert alert-error">
                <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
            </div>
        <?php endif; ?>

        <!-- Form Section -->
        <div id="formSection" class="form-section">
            <h2 style="margin-bottom: 1.5rem; color: #333;">
                <i class="fas fa-plus-circle"></i> Agregar Nuevo Repuesto
            </h2>
            
            <form method="POST" enctype="multipart/form-data">
                <input type="hidden" name="action" value="agregar">
                
                <div class="form-grid">
                    <div class="form-group">
                        <label for="nombre">Nombre del Repuesto *</label>
                        <input type="text" id="nombre" name="nombre" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="categoria">Categoría *</label>
                        <select id="categoria" name="categoria" required>
                            <option value="">Seleccionar categoría</option>
                            <?php foreach ($categorias as $categoria): ?>
                                <option value="<?php echo $categoria; ?>"><?php echo $categoria; ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="marca">Marca *</label>
                        <input type="text" id="marca" name="marca" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="modelo">Modelo/Aplicación</label>
                        <input type="text" id="modelo" name="modelo">
                    </div>
                    
                    <div class="form-group">
                        <label for="precio">Precio *</label>
                        <input type="number" id="precio" name="precio" step="0.01" min="0" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="stock">Stock Inicial *</label>
                        <input type="number" id="stock" name="stock" min="0" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="codigo">Código de Producto *</label>
                        <input type="text" id="codigo" name="codigo" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="descripcion">Descripción</label>
                    <textarea id="descripcion" name="descripcion" rows="3" placeholder="Descripción detallada del repuesto..."></textarea>
                </div>

                <!-- Sección de carga de imágenes -->
                <div class="image-upload-section">
                    <h4><i class="fas fa-images"></i> Imágenes del Repuesto</h4>
                    <p style="margin-bottom: 1rem; color: #666;">Puedes subir múltiples imágenes (JPG, PNG, GIF, WEBP). La primera imagen será la principal.</p>

                    <div class="file-input-wrapper">
                        <input type="file" id="imagenes" name="imagenes[]" multiple accept="image/*" onchange="previewImages(this)">
                        <div class="file-input-display">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <div class="file-info">
                                <h5>Seleccionar Imágenes</h5>
                                <p>Haz clic aquí o arrastra las imágenes</p>
                            </div>
                        </div>
                    </div>

                    <div id="imagePreview" class="image-preview"></div>
                </div>
                
                <div style="display: flex; gap: 1rem; margin-top: 1.5rem;">
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-save"></i> Guardar Repuesto
                    </button>
                    <button type="button" class="btn-primary" onclick="toggleForm()" style="background: #666;">
                        <i class="fas fa-times"></i> Cancelar
                    </button>
                </div>
            </form>
        </div>

        <!-- Inventory Grid -->
        <?php if (empty($repuestos)): ?>
            <div class="empty-state">
                <i class="fas fa-box-open"></i>
                <h3>No hay productos en tu inventario</h3>
                <p>Comienza agregando tu primer repuesto usando el botón "Agregar Repuesto"</p>
            </div>
        <?php else: ?>
            <div class="inventory-grid">
                <?php foreach ($repuestos as $repuesto): ?>
                    <div class="product-card">
                        <!-- Imágenes del producto -->
                        <?php if (!empty($repuesto['imagenes_array'])): ?>
                            <div class="product-images">
                                <?php foreach ($repuesto['imagenes_array'] as $index => $imagen): ?>
                                    <div class="product-image <?php echo $index === 0 ? 'main' : ''; ?>" onclick="openImageModal('<?php echo htmlspecialchars($imagen['url']); ?>')">
                                        <img src="<?php echo htmlspecialchars($imagen['url']); ?>" alt="Imagen del repuesto" loading="lazy">
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <div class="product-images">
                                <div class="no-images">
                                    <i class="fas fa-image"></i>
                                </div>
                            </div>
                        <?php endif; ?>

                        <div class="product-header">
                            <div>
                                <div class="product-name"><?php echo htmlspecialchars($repuesto['nombre']); ?></div>
                                <div class="product-code"><?php echo htmlspecialchars($repuesto['codigo_producto']); ?></div>
                            </div>
                        </div>
                        
                        <div class="product-info">
                            <p><strong>Marca:</strong> <?php echo htmlspecialchars($repuesto['marca']); ?></p>
                            <p><strong>Categoría:</strong> <?php echo htmlspecialchars($repuesto['categoria']); ?></p>
                            <?php if ($repuesto['modelo']): ?>
                                <p><strong>Modelo:</strong> <?php echo htmlspecialchars($repuesto['modelo']); ?></p>
                            <?php endif; ?>
                            <?php if ($repuesto['descripcion']): ?>
                                <p><strong>Descripción:</strong> <?php echo htmlspecialchars(substr($repuesto['descripcion'], 0, 100)); ?><?php echo strlen($repuesto['descripcion']) > 100 ? '...' : ''; ?></p>
                            <?php endif; ?>
                        </div>
                        
                        <div class="product-footer">
                            <div class="product-price">$<?php echo number_format($repuesto['precio'], 0, ',', '.'); ?></div>
                            <div class="stock-badge <?php 
                                if ($repuesto['stock'] > 20) echo 'stock-high';
                                elseif ($repuesto['stock'] > 5) echo 'stock-medium';
                                else echo 'stock-low';
                            ?>">
                                Stock: <?php echo $repuesto['stock']; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>

    <!-- Modal para ver imágenes -->
    <div id="imageModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 1000; cursor: pointer;" onclick="closeImageModal()">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); max-width: 90%; max-height: 90%;">
            <img id="modalImage" style="max-width: 100%; max-height: 100%; border-radius: 8px;">
        </div>
        <div style="position: absolute; top: 20px; right: 20px; color: white; font-size: 2rem; cursor: pointer;" onclick="closeImageModal()">
            <i class="fas fa-times"></i>
        </div>
    </div>

    <script>
        function toggleForm() {
            const formSection = document.getElementById('formSection');
            formSection.classList.toggle('active');

            if (formSection.classList.contains('active')) {
                formSection.scrollIntoView({ behavior: 'smooth' });
            }
        }

        function previewImages(input) {
            const preview = document.getElementById('imagePreview');
            preview.innerHTML = '';

            if (input.files) {
                Array.from(input.files).forEach((file, index) => {
                    if (file.type.startsWith('image/')) {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            const div = document.createElement('div');
                            div.className = 'image-preview-item';
                            div.innerHTML = `
                                <img src="${e.target.result}" alt="Preview ${index + 1}">
                                <div style="position: absolute; top: 5px; right: 5px; background: rgba(255,107,53,0.8); color: white; border-radius: 50%; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center; font-size: 0.8rem;">
                                    ${index + 1}
                                </div>
                            `;
                            preview.appendChild(div);
                        };
                        reader.readAsDataURL(file);
                    }
                });
            }

            // Actualizar texto del input
            const fileInfo = document.querySelector('.file-info');
            if (input.files.length > 0) {
                fileInfo.innerHTML = `
                    <h5>${input.files.length} imagen(es) seleccionada(s)</h5>
                    <p>Listo para subir</p>
                `;
            } else {
                fileInfo.innerHTML = `
                    <h5>Seleccionar Imágenes</h5>
                    <p>Haz clic aquí o arrastra las imágenes</p>
                `;
            }
        }

        function openImageModal(imageSrc) {
            document.getElementById('modalImage').src = imageSrc;
            document.getElementById('imageModal').style.display = 'block';
        }

        function closeImageModal() {
            document.getElementById('imageModal').style.display = 'none';
        }

        // Cerrar modal con ESC
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeImageModal();
            }
        });
    </script>
</body>
</html>
