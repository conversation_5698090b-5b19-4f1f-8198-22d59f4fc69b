<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crear Cuenta - RepuMovil</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .container-custom {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }

        .logo-container {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo-circle {
            width: 80px;
            height: 80px;
            background: #FF6B35;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
            box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
        }

        .logo-icon {
            font-size: 35px;
            color: white;
        }

        .logo-title {
            font-size: 32px;
            font-weight: bold;
            margin: 0;
        }

        .logo-repu {
            color: #FF6B35;
        }

        .logo-movil {
            color: #FFA500;
        }

        .card-custom {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: none;
            overflow: hidden;
        }

        .card-header-custom {
            background: linear-gradient(135deg, #FF6B35 0%, #FFA500 100%);
            color: white;
            text-align: center;
            padding: 25px;
            border: none;
        }

        .card-title {
            font-size: 24px;
            font-weight: 600;
            margin: 0;
        }

        .form-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 12px 15px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #FF6B35;
            box-shadow: 0 0 0 0.2rem rgba(255, 107, 53, 0.25);
        }

        .form-select {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 12px 15px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-select:focus {
            border-color: #FF6B35;
            box-shadow: 0 0 0 0.2rem rgba(255, 107, 53, 0.25);
        }

        .btn-primary-custom {
            background: linear-gradient(135deg, #FF6B35 0%, #FFA500 100%);
            border: none;
            border-radius: 10px;
            padding: 15px 30px;
            font-size: 16px;
            font-weight: 600;
            width: 100%;
            transition: all 0.3s ease;
        }

        .btn-primary-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 107, 53, 0.4);
        }

        .helper-text {
            font-size: 12px;
            color: #666;
            font-style: italic;
            margin-bottom: 8px;
        }

        .dynamic-fields {
            display: none;
            animation: fadeIn 0.3s ease-in-out;
        }

        .dynamic-fields.show {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .maps-container {
            position: relative;
            margin-bottom: 15px;
        }

        .maps-buttons {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }

        .btn-maps {
            flex: 1;
            padding: 8px 12px;
            border-radius: 8px;
            border: none;
            font-size: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-search {
            background: #FF8C00;
            color: white;
        }

        .btn-search:hover {
            background: #FF7700;
            color: white;
        }

        .btn-map {
            background: #4285F4;
            color: white;
        }

        .btn-map:hover {
            background: #3367D6;
            color: white;
        }

        .back-link {
            text-align: center;
            margin-top: 20px;
        }

        .back-link a {
            color: #666;
            text-decoration: none;
            font-size: 14px;
        }

        .back-link a:hover {
            color: #FF6B35;
        }
    </style>
</head>
<body>
    <div class="container-custom">
        <!-- Logo -->
        <div class="logo-container">
            <div class="logo-circle">
                <i class="fas fa-wrench logo-icon"></i>
            </div>
            <h1 class="logo-title">
                <span class="logo-repu">Repu</span><span class="logo-movil">Movil</span>
            </h1>
        </div>

        <!-- Formulario de Registro -->
        <div class="card card-custom">
            <div class="card-header-custom">
                <h2 class="card-title">Crear Cuenta</h2>
            </div>
            <div class="card-body p-4">
                <form id="registroForm" method="POST" action="api/registro-dinamico.php">
                    <!-- Tipo de Usuario -->
                    <div class="mb-3">
                        <label for="userType" class="form-label">Tipo de Usuario</label>
                        <select class="form-select" id="userType" name="user_type" required>
                            <option value="">Seleccionar tipo</option>
                            <option value="usuario_regular">Usuario regular</option>
                            <option value="taller_mecanico">Taller mecánico</option>
                            <option value="mecanico_independiente">Mecánico independiente</option>
                            <option value="proveedor_repuestos">Proveedor de repuestos</option>
                        </select>
                    </div>

                    <!-- Campos Básicos -->
                    <div class="mb-3">
                        <label for="nombre" class="form-label" id="labelNombre">Nombre Completo</label>
                        <input type="text" class="form-control" id="nombre" name="nombre" required>
                    </div>

                    <div class="mb-3">
                        <label for="email" class="form-label">Correo Electrónico</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>

                    <div class="mb-3">
                        <label for="telefono" class="form-label">Teléfono</label>
                        <div id="smsHelper" class="helper-text" style="display: none;">
                            📱 Recibirás un SMS con la confirmación de la cuenta
                        </div>
                        <input type="tel" class="form-control" id="telefono" name="telefono" required>
                    </div>

                    <!-- Campos para Talleres Mecánicos -->
                    <div id="tallerFields" class="dynamic-fields">
                        <div class="mb-3">
                            <label for="username" class="form-label">Nombre de Usuario</label>
                            <input type="text" class="form-control" id="username" name="username" placeholder="Nombre de usuario único">
                        </div>

                        <div class="mb-3">
                            <label for="direccion" class="form-label">Dirección</label>
                            <div class="maps-container">
                                <textarea class="form-control" id="direccion" name="direccion" rows="2" placeholder="Dirección del taller"></textarea>
                                <div class="maps-buttons">
                                    <button type="button" class="btn btn-search btn-maps" onclick="buscarDireccion()">
                                        🔍 Buscar
                                    </button>
                                    <button type="button" class="btn btn-map btn-maps" onclick="abrirGoogleMaps()">
                                        🗺️ Abrir Mapa
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="rubroPrincipal" class="form-label">Rubro Principal</label>
                            <select class="form-select" id="rubroPrincipal" name="rubro_principal">
                                <option value="">Seleccionar rubro</option>
                                <option value="autos">Autos</option>
                                <option value="motos">Motos</option>
                                <option value="maquinaria">Maquinaria</option>
                                <option value="otro">Otro</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="datosFiscales" class="form-label">Datos Fiscales (Opcional)</label>
                            <textarea class="form-control" id="datosFiscales" name="datos_fiscales" rows="2" placeholder="CUIT, Razón Social, etc."></textarea>
                        </div>
                    </div>

                    <!-- Campos para Mecánicos Independientes -->
                    <div id="mecanicoFields" class="dynamic-fields">
                        <div class="mb-3">
                            <label for="especialidades" class="form-label">Especialidades</label>
                            <div class="helper-text">
                                🔧 Especifica en qué tipo de reparaciones te especializas
                            </div>
                            <textarea class="form-control" id="especialidades" name="especialidades" rows="3" placeholder="Ej: Motor, Transmisión, Frenos, Electricidad, Aire Acondicionado..."></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="experienciaAnos" class="form-label">Años de Experiencia</label>
                            <input type="number" class="form-control" id="experienciaAnos" name="experiencia_anos" min="0" max="50" placeholder="Años de experiencia">
                        </div>

                        <div class="mb-3">
                            <label for="descripcion" class="form-label">Descripción de Servicios</label>
                            <div class="helper-text">
                                📝 Describe brevemente tus servicios y experiencia
                            </div>
                            <textarea class="form-control" id="descripcion" name="descripcion" rows="4" placeholder="Mecánico con amplia experiencia en reparación de motores y sistemas eléctricos. Trabajo a domicilio y en mi taller..."></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="zonaTrabajo" class="form-label">Zona de Trabajo</label>
                            <div class="helper-text">
                                📍 Especifica las zonas donde ofreces tus servicios
                            </div>
                            <textarea class="form-control" id="zonaTrabajo" name="zona_trabajo" rows="2" placeholder="Ej: CABA, Zona Norte, Zona Oeste..."></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="precioHora" class="form-label">Precio por Hora (Opcional)</label>
                            <div class="input-group">
                                <span class="input-group-text">$</span>
                                <input type="number" class="form-control" id="precioHora" name="precio_hora" min="0" step="0.01" placeholder="0.00">
                                <span class="input-group-text">ARS</span>
                            </div>
                        </div>
                    </div>

                    <!-- Campos para Proveedores de Repuestos -->
                    <div id="proveedorFields" class="dynamic-fields">
                        <div class="mb-3">
                            <label for="ubicacionLocal" class="form-label">Ubicación del Local</label>
                            <div class="maps-container">
                                <textarea class="form-control" id="ubicacionLocal" name="ubicacion_local" rows="2" placeholder="Dirección del local"></textarea>
                                <div class="maps-buttons">
                                    <button type="button" class="btn btn-search btn-maps" onclick="buscarUbicacion()">
                                        🔍 Buscar
                                    </button>
                                    <button type="button" class="btn btn-map btn-maps" onclick="abrirGoogleMapsLocal()">
                                        🗺️ Abrir Mapa
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="zonasEntrega" class="form-label">Zonas de Entrega</label>
                            <div class="helper-text">
                                📦 Especifica las zonas donde realizas entregas
                            </div>
                            <textarea class="form-control" id="zonasEntrega" name="zonas_entrega" rows="3" placeholder="Ej: Centro, Palermo, Belgrano, Villa Crespo..."></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="datosContacto" class="form-label">Datos de Contacto</label>
                            <div class="helper-text">
                                📞 WhatsApp, teléfono fijo, redes sociales, etc.
                            </div>
                            <textarea class="form-control" id="datosContacto" name="datos_contacto" rows="4" placeholder="WhatsApp: +54 9 11 1234-5678&#10;Instagram: @milocal&#10;Facebook: Mi Local de Repuestos"></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="horariosAtencion" class="form-label">Horarios de Atención</label>
                            <div class="helper-text">
                                🕒 Especifica días y horarios de atención
                            </div>
                            <textarea class="form-control" id="horariosAtencion" name="horarios_atencion" rows="4" placeholder="Lunes a Viernes: 8:00 - 18:00&#10;Sábados: 8:00 - 13:00&#10;Domingos: Cerrado"></textarea>
                        </div>
                    </div>

                    <!-- Contraseña -->
                    <div class="mb-3">
                        <label for="password" class="form-label">Contraseña</label>
                        <input type="password" class="form-control" id="password" name="password" required placeholder="Mínimo 6 caracteres">
                    </div>

                    <div class="mb-4">
                        <label for="confirmPassword" class="form-label">Confirmar Contraseña</label>
                        <input type="password" class="form-control" id="confirmPassword" name="confirm_password" required placeholder="Repita su contraseña">
                    </div>

                    <!-- Botón de Registro -->
                    <button type="submit" class="btn btn-primary-custom">
                        <i class="fas fa-user-plus me-2"></i>Crear Cuenta
                    </button>
                </form>
            </div>
        </div>

        <!-- Enlace de regreso -->
        <div class="back-link">
            <a href="index.php">
                <i class="fas fa-arrow-left me-1"></i>¿Ya tiene una cuenta? Iniciar Sesión
            </a>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Configuración de Google Maps
        const GOOGLE_MAPS_API_KEY = 'AIzaSyBOti4mM-6x9WDnZIjIeyEU21OpBXqWBgw';

        // Elementos del DOM
        const userTypeSelect = document.getElementById('userType');
        const labelNombre = document.getElementById('labelNombre');
        const smsHelper = document.getElementById('smsHelper');
        const tallerFields = document.getElementById('tallerFields');
        const mecanicoFields = document.getElementById('mecanicoFields');
        const proveedorFields = document.getElementById('proveedorFields');

        // Función para cambiar campos según tipo de usuario
        userTypeSelect.addEventListener('change', function() {
            const selectedType = this.value;

            // Ocultar todos los campos dinámicos
            tallerFields.classList.remove('show');
            mecanicoFields.classList.remove('show');
            proveedorFields.classList.remove('show');
            smsHelper.style.display = 'none';

            // Cambiar label del nombre según el tipo
            switch(selectedType) {
                case 'taller_mecanico':
                    labelNombre.textContent = 'Nombre del Taller';
                    smsHelper.style.display = 'block';
                    setTimeout(() => tallerFields.classList.add('show'), 100);
                    break;

                case 'proveedor_repuestos':
                    labelNombre.textContent = 'Nombre del Local';
                    setTimeout(() => proveedorFields.classList.add('show'), 100);
                    break;

                case 'mecanico_independiente':
                    labelNombre.textContent = 'Nombre Completo';
                    setTimeout(() => mecanicoFields.classList.add('show'), 100);
                    break;

                default:
                    labelNombre.textContent = 'Nombre Completo';
            }
        });

        // Funciones para Google Maps
        function buscarDireccion() {
            const direccion = document.getElementById('direccion').value;
            if (!direccion.trim()) {
                alert('Por favor ingrese una dirección');
                return;
            }

            // Simular búsqueda de geocodificación
            alert('Función de búsqueda de dirección.\nEn la versión completa, aquí se integraría con la API de Google Maps para geocodificar la dirección.');
        }

        function abrirGoogleMaps() {
            const direccion = document.getElementById('direccion').value;
            if (!direccion.trim()) {
                alert('Por favor ingrese una dirección primero');
                return;
            }

            const url = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(direccion)}`;
            window.open(url, '_blank');
        }

        function buscarUbicacion() {
            const ubicacion = document.getElementById('ubicacionLocal').value;
            if (!ubicacion.trim()) {
                alert('Por favor ingrese una ubicación');
                return;
            }

            // Simular búsqueda de geocodificación
            alert('Función de búsqueda de ubicación.\nEn la versión completa, aquí se integraría con la API de Google Maps para geocodificar la ubicación.');
        }

        function abrirGoogleMapsLocal() {
            const ubicacion = document.getElementById('ubicacionLocal').value;
            if (!ubicacion.trim()) {
                alert('Por favor ingrese una ubicación primero');
                return;
            }

            const url = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(ubicacion)}`;
            window.open(url, '_blank');
        }

        // Validación del formulario
        document.getElementById('registroForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirmPassword').value;

            if (password !== confirmPassword) {
                alert('Las contraseñas no coinciden');
                return;
            }

            if (password.length < 6) {
                alert('La contraseña debe tener al menos 6 caracteres');
                return;
            }

            // Enviar formulario
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creando cuenta...';
            submitBtn.disabled = true;

            // Envío real del formulario
            fetch(this.action, {
                method: 'POST',
                body: new FormData(this)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('¡Cuenta creada exitosamente! Redirigiendo al login...');
                    setTimeout(() => {
                        window.location.href = 'login-dinamico.php';
                    }, 1500);
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                alert('Error de conexión. Intenta nuevamente.');
                console.error('Error:', error);
            })
            .finally(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });
    </script>
</body>
</html>
