<?php
// PASO 10: API para métricas administrativas
// RepuMovil - Dashboard Administrativo

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Manejar preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Configuración de base de datos
$host = 'localhost';
$dbname = 'mechanical_workshop';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    sendResponse(false, 'Error de conexión a la base de datos: ' . $e->getMessage());
}

// Función para enviar respuesta JSON
function sendResponse($success, $message, $data = null) {
    echo json_encode([
        'success' => $success,
        'message' => $message,
        'data' => $data,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    exit();
}

// Manejar diferentes endpoints
$action = $_GET['action'] ?? '';

switch ($action) {
    case 'kpis_principales':
        obtenerKPIsPrincipales($pdo);
        break;
        
    case 'metricas_deliveries':
        obtenerMetricasDeliveries($pdo);
        break;
        
    case 'metricas_pedidos':
        obtenerMetricasPedidos($pdo);
        break;
        
    case 'metricas_calificaciones':
        obtenerMetricasCalificaciones($pdo);
        break;
        
    case 'metricas_financieras':
        obtenerMetricasFinancieras($pdo);
        break;
        
    case 'actividad_reciente':
        obtenerActividadReciente($pdo);
        break;
        
    case 'graficos_dashboard':
        obtenerGraficosDashboard($pdo);
        break;
        
    case 'alertas_sistema':
        obtenerAlertasSistema($pdo);
        break;
        
    default:
        obtenerResumenCompleto($pdo);
}

/**
 * PASO 10: Obtener KPIs principales
 */
function obtenerKPIsPrincipales($pdo) {
    try {
        // Total de pedidos
        $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM pedidos WHERE MONTH(fecha_pedido) = MONTH(NOW())");
        $stmt->execute();
        $totalPedidos = $stmt->fetchColumn();
        
        // Deliveries activos
        $stmt = $pdo->prepare("SELECT COUNT(*) as activos FROM repartidores WHERE estado = 'disponible' AND activo = 1");
        $stmt->execute();
        $deliveriesActivos = $stmt->fetchColumn();
        
        // Ingresos del mes (simulado)
        $ingresosMes = rand(80000, 95000);
        
        // Calificación promedio
        $stmt = $pdo->prepare("SELECT AVG(calificacion) as promedio FROM calificaciones WHERE MONTH(fecha_calificacion) = MONTH(NOW())");
        $stmt->execute();
        $calificacionPromedio = $stmt->fetchColumn() ?: 4.8;
        
        // Tiempo promedio de entrega
        $stmt = $pdo->prepare("SELECT AVG(eta_minutos) as promedio FROM pedidos WHERE estado = 'entregado' AND DATE(fecha_pedido) = CURDATE()");
        $stmt->execute();
        $tiempoPromedio = $stmt->fetchColumn() ?: 18.5;
        
        // Tasa de éxito
        $stmt = $pdo->prepare("
            SELECT 
                (COUNT(CASE WHEN estado = 'entregado' THEN 1 END) / COUNT(*)) * 100 as tasa_exito
            FROM pedidos 
            WHERE MONTH(fecha_pedido) = MONTH(NOW())
        ");
        $stmt->execute();
        $tasaExito = $stmt->fetchColumn() ?: 94.2;
        
        sendResponse(true, 'KPIs obtenidos', [
            'total_pedidos' => $totalPedidos,
            'deliveries_activos' => $deliveriesActivos,
            'ingresos_mes' => $ingresosMes,
            'calificacion_promedio' => round($calificacionPromedio, 1),
            'tiempo_promedio' => round($tiempoPromedio, 1),
            'tasa_exito' => round($tasaExito, 1)
        ]);
        
    } catch (Exception $e) {
        sendResponse(false, 'Error obteniendo KPIs: ' . $e->getMessage());
    }
}

/**
 * PASO 10: Obtener métricas de deliveries
 */
function obtenerMetricasDeliveries($pdo) {
    try {
        // Deliveries activos
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM repartidores WHERE estado IN ('disponible', 'ocupado') AND activo = 1");
        $stmt->execute();
        $activos = $stmt->fetchColumn();
        
        // Entregas de hoy
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM pedidos WHERE estado = 'entregado' AND DATE(fecha_pedido) = CURDATE()");
        $stmt->execute();
        $entregasHoy = $stmt->fetchColumn();
        
        // Calificación promedio de deliveries
        $stmt = $pdo->prepare("SELECT AVG(calificacion_promedio) FROM repartidores WHERE total_calificaciones > 0");
        $stmt->execute();
        $calificacionPromedio = $stmt->fetchColumn() ?: 4.8;
        
        // Disponibilidad (simulada)
        $disponibilidad = rand(88, 95);
        
        // Top deliveries
        $stmt = $pdo->prepare("
            SELECT u.nombre, r.calificacion_promedio, r.entregas_completadas, r.estado
            FROM repartidores r
            JOIN users u ON r.user_id = u.id
            WHERE r.total_calificaciones >= 3
            ORDER BY r.calificacion_promedio DESC, r.entregas_completadas DESC
            LIMIT 5
        ");
        $stmt->execute();
        $topDeliveries = $stmt->fetchAll();
        
        sendResponse(true, 'Métricas de deliveries obtenidas', [
            'activos' => $activos,
            'entregas_hoy' => $entregasHoy,
            'calificacion_promedio' => round($calificacionPromedio, 1),
            'disponibilidad' => $disponibilidad,
            'top_deliveries' => $topDeliveries
        ]);
        
    } catch (Exception $e) {
        sendResponse(false, 'Error obteniendo métricas de deliveries: ' . $e->getMessage());
    }
}

/**
 * PASO 10: Obtener métricas de pedidos
 */
function obtenerMetricasPedidos($pdo) {
    try {
        // Estados de pedidos actuales
        $stmt = $pdo->prepare("
            SELECT 
                estado,
                COUNT(*) as cantidad
            FROM pedidos 
            WHERE DATE(fecha_pedido) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
            GROUP BY estado
        ");
        $stmt->execute();
        $estadosPedidos = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
        
        // Pedidos por día (últimos 30 días)
        $stmt = $pdo->prepare("
            SELECT 
                DATE(fecha_pedido) as fecha,
                COUNT(*) as cantidad
            FROM pedidos 
            WHERE fecha_pedido >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            GROUP BY DATE(fecha_pedido)
            ORDER BY fecha ASC
        ");
        $stmt->execute();
        $pedidosPorDia = $stmt->fetchAll();
        
        // Promedio de pedidos por hora
        $stmt = $pdo->prepare("
            SELECT 
                HOUR(fecha_pedido) as hora,
                COUNT(*) as cantidad
            FROM pedidos 
            WHERE DATE(fecha_pedido) = CURDATE()
            GROUP BY HOUR(fecha_pedido)
            ORDER BY hora ASC
        ");
        $stmt->execute();
        $pedidosPorHora = $stmt->fetchAll();
        
        sendResponse(true, 'Métricas de pedidos obtenidas', [
            'estados_pedidos' => $estadosPedidos,
            'pedidos_por_dia' => $pedidosPorDia,
            'pedidos_por_hora' => $pedidosPorHora
        ]);
        
    } catch (Exception $e) {
        sendResponse(false, 'Error obteniendo métricas de pedidos: ' . $e->getMessage());
    }
}

/**
 * PASO 10: Obtener métricas de calificaciones
 */
function obtenerMetricasCalificaciones($pdo) {
    try {
        // Estadísticas generales de calificaciones
        $stmt = $pdo->prepare("
            SELECT 
                AVG(calificacion) as promedio,
                COUNT(*) as total,
                SUM(CASE WHEN calificacion = 5 THEN 1 ELSE 0 END) as cinco_estrellas,
                SUM(CASE WHEN calificacion = 4 THEN 1 ELSE 0 END) as cuatro_estrellas,
                SUM(CASE WHEN calificacion = 3 THEN 1 ELSE 0 END) as tres_estrellas,
                SUM(CASE WHEN calificacion = 2 THEN 1 ELSE 0 END) as dos_estrellas,
                SUM(CASE WHEN calificacion = 1 THEN 1 ELSE 0 END) as una_estrella,
                SUM(CASE WHEN respuesta_delivery IS NOT NULL THEN 1 ELSE 0 END) as con_respuesta
            FROM calificaciones 
            WHERE MONTH(fecha_calificacion) = MONTH(NOW())
        ");
        $stmt->execute();
        $estadisticas = $stmt->fetch();
        
        // Calcular porcentajes
        $total = $estadisticas['total'] ?: 1;
        $porcentajeCincoEstrellas = round(($estadisticas['cinco_estrellas'] / $total) * 100, 1);
        $porcentajeConRespuesta = round(($estadisticas['con_respuesta'] / $total) * 100, 1);
        
        sendResponse(true, 'Métricas de calificaciones obtenidas', [
            'promedio' => round($estadisticas['promedio'], 1),
            'total_mes' => $estadisticas['total'],
            'porcentaje_cinco_estrellas' => $porcentajeCincoEstrellas,
            'porcentaje_con_respuesta' => $porcentajeConRespuesta,
            'distribucion' => [
                'cinco' => $estadisticas['cinco_estrellas'],
                'cuatro' => $estadisticas['cuatro_estrellas'],
                'tres' => $estadisticas['tres_estrellas'],
                'dos' => $estadisticas['dos_estrellas'],
                'una' => $estadisticas['una_estrella']
            ]
        ]);
        
    } catch (Exception $e) {
        sendResponse(false, 'Error obteniendo métricas de calificaciones: ' . $e->getMessage());
    }
}

/**
 * PASO 10: Obtener métricas financieras
 */
function obtenerMetricasFinancieras($pdo) {
    try {
        // Ingresos del mes (simulado basado en pedidos)
        $stmt = $pdo->prepare("SELECT SUM(total) as ingresos FROM pedidos WHERE MONTH(fecha_pedido) = MONTH(NOW()) AND estado = 'entregado'");
        $stmt->execute();
        $ingresosMes = $stmt->fetchColumn() ?: 0;
        
        // Ingresos de hoy
        $stmt = $pdo->prepare("SELECT SUM(total) as ingresos FROM pedidos WHERE DATE(fecha_pedido) = CURDATE() AND estado = 'entregado'");
        $stmt->execute();
        $ingresosHoy = $stmt->fetchColumn() ?: 0;
        
        // Comisión promedio (15%)
        $comision = 15;
        
        // Crecimiento simulado
        $crecimiento = rand(15, 25);
        
        // Ingresos por día (últimos 30 días)
        $stmt = $pdo->prepare("
            SELECT 
                DATE(fecha_pedido) as fecha,
                SUM(total) as ingresos
            FROM pedidos 
            WHERE fecha_pedido >= DATE_SUB(NOW(), INTERVAL 30 DAY) 
                AND estado = 'entregado'
            GROUP BY DATE(fecha_pedido)
            ORDER BY fecha ASC
        ");
        $stmt->execute();
        $ingresosPorDia = $stmt->fetchAll();
        
        sendResponse(true, 'Métricas financieras obtenidas', [
            'ingresos_mes' => $ingresosMes,
            'ingresos_hoy' => $ingresosHoy,
            'comision' => $comision,
            'crecimiento' => $crecimiento,
            'ingresos_por_dia' => $ingresosPorDia
        ]);
        
    } catch (Exception $e) {
        sendResponse(false, 'Error obteniendo métricas financieras: ' . $e->getMessage());
    }
}

/**
 * PASO 10: Obtener actividad reciente
 */
function obtenerActividadReciente($pdo) {
    try {
        $actividades = [];
        
        // Últimos pedidos
        $stmt = $pdo->prepare("
            SELECT 'pedido' as tipo, id, fecha_pedido as fecha, total, estado
            FROM pedidos 
            ORDER BY fecha_pedido DESC 
            LIMIT 5
        ");
        $stmt->execute();
        $pedidos = $stmt->fetchAll();
        
        foreach ($pedidos as $pedido) {
            $actividades[] = [
                'tipo' => 'order',
                'titulo' => 'Nuevo pedido creado',
                'descripcion' => "Pedido #{$pedido['id']} - Estado: {$pedido['estado']} - $" . number_format($pedido['total']),
                'tiempo' => calcularTiempoTranscurrido($pedido['fecha']),
                'fecha' => $pedido['fecha']
            ];
        }
        
        // Últimas calificaciones
        $stmt = $pdo->prepare("
            SELECT c.calificacion, c.fecha_calificacion, u.nombre as delivery_nombre
            FROM calificaciones c
            JOIN users u ON c.delivery_id = u.id
            ORDER BY c.fecha_calificacion DESC 
            LIMIT 3
        ");
        $stmt->execute();
        $calificaciones = $stmt->fetchAll();
        
        foreach ($calificaciones as $cal) {
            $actividades[] = [
                'tipo' => 'rating',
                'titulo' => 'Nueva calificación',
                'descripcion' => "{$cal['delivery_nombre']} recibió {$cal['calificacion']} estrellas",
                'tiempo' => calcularTiempoTranscurrido($cal['fecha_calificacion']),
                'fecha' => $cal['fecha_calificacion']
            ];
        }
        
        // Ordenar por fecha
        usort($actividades, function($a, $b) {
            return strtotime($b['fecha']) - strtotime($a['fecha']);
        });
        
        sendResponse(true, 'Actividad reciente obtenida', array_slice($actividades, 0, 10));
        
    } catch (Exception $e) {
        sendResponse(false, 'Error obteniendo actividad reciente: ' . $e->getMessage());
    }
}

/**
 * PASO 10: Calcular tiempo transcurrido
 */
function calcularTiempoTranscurrido($fecha) {
    $ahora = new DateTime();
    $fechaObj = new DateTime($fecha);
    $diff = $ahora->diff($fechaObj);
    
    if ($diff->d > 0) {
        return "hace {$diff->d} día" . ($diff->d > 1 ? 's' : '');
    } elseif ($diff->h > 0) {
        return "hace {$diff->h} hora" . ($diff->h > 1 ? 's' : '');
    } elseif ($diff->i > 0) {
        return "hace {$diff->i} minuto" . ($diff->i > 1 ? 's' : '');
    } else {
        return "hace unos segundos";
    }
}

/**
 * PASO 10: Obtener alertas del sistema
 */
function obtenerAlertasSistema($pdo) {
    try {
        $alertas = [];
        
        // Deliveries sin respuesta
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as sin_respuesta
            FROM notificaciones_log 
            WHERE respondida = 0 
                AND fecha_envio <= DATE_SUB(NOW(), INTERVAL 5 MINUTE)
        ");
        $stmt->execute();
        $sinRespuesta = $stmt->fetchColumn();
        
        if ($sinRespuesta > 0) {
            $alertas[] = [
                'tipo' => 'warning',
                'titulo' => 'Deliveries sin respuesta',
                'descripcion' => "$sinRespuesta deliveries no han respondido en más de 5 minutos",
                'prioridad' => 'alta'
            ];
        }
        
        // Pedidos con demora
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as con_demora
            FROM pedidos 
            WHERE estado IN ('asignado', 'en_camino') 
                AND fecha_asignacion <= DATE_SUB(NOW(), INTERVAL 30 MINUTE)
        ");
        $stmt->execute();
        $conDemora = $stmt->fetchColumn();
        
        if ($conDemora > 0) {
            $alertas[] = [
                'tipo' => 'warning',
                'titulo' => 'Pedidos con demora',
                'descripcion' => "$conDemora pedidos llevan más de 30 minutos en proceso",
                'prioridad' => 'media'
            ];
        }
        
        // Calificaciones bajas recientes
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as bajas
            FROM calificaciones 
            WHERE calificacion <= 2 
                AND fecha_calificacion >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ");
        $stmt->execute();
        $calificacionesBajas = $stmt->fetchColumn();
        
        if ($calificacionesBajas > 0) {
            $alertas[] = [
                'tipo' => 'danger',
                'titulo' => 'Calificaciones bajas',
                'descripcion' => "$calificacionesBajas calificaciones de 1-2 estrellas en las últimas 24h",
                'prioridad' => 'alta'
            ];
        }
        
        sendResponse(true, 'Alertas del sistema obtenidas', $alertas);
        
    } catch (Exception $e) {
        sendResponse(false, 'Error obteniendo alertas: ' . $e->getMessage());
    }
}

/**
 * PASO 10: Obtener resumen completo
 */
function obtenerResumenCompleto($pdo) {
    try {
        $resumen = [
            'timestamp' => date('Y-m-d H:i:s'),
            'sistema_activo' => true,
            'version' => '1.0.0',
            'uptime' => '99.8%'
        ];
        
        sendResponse(true, 'Resumen completo obtenido', $resumen);
        
    } catch (Exception $e) {
        sendResponse(false, 'Error obteniendo resumen: ' . $e->getMessage());
    }
}
?>
