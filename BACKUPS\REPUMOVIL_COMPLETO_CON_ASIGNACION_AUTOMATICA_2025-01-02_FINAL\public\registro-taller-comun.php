<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registro RepuMovil - Taller</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .container-form {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .card-custom {
            background: white;
            border-radius: 25px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            border: none;
            overflow: hidden;
        }
        
        .card-header-custom {
            background: linear-gradient(135deg, #FF6B35 0%, #FFA500 100%);
            color: white;
            padding: 30px;
            text-align: center;
            border: none;
        }
        
        .logo-title {
            font-size: 32px;
            font-weight: 900;
            margin-bottom: 10px;
        }
        
        .logo-repu {
            color: white;
        }
        
        .logo-movil {
            color: #FFE4B5;
        }
        
        .plan-subtitle {
            font-size: 18px;
            opacity: 0.9;
            margin-bottom: 0;
        }
        
        .form-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        
        .form-control, .form-select {
            border-radius: 12px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #FF6B35;
            box-shadow: 0 0 0 0.2rem rgba(255, 107, 53, 0.25);
        }
        
        .btn-register {
            background: linear-gradient(135deg, #FF6B35 0%, #FFA500 100%);
            border: none;
            border-radius: 12px;
            padding: 15px 30px;
            color: white;
            font-weight: 700;
            font-size: 16px;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(255, 107, 53, 0.4);
            color: white;
        }
        
        .back-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid white;
            color: white;
            padding: 10px 20px;
            border-radius: 10px;
            text-decoration: none;
            transition: all 0.3s ease;
            display: inline-block;
            margin-bottom: 20px;
        }
        
        .back-btn:hover {
            background: white;
            color: #667eea;
        }
        
        .maps-btn {
            background: #4285f4;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 8px 15px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .maps-btn:hover {
            background: #3367d6;
            color: white;
        }
        
        .feature-highlight {
            background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
            border-left: 4px solid #4CAF50;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .feature-list li {
            padding: 5px 0;
            color: #4CAF50;
            font-weight: 600;
        }
        
        .feature-list li i {
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="container-form">
        <a href="seleccionar-plan-taller.php" class="back-btn">
            <i class="fas fa-arrow-left me-2"></i>Volver a Planes
        </a>

        <div class="card card-custom">
            <div class="card-header-custom">
                <h1 class="logo-title">
                    <i class="fas fa-wrench me-2"></i>
                    <span class="logo-repu">Repu</span><span class="logo-movil">Movil</span>
                </h1>
                <p class="plan-subtitle">Repuestos que llegan a tu taller, cuando los necesitas</p>
            </div>
            
            <div class="card-body p-4">
                <!-- Características del Plan -->
                <div class="feature-highlight">
                    <h6 class="fw-bold mb-2">✨ Tu plan incluye:</h6>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i>Pedido de repuestos con changuito</li>
                        <li><i class="fas fa-check"></i>Calificaciones de clientes</li>
                        <li><i class="fas fa-check"></i>Búsqueda avanzada de repuestos</li>
                        <li><i class="fas fa-check"></i>Notificaciones en tiempo real</li>
                    </ul>
                </div>

                <form id="registroForm" method="POST" action="api/registro-taller-comun.php">
                    <input type="hidden" name="user_type" value="taller_mecanico">
                    <input type="hidden" name="plan_type" value="comun">
                    
                    <!-- Datos Básicos -->
                    <div class="mb-3">
                        <label for="nombre" class="form-label">Nombre Completo</label>
                        <input type="text" class="form-control" id="nombre" name="nombre" required>
                    </div>

                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>

                    <div class="mb-3">
                        <label for="telefono" class="form-label">Teléfono</label>
                        <input type="tel" class="form-control" id="telefono" name="telefono" required>
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label">Contraseña</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>

                    <!-- Datos del Taller -->
                    <hr class="my-4">
                    <h5 class="mb-3">🔧 Datos del Taller</h5>

                    <div class="mb-3">
                        <label for="username" class="form-label">Nombre del Taller</label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>

                    <div class="mb-3">
                        <label for="direccion" class="form-label">Dirección del Taller</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="direccion" name="direccion" required>
                            <button type="button" class="btn maps-btn" onclick="abrirGoogleMaps()">
                                <i class="fas fa-map-marker-alt me-1"></i>Maps
                            </button>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="rubro_principal" class="form-label">Rubro Principal</label>
                        <select class="form-select" id="rubro_principal" name="rubro_principal" required>
                            <option value="">Selecciona tu especialidad</option>
                            <option value="mecanica_general">Mecánica General</option>
                            <option value="electricidad_automotriz">Electricidad Automotriz</option>
                            <option value="chapa_pintura">Chapa y Pintura</option>
                            <option value="neumaticos">Neumáticos</option>
                            <option value="frenos">Frenos</option>
                            <option value="suspension">Suspensión</option>
                            <option value="aire_acondicionado">Aire Acondicionado</option>
                            <option value="gnc">GNC</option>
                            <option value="diesel">Diesel</option>
                            <option value="transmision">Transmisión</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label for="datos_fiscales" class="form-label">Datos Fiscales (CUIT/DNI)</label>
                        <input type="text" class="form-control" id="datos_fiscales" name="datos_fiscales" required>
                    </div>

                    <button type="submit" class="btn btn-register">
                        <i class="fas fa-rocket me-2"></i>Crear mi Cuenta RepuMovil
                    </button>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function abrirGoogleMaps() {
            const direccion = document.getElementById('direccion').value;
            if (direccion) {
                const url = `https://www.google.com/maps/search/${encodeURIComponent(direccion)}`;
                window.open(url, '_blank');
            } else {
                alert('Por favor, ingresa una dirección primero');
            }
        }

        document.getElementById('registroForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Validaciones
            const password = document.getElementById('password').value;
            if (password.length < 6) {
                alert('La contraseña debe tener al menos 6 caracteres');
                return;
            }

            // Enviar formulario
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creando cuenta...';
            submitBtn.disabled = true;
            
            fetch(this.action, {
                method: 'POST',
                body: new FormData(this)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('¡Cuenta RepuMovil creada exitosamente! Redirigiendo al login...');
                    setTimeout(() => {
                        window.location.href = 'login-dinamico.php';
                    }, 1500);
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                alert('Error de conexión. Intenta nuevamente.');
                console.error('Error:', error);
            })
            .finally(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });

        // Animación de entrada
        document.addEventListener('DOMContentLoaded', function() {
            const card = document.querySelector('.card-custom');
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            
            setTimeout(() => {
                card.style.transition = 'all 0.8s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 100);
        });
    </script>
</body>
</html>
