<?php
// Mostrar todos los errores para depuración
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Incluir archivo de configuración de la base de datos
require_once 'db_config.php';

// Iniciar sesión
session_start();

// Verificar si el usuario está autenticado y es administrador
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: login_reemplazo.php');
    exit;
}

// Obtener información del usuario actual
$userId = $_SESSION['user_id'];
$username = $_SESSION['user_name'] ?? $_SESSION['username'] ?? 'Administrador';

// Conectar a la base de datos
try {
    $conn = connectDB();

    // Obtener estadísticas básicas
    $userCount = $conn->query("SELECT COUNT(*) FROM users")->fetchColumn();
    $workshopCount = $conn->query("SELECT COUNT(*) FROM workshops")->fetchColumn();
    $supplierCount = $conn->query("SELECT COUNT(*) FROM suppliers")->fetchColumn();

    // Obtener lista de usuarios
    $stmt = $conn->query("SELECT u.id, u.username, u.email, u.status, u.created_at, u.last_login, r.name as role_name
                         FROM users u
                         JOIN roles r ON u.role_id = r.id
                         ORDER BY u.created_at DESC");
    $users = $stmt->fetchAll();

} catch (PDOException $e) {
    $error = "Error de base de datos: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Panel de Administración - Repumóvil</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding-top: 50px;
        }
        .admin-container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #fff;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            backdrop-filter: blur(10px);
        }
        .row {
            margin: 0;
        }
        .admin-sidebar {
            background: linear-gradient(135deg, #FF6B35 0%, #FFA500 100%);
            color: white;
            padding: 40px 20px;
            height: 100%;
            min-height: 600px;
        }
        .admin-content {
            padding: 40px;
        }
        .logo-container {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo-circle {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
            box-shadow: 0 8px 25px rgba(255, 255, 255, 0.3);
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        .logo-icon {
            font-size: 35px;
            color: white;
        }
        .logo-title {
            font-size: 28px;
            font-weight: bold;
            margin: 0;
            color: white;
        }
        .logo-subtitle {
            font-size: 14px;
            opacity: 0.9;
            margin-top: 5px;
            color: white;
        }
        .admin-sidebar ul {
            padding-left: 0;
            margin-top: 20px;
        }
        .admin-sidebar li {
            margin-bottom: 8px;
            list-style: none;
        }
        .admin-sidebar .nav-link {
            color: rgba(255, 255, 255, 0.85);
            padding: 12px 15px;
            border-radius: 12px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: block;
        }
        .admin-sidebar .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.2);
            transform: translateX(5px);
        }
        .admin-sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.3);
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(255, 255, 255, 0.2);
        }
        .page-title {
            font-size: 2rem;
            margin-bottom: 30px;
            color: #FF6B35;
            font-weight: 600;
        }
        .card-counter {
            background: linear-gradient(135deg, #FF6B35 0%, #FFA500 100%);
            box-shadow: 0 8px 25px rgba(255, 107, 53, 0.3);
            margin-bottom: 20px;
            padding: 25px;
            border-radius: 15px;
            color: white;
            position: relative;
            transition: all 0.3s ease;
        }
        .card-counter:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(255, 107, 53, 0.4);
        }
        .card-counter i {
            font-size: 4em;
            opacity: 0.3;
        }
        .card-counter .count-numbers {
            position: absolute;
            right: 35px;
            top: 20px;
            font-size: 36px;
            display: block;
            font-weight: bold;
        }
        .card-counter .count-name {
            position: absolute;
            right: 35px;
            top: 65px;
            font-style: italic;
            text-transform: capitalize;
            opacity: 0.9;
            display: block;
            font-size: 14px;
        }
        .bg-info {
            background: linear-gradient(135deg, #FF6B35 0%, #FFA500 100%) !important;
        }
        .bg-success {
            background: linear-gradient(135deg, #FFA500 0%, #FFD700 100%) !important;
        }
        .bg-warning {
            background: linear-gradient(135deg, #FF8C00 0%, #FF6B35 100%) !important;
        }
        .btn-primary {
            background: linear-gradient(135deg, #FF6B35 0%, #FFA500 100%);
            border: none;
            border-radius: 12px;
            padding: 12px 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 107, 53, 0.4);
        }
        .alert {
            border-radius: 12px;
            border: none;
        }
        .admin-footer {
            margin-top: 30px;
            text-align: center;
            color: #6c757d;
        }
        .admin-footer a {
            color: #FF6B35;
            text-decoration: none;
        }
        .admin-footer a:hover {
            text-decoration: underline;
        }
        .table-responsive {
            margin-top: 20px;
        }
        .card {
            border-radius: 15px;
            border: none;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        .card-header {
            background: linear-gradient(135deg, #FF6B35 0%, #FFA500 100%) !important;
            color: white !important;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }
        .badge-info {
            background: linear-gradient(135deg, #FF6B35 0%, #FFA500 100%) !important;
        }
        .table thead {
            background-color: #f8f9fa;
        }
        .table-striped tbody tr:nth-of-type(odd) {
            background-color: rgba(255, 107, 53, 0.05);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="admin-container">
            <div class="row">
                <div class="col-md-4 d-none d-md-block">
                    <div class="admin-sidebar">
                        <div class="logo-container">
                            <div class="logo-circle">
                                <i class="fas fa-wrench logo-icon"></i>
                            </div>
                            <h1 class="logo-title">RepuMovil</h1>
                            <p class="logo-subtitle">Panel de Administración</p>
                        </div>
                        <p style="opacity: 0.9; margin-bottom: 20px;">Gestiona usuarios, talleres y proveedores de la plataforma RepuMovil.</p>
                        <ul class="nav flex-column">
                            <li class="nav-item">
                                <a class="nav-link active" href="admin.php">
                                    <i class="fas fa-tachometer-alt mr-2"></i> Dashboard
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="admin_users.php">
                                    <i class="fas fa-users mr-2"></i> Usuarios
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="admin_add_user.php">
                                    <i class="fas fa-user-plus mr-2"></i> Agregar Usuario
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="admin_workshops.php">
                                    <i class="fas fa-tools mr-2"></i> Talleres
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="admin_suppliers.php">
                                    <i class="fas fa-truck mr-2"></i> Proveedores
                                </a>
                            </li>
                            <li class="nav-item mt-3">
                                <a class="nav-link" href="logout.php">
                                    <i class="fas fa-sign-out-alt mr-2"></i> Cerrar Sesión
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>

                <div class="col-md-8">
                    <div class="admin-content">
                        <div class="logo-container d-block d-md-none" style="margin-bottom: 30px;">
                            <div class="logo-circle" style="background: linear-gradient(135deg, #FF6B35 0%, #FFA500 100%);">
                                <i class="fas fa-wrench logo-icon"></i>
                            </div>
                            <h1 class="logo-title" style="color: #FF6B35;">RepuMovil</h1>
                            <p class="text-muted">Panel de Administración</p>
                        </div>

                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h2 class="page-title">Dashboard Administrativo</h2>
                            <div>
                                <span class="mr-2">Bienvenido, <strong><?php echo htmlspecialchars($username); ?></strong></span>
                                <a href="logout.php" class="btn btn-sm btn-primary">
                                    <i class="fas fa-sign-out-alt"></i> Salir
                                </a>
                            </div>
                        </div>

                        <?php if (isset($error)): ?>
                            <div class="alert alert-danger" role="alert">
                                <i class="fas fa-exclamation-triangle mr-2"></i>
                                <?php echo $error; ?>
                            </div>
                        <?php endif; ?>

                        <!-- Statistics Cards -->
                        <div class="row">
                            <div class="col-md-4">
                                <div class="card-counter bg-info">
                                    <i class="fas fa-users"></i>
                                    <span class="count-numbers"><?php echo $userCount; ?></span>
                                    <span class="count-name">Usuarios</span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card-counter bg-success">
                                    <i class="fas fa-tools"></i>
                                    <span class="count-numbers"><?php echo $workshopCount; ?></span>
                                    <span class="count-name">Talleres</span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card-counter bg-warning">
                                    <i class="fas fa-truck"></i>
                                    <span class="count-numbers"><?php echo $supplierCount; ?></span>
                                    <span class="count-name">Proveedores</span>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Users Table -->
                        <div class="card mt-4">
                            <div class="card-header" style="background-color: #343a40; color: white;">
                                <h5 class="mb-0"><i class="fas fa-users mr-2"></i>Usuarios Recientes</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead style="background-color: #f8f9fa;">
                                            <tr>
                                                <th>ID</th>
                                                <th>Usuario</th>
                                                <th>Email</th>
                                                <th>Rol</th>
                                                <th>Estado</th>
                                                <th>Fecha Registro</th>
                                                <th>Último Acceso</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($users as $user): ?>
                                            <tr>
                                                <td><?php echo $user['id']; ?></td>
                                                <td><?php echo htmlspecialchars($user['username']); ?></td>
                                                <td><?php echo htmlspecialchars($user['email']); ?></td>
                                                <td>
                                                    <span class="badge badge-info"><?php echo htmlspecialchars($user['role_name']); ?></span>
                                                </td>
                                                <td>
                                                    <?php if ($user['status'] == 'active'): ?>
                                                        <span class="badge badge-success">Activo</span>
                                                    <?php elseif ($user['status'] == 'inactive'): ?>
                                                        <span class="badge badge-secondary">Inactivo</span>
                                                    <?php else: ?>
                                                        <span class="badge badge-danger">Suspendido</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo date('d/m/Y H:i', strtotime($user['created_at'])); ?></td>
                                                <td><?php echo $user['last_login'] ? date('d/m/Y H:i', strtotime($user['last_login'])) : 'Nunca'; ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <div class="admin-footer">
                            <p>Panel de Administración - Repumóvil &copy; 2024</p>
                            <p>Gestiona tu plataforma de talleres y proveedores de manera eficiente.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
</body>
</html>



