<?php
/**
 * API para obtener categorías de repuestos
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Manejar preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'db_config.php';

try {
    $pdo = connectDB();

    // Obtener categorías únicas (evitar duplicados)
    $sql = "SELECT
                MAX(id) as id,
                nombre,
                MAX(descripcion) as descripcion,
                MAX(icono) as icono,
                MIN(orden) as orden,
                (SELECT COUNT(*) FROM repuestos WHERE
                    categoria = c.nombre
                    AND estado = 'disponible'
                    AND stock > 0
                ) as total_productos
            FROM categorias c
            WHERE activa = TRUE
            GROUP BY nombre
            ORDER BY MIN(orden) ASC, nombre ASC";

    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $categorias = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Formatear resultados
    $resultados = [];
    foreach ($categorias as $categoria) {
        $resultados[] = [
            'id' => (int)$categoria['id'],
            'nombre' => $categoria['nombre'],
            'descripcion' => $categoria['descripcion'],
            'icono' => $categoria['icono'],
            'orden' => (int)$categoria['orden'],
            'total_productos' => (int)$categoria['total_productos']
        ];
    }

    // Respuesta exitosa
    echo json_encode([
        'success' => true,
        'message' => 'Categorías obtenidas exitosamente',
        'data' => $resultados,
        'total' => count($resultados)
    ]);

} catch (PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error en la base de datos: ' . $e->getMessage(),
        'data' => []
    ]);
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error del servidor: ' . $e->getMessage(),
        'data' => []
    ]);
}
?>
