<?php
// API para geocodificación con Google Maps
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header("Content-Type: application/json; charset=UTF-8");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once '../db_config.php';

// Configuración de Google Maps
$GOOGLE_MAPS_API_KEY = 'AIzaSyBOti4mM-6x9WDnZIjIeyEU21OpBXqWBgw';

try {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $data = json_decode(file_get_contents("php://input"), true);
        $direccion = $data['direccion'] ?? '';
        
        if (empty($direccion)) {
            throw new Exception('Dirección requerida');
        }
        
        // Buscar en caché primero
        $stmt = $pdo->prepare("
            SELECT * FROM geocoding_cache 
            WHERE direccion_original = ? 
            ORDER BY created_at DESC 
            LIMIT 1
        ");
        $stmt->execute([$direccion]);
        $cached = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($cached && (time() - strtotime($cached['created_at'])) < 86400) { // 24 horas
            echo json_encode([
                'success' => true,
                'cached' => true,
                'direccion_formateada' => $cached['direccion_formateada'],
                'coordenadas' => [
                    'lat' => (float)$cached['coordenadas_lat'],
                    'lng' => (float)$cached['coordenadas_lng']
                ],
                'place_id' => $cached['place_id'],
                'ubicacion' => [
                    'pais' => $cached['pais'],
                    'ciudad' => $cached['ciudad'],
                    'provincia' => $cached['provincia'],
                    'codigo_postal' => $cached['codigo_postal']
                ]
            ]);
            exit;
        }
        
        // Llamar a Google Maps API
        $url = "https://maps.googleapis.com/maps/api/geocode/json?" . http_build_query([
            'address' => $direccion,
            'key' => $GOOGLE_MAPS_API_KEY,
            'language' => 'es',
            'region' => 'ar',
            'components' => 'country:AR'
        ]);
        
        $response = file_get_contents($url);
        $data = json_decode($response, true);
        
        if ($data['status'] !== 'OK' || empty($data['results'])) {
            throw new Exception('No se encontró la dirección');
        }
        
        $result = $data['results'][0];
        $geometry = $result['geometry']['location'];
        $components = $result['address_components'];
        
        // Extraer componentes de la dirección
        $pais = '';
        $ciudad = '';
        $provincia = '';
        $codigo_postal = '';
        
        foreach ($components as $component) {
            $types = $component['types'];
            if (in_array('country', $types)) {
                $pais = $component['long_name'];
            } elseif (in_array('locality', $types) || in_array('administrative_area_level_2', $types)) {
                $ciudad = $component['long_name'];
            } elseif (in_array('administrative_area_level_1', $types)) {
                $provincia = $component['long_name'];
            } elseif (in_array('postal_code', $types)) {
                $codigo_postal = $component['long_name'];
            }
        }
        
        // Guardar en caché
        $stmt = $pdo->prepare("
            INSERT INTO geocoding_cache 
            (direccion_original, direccion_formateada, coordenadas_lat, coordenadas_lng, 
             place_id, pais, ciudad, provincia, codigo_postal) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            $direccion,
            $result['formatted_address'],
            $geometry['lat'],
            $geometry['lng'],
            $result['place_id'],
            $pais,
            $ciudad,
            $provincia,
            $codigo_postal
        ]);
        
        echo json_encode([
            'success' => true,
            'cached' => false,
            'direccion_formateada' => $result['formatted_address'],
            'coordenadas' => [
                'lat' => $geometry['lat'],
                'lng' => $geometry['lng']
            ],
            'place_id' => $result['place_id'],
            'ubicacion' => [
                'pais' => $pais,
                'ciudad' => $ciudad,
                'provincia' => $provincia,
                'codigo_postal' => $codigo_postal
            ]
        ]);
        
    } elseif ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // Obtener sugerencias de direcciones
        $input = $_GET['input'] ?? '';
        
        if (empty($input)) {
            throw new Exception('Input requerido');
        }
        
        $url = "https://maps.googleapis.com/maps/api/place/autocomplete/json?" . http_build_query([
            'input' => $input,
            'key' => $GOOGLE_MAPS_API_KEY,
            'language' => 'es',
            'components' => 'country:ar',
            'types' => 'address'
        ]);
        
        $response = file_get_contents($url);
        $data = json_decode($response, true);
        
        if ($data['status'] !== 'OK') {
            throw new Exception('Error al obtener sugerencias');
        }
        
        $sugerencias = array_map(function($prediction) {
            return [
                'descripcion' => $prediction['description'],
                'place_id' => $prediction['place_id']
            ];
        }, $data['predictions']);
        
        echo json_encode([
            'success' => true,
            'sugerencias' => $sugerencias
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

// Función para obtener detalles de un lugar por place_id
function obtenerDetallesLugar($place_id, $api_key) {
    $url = "https://maps.googleapis.com/maps/api/place/details/json?" . http_build_query([
        'place_id' => $place_id,
        'key' => $api_key,
        'language' => 'es',
        'fields' => 'formatted_address,geometry,address_components'
    ]);
    
    $response = file_get_contents($url);
    return json_decode($response, true);
}
?>
