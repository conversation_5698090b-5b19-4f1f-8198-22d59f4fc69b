<?php
// Mostrar todos los errores para depuración
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Iniciar sesión
session_start();

// Verificar si el usuario está autenticado y es administrador
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: login.php');
    exit;
}

// Incluir archivos necesarios
require_once 'db_config.php';

// Obtener lista de usuarios
try {
    $conn = connectDB();
    $query = "SELECT u.*, r.name as role_name 
              FROM users u 
              JOIN roles r ON u.role_id = r.id 
              ORDER BY u.id";
    $users = $conn->query($query)->fetchAll();
} catch (PDOException $e) {
    $error = 'Error al cargar usuarios: ' . $e->getMessage();
    $users = [];
}

// Procesar acciones
$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        // Cambiar estado de usuario
        if ($_POST['action'] === 'change_status') {
            $userId = (int)$_POST['user_id'];
            $status = filter_input(INPUT_POST, 'status', FILTER_SANITIZE_STRING);
            
            try {
                $stmt = $conn->prepare("UPDATE users SET status = :status WHERE id = :id");
                $stmt->execute(['status' => $status, 'id' => $userId]);
                
                $message = 'Estado del usuario actualizado exitosamente.';
                
                // Recargar la lista de usuarios
                $users = $conn->query($query)->fetchAll();
            } catch (PDOException $e) {
                $error = 'Error al actualizar el estado: ' . $e->getMessage();
            }
        }
        
        // Eliminar usuario
        if ($_POST['action'] === 'delete_user') {
            $userId = (int)$_POST['user_id'];
            
            try {
                $conn->beginTransaction();
                
                // Eliminar registros relacionados primero
                $stmt = $conn->prepare("DELETE FROM workshops WHERE user_id = :user_id");
                $stmt->execute(['user_id' => $userId]);
                
                $stmt = $conn->prepare("DELETE FROM suppliers WHERE user_id = :user_id");
                $stmt->execute(['user_id' => $userId]);
                
                // Finalmente eliminar el usuario
                $stmt = $conn->prepare("DELETE FROM users WHERE id = :id");
                $stmt->execute(['id' => $userId]);
                
                $conn->commit();
                $message = 'Usuario eliminado exitosamente.';
                
                // Recargar la lista de usuarios
                $users = $conn->query($query)->fetchAll();
            } catch (PDOException $e) {
                $conn->rollBack();
                $error = 'Error al eliminar el usuario: ' . $e->getMessage();
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestión de Usuarios - Repumóvil</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #343a40;
            color: white;
            padding-top: 20px;
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.75);
        }
        .sidebar .nav-link:hover {
            color: rgba(255, 255, 255, 1);
        }
        .sidebar .nav-link.active {
            color: white;
            font-weight: bold;
        }
        .main-content {
            padding: 20px;
        }
        .logo-container {
            text-align: center;
            margin-bottom: 20px;
        }
        .logo-container img {
            max-width: 80%;
            height: auto;
        }
        .admin-panel-text {
            text-align: center;
            font-size: 14px;
            margin-top: 5px;
            color: rgba(255, 255, 255, 0.7);
        }
        .status-active {
            background-color: #28a745;
            color: white;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 0.8rem;
        }
        .status-inactive {
            background-color: #6c757d;
            color: white;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 0.8rem;
        }
        .status-suspended {
            background-color: #dc3545;
            color: white;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2 sidebar">
                <div class="logo-container">
                    <img src="logo.png" alt="Repumóvil Logo">
                    <p class="admin-panel-text">Panel de Administración</p>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="admin.php">
                            <i class="fas fa-tachometer-alt mr-2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="admin_users.php">
                            <i class="fas fa-users mr-2"></i> Usuarios
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="admin_workshops.php">
                            <i class="fas fa-tools mr-2"></i> Talleres
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="admin_suppliers.php">
                            <i class="fas fa-truck mr-2"></i> Proveedores
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="admin_settings.php">
                            <i class="fas fa-cog mr-2"></i> Configuración
                        </a>
                    </li>
                    <li class="nav-item mt-5">
                        <a class="nav-link" href="logout.php">
                            <i class="fas fa-sign-out-alt mr-2"></i> Cerrar Sesión
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-10 main-content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>Gestión de Usuarios</h2>
                    <div>
                        <a href="admin_add_user.php" class="btn btn-primary">
                            <i class="fas fa-user-plus"></i> Agregar Usuario
                        </a>
                    </div>
                </div>
                
                <?php if (!empty($message)): ?>
                    <div class="alert alert-success" role="alert">
                        <?php echo htmlspecialchars($message); ?>
                    </div>
                <?php endif; ?>
                
                <?php if (!empty($error)): ?>
                    <div class="alert alert-danger" role="alert">
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>
                
                <div class="card">
                    <div class="card-header">
                        <h5>Lista de Usuarios</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Usuario</th>
                                        <th>Email</th>
                                        <th>Rol</th>
                                        <th>Estado</th>
                                        <th>Fecha Registro</th>
                                        <th>Último Acceso</th>
                                        <th>Acciones</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (!empty($users)): ?>
                                        <?php foreach ($users as $user): ?>
                                            <tr>
                                                <td><?php echo $user['id']; ?></td>
                                                <td><?php echo htmlspecialchars($user['username']); ?></td>
                                                <td><?php echo htmlspecialchars($user['email']); ?></td>
                                                <td><?php echo htmlspecialchars($user['role_name']); ?></td>
                                                <td>
                                                    <?php if ($user['status'] == 'active'): ?>
                                                        <span class="status-active">Activo</span>
                                                    <?php elseif ($user['status'] == 'inactive'): ?>
                                                        <span class="status-inactive">Inactivo</span>
                                                    <?php else: ?>
                                                        <span class="status-suspended">Suspendido</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo $user['created_at'] ?? 'N/A'; ?></td>
                                                <td><?php echo $user['last_login'] ?? 'Nunca'; ?></td>
                                                <td>
                                                    <a href="admin_edit_user.php?id=<?php echo $user['id']; ?>" class="btn btn-sm btn-info" title="Editar">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    
                                                    <form method="post" style="display: inline;">
                                                        <input type="hidden" name="action" value="change_status">
                                                        <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                        <select name="status" onchange="this.form.submit()" class="form-control-sm">
                                                            <option value="">Estado</option>
                                                            <option value="active">Activar</option>
                                                            <option value="inactive">Desactivar</option>
                                                            <option value="suspended">Suspender</option>
                                                        </select>
                                                    </form>
                                                    
                                                    <?php if ($user['id'] != $_SESSION['user_id']): ?>
                                                        <form method="post" style="display: inline;" onsubmit="return confirm('¿Está seguro de eliminar este usuario? Esta acción no se puede deshacer.');">
                                                            <input type="hidden" name="action" value="delete_user">
                                                            <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                            <button type="submit" class="btn btn-sm btn-danger" title="Eliminar">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </form>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="8" class="text-center">No hay usuarios registrados</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
</body>
</html>

<!-- Selector de roles actualizado -->
<div class="form-group">
    <label for="role_id">Rol de usuario</label>
    <select class="form-control" id="role_id" name="role_id" required>
        <option value="">Seleccione un rol</option>
        <option value="1">admin - Administrador del sistema</option>
        <option value="2">workshop - Taller mecánico</option>
        <option value="3">mechanic - Mecánico independiente</option>
        <option value="4">person - Usuario regular</option>
        <option value="5">supplier - Proveedor de repuestos</option>
    </select>
</div>


