// PASO 1: Servicio de Notificaciones Push para RepuMovil
// Manejo completo de notificaciones para deliveries

import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import Constants from 'expo-constants';
import { Platform } from 'react-native';

// Configuración de notificaciones
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

export interface PedidoNotification {
  id: string;
  cliente: string;
  direccion: string;
  total: number;
  distancia: number;
  tiempo_estimado: number;
  tipo: 'nuevo_pedido' | 'pedido_cancelado' | 'recordatorio';
  prioridad: 'alta' | 'media' | 'baja';
}

export interface NotificationResponse {
  success: boolean;
  token?: string;
  error?: string;
}

class NotificationService {
  private expoPushToken: string | null = null;
  private notificationListener: any = null;
  private responseListener: any = null;

  /**
   * PASO 1: Inicializar servicio de notificaciones
   */
  async initialize(): Promise<NotificationResponse> {
    try {
      console.log('🔔 Inicializando servicio de notificaciones...');

      // Verificar si es dispositivo físico
      if (!Device.isDevice) {
        console.warn('⚠️ Las notificaciones push solo funcionan en dispositivos físicos');
        return {
          success: false,
          error: 'Las notificaciones push solo funcionan en dispositivos físicos'
        };
      }

      // Solicitar permisos
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        console.error('❌ Permisos de notificación denegados');
        return {
          success: false,
          error: 'Permisos de notificación denegados'
        };
      }

      // Obtener token de Expo Push
      const token = await this.getExpoPushToken();
      if (!token) {
        return {
          success: false,
          error: 'No se pudo obtener el token de notificaciones'
        };
      }

      // Configurar canal de notificaciones para Android
      if (Platform.OS === 'android') {
        await this.setupAndroidChannel();
      }

      // Configurar listeners
      this.setupNotificationListeners();

      console.log('✅ Servicio de notificaciones inicializado correctamente');
      return {
        success: true,
        token: token
      };

    } catch (error) {
      console.error('❌ Error inicializando notificaciones:', error);
      return {
        success: false,
        error: `Error de inicialización: ${error}`
      };
    }
  }

  /**
   * PASO 1: Obtener token de Expo Push
   */
  private async getExpoPushToken(): Promise<string | null> {
    try {
      const projectId = Constants.expoConfig?.extra?.eas?.projectId ?? Constants.easConfig?.projectId;
      
      if (!projectId) {
        console.error('❌ Project ID no encontrado');
        return null;
      }

      const token = await Notifications.getExpoPushTokenAsync({
        projectId: projectId,
      });

      this.expoPushToken = token.data;
      console.log('🎯 Token de notificaciones obtenido:', token.data);
      
      return token.data;
    } catch (error) {
      console.error('❌ Error obteniendo token:', error);
      return null;
    }
  }

  /**
   * PASO 1: Configurar canal de Android
   */
  private async setupAndroidChannel(): Promise<void> {
    await Notifications.setNotificationChannelAsync('repumovil-notifications', {
      name: 'RepuMovil Notifications',
      description: 'Notificaciones de pedidos y entregas',
      importance: Notifications.AndroidImportance.HIGH,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#FF6B35',
      sound: 'default',
      enableLights: true,
      enableVibrate: true,
    });

    // Canal para pedidos urgentes
    await Notifications.setNotificationChannelAsync('repumovil-urgent', {
      name: 'RepuMovil Urgente',
      description: 'Pedidos urgentes y de alta prioridad',
      importance: Notifications.AndroidImportance.MAX,
      vibrationPattern: [0, 500, 200, 500],
      lightColor: '#FF0000',
      sound: 'default',
      enableLights: true,
      enableVibrate: true,
    });
  }

  /**
   * PASO 1: Configurar listeners de notificaciones
   */
  private setupNotificationListeners(): void {
    // Listener para notificaciones recibidas
    this.notificationListener = Notifications.addNotificationReceivedListener(notification => {
      console.log('📱 Notificación recibida:', notification);
      this.handleNotificationReceived(notification);
    });

    // Listener para respuestas a notificaciones
    this.responseListener = Notifications.addNotificationResponseReceivedListener(response => {
      console.log('👆 Usuario interactuó con notificación:', response);
      this.handleNotificationResponse(response);
    });
  }

  /**
   * PASO 1: Manejar notificación recibida
   */
  private handleNotificationReceived(notification: Notifications.Notification): void {
    const { request } = notification;
    const data = request.content.data as any;

    console.log('📦 Datos de notificación:', data);

    // Aquí puedes agregar lógica específica según el tipo de notificación
    if (data?.tipo === 'nuevo_pedido') {
      // Lógica para nuevo pedido
      console.log('🆕 Nuevo pedido recibido:', data.pedido_id);
    }
  }

  /**
   * PASO 1: Manejar respuesta a notificación
   */
  private handleNotificationResponse(response: Notifications.NotificationResponse): void {
    const data = response.notification.request.content.data as any;
    const actionIdentifier = response.actionIdentifier;

    console.log('🎯 Acción de notificación:', actionIdentifier);

    if (actionIdentifier === 'aceptar_pedido') {
      console.log('✅ Usuario aceptó pedido:', data.pedido_id);
      // Aquí navegarías a la pantalla del pedido
    } else if (actionIdentifier === 'rechazar_pedido') {
      console.log('❌ Usuario rechazó pedido:', data.pedido_id);
      // Aquí manejarías el rechazo
    }
  }

  /**
   * PASO 1: Enviar notificación local (para testing)
   */
  async sendLocalNotification(pedido: PedidoNotification): Promise<void> {
    try {
      const channelId = pedido.prioridad === 'alta' ? 'repumovil-urgent' : 'repumovil-notifications';

      await Notifications.scheduleNotificationAsync({
        content: {
          title: '🚚 Nuevo Pedido Disponible',
          body: `${pedido.cliente} - $${pedido.total.toLocaleString()} - ${pedido.distancia}km`,
          data: {
            pedido_id: pedido.id,
            tipo: pedido.tipo,
            cliente: pedido.cliente,
            total: pedido.total,
            distancia: pedido.distancia
          },
          sound: 'default',
          priority: pedido.prioridad === 'alta' ? 'high' : 'normal',
        },
        trigger: null, // Inmediata
        identifier: `pedido_${pedido.id}`,
      });

      console.log('📤 Notificación local enviada para pedido:', pedido.id);
    } catch (error) {
      console.error('❌ Error enviando notificación local:', error);
    }
  }

  /**
   * PASO 1: Registrar token en el servidor
   */
  async registerTokenWithServer(deliveryId: number): Promise<boolean> {
    try {
      if (!this.expoPushToken) {
        console.error('❌ No hay token disponible para registrar');
        return false;
      }

      const response = await fetch('http://localhost/mechanical-workshop/public/api/notifications.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'register_token',
          delivery_id: deliveryId,
          expo_push_token: this.expoPushToken,
          device_info: {
            platform: Platform.OS,
            device_name: Device.deviceName,
            device_model: Device.modelName,
          }
        }),
      });

      const result = await response.json();
      
      if (result.success) {
        console.log('✅ Token registrado en servidor correctamente');
        return true;
      } else {
        console.error('❌ Error registrando token:', result.message);
        return false;
      }
    } catch (error) {
      console.error('❌ Error conectando con servidor:', error);
      return false;
    }
  }

  /**
   * PASO 1: Obtener token actual
   */
  getToken(): string | null {
    return this.expoPushToken;
  }

  /**
   * PASO 1: Limpiar listeners
   */
  cleanup(): void {
    if (this.notificationListener) {
      Notifications.removeNotificationSubscription(this.notificationListener);
    }
    if (this.responseListener) {
      Notifications.removeNotificationSubscription(this.responseListener);
    }
  }

  /**
   * PASO 1: Cancelar notificación específica
   */
  async cancelNotification(pedidoId: string): Promise<void> {
    try {
      await Notifications.cancelScheduledNotificationAsync(`pedido_${pedidoId}`);
      console.log('🗑️ Notificación cancelada para pedido:', pedidoId);
    } catch (error) {
      console.error('❌ Error cancelando notificación:', error);
    }
  }

  /**
   * PASO 1: Limpiar todas las notificaciones
   */
  async clearAllNotifications(): Promise<void> {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
      await Notifications.dismissAllNotificationsAsync();
      console.log('🧹 Todas las notificaciones limpiadas');
    } catch (error) {
      console.error('❌ Error limpiando notificaciones:', error);
    }
  }
}

// Exportar instancia singleton
export const notificationService = new NotificationService();
export default NotificationService;
