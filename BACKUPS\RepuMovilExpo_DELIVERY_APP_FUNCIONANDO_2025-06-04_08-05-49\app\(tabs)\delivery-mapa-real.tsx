import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Alert, TouchableOpacity, Modal, ScrollView, SafeAreaView, StatusBar } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import MapaWrapper from '../../components/MapaWrapper';
import { Coordinates, LocationData } from '../../services/GoogleMapsService';

interface PedidoActivo {
  id: string;
  clienteNombre: string;
  clienteDireccion: string;
  clienteCoords: Coordinates;
  estado: 'asignado' | 'en_camino' | 'entregado';
  distancia?: string;
  tiempoEstimado?: string;
  ganancia: string;
  telefono: string;
}

export default function DeliveryMapaReal() {
  const router = useRouter();
  const [pedidoActivo, setPedidoActivo] = useState<PedidoActivo | null>(null);
  const [repartidorLocation, setRepartidorLocation] = useState<LocationData | null>(null);
  const [showPedidoModal, setShowPedidoModal] = useState(false);
  const [isOnline, setIsOnline] = useState(false);

  // Datos de ejemplo para testing (San Juan, Argentina)
  const pedidosEjemplo: PedidoActivo[] = [
    {
      id: '1',
      clienteNombre: 'Taller Rodríguez',
      clienteDireccion: 'Av. Libertador 1234, San Juan',
      clienteCoords: { latitude: -31.5375, longitude: -68.5364 },
      estado: 'asignado',
      ganancia: '$240',
      telefono: '+54 9 ************',
    },
    {
      id: '2',
      clienteNombre: 'Mecánica González',
      clienteDireccion: 'Calle Mitre 567, San Juan',
      clienteCoords: { latitude: -31.5400, longitude: -68.5300 },
      estado: 'asignado',
      ganancia: '$180',
      telefono: '+54 9 ************',
    },
  ];

  useEffect(() => {
    // Simular pedido activo para testing
    if (!pedidoActivo && isOnline) {
      setPedidoActivo(pedidosEjemplo[0]);
    }
  }, [isOnline]);

  const handleLocationUpdate = (location: LocationData) => {
    setRepartidorLocation(location);
    console.log('📍 Ubicación actualizada:', location);
  };

  const toggleOnlineStatus = () => {
    setIsOnline(!isOnline);
    if (!isOnline) {
      Alert.alert(
        '🟢 ¡Conectado!',
        'Ahora estás disponible para recibir pedidos. El mapa comenzará a rastrear tu ubicación.',
        [{ text: 'OK' }]
      );
    } else {
      setPedidoActivo(null);
      Alert.alert(
        '🔴 Desconectado',
        'Ya no recibirás nuevos pedidos y se detuvo el rastreo de ubicación.',
        [{ text: 'OK' }]
      );
    }
  };

  const aceptarPedido = (pedido: PedidoActivo) => {
    setPedidoActivo(pedido);
    setShowPedidoModal(false);
    Alert.alert(
      '✅ Pedido Aceptado',
      `Has aceptado el pedido de ${pedido.clienteNombre}. Dirígete al punto de recogida.`,
      [{ text: 'OK' }]
    );
  };

  const marcarEnCamino = () => {
    if (!pedidoActivo) return;
    
    setPedidoActivo({
      ...pedidoActivo,
      estado: 'en_camino'
    });
    
    Alert.alert(
      '🚚 En Camino',
      'Has marcado que estás en camino hacia el destino. El cliente ha sido notificado.',
      [{ text: 'OK' }]
    );
  };

  const completarEntrega = () => {
    Alert.alert(
      '✅ Confirmar Entrega',
      '¿Has completado la entrega exitosamente?',
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Sí, completada',
          onPress: () => {
            const ganancia = pedidoActivo?.ganancia || '$0';
            setPedidoActivo(null);
            Alert.alert(
              '🎉 ¡Entrega Completada!',
              `Excelente trabajo. Has ganado ${ganancia}. La ganancia se reflejará en tu cuenta.`,
              [
                {
                  text: 'Ver Dashboard',
                  onPress: () => router.push('/delivery-dashboard')
                }
              ]
            );
          }
        }
      ]
    );
  };

  const llamarCliente = () => {
    if (!pedidoActivo) return;
    
    Alert.alert(
      '📞 Llamar Cliente',
      `¿Deseas llamar a ${pedidoActivo.clienteNombre}?`,
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Llamar',
          onPress: () => Alert.alert('📱 Llamando...', `Marcando ${pedidoActivo.telefono}`)
        }
      ]
    );
  };

  const enviarMensaje = () => {
    Alert.alert(
      '💬 Mensaje',
      'Esta función permitirá enviar mensajes predefinidos al cliente.',
      [{ text: 'OK' }]
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#FF6B35" />
      
      {/* Header */}
      <LinearGradient
        colors={['#FF6B35', '#E53E3E']}
        style={styles.header}
      >
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color="white" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>🗺️ Mapa de Entregas</Text>
        <TouchableOpacity style={styles.menuButton}>
          <Ionicons name="menu" size={24} color="white" />
        </TouchableOpacity>
      </LinearGradient>

      {/* Mapa Principal */}
      <View style={styles.mapContainer}>
        <MapaWrapper
          clienteLocation={pedidoActivo?.clienteCoords}
          pedidoId={pedidoActivo?.id}
          onLocationUpdate={handleLocationUpdate}
          showRoute={!!pedidoActivo}
        />
      </View>

      {/* Status Bar */}
      <View style={styles.statusBar}>
        <TouchableOpacity
          style={[styles.statusButton, isOnline ? styles.online : styles.offline]}
          onPress={toggleOnlineStatus}
        >
          <Ionicons 
            name={isOnline ? "radio-button-on" : "radio-button-off"} 
            size={20} 
            color="white" 
          />
          <Text style={styles.statusText}>
            {isOnline ? 'EN LÍNEA' : 'DESCONECTADO'}
          </Text>
        </TouchableOpacity>

        {repartidorLocation && (
          <View style={styles.locationInfo}>
            <Ionicons name="location" size={16} color="#4CAF50" />
            <Text style={styles.locationText}>
              GPS: {repartidorLocation.accuracy?.toFixed(0)}m
            </Text>
          </View>
        )}
      </View>

      {/* Información del Pedido Activo */}
      {pedidoActivo && (
        <View style={styles.pedidoCard}>
          <View style={styles.pedidoHeader}>
            <Text style={styles.pedidoTitle}>📦 Pedido Activo</Text>
            <View style={[styles.estadoBadge, styles[`estado_${pedidoActivo.estado}`]]}>
              <Text style={styles.estadoText}>
                {pedidoActivo.estado.replace('_', ' ').toUpperCase()}
              </Text>
            </View>
          </View>
          
          <Text style={styles.clienteNombre}>{pedidoActivo.clienteNombre}</Text>
          <Text style={styles.clienteDireccion}>{pedidoActivo.clienteDireccion}</Text>
          <Text style={styles.ganancia}>💰 {pedidoActivo.ganancia}</Text>
          
          <View style={styles.pedidoActions}>
            <TouchableOpacity style={styles.actionButton} onPress={llamarCliente}>
              <Ionicons name="call" size={20} color="#4CAF50" />
              <Text style={styles.actionText}>Llamar</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.actionButton} onPress={enviarMensaje}>
              <Ionicons name="chatbubble" size={20} color="#FF6B35" />
              <Text style={styles.actionText}>Mensaje</Text>
            </TouchableOpacity>
            
            {pedidoActivo.estado === 'asignado' && (
              <TouchableOpacity
                style={[styles.actionButton, styles.enCaminoButton]}
                onPress={marcarEnCamino}
              >
                <Ionicons name="car" size={20} color="white" />
                <Text style={[styles.actionText, styles.enCaminoText]}>En Camino</Text>
              </TouchableOpacity>
            )}
            
            {pedidoActivo.estado === 'en_camino' && (
              <TouchableOpacity
                style={[styles.actionButton, styles.completarButton]}
                onPress={completarEntrega}
              >
                <Ionicons name="checkmark-circle" size={20} color="white" />
                <Text style={[styles.actionText, styles.completarText]}>Completar</Text>
              </TouchableOpacity>
            )}
          </View>
        </View>
      )}

      {/* Modal de Nuevos Pedidos */}
      <Modal
        visible={showPedidoModal}
        animationType="slide"
        transparent={true}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>🚀 ¡Nuevos Pedidos Disponibles!</Text>
            
            <ScrollView style={styles.pedidosList}>
              {pedidosEjemplo.map((pedido) => (
                <TouchableOpacity
                  key={pedido.id}
                  style={styles.pedidoItem}
                  onPress={() => aceptarPedido(pedido)}
                >
                  <Text style={styles.pedidoItemNombre}>{pedido.clienteNombre}</Text>
                  <Text style={styles.pedidoItemDireccion}>{pedido.clienteDireccion}</Text>
                  <View style={styles.pedidoItemInfo}>
                    <Text style={styles.pedidoItemDistancia}>📍 ~2.5 km • ⏱️ ~8 min</Text>
                    <Text style={styles.pedidoItemGanancia}>{pedido.ganancia}</Text>
                  </View>
                </TouchableOpacity>
              ))}
            </ScrollView>
            
            <TouchableOpacity
              style={styles.modalCloseButton}
              onPress={() => setShowPedidoModal(false)}
            >
              <Text style={styles.modalCloseText}>Cerrar</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Botón de Pedidos Disponibles */}
      {isOnline && !pedidoActivo && (
        <TouchableOpacity
          style={styles.pedidosButton}
          onPress={() => setShowPedidoModal(true)}
        >
          <Ionicons name="list" size={24} color="white" />
          <Text style={styles.pedidosButtonText}>Ver Pedidos Disponibles</Text>
        </TouchableOpacity>
      )}

      {/* Mensaje Motivacional */}
      {!isOnline && (
        <View style={styles.motivationalCard}>
          <Text style={styles.motivationalText}>
            🚀 "Cada entrega es una oportunidad de brillar"
          </Text>
          <Text style={styles.heartMessage}>
            Hecho con ❤️ para repartidores que nunca se rinden
          </Text>
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    paddingTop: 10,
    paddingBottom: 15,
    paddingHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: 'white',
    flex: 1,
    textAlign: 'center',
  },
  menuButton: {
    padding: 5,
  },
  mapContainer: {
    flex: 1,
    margin: 10,
    borderRadius: 15,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 5,
  },
  statusBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 10,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    marginHorizontal: 10,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statusButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
    gap: 8,
  },
  online: {
    backgroundColor: '#4CAF50',
  },
  offline: {
    backgroundColor: '#757575',
  },
  statusText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 12,
  },
  locationInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 5,
  },
  locationText: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
  },
  pedidoCard: {
    backgroundColor: 'white',
    margin: 10,
    padding: 20,
    borderRadius: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  pedidoHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  pedidoTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: '#2D3748',
  },
  estadoBadge: {
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 15,
  },
  estado_asignado: {
    backgroundColor: '#E3F2FD',
  },
  estado_en_camino: {
    backgroundColor: '#FFF3E0',
  },
  estado_entregado: {
    backgroundColor: '#E8F5E8',
  },
  estadoText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#333',
  },
  clienteNombre: {
    fontSize: 18,
    fontWeight: '700',
    color: '#2D3748',
    marginBottom: 5,
  },
  clienteDireccion: {
    fontSize: 14,
    color: '#666',
    marginBottom: 10,
    lineHeight: 20,
  },
  ganancia: {
    fontSize: 16,
    fontWeight: '700',
    color: '#4CAF50',
    marginBottom: 15,
  },
  pedidoActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 10,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 15,
    borderRadius: 10,
    backgroundColor: '#f8f9fa',
    gap: 5,
  },
  actionText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#333',
  },
  enCaminoButton: {
    backgroundColor: '#FF9800',
  },
  enCaminoText: {
    color: 'white',
  },
  completarButton: {
    backgroundColor: '#4CAF50',
  },
  completarText: {
    color: 'white',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    maxHeight: '70%',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#2D3748',
    textAlign: 'center',
    marginBottom: 20,
  },
  pedidosList: {
    maxHeight: 300,
  },
  pedidoItem: {
    backgroundColor: '#f8f9fa',
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
    borderLeftWidth: 4,
    borderLeftColor: '#4CAF50',
  },
  pedidoItemNombre: {
    fontSize: 16,
    fontWeight: '700',
    color: '#2D3748',
    marginBottom: 5,
  },
  pedidoItemDireccion: {
    fontSize: 14,
    color: '#666',
    marginBottom: 10,
  },
  pedidoItemInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  pedidoItemDistancia: {
    fontSize: 12,
    color: '#666',
  },
  pedidoItemGanancia: {
    fontSize: 16,
    fontWeight: '700',
    color: '#4CAF50',
  },
  modalCloseButton: {
    backgroundColor: '#FF6B35',
    paddingVertical: 15,
    borderRadius: 10,
    marginTop: 20,
  },
  modalCloseText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  pedidosButton: {
    position: 'absolute',
    bottom: 30,
    left: 20,
    right: 20,
    backgroundColor: '#4CAF50',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 15,
    borderRadius: 25,
    gap: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  pedidosButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '700',
  },
  motivationalCard: {
    backgroundColor: 'white',
    margin: 20,
    padding: 20,
    borderRadius: 15,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  motivationalText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2D3748',
    textAlign: 'center',
    marginBottom: 10,
  },
  heartMessage: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    fontStyle: 'italic',
  },
});
