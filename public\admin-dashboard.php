<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RepuMovil - Dashboard Administrativo 📊</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary-color: #FF6B35;
            --secondary-color: #FFA500;
            --dark-color: #2c3e50;
            --light-color: #f8f9fa;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --purple-color: #6f42c1;
            --indigo-color: #6610f2;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Se<PERSON>e <PERSON>I', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
            color: white;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo {
            font-size: 3rem;
            margin-bottom: 10px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .title {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .admin-info {
            background: rgba(255,255,255,0.1);
            padding: 15px 30px;
            border-radius: 25px;
            display: inline-block;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .kpi-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .kpi-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            color: var(--dark-color);
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: transform 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .kpi-card:hover {
            transform: translateY(-5px);
        }

        .kpi-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        }

        .kpi-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            color: var(--primary-color);
        }

        .kpi-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 5px;
        }

        .kpi-label {
            font-size: 1rem;
            color: #666;
            margin-bottom: 10px;
        }

        .kpi-trend {
            font-size: 0.9rem;
            font-weight: bold;
        }

        .trend-up {
            color: var(--success-color);
        }

        .trend-down {
            color: var(--danger-color);
        }

        .trend-neutral {
            color: var(--warning-color);
        }

        .charts-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .chart-container {
            background: white;
            border-radius: 15px;
            padding: 25px;
            color: var(--dark-color);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .chart-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 20px;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .metric-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            color: var(--dark-color);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .metric-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }

        .metric-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .metric-icon.deliveries {
            background: var(--info-color);
        }

        .metric-icon.orders {
            background: var(--success-color);
        }

        .metric-icon.ratings {
            background: var(--warning-color);
        }

        .metric-icon.revenue {
            background: var(--purple-color);
        }

        .metric-title {
            font-size: 1.2rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        .metric-subtitle {
            font-size: 0.9rem;
            color: #666;
        }

        .metric-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            background: var(--light-color);
            border-radius: 10px;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.8rem;
            color: #666;
        }

        .activity-feed {
            background: white;
            border-radius: 15px;
            padding: 25px;
            color: var(--dark-color);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-height: 500px;
            overflow-y: auto;
        }

        .activity-item {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #eee;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 1.2rem;
            color: white;
        }

        .activity-icon.order {
            background: var(--success-color);
        }

        .activity-icon.delivery {
            background: var(--info-color);
        }

        .activity-icon.rating {
            background: var(--warning-color);
        }

        .activity-icon.system {
            background: var(--purple-color);
        }

        .activity-content {
            flex: 1;
        }

        .activity-title {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .activity-description {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 5px;
        }

        .activity-time {
            font-size: 0.8rem;
            color: #999;
        }

        .controls-panel {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .controls-title {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 15px;
            text-align: center;
        }

        .controls-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .control-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 15px 20px;
            border-radius: 10px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .control-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .refresh-btn {
            background: var(--success-color);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            margin: 20px auto;
            display: block;
            transition: all 0.3s ease;
        }

        .refresh-btn:hover {
            background: #218838;
            transform: translateY(-2px);
        }

        @media (max-width: 1200px) {
            .charts-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .kpi-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .metrics-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">🏆📊🚀</div>
            <h1 class="title">Dashboard Administrativo RepuMovil</h1>
            <p class="subtitle">Centro de Control y Métricas del Sistema</p>
            <div class="admin-info">
                <i class="fas fa-user-shield"></i>
                Administrador: Carlos Mendoza • Último acceso: Hoy 14:30
            </div>
        </div>

        <!-- Panel de Controles -->
        <div class="controls-panel">
            <div class="controls-title">
                <i class="fas fa-cogs"></i>
                Panel de Control Rápido
            </div>
            <div class="controls-grid">
                <button class="control-btn" onclick="exportarReporte()">
                    <i class="fas fa-download"></i>
                    Exportar Reporte
                </button>
                <button class="control-btn" onclick="gestionarUsuarios()">
                    <i class="fas fa-users"></i>
                    Gestionar Usuarios
                </button>
                <button class="control-btn" onclick="configurarSistema()">
                    <i class="fas fa-cog"></i>
                    Configuración
                </button>
                <button class="control-btn" onclick="verAlertas()">
                    <i class="fas fa-bell"></i>
                    Alertas (3)
                </button>
            </div>
        </div>

        <!-- KPIs Principales -->
        <div class="kpi-grid">
            <div class="kpi-card">
                <div class="kpi-icon">📦</div>
                <div class="kpi-number" id="total-pedidos">1,247</div>
                <div class="kpi-label">Pedidos Totales</div>
                <div class="kpi-trend trend-up">
                    <i class="fas fa-arrow-up"></i> +12.5% vs mes anterior
                </div>
            </div>

            <div class="kpi-card">
                <div class="kpi-icon">🏍️</div>
                <div class="kpi-number" id="deliveries-activos">24</div>
                <div class="kpi-label">Deliveries Activos</div>
                <div class="kpi-trend trend-up">
                    <i class="fas fa-arrow-up"></i> +3 nuevos esta semana
                </div>
            </div>

            <div class="kpi-card">
                <div class="kpi-icon">💰</div>
                <div class="kpi-number" id="ingresos-mes">$89,450</div>
                <div class="kpi-label">Ingresos del Mes</div>
                <div class="kpi-trend trend-up">
                    <i class="fas fa-arrow-up"></i> +18.3% vs mes anterior
                </div>
            </div>

            <div class="kpi-card">
                <div class="kpi-icon">⭐</div>
                <div class="kpi-number" id="calificacion-promedio">4.8</div>
                <div class="kpi-label">Calificación Promedio</div>
                <div class="kpi-trend trend-up">
                    <i class="fas fa-arrow-up"></i> +0.2 vs mes anterior
                </div>
            </div>

            <div class="kpi-card">
                <div class="kpi-icon">⚡</div>
                <div class="kpi-number" id="tiempo-promedio">18.5</div>
                <div class="kpi-label">Tiempo Promedio (min)</div>
                <div class="kpi-trend trend-down">
                    <i class="fas fa-arrow-down"></i> -2.1 min (mejorando)
                </div>
            </div>

            <div class="kpi-card">
                <div class="kpi-icon">📈</div>
                <div class="kpi-number" id="tasa-exito">94.2%</div>
                <div class="kpi-label">Tasa de Éxito</div>
                <div class="kpi-trend trend-up">
                    <i class="fas fa-arrow-up"></i> +1.8% vs mes anterior
                </div>
            </div>
        </div>

        <!-- Gráficos Principales -->
        <div class="charts-grid">
            <div class="chart-container">
                <div class="chart-title">
                    <i class="fas fa-chart-line"></i>
                    Pedidos por Día (Últimos 30 días)
                </div>
                <canvas id="pedidosChart" width="400" height="200"></canvas>
            </div>

            <div class="chart-container">
                <div class="chart-title">
                    <i class="fas fa-chart-pie"></i>
                    Estados de Pedidos
                </div>
                <canvas id="estadosChart" width="300" height="300"></canvas>
            </div>
        </div>

        <!-- Métricas Detalladas -->
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-header">
                    <div class="metric-icon deliveries">🏍️</div>
                    <div>
                        <div class="metric-title">Rendimiento Deliveries</div>
                        <div class="metric-subtitle">Estadísticas de repartidores</div>
                    </div>
                </div>
                <div class="metric-stats">
                    <div class="stat-item">
                        <div class="stat-number">24</div>
                        <div class="stat-label">Activos</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">156</div>
                        <div class="stat-label">Entregas Hoy</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">4.8</div>
                        <div class="stat-label">Calificación</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">92%</div>
                        <div class="stat-label">Disponibilidad</div>
                    </div>
                </div>
            </div>

            <div class="metric-card">
                <div class="metric-header">
                    <div class="metric-icon orders">📦</div>
                    <div>
                        <div class="metric-title">Gestión de Pedidos</div>
                        <div class="metric-subtitle">Estado actual del sistema</div>
                    </div>
                </div>
                <div class="metric-stats">
                    <div class="stat-item">
                        <div class="stat-number">45</div>
                        <div class="stat-label">En Proceso</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">12</div>
                        <div class="stat-label">En Camino</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">156</div>
                        <div class="stat-label">Entregados</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">3</div>
                        <div class="stat-label">Problemas</div>
                    </div>
                </div>
            </div>

            <div class="metric-card">
                <div class="metric-header">
                    <div class="metric-icon ratings">⭐</div>
                    <div>
                        <div class="metric-title">Sistema de Calificaciones</div>
                        <div class="metric-subtitle">Feedback y satisfacción</div>
                    </div>
                </div>
                <div class="metric-stats">
                    <div class="stat-item">
                        <div class="stat-number">4.8</div>
                        <div class="stat-label">Promedio</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">89</div>
                        <div class="stat-label">Este Mes</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">78%</div>
                        <div class="stat-label">5 Estrellas</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">65%</div>
                        <div class="stat-label">Con Respuesta</div>
                    </div>
                </div>
            </div>

            <div class="metric-card">
                <div class="metric-header">
                    <div class="metric-icon revenue">💰</div>
                    <div>
                        <div class="metric-title">Análisis Financiero</div>
                        <div class="metric-subtitle">Ingresos y comisiones</div>
                    </div>
                </div>
                <div class="metric-stats">
                    <div class="stat-item">
                        <div class="stat-number">$89K</div>
                        <div class="stat-label">Este Mes</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">$2.8K</div>
                        <div class="stat-label">Hoy</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">15%</div>
                        <div class="stat-label">Comisión</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">+18%</div>
                        <div class="stat-label">Crecimiento</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Actividad Reciente -->
        <div class="activity-feed">
            <div class="chart-title">
                <i class="fas fa-clock"></i>
                Actividad Reciente del Sistema
            </div>
            
            <div id="activity-container">
                <!-- La actividad se cargará dinámicamente -->
            </div>
        </div>

        <button class="refresh-btn" onclick="refreshDashboard()">
            <i class="fas fa-sync-alt"></i>
            Actualizar Dashboard
        </button>
    </div>

    <script>
        // PASO 10: Variables globales
        let pedidosChart, estadosChart;

        // PASO 10: Inicializar dashboard
        document.addEventListener('DOMContentLoaded', function() {
            initCharts();
            loadActivityFeed();
            
            // Auto-refresh cada 30 segundos
            setInterval(refreshDashboard, 30000);
        });

        // PASO 10: Inicializar gráficos
        function initCharts() {
            // Gráfico de pedidos por día
            const ctxPedidos = document.getElementById('pedidosChart').getContext('2d');
            pedidosChart = new Chart(ctxPedidos, {
                type: 'line',
                data: {
                    labels: generateDateLabels(30),
                    datasets: [{
                        label: 'Pedidos',
                        data: generateRandomData(30, 20, 60),
                        borderColor: '#FF6B35',
                        backgroundColor: 'rgba(255, 107, 53, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        },
                        x: {
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        }
                    }
                }
            });

            // Gráfico de estados
            const ctxEstados = document.getElementById('estadosChart').getContext('2d');
            estadosChart = new Chart(ctxEstados, {
                type: 'doughnut',
                data: {
                    labels: ['Entregados', 'En Camino', 'En Proceso', 'Problemas'],
                    datasets: [{
                        data: [156, 12, 45, 3],
                        backgroundColor: [
                            '#28a745',
                            '#17a2b8',
                            '#ffc107',
                            '#dc3545'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        // PASO 10: Generar etiquetas de fecha
        function generateDateLabels(days) {
            const labels = [];
            for (let i = days - 1; i >= 0; i--) {
                const date = new Date();
                date.setDate(date.getDate() - i);
                labels.push(date.toLocaleDateString('es-ES', { month: 'short', day: 'numeric' }));
            }
            return labels;
        }

        // PASO 10: Generar datos aleatorios
        function generateRandomData(count, min, max) {
            const data = [];
            for (let i = 0; i < count; i++) {
                data.push(Math.floor(Math.random() * (max - min + 1)) + min);
            }
            return data;
        }

        // PASO 10: Cargar feed de actividad
        function loadActivityFeed() {
            const activities = [
                {
                    type: 'order',
                    icon: '📦',
                    title: 'Nuevo pedido creado',
                    description: 'Pedido #1248 - Taller Mecánico Central - $2,450',
                    time: 'hace 2 minutos'
                },
                {
                    type: 'delivery',
                    icon: '🏍️',
                    title: 'Entrega completada',
                    description: 'Juan Pérez entregó pedido #1247 - Calificación: 5⭐',
                    time: 'hace 5 minutos'
                },
                {
                    type: 'rating',
                    icon: '⭐',
                    title: 'Nueva calificación',
                    description: 'María González recibió 5 estrellas - "Excelente servicio"',
                    time: 'hace 8 minutos'
                },
                {
                    type: 'system',
                    icon: '🔧',
                    title: 'Reasignación automática',
                    description: 'Pedido #1246 reasignado por timeout - Nuevo delivery: Carlos R.',
                    time: 'hace 12 minutos'
                },
                {
                    type: 'delivery',
                    icon: '🏍️',
                    title: 'Delivery conectado',
                    description: 'Ana López se conectó y está disponible para entregas',
                    time: 'hace 15 minutos'
                },
                {
                    type: 'order',
                    icon: '📦',
                    title: 'Pedido empacado',
                    description: 'Repuestos San Juan empacó pedido #1245',
                    time: 'hace 18 minutos'
                }
            ];

            const container = document.getElementById('activity-container');
            container.innerHTML = activities.map(activity => `
                <div class="activity-item">
                    <div class="activity-icon ${activity.type}">
                        ${activity.icon}
                    </div>
                    <div class="activity-content">
                        <div class="activity-title">${activity.title}</div>
                        <div class="activity-description">${activity.description}</div>
                        <div class="activity-time">${activity.time}</div>
                    </div>
                </div>
            `).join('');
        }

        // PASO 10: Funciones de control
        function exportarReporte() {
            alert('📊 Exportando Reporte\n\nGenerando reporte completo en PDF...\n\n• Métricas de rendimiento\n• Estadísticas de deliveries\n• Análisis de calificaciones\n• Tendencias de pedidos');
        }

        function gestionarUsuarios() {
            alert('👥 Gestión de Usuarios\n\n• 24 Deliveries activos\n• 156 Clientes registrados\n• 12 Proveedores activos\n• 3 Administradores\n\n¿Abrir panel de gestión?');
        }

        function configurarSistema() {
            alert('⚙️ Configuración del Sistema\n\n• Parámetros de asignación\n• Tiempos de timeout\n• Tarifas de delivery\n• Notificaciones push\n• Integración con APIs');
        }

        function verAlertas() {
            alert('🚨 Alertas del Sistema\n\n1. 🔴 Delivery Juan P. sin respuesta (5 min)\n2. 🟡 Proveedor "AutoPartes" con demora\n3. 🟠 Pico de demanda en Zona Norte\n\n¿Gestionar alertas?');
        }

        // PASO 10: Actualizar dashboard
        function refreshDashboard() {
            const btn = document.querySelector('.refresh-btn');
            const originalText = btn.innerHTML;
            
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Actualizando...';
            btn.disabled = true;
            
            // Simular actualización de datos
            setTimeout(() => {
                // Actualizar KPIs
                updateKPIs();
                
                // Actualizar gráficos
                updateCharts();
                
                // Recargar actividad
                loadActivityFeed();
                
                btn.innerHTML = '<i class="fas fa-check"></i> Actualizado';
                
                setTimeout(() => {
                    btn.innerHTML = originalText;
                    btn.disabled = false;
                }, 1000);
            }, 2000);
        }

        // PASO 10: Actualizar KPIs
        function updateKPIs() {
            const kpis = {
                'total-pedidos': Math.floor(Math.random() * 50) + 1220,
                'deliveries-activos': Math.floor(Math.random() * 5) + 22,
                'ingresos-mes': '$' + (Math.floor(Math.random() * 10000) + 85000).toLocaleString(),
                'calificacion-promedio': (Math.random() * 0.4 + 4.6).toFixed(1),
                'tiempo-promedio': (Math.random() * 5 + 16).toFixed(1),
                'tasa-exito': (Math.random() * 3 + 92).toFixed(1) + '%'
            };

            Object.keys(kpis).forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = kpis[id];
                }
            });
        }

        // PASO 10: Actualizar gráficos
        function updateCharts() {
            // Actualizar datos del gráfico de pedidos
            pedidosChart.data.datasets[0].data = generateRandomData(30, 20, 60);
            pedidosChart.update();

            // Actualizar datos del gráfico de estados
            const newData = [
                Math.floor(Math.random() * 20) + 150,
                Math.floor(Math.random() * 8) + 10,
                Math.floor(Math.random() * 15) + 40,
                Math.floor(Math.random() * 3) + 2
            ];
            estadosChart.data.datasets[0].data = newData;
            estadosChart.update();
        }
    </script>
</body>
</html>
