<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Iniciar <PERSON><PERSON><PERSON> - <PERSON>uMovil</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container-custom {
            max-width: 450px;
            width: 100%;
            padding: 20px;
        }

        .logo-container {
            text-align: center;
            margin-bottom: 40px;
        }

        .logo-circle {
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, #FF6B35 0%, #FFA500 100%);
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            box-shadow: 0 8px 25px rgba(255, 107, 53, 0.4);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .logo-icon {
            font-size: 45px;
            color: white;
        }

        .logo-title {
            font-size: 36px;
            font-weight: bold;
            margin: 0;
        }

        .logo-repu {
            color: #FF6B35;
        }

        .logo-movil {
            color: #FFA500;
        }

        .card-custom {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            border: none;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .card-header-custom {
            background: linear-gradient(135deg, #FF6B35 0%, #FFA500 100%);
            color: white;
            text-align: center;
            padding: 30px;
            border: none;
        }

        .card-title {
            font-size: 28px;
            font-weight: 600;
            margin: 0;
        }

        .card-subtitle {
            font-size: 14px;
            opacity: 0.9;
            margin-top: 5px;
        }

        .form-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 15px 20px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-control:focus {
            border-color: #FF6B35;
            box-shadow: 0 0 0 0.2rem rgba(255, 107, 53, 0.25);
            background: white;
        }

        .input-group-text {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-right: none;
            border-radius: 12px 0 0 12px;
        }

        .input-group .form-control {
            border-left: none;
            border-radius: 0 12px 12px 0;
        }

        .btn-primary-custom {
            background: linear-gradient(135deg, #FF6B35 0%, #FFA500 100%);
            border: none;
            border-radius: 12px;
            padding: 15px 30px;
            font-size: 18px;
            font-weight: 600;
            width: 100%;
            transition: all 0.3s ease;
            margin-top: 10px;
        }

        .btn-primary-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 107, 53, 0.4);
        }

        .btn-outline-custom {
            border: 2px solid #FF6B35;
            color: #FF6B35;
            border-radius: 12px;
            padding: 12px 30px;
            font-size: 16px;
            font-weight: 600;
            width: 100%;
            transition: all 0.3s ease;
            background: transparent;
        }

        .btn-outline-custom:hover {
            background: #FF6B35;
            color: white;
            transform: translateY(-1px);
        }

        .forgot-password {
            text-align: center;
            margin: 20px 0;
        }

        .forgot-password a {
            color: #666;
            text-decoration: none;
            font-size: 14px;
            transition: color 0.3s ease;
        }

        .forgot-password a:hover {
            color: #FF6B35;
        }

        .divider {
            text-align: center;
            margin: 25px 0;
            position: relative;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e9ecef;
        }

        .divider span {
            background: white;
            padding: 0 20px;
            color: #666;
            font-size: 14px;
        }

        .register-link {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }

        .register-link p {
            margin: 0;
            color: #666;
            font-size: 14px;
        }

        .register-link a {
            color: #FF6B35;
            text-decoration: none;
            font-weight: 600;
        }

        .register-link a:hover {
            text-decoration: underline;
        }

        .alert-custom {
            border-radius: 12px;
            border: none;
            margin-bottom: 20px;
        }

        .user-type-badges {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 15px;
            justify-content: center;
        }

        .badge-custom {
            background: rgba(255, 107, 53, 0.1);
            color: #FF6B35;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="container-custom">
        <!-- Logo -->
        <div class="logo-container">
            <div class="logo-circle">
                <i class="fas fa-wrench logo-icon"></i>
            </div>
            <h1 class="logo-title">
                <span class="logo-repu">Repu</span><span class="logo-movil">Movil</span>
            </h1>
        </div>

        <!-- Formulario de Login -->
        <div class="card card-custom">
            <div class="card-header-custom">
                <h2 class="card-title">Bienvenido</h2>
                <p class="card-subtitle">Inicia sesión en tu cuenta</p>
            </div>
            <div class="card-body p-4">
                <!-- Alertas -->
                <div id="alertContainer"></div>

                <form id="loginForm" method="POST" action="api/login.php">
                    <!-- Email -->
                    <div class="mb-3">
                        <label for="email" class="form-label">
                            <i class="fas fa-envelope me-2"></i>Correo Electrónico
                        </label>
                        <input type="email" class="form-control" id="email" name="email" required placeholder="<EMAIL>">
                    </div>

                    <!-- Contraseña -->
                    <div class="mb-3">
                        <label for="password" class="form-label">
                            <i class="fas fa-lock me-2"></i>Contraseña
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-key"></i>
                            </span>
                            <input type="password" class="form-control" id="password" name="password" required placeholder="Tu contraseña">
                            <button type="button" class="btn btn-outline-secondary" id="togglePassword">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Recordar sesión -->
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="remember" name="remember">
                        <label class="form-check-label" for="remember">
                            Recordar mi sesión
                        </label>
                    </div>

                    <!-- Botón de Login -->
                    <button type="submit" class="btn btn-primary-custom">
                        <i class="fas fa-sign-in-alt me-2"></i>Iniciar Sesión
                    </button>
                </form>

                <!-- Enlace de contraseña olvidada -->
                <div class="forgot-password">
                    <a href="#" onclick="recuperarPassword()">
                        <i class="fas fa-question-circle me-1"></i>¿Olvidaste tu contraseña?
                    </a>
                </div>

                <!-- Divider -->
                <div class="divider">
                    <span>o</span>
                </div>

                <!-- Botón de Registro -->
                <a href="registro-dinamico.php" class="btn btn-outline-custom">
                    <i class="fas fa-user-plus me-2"></i>Crear Nueva Cuenta
                </a>

                <!-- Tipos de usuario -->
                <div class="user-type-badges">
                    <span class="badge-custom">👤 Usuario Regular</span>
                    <span class="badge-custom">🔧 Taller Mecánico</span>
                    <span class="badge-custom">⚙️ Mecánico Independiente</span>
                    <span class="badge-custom">🏪 Proveedor de Repuestos</span>
                </div>
            </div>
        </div>

        <!-- Información adicional -->
        <div class="register-link">
            <p>¿Eres nuevo en RepuMovil? <a href="registro-dinamico.php">Regístrate aquí</a></p>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Toggle password visibility
        document.getElementById('togglePassword').addEventListener('click', function() {
            const passwordField = document.getElementById('password');
            const icon = this.querySelector('i');

            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                passwordField.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });

        // Función para mostrar alertas
        function showAlert(message, type = 'danger') {
            const alertContainer = document.getElementById('alertContainer');
            const alertHtml = `
                <div class="alert alert-${type} alert-custom alert-dismissible fade show" role="alert">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            alertContainer.innerHTML = alertHtml;
        }

        // Función para recuperar contraseña
        function recuperarPassword() {
            const email = document.getElementById('email').value;
            if (!email) {
                showAlert('Por favor ingresa tu email primero', 'warning');
                document.getElementById('email').focus();
                return;
            }

            // Aquí se implementaría la recuperación de contraseña
            showAlert('Se ha enviado un enlace de recuperación a tu email', 'success');
        }

        // Validación del formulario de login
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;

            if (!email || !password) {
                showAlert('Por favor completa todos los campos', 'warning');
                return;
            }

            if (!isValidEmail(email)) {
                showAlert('Por favor ingresa un email válido', 'warning');
                return;
            }

            // Mostrar loading
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Iniciando sesión...';
            submitBtn.disabled = true;

            // Simular llamada al API
            setTimeout(() => {
                // Aquí se haría la llamada real al API
                fetch('api/login.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams(new FormData(this))
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showAlert('¡Inicio de sesión exitoso! Redirigiendo...', 'success');
                        setTimeout(() => {
                            window.location.href = 'dashboard.php';
                        }, 1500);
                    } else {
                        showAlert(data.message || 'Error al iniciar sesión', 'danger');
                    }
                })
                .catch(error => {
                    showAlert('Error de conexión. Intenta nuevamente.', 'danger');
                })
                .finally(() => {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                });
            }, 1000);
        });

        // Función para validar email
        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        // Auto-focus en el campo de email
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('email').focus();
        });

        // Detectar Enter en los campos
        document.getElementById('email').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.getElementById('password').focus();
            }
        });

        document.getElementById('password').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.getElementById('loginForm').dispatchEvent(new Event('submit'));
            }
        });
    </script>
</body>
</html>
