<?php
// Mostrar todos los errores
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Depuración de Login</h1>";

// Verificar si hay archivos incluidos automáticamente
echo "<h2>Archivos incluidos automáticamente:</h2>";
echo "<pre>";
print_r(get_included_files());
echo "</pre>";

// Verificar si existen los archivos problemáticos
echo "<h2>Verificación de archivos problemáticos:</h2>";
$files = [
    '../src/User.php',
    '../src/UserManager.php',
    '../src/Database.php',
    '../src/UserDB.php'
];

echo "<ul>";
foreach ($files as $file) {
    if (file_exists($file)) {
        echo "<li>$file: ✅ Existe</li>";
        
        // Mostrar el contenido del archivo
        echo "<details>";
        echo "<summary>Ver contenido</summary>";
        echo "<pre>";
        highlight_file($file);
        echo "</pre>";
        echo "</details>";
    } else {
        echo "<li>$file: ❌ No existe</li>";
    }
}
echo "</ul>";

// Verificar configuración de PHP
echo "<h2>Configuración de PHP:</h2>";
echo "<p>Directorio actual: " . getcwd() . "</p>";
echo "<p>Ruta de inclusión: " . get_include_path() . "</p>";
echo "<p>Directorio del script: " . __DIR__ . "</p>";

// Verificar si hay archivos de autoload
echo "<h2>Verificación de autoload:</h2>";
$autoloadFiles = [
    '../vendor/autoload.php',
    '../autoload.php'
];

echo "<ul>";
foreach ($autoloadFiles as $file) {
    if (file_exists($file)) {
        echo "<li>$file: ✅ Existe</li>";
    } else {
        echo "<li>$file: ❌ No existe</li>";
    }
}
echo "</ul>";

// Verificar si hay archivos de configuración
echo "<h2>Verificación de archivos de configuración:</h2>";
$configFiles = [
    '../config.php',
    '../config/config.php',
    '../config/database.php'
];

echo "<ul>";
foreach ($configFiles as $file) {
    if (file_exists($file)) {
        echo "<li>$file: ✅ Existe</li>";
    } else {
        echo "<li>$file: ❌ No existe</li>";
    }
}
echo "</ul>";

// Verificar si hay archivos .htaccess
echo "<h2>Verificación de .htaccess:</h2>";
$htaccessFiles = [
    '../.htaccess',
    '.htaccess'
];

echo "<ul>";
foreach ($htaccessFiles as $file) {
    if (file_exists($file)) {
        echo "<li>$file: ✅ Existe</li>";
        
        // Mostrar el contenido del archivo
        echo "<details>";
        echo "<summary>Ver contenido</summary>";
        echo "<pre>";
        readfile($file);
        echo "</pre>";
        echo "</details>";
    } else {
        echo "<li>$file: ❌ No existe</li>";
    }
}
echo "</ul>";

// Verificar si hay archivos index.php que puedan estar redirigiendo
echo "<h2>Verificación de index.php:</h2>";
$indexFiles = [
    '../index.php',
    'index.php'
];

echo "<ul>";
foreach ($indexFiles as $file) {
    if (file_exists($file)) {
        echo "<li>$file: ✅ Existe</li>";
    } else {
        echo "<li>$file: ❌ No existe</li>";
    }
}
echo "</ul>";

// Verificar si hay archivos de caché
echo "<h2>Verificación de caché:</h2>";
$cacheFiles = [
    '../cache',
    '../tmp',
    '../temp'
];

echo "<ul>";
foreach ($cacheFiles as $file) {
    if (file_exists($file)) {
        echo "<li>$file: ✅ Existe</li>";
    } else {
        echo "<li>$file: ❌ No existe</li>";
    }
}
echo "</ul>";

// Mostrar información del servidor
echo "<h2>Información del servidor:</h2>";
echo "<p>Servidor: " . $_SERVER['SERVER_SOFTWARE'] . "</p>";
echo "<p>Documento raíz: " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p>Ruta del script: " . $_SERVER['SCRIPT_FILENAME'] . "</p>";

// Mostrar enlaces útiles
echo "<h2>Enlaces útiles:</h2>";
echo "<ul>";
echo "<li><a href='login.php'>Página de login</a></li>";
echo "<li><a href='login_new.php'>Página de login nueva</a></li>";
echo "<li><a href='index.php'>Página de inicio</a></li>";
echo "</ul>";
?>
