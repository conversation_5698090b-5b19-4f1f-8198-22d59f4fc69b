import React, { useState, useEffect } from 'react';
import {
  SafeAreaView,
  StatusBar,
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Alert,
  Dimensions,
  Modal,
} from 'react-native';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';

const { width, height } = Dimensions.get('window');

interface Ubicacion {
  latitud: number;
  longitud: number;
  direccion: string;
}

interface PedidoActivo {
  id: string;
  cliente: string;
  telefono: string;
  origen: Ubicacion;
  destino: Ubicacion;
  estado: 'asignado' | 'en_camino' | 'llegando';
  ganancia: string;
  tiempo_estimado: string;
}

export default function DeliveryMapa() {
  const router = useRouter();
  const [pedidoActivo, setPedidoActivo] = useState<PedidoActivo | null>(null);
  const [ubicacionActual, setUbicacionActual] = useState<Ubicacion | null>(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [navegacionActiva, setNavegacionActiva] = useState(false);

  // Datos de ejemplo
  useEffect(() => {
    // Simular pedido activo
    setPedidoActivo({
      id: '1',
      cliente: 'Taller Rodríguez',
      telefono: '+54 9 ************',
      origen: {
        latitud: -31.5375,
        longitud: -68.5364,
        direccion: 'Av. San Martín 1234, Centro'
      },
      destino: {
        latitud: -31.5425,
        longitud: -68.5314,
        direccion: 'Calle Rivadavia 567, Barrio Norte'
      },
      estado: 'asignado',
      ganancia: '$240',
      tiempo_estimado: '15 min'
    });

    // Simular ubicación actual
    setUbicacionActual({
      latitud: -31.5355,
      longitud: -68.5384,
      direccion: 'Tu ubicación actual'
    });
  }, []);

  const iniciarNavegacion = () => {
    if (!pedidoActivo) return;
    
    setNavegacionActiva(true);
    Alert.alert(
      '🗺️ Navegación Iniciada',
      'Te estamos guiando hacia el punto de recogida. Mantén la app abierta para recibir indicaciones.',
      [{ text: 'Entendido' }]
    );
  };

  const marcarEnCamino = () => {
    if (!pedidoActivo) return;
    
    setPedidoActivo({
      ...pedidoActivo,
      estado: 'en_camino'
    });
    
    Alert.alert(
      '🚚 En Camino',
      'Has marcado que estás en camino hacia el destino. El cliente ha sido notificado.',
      [{ text: 'OK' }]
    );
  };

  const completarEntrega = () => {
    Alert.alert(
      '✅ Completar Entrega',
      '¿Confirmas que has completado la entrega exitosamente?',
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Completar',
          onPress: () => {
            Alert.alert(
              '🎉 ¡Entrega Completada!',
              `Has ganado ${pedidoActivo?.ganancia}. La ganancia se reflejará en tu cuenta.`,
              [
                {
                  text: 'Ver Dashboard',
                  onPress: () => router.push('/delivery-dashboard')
                }
              ]
            );
          }
        }
      ]
    );
  };

  const llamarCliente = () => {
    if (!pedidoActivo) return;
    
    Alert.alert(
      '📞 Llamar Cliente',
      `¿Deseas llamar a ${pedidoActivo.cliente}?`,
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Llamar',
          onPress: () => Alert.alert('📱 Llamando...', `Marcando ${pedidoActivo.telefono}`)
        }
      ]
    );
  };

  const abrirMapaExterno = () => {
    Alert.alert(
      '🗺️ Abrir en Mapa',
      'Esta función abrirá Google Maps o la app de mapas predeterminada.',
      [
        { text: 'Cancelar', style: 'cancel' },
        { text: 'Abrir', onPress: () => Alert.alert('🚧 En desarrollo', 'Función próximamente disponible') }
      ]
    );
  };

  if (!pedidoActivo) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="light-content" backgroundColor="#FF6B35" />
        
        <LinearGradient
          colors={['#FF6B35', '#E53E3E']}
          style={styles.header}
        >
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <Text style={styles.backButtonText}>← Volver</Text>
          </TouchableOpacity>
          <Text style={styles.headerTitle}>🗺️ Mapa de Entregas</Text>
        </LinearGradient>

        <View style={styles.emptyContainer}>
          <Text style={styles.emptyIcon}>📍</Text>
          <Text style={styles.emptyText}>No hay entregas activas</Text>
          <Text style={styles.emptySubtext}>
            Acepta un pedido desde el dashboard para ver la ruta aquí
          </Text>
          <TouchableOpacity 
            style={styles.dashboardButton}
            onPress={() => router.push('/delivery-dashboard')}
          >
            <Text style={styles.dashboardButtonText}>Ir al Dashboard</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#FF6B35" />
      
      {/* Header */}
      <LinearGradient
        colors={['#FF6B35', '#E53E3E']}
        style={styles.header}
      >
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <Text style={styles.backButtonText}>← Volver</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>🗺️ Entrega en Curso</Text>
        <TouchableOpacity style={styles.infoButton} onPress={() => setModalVisible(true)}>
          <Text style={styles.infoButtonText}>ℹ️</Text>
        </TouchableOpacity>
      </LinearGradient>

      {/* Mapa Simulado */}
      <View style={styles.mapaContainer}>
        <View style={styles.mapaSimulado}>
          <Text style={styles.mapaTexto}>🗺️ MAPA INTERACTIVO</Text>
          <Text style={styles.mapaSubtexto}>
            Aquí se mostraría el mapa real con tu ubicación y la ruta
          </Text>
          
          {/* Puntos en el mapa */}
          <View style={styles.puntosContainer}>
            <View style={[styles.punto, styles.puntoOrigen]}>
              <Text style={styles.puntoTexto}>📍 Origen</Text>
            </View>
            
            <View style={styles.rutaLinea} />
            
            <View style={[styles.punto, styles.puntoActual]}>
              <Text style={styles.puntoTexto}>🚗 Tú</Text>
            </View>
            
            <View style={styles.rutaLinea} />
            
            <View style={[styles.punto, styles.puntoDestino]}>
              <Text style={styles.puntoTexto}>🏁 Destino</Text>
            </View>
          </View>
        </View>
      </View>

      {/* Panel de Información */}
      <View style={styles.infoPanel}>
        <View style={styles.clienteInfo}>
          <Text style={styles.clienteNombre}>{pedidoActivo.cliente}</Text>
          <Text style={styles.estadoBadge}>
            {pedidoActivo.estado === 'asignado' ? '📦 Ir a recoger' : 
             pedidoActivo.estado === 'en_camino' ? '🚚 En camino' : '📍 Llegando'}
          </Text>
        </View>
        
        <Text style={styles.direccionTexto}>
          {pedidoActivo.estado === 'asignado' 
            ? `📍 ${pedidoActivo.origen.direccion}`
            : `🏁 ${pedidoActivo.destino.direccion}`
          }
        </Text>
        
        <View style={styles.detallesRow}>
          <Text style={styles.detalle}>💰 {pedidoActivo.ganancia}</Text>
          <Text style={styles.detalle}>⏱️ {pedidoActivo.tiempo_estimado}</Text>
        </View>
      </View>

      {/* Botones de Acción */}
      <View style={styles.accionesContainer}>
        <View style={styles.accionesRow}>
          <TouchableOpacity style={styles.accionButton} onPress={llamarCliente}>
            <Text style={styles.accionIcon}>📞</Text>
            <Text style={styles.accionText}>Llamar</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.accionButton} onPress={abrirMapaExterno}>
            <Text style={styles.accionIcon}>🗺️</Text>
            <Text style={styles.accionText}>Mapa</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.accionButton}>
            <Text style={styles.accionIcon}>💬</Text>
            <Text style={styles.accionText}>Chat</Text>
          </TouchableOpacity>
        </View>
        
        {pedidoActivo.estado === 'asignado' && (
          <TouchableOpacity style={styles.primaryButton} onPress={iniciarNavegacion}>
            <LinearGradient
              colors={['#4CAF50', '#45A049']}
              style={styles.buttonGradient}
            >
              <Text style={styles.primaryButtonText}>🚀 Iniciar Navegación</Text>
            </LinearGradient>
          </TouchableOpacity>
        )}
        
        {pedidoActivo.estado === 'asignado' && navegacionActiva && (
          <TouchableOpacity style={styles.primaryButton} onPress={marcarEnCamino}>
            <LinearGradient
              colors={['#FF9800', '#F57C00']}
              style={styles.buttonGradient}
            >
              <Text style={styles.primaryButtonText}>📦 Recogido - En Camino</Text>
            </LinearGradient>
          </TouchableOpacity>
        )}
        
        {pedidoActivo.estado === 'en_camino' && (
          <TouchableOpacity style={styles.primaryButton} onPress={completarEntrega}>
            <LinearGradient
              colors={['#4CAF50', '#45A049']}
              style={styles.buttonGradient}
            >
              <Text style={styles.primaryButtonText}>✅ Completar Entrega</Text>
            </LinearGradient>
          </TouchableOpacity>
        )}
      </View>

      {/* Modal de Información */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>📋 Detalles del Pedido</Text>
            
            <View style={styles.modalInfo}>
              <Text style={styles.modalLabel}>Cliente:</Text>
              <Text style={styles.modalValue}>{pedidoActivo.cliente}</Text>
            </View>
            
            <View style={styles.modalInfo}>
              <Text style={styles.modalLabel}>Teléfono:</Text>
              <Text style={styles.modalValue}>{pedidoActivo.telefono}</Text>
            </View>
            
            <View style={styles.modalInfo}>
              <Text style={styles.modalLabel}>Origen:</Text>
              <Text style={styles.modalValue}>{pedidoActivo.origen.direccion}</Text>
            </View>
            
            <View style={styles.modalInfo}>
              <Text style={styles.modalLabel}>Destino:</Text>
              <Text style={styles.modalValue}>{pedidoActivo.destino.direccion}</Text>
            </View>
            
            <View style={styles.modalInfo}>
              <Text style={styles.modalLabel}>Ganancia:</Text>
              <Text style={styles.modalValue}>{pedidoActivo.ganancia}</Text>
            </View>
            
            <TouchableOpacity
              style={styles.modalCloseButton}
              onPress={() => setModalVisible(false)}
            >
              <Text style={styles.modalCloseText}>Cerrar</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    paddingTop: 20,
    paddingBottom: 20,
    paddingHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    padding: 5,
  },
  backButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: 'white',
    flex: 1,
    textAlign: 'center',
  },
  infoButton: {
    padding: 5,
  },
  infoButtonText: {
    fontSize: 18,
  },
  mapaContainer: {
    flex: 1,
    margin: 10,
    borderRadius: 15,
    overflow: 'hidden',
  },
  mapaSimulado: {
    flex: 1,
    backgroundColor: '#E8F5E8',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  mapaTexto: {
    fontSize: 24,
    fontWeight: '800',
    color: '#2E7D32',
    marginBottom: 10,
  },
  mapaSubtexto: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 30,
  },
  puntosContainer: {
    position: 'absolute',
    top: 50,
    left: 50,
    right: 50,
    bottom: 50,
  },
  punto: {
    backgroundColor: 'white',
    padding: 10,
    borderRadius: 10,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
    marginVertical: 20,
  },
  puntoOrigen: {
    borderLeftWidth: 4,
    borderLeftColor: '#FF6B35',
  },
  puntoActual: {
    borderLeftWidth: 4,
    borderLeftColor: '#2196F3',
  },
  puntoDestino: {
    borderLeftWidth: 4,
    borderLeftColor: '#4CAF50',
  },
  puntoTexto: {
    fontSize: 12,
    fontWeight: '600',
    color: '#2D3748',
  },
  rutaLinea: {
    width: 2,
    height: 30,
    backgroundColor: '#ddd',
    alignSelf: 'center',
    marginVertical: 5,
  },
  infoPanel: {
    backgroundColor: 'white',
    margin: 10,
    padding: 20,
    borderRadius: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  clienteInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  clienteNombre: {
    fontSize: 18,
    fontWeight: '700',
    color: '#2D3748',
  },
  estadoBadge: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FF6B35',
    backgroundColor: '#FFF3E0',
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 10,
  },
  direccionTexto: {
    fontSize: 14,
    color: '#666',
    marginBottom: 15,
    lineHeight: 20,
  },
  detallesRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  detalle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2D3748',
  },
  accionesContainer: {
    padding: 20,
  },
  accionesRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 15,
  },
  accionButton: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 12,
    alignItems: 'center',
    minWidth: 80,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  accionIcon: {
    fontSize: 20,
    marginBottom: 5,
  },
  accionText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#2D3748',
  },
  primaryButton: {
    borderRadius: 15,
    overflow: 'hidden',
    marginTop: 10,
  },
  buttonGradient: {
    paddingVertical: 18,
    alignItems: 'center',
  },
  primaryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '700',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyIcon: {
    fontSize: 80,
    marginBottom: 20,
  },
  emptyText: {
    fontSize: 20,
    fontWeight: '600',
    color: '#2D3748',
    marginBottom: 10,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 30,
  },
  dashboardButton: {
    backgroundColor: '#FF6B35',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 25,
  },
  dashboardButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: 'white',
    margin: 20,
    borderRadius: 20,
    padding: 25,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    minWidth: width * 0.8,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#2D3748',
    marginBottom: 20,
    textAlign: 'center',
  },
  modalInfo: {
    flexDirection: 'row',
    marginBottom: 15,
  },
  modalLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666',
    width: 80,
  },
  modalValue: {
    fontSize: 14,
    color: '#2D3748',
    flex: 1,
  },
  modalCloseButton: {
    backgroundColor: '#FF6B35',
    paddingVertical: 12,
    borderRadius: 10,
    alignItems: 'center',
    marginTop: 20,
  },
  modalCloseText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});
