<?php
/**
 * API para búsqueda de repuestos en tiempo real
 * Sistema AJAX autocompletado estilo AUTODOC
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Manejar preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'db_config.php';

try {
    $pdo = connectDB();

    // Obtener parámetros de búsqueda
    $query = isset($_GET['q']) ? trim($_GET['q']) : '';
    $categoria = isset($_GET['categoria']) ? trim($_GET['categoria']) : '';
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;

    // Validar que hay algo que buscar
    if (empty($query) && empty($categoria)) {
        echo json_encode([
            'success' => false,
            'message' => 'Parámetro de búsqueda requerido',
            'data' => []
        ]);
        exit;
    }

    // Construir consulta SQL (estructura real confirmada)
    $sql = "SELECT
                r.id,
                r.nombre,
                r.descripcion,
                r.categoria,
                r.marca,
                r.modelo,
                r.precio,
                r.stock,
                r.codigo_producto,
                r.estado,
                s.name as proveedor_nombre
            FROM repuestos r
            LEFT JOIN suppliers s ON r.supplier_id = s.id
            WHERE r.estado = 'disponible' AND r.stock > 0";

    $params = [];

    // Agregar filtro de búsqueda por texto
    if (!empty($query)) {
        $sql .= " AND (
            r.nombre LIKE ? OR
            r.descripcion LIKE ? OR
            r.marca LIKE ? OR
            r.modelo LIKE ? OR
            r.codigo_producto LIKE ?
        )";
    }

    // Agregar filtro por categoría
    if (!empty($categoria)) {
        $sql .= " AND r.categoria = ?";
    }

    // Ordenar y limitar resultados
    $sql .= " ORDER BY r.nombre ASC LIMIT ?";

    // Ejecutar consulta (método que funciona)
    $stmt = $pdo->prepare($sql);

    // Ejecutar con array simple
    $executeParams = [];
    if (!empty($query)) {
        // 5 parámetros para la búsqueda
        $queryParam = '%' . $query . '%';
        $executeParams[] = $queryParam;
        $executeParams[] = $queryParam;
        $executeParams[] = $queryParam;
        $executeParams[] = $queryParam;
        $executeParams[] = $queryParam;
    }
    if (!empty($categoria)) {
        $executeParams[] = $categoria;
    }
    $executeParams[] = $limit;

    $stmt->execute($executeParams);
    $repuestos = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Formatear resultados
    $resultados = [];
    foreach ($repuestos as $repuesto) {
        $resultados[] = [
            'id' => (int)$repuesto['id'],
            'nombre' => $repuesto['nombre'],
            'descripcion' => $repuesto['descripcion'],
            'categoria' => $repuesto['categoria'],
            'marca' => $repuesto['marca'],
            'modelo' => $repuesto['modelo'],
            'precio' => (float)$repuesto['precio'],
            'stock' => (int)$repuesto['stock'],
            'codigo' => $repuesto['codigo_producto'],
            'proveedor' => $repuesto['proveedor_nombre'],
            'precio_formateado' => '$' . number_format($repuesto['precio'], 2),
            'disponible' => $repuesto['stock'] > 0,
            'texto_busqueda' => $repuesto['nombre'] . ' ' . $repuesto['marca'] . ' ' . $repuesto['modelo']
        ];
    }

    // Respuesta exitosa
    echo json_encode([
        'success' => true,
        'message' => 'Búsqueda realizada exitosamente',
        'data' => $resultados,
        'total' => count($resultados),
        'query' => $query,
        'categoria' => $categoria
    ]);

} catch (PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error en la base de datos: ' . $e->getMessage(),
        'data' => []
    ]);
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error del servidor: ' . $e->getMessage(),
        'data' => []
    ]);
}
?>
