{"name": "repumovil-delivery-mobile", "version": "1.0.0", "description": "RepuMovil Delivery - App móvil para repartidores", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "eject": "expo eject", "build": "expo build:android", "publish": "expo publish"}, "dependencies": {"expo": "~49.0.0", "react": "18.2.0", "react-native": "0.72.6", "expo-linear-gradient": "~12.3.0", "expo-vector-icons": "^13.0.0", "@react-navigation/native": "^6.1.0", "@react-navigation/stack": "^6.3.0", "@react-navigation/bottom-tabs": "^6.5.0", "react-native-screens": "~3.22.0", "react-native-safe-area-context": "4.6.3", "@react-native-async-storage/async-storage": "1.18.2", "expo-splash-screen": "~0.20.0", "axios": "^1.5.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "@tsconfig/react-native": "^3.0.0", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "engines": {"node": ">=16"}, "keywords": ["react-native", "delivery", "repumovil", "repartidor", "argentina"], "author": "RepuMovil Team", "license": "MIT"}