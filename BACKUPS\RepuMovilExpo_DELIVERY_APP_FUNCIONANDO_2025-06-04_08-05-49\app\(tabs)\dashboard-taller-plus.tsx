import React, { useState } from 'react';
import {
  SafeAreaView,
  StatusBar,
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Modal,
  TextInput,
  Alert,
  FlatList,
  Dimensions,
} from 'react-native';

const { width } = Dimensions.get('window');

interface ChanguitoItem {
  id: string;
  nombre: string;
  precio: number;
  cantidad: number;
}

interface Cliente {
  id: string;
  nombre: string;
  telefono: string;
  vehiculo: string;
  hora: string;
}

interface OrdenTrabajo {
  id: string;
  cliente: string;
  vehiculo: string;
  descripcion: string;
  estado: 'pendiente' | 'en_proceso' | 'urgente';
  precio: number;
}

const repuestosData = [
  { id: '1', nombre: 'Filtro de Aceite', precio: 2500, categoria: 'Filtros', stockStatus: 'alto' },
  { id: '2', nombre: 'Pastillas de Freno', precio: 8500, categoria: 'Frenos', stockStatus: 'medio' },
  { id: '3', nombre: '<PERSON><PERSON><PERSON><PERSON>', precio: 3200, categoria: 'Encendido', stockStatus: 'bajo' },
  { id: '4', nombre: 'Correa de Distribución', precio: 12000, categoria: 'Motor', stockStatus: 'alto' }
];

const clientesHoy: Cliente[] = [
  { id: '1', nombre: 'Juan Pérez', telefono: '+54 9 11 1234-5678', vehiculo: 'Ford Focus 2018', hora: '09:00' },
  { id: '2', nombre: 'María García', telefono: '+54 9 11 8765-4321', vehiculo: 'Toyota Corolla 2020', hora: '11:30' },
  { id: '3', nombre: 'Carlos López', telefono: '+54 9 11 5555-6666', vehiculo: 'Chevrolet Onix 2019', hora: '14:00' },
];

const ordenesData: OrdenTrabajo[] = [
  { id: '1', cliente: 'Ana Martínez', vehiculo: 'Peugeot 208', descripcion: 'Cambio de aceite y filtros', estado: 'pendiente', precio: 8500 },
  { id: '2', cliente: 'Roberto Silva', vehiculo: 'Volkswagen Gol', descripcion: 'Reparación de frenos', estado: 'en_proceso', precio: 15000 },
  { id: '3', cliente: 'Laura Fernández', vehiculo: 'Renault Sandero', descripcion: 'Diagnóstico eléctrico', estado: 'urgente', precio: 12000 },
];

export default function DashboardTallerPlus() {
  const [modalVisible, setModalVisible] = useState(false);
  const [modalType, setModalType] = useState<'repuestos' | 'clientes' | 'ordenes' | 'turnos' | 'registro' | 'detalle_diarios' | 'ingresos_mes' | 'estadisticas' | 'inventario' | 'calificaciones'>('repuestos');
  const [busqueda, setBusqueda] = useState('');
  const [changuito, setChanguito] = useState<ChanguitoItem[]>([]);
  const [metodoPago, setMetodoPago] = useState('efectivo');

  // Estados para registro de cliente
  const [nuevoCliente, setNuevoCliente] = useState({
    nombre: '',
    telefono: '',
    email: '',
    vehiculo: '',
    patente: '',
  });

  const abrirModal = (tipo: typeof modalType) => {
    setModalType(tipo);
    setModalVisible(true);
  };

  const agregarAlChanguito = (repuesto: any) => {
    const existe = changuito.find(item => item.id === repuesto.id);
    if (existe) {
      setChanguito(changuito.map(item =>
        item.id === repuesto.id
          ? { ...item, cantidad: item.cantidad + 1 }
          : item
      ));
    } else {
      setChanguito([...changuito, { ...repuesto, cantidad: 1 }]);
    }
    Alert.alert('✅ Agregado', `${repuesto.nombre} agregado al changuito`);
  };

  const finalizarPedido = () => {
    if (changuito.length === 0) {
      Alert.alert('⚠️ Changuito vacío', 'Agrega repuestos antes de finalizar');
      return;
    }

    const total = changuito.reduce((sum, item) => sum + (item.precio * item.cantidad), 0);
    Alert.alert(
      '🎉 Pedido Realizado',
      `Total: $${total.toLocaleString()}\nMétodo: ${metodoPago}\n\n¡Tu pedido será procesado pronto!`,
      [{ text: 'OK', onPress: () => { setChanguito([]); setModalVisible(false); } }]
    );
  };

  const registrarCliente = () => {
    if (!nuevoCliente.nombre || !nuevoCliente.telefono) {
      Alert.alert('⚠️ Campos requeridos', 'Completa nombre y teléfono');
      return;
    }

    Alert.alert(
      '✅ Cliente Registrado',
      `${nuevoCliente.nombre} ha sido registrado exitosamente.\n\n📱 Se enviará notificación WhatsApp`,
      [{ text: 'OK', onPress: () => {
        setNuevoCliente({ nombre: '', telefono: '', email: '', vehiculo: '', patente: '' });
        setModalVisible(false);
      }}]
    );
  };

  const getEstadoColor = (estado: string) => {
    switch (estado) {
      case 'pendiente': return '#FFA500';
      case 'en_proceso': return '#4CAF50';
      case 'urgente': return '#FF4444';
      default: return '#666';
    }
  };

  const renderRepuesto = ({ item }: { item: any }) => (
    <View style={styles.repuestoItem}>
      <View style={styles.repuestoInfo}>
        <Text style={styles.repuestoNombre}>{item.nombre}</Text>
        <Text style={styles.repuestoCategoria}>{item.categoria}</Text>
        <Text style={styles.repuestoPrecio}>${item.precio.toLocaleString()}</Text>
      </View>
      <TouchableOpacity
        style={styles.btnAgregar}
        onPress={() => agregarAlChanguito(item)}
      >
        <Text style={styles.btnAgregarText}>+</Text>
      </TouchableOpacity>
    </View>
  );

  const renderCliente = ({ item }: { item: Cliente }) => (
    <View style={styles.clienteItem}>
      <View style={styles.clienteInfo}>
        <Text style={styles.clienteNombre}>{item.nombre}</Text>
        <Text style={styles.clienteVehiculo}>{item.vehiculo}</Text>
        <Text style={styles.clienteHora}>🕐 {item.hora}</Text>
      </View>
      <TouchableOpacity style={styles.btnWhatsapp}>
        <Text style={styles.btnWhatsappText}>📱</Text>
      </TouchableOpacity>
    </View>
  );

  const renderOrden = ({ item }: { item: OrdenTrabajo }) => (
    <View style={styles.ordenItem}>
      <View style={styles.ordenInfo}>
        <Text style={styles.ordenCliente}>{item.cliente}</Text>
        <Text style={styles.ordenVehiculo}>{item.vehiculo}</Text>
        <Text style={styles.ordenDescripcion}>{item.descripcion}</Text>
        <View style={styles.ordenFooter}>
          <View style={[styles.estadoBadge, { backgroundColor: getEstadoColor(item.estado) }]}>
            <Text style={styles.estadoText}>{item.estado.toUpperCase()}</Text>
          </View>
          <Text style={styles.ordenPrecio}>${item.precio.toLocaleString()}</Text>
        </View>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#4A148C" />

      {/* Header Premium */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <Text style={styles.logoText}>
            <Text style={styles.logoRepu}>Repu</Text>
            <Text style={styles.logoMovil}>Movil</Text>
            <Text style={styles.logoPlus}> Plus</Text>
          </Text>
          <View style={styles.crownContainer}>
            <Text style={styles.crownIcon}>👑</Text>
          </View>
        </View>
        <Text style={styles.subtitle}>La solución completa para tu taller</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Estadísticas Premium */}
        <View style={styles.statsContainer}>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>5</Text>
            <Text style={styles.statLabel}>Órdenes Pendientes</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>12</Text>
            <Text style={styles.statLabel}>Clientes del Día</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>$45K</Text>
            <Text style={styles.statLabel}>Ingresos del Mes</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>4.9</Text>
            <Text style={styles.statLabel}>Calificación</Text>
          </View>
        </View>

        {/* Funciones Premium */}
        <View style={styles.functionsGrid}>
          {/* Pedido de Repuestos */}
          <TouchableOpacity
            style={[styles.functionCard, styles.functionCardHighlight]}
            onPress={() => abrirModal('repuestos')}
          >
            <Text style={styles.functionIcon}>📋</Text>
            <Text style={styles.functionTitle}>Pedido de Repuestos</Text>
            <Text style={styles.functionSubtitle}>Sistema changuito avanzado</Text>
          </TouchableOpacity>

          {/* Órdenes de Trabajo */}
          <TouchableOpacity
            style={styles.functionCard}
            onPress={() => abrirModal('ordenes')}
          >
            <Text style={styles.functionIcon}>📋</Text>
            <Text style={styles.functionTitle}>Órdenes Pendientes</Text>
            <Text style={styles.functionSubtitle}>Gestión completa</Text>
          </TouchableOpacity>

          {/* Clientes del Día */}
          <TouchableOpacity
            style={styles.functionCard}
            onPress={() => abrirModal('clientes')}
          >
            <Text style={styles.functionIcon}>👥</Text>
            <Text style={styles.functionTitle}>Clientes del Día</Text>
            <Text style={styles.functionSubtitle}>WhatsApp automático</Text>
          </TouchableOpacity>

          {/* Registro de Clientes */}
          <TouchableOpacity
            style={[styles.functionCard, styles.functionCardPremium]}
            onPress={() => abrirModal('registro')}
          >
            <Text style={styles.functionIcon}>👤</Text>
            <Text style={styles.functionTitle}>Registro Clientes</Text>
            <Text style={styles.functionSubtitle}>Gestión Premium</Text>
          </TouchableOpacity>

          {/* Calendario de Turnos */}
          <TouchableOpacity
            style={[styles.functionCard, styles.functionCardPremium]}
            onPress={() => abrirModal('turnos')}
          >
            <Text style={styles.functionIcon}>📅</Text>
            <Text style={styles.functionTitle}>Calendario Turnos</Text>
            <Text style={styles.functionSubtitle}>Planificación avanzada</Text>
          </TouchableOpacity>

          {/* Detalle Diarios */}
          <TouchableOpacity
            style={[styles.functionCard, styles.functionCardPremium]}
            onPress={() => abrirModal('detalle_diarios')}
          >
            <Text style={styles.functionIcon}>💰</Text>
            <Text style={styles.functionTitle}>Detalle Diarios</Text>
            <Text style={styles.functionSubtitle}>Exportación PDF/Excel</Text>
          </TouchableOpacity>

          {/* Ingresos del Mes */}
          <TouchableOpacity
            style={[styles.functionCard, styles.functionCardPremium]}
            onPress={() => abrirModal('ingresos_mes')}
          >
            <Text style={styles.functionIcon}>💰</Text>
            <Text style={styles.functionTitle}>Ingresos del Mes</Text>
            <Text style={styles.functionSubtitle}>Análisis detallado</Text>
          </TouchableOpacity>

          {/* Estadísticas del Taller */}
          <TouchableOpacity
            style={[styles.functionCard, styles.functionCardPremium]}
            onPress={() => abrirModal('estadisticas')}
          >
            <Text style={styles.functionIcon}>📊</Text>
            <Text style={styles.functionTitle}>Estadísticas Taller</Text>
            <Text style={styles.functionSubtitle}>KPIs completos</Text>
          </TouchableOpacity>

          {/* Inventario de Herramientas */}
          <TouchableOpacity
            style={[styles.functionCard, styles.functionCardPremium]}
            onPress={() => abrirModal('inventario')}
          >
            <Text style={styles.functionIcon}>🔧</Text>
            <Text style={styles.functionTitle}>Inventario Herramientas</Text>
            <Text style={styles.functionSubtitle}>Control completo</Text>
          </TouchableOpacity>

          {/* Calificaciones Recibidas */}
          <TouchableOpacity
            style={styles.functionCard}
            onPress={() => abrirModal('calificaciones')}
          >
            <Text style={styles.functionIcon}>⭐</Text>
            <Text style={styles.functionTitle}>Calificaciones</Text>
            <Text style={styles.functionSubtitle}>Sistema avanzado</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* Modal Universal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                {modalType === 'repuestos' && '🛒 Pedido de Repuestos'}
                {modalType === 'clientes' && '👥 Clientes del Día'}
                {modalType === 'ordenes' && '📋 Órdenes de Trabajo'}
                {modalType === 'turnos' && '📅 Calendario de Turnos'}
                {modalType === 'registro' && '👤 Registro de Cliente'}
                {modalType === 'detalle_diarios' && '💰 Detalle Diarios'}
                {modalType === 'ingresos_mes' && '💰 Ingresos del Mes'}
                {modalType === 'estadisticas' && '📊 Estadísticas del Taller'}
                {modalType === 'inventario' && '🔧 Inventario de Herramientas'}
                {modalType === 'calificaciones' && '⭐ Calificaciones Recibidas'}
              </Text>
              <TouchableOpacity onPress={() => setModalVisible(false)}>
                <Text style={styles.closeButton}>✕</Text>
              </TouchableOpacity>
            </View>

            {/* Contenido según tipo */}
            {modalType === 'repuestos' && (
              <View style={styles.modalBody}>
                <TextInput
                  style={styles.searchInput}
                  placeholder="Buscar repuestos..."
                  value={busqueda}
                  onChangeText={setBusqueda}
                />
                <FlatList
                  data={repuestosData.filter(item =>
                    item.nombre.toLowerCase().includes(busqueda.toLowerCase())
                  )}
                  renderItem={renderRepuesto}
                  keyExtractor={item => item.id}
                  style={styles.lista}
                />
                {changuito.length > 0 && (
                  <View style={styles.changuitoResumen}>
                    <Text style={styles.changuitoTitle}>
                      🛒 Changuito ({changuito.length} items)
                    </Text>
                    <Text style={styles.changuitoTotal}>
                      Total: ${changuito.reduce((sum, item) => sum + (item.precio * item.cantidad), 0).toLocaleString()}
                    </Text>
                    <TouchableOpacity style={styles.btnFinalizar} onPress={finalizarPedido}>
                      <Text style={styles.btnFinalizarText}>Finalizar Pedido</Text>
                    </TouchableOpacity>
                  </View>
                )}
              </View>
            )}

            {modalType === 'clientes' && (
              <View style={styles.modalBody}>
                <FlatList
                  data={clientesHoy}
                  renderItem={renderCliente}
                  keyExtractor={item => item.id}
                  style={styles.lista}
                />
              </View>
            )}

            {modalType === 'ordenes' && (
              <View style={styles.modalBody}>
                <FlatList
                  data={ordenesData}
                  renderItem={renderOrden}
                  keyExtractor={item => item.id}
                  style={styles.lista}
                />
              </View>
            )}

            {modalType === 'turnos' && (
              <View style={styles.modalBody}>
                <Text style={styles.comingSoon}>📅 Calendario Interactivo</Text>
                <Text style={styles.comingSoonDesc}>
                  • Navegación por meses{'\n'}
                  • Click en días para ver turnos{'\n'}
                  • Programación automática{'\n'}
                  • Notificaciones WhatsApp
                </Text>
              </View>
            )}

            {modalType === 'registro' && (
              <View style={styles.modalBody}>
                <TextInput
                  style={styles.input}
                  placeholder="Nombre completo *"
                  value={nuevoCliente.nombre}
                  onChangeText={(text) => setNuevoCliente({...nuevoCliente, nombre: text})}
                />
                <TextInput
                  style={styles.input}
                  placeholder="Teléfono *"
                  value={nuevoCliente.telefono}
                  onChangeText={(text) => setNuevoCliente({...nuevoCliente, telefono: text})}
                />
                <TextInput
                  style={styles.input}
                  placeholder="Email"
                  value={nuevoCliente.email}
                  onChangeText={(text) => setNuevoCliente({...nuevoCliente, email: text})}
                />
                <TextInput
                  style={styles.input}
                  placeholder="Vehículo (marca, modelo, año)"
                  value={nuevoCliente.vehiculo}
                  onChangeText={(text) => setNuevoCliente({...nuevoCliente, vehiculo: text})}
                />
                <TextInput
                  style={styles.input}
                  placeholder="Patente"
                  value={nuevoCliente.patente}
                  onChangeText={(text) => setNuevoCliente({...nuevoCliente, patente: text})}
                />
                <TouchableOpacity style={styles.btnRegistrar} onPress={registrarCliente}>
                  <Text style={styles.btnRegistrarText}>✅ Registrar Cliente</Text>
                </TouchableOpacity>
              </View>
            )}

            {modalType === 'detalle_diarios' && (
              <View style={styles.modalBody}>
                <Text style={styles.comingSoon}>💰 Detalle Diarios</Text>
                <View style={styles.detalleContainer}>
                  <View style={styles.detalleItem}>
                    <Text style={styles.detalleLabel}>📅 Fecha:</Text>
                    <Text style={styles.detalleValue}>29 Mayo 2025</Text>
                  </View>
                  <View style={styles.detalleItem}>
                    <Text style={styles.detalleLabel}>💵 Ingresos:</Text>
                    <Text style={styles.detalleValue}>$45,000</Text>
                  </View>
                  <View style={styles.detalleItem}>
                    <Text style={styles.detalleLabel}>🔧 Servicios:</Text>
                    <Text style={styles.detalleValue}>12 trabajos</Text>
                  </View>
                  <View style={styles.detalleItem}>
                    <Text style={styles.detalleLabel}>👥 Clientes:</Text>
                    <Text style={styles.detalleValue}>8 atendidos</Text>
                  </View>
                </View>
                <TouchableOpacity style={styles.btnExportar}>
                  <Text style={styles.btnExportarText}>📄 Exportar PDF</Text>
                </TouchableOpacity>
                <TouchableOpacity style={styles.btnExportar}>
                  <Text style={styles.btnExportarText}>📊 Exportar Excel</Text>
                </TouchableOpacity>
              </View>
            )}

            {modalType === 'ingresos_mes' && (
              <View style={styles.modalBody}>
                <Text style={styles.comingSoon}>💰 Ingresos del Mes</Text>
                <View style={styles.ingresosContainer}>
                  <View style={styles.ingresoCard}>
                    <Text style={styles.ingresoTitulo}>Mayo 2025</Text>
                    <Text style={styles.ingresoMonto}>$450,000</Text>
                    <Text style={styles.ingresoDetalle}>↗️ +15% vs mes anterior</Text>
                  </View>
                  <View style={styles.ingresoCard}>
                    <Text style={styles.ingresoTitulo}>Promedio Diario</Text>
                    <Text style={styles.ingresoMonto}>$15,000</Text>
                    <Text style={styles.ingresoDetalle}>📈 Tendencia positiva</Text>
                  </View>
                  <View style={styles.ingresoCard}>
                    <Text style={styles.ingresoTitulo}>Servicios Top</Text>
                    <Text style={styles.ingresoDetalle}>🔧 Cambio aceite: 45%</Text>
                    <Text style={styles.ingresoDetalle}>🛞 Frenos: 30%</Text>
                    <Text style={styles.ingresoDetalle}>⚡ Eléctrico: 25%</Text>
                  </View>
                </View>
              </View>
            )}

            {modalType === 'estadisticas' && (
              <View style={styles.modalBody}>
                <Text style={styles.comingSoon}>📊 Estadísticas del Taller</Text>
                <View style={styles.estadisticasContainer}>
                  <View style={styles.statRow}>
                    <Text style={styles.statLabel}>🎯 Eficiencia:</Text>
                    <Text style={styles.statValue}>92%</Text>
                  </View>
                  <View style={styles.statRow}>
                    <Text style={styles.statLabel}>⭐ Satisfacción:</Text>
                    <Text style={styles.statValue}>4.9/5</Text>
                  </View>
                  <View style={styles.statRow}>
                    <Text style={styles.statLabel}>🔄 Clientes Recurrentes:</Text>
                    <Text style={styles.statValue}>78%</Text>
                  </View>
                  <View style={styles.statRow}>
                    <Text style={styles.statLabel}>⏱️ Tiempo Promedio:</Text>
                    <Text style={styles.statValue}>2.5 hrs</Text>
                  </View>
                  <View style={styles.statRow}>
                    <Text style={styles.statLabel}>💰 Ticket Promedio:</Text>
                    <Text style={styles.statValue}>$3,750</Text>
                  </View>
                </View>
              </View>
            )}

            {modalType === 'inventario' && (
              <View style={styles.modalBody}>
                <Text style={styles.comingSoon}>🔧 Inventario de Herramientas</Text>
                <View style={styles.inventarioContainer}>
                  <View style={styles.inventarioItem}>
                    <Text style={styles.inventarioNombre}>🔧 Llaves Combinadas</Text>
                    <Text style={styles.inventarioEstado}>✅ Disponible</Text>
                  </View>
                  <View style={styles.inventarioItem}>
                    <Text style={styles.inventarioNombre}>🔩 Pistola Neumática</Text>
                    <Text style={styles.inventarioEstado}>✅ Disponible</Text>
                  </View>
                  <View style={styles.inventarioItem}>
                    <Text style={styles.inventarioNombre}>⚡ Scanner OBD</Text>
                    <Text style={styles.inventarioEstado}>🔄 En uso</Text>
                  </View>
                  <View style={styles.inventarioItem}>
                    <Text style={styles.inventarioNombre}>🔋 Multímetro</Text>
                    <Text style={styles.inventarioEstado}>⚠️ Mantenimiento</Text>
                  </View>
                </View>
                <TouchableOpacity style={styles.btnInventario}>
                  <Text style={styles.btnInventarioText}>➕ Agregar Herramienta</Text>
                </TouchableOpacity>
              </View>
            )}

            {modalType === 'calificaciones' && (
              <View style={styles.modalBody}>
                <Text style={styles.comingSoon}>⭐ Calificaciones Recibidas</Text>
                <View style={styles.calificacionesContainer}>
                  <View style={styles.calificacionItem}>
                    <Text style={styles.clienteCalif}>Juan Pérez</Text>
                    <Text style={styles.estrellas}>⭐⭐⭐⭐⭐</Text>
                    <Text style={styles.comentario}>"Excelente servicio, muy profesional"</Text>
                  </View>
                  <View style={styles.calificacionItem}>
                    <Text style={styles.clienteCalif}>María García</Text>
                    <Text style={styles.estrellas}>⭐⭐⭐⭐⭐</Text>
                    <Text style={styles.comentario}>"Rápido y confiable, lo recomiendo"</Text>
                  </View>
                  <View style={styles.calificacionItem}>
                    <Text style={styles.clienteCalif}>Carlos López</Text>
                    <Text style={styles.estrellas}>⭐⭐⭐⭐</Text>
                    <Text style={styles.comentario}>"Buen trabajo, precios justos"</Text>
                  </View>
                </View>
                <View style={styles.promedioContainer}>
                  <Text style={styles.promedioText}>Promedio General: ⭐ 4.9/5</Text>
                  <Text style={styles.totalReviews}>Total: 127 reseñas</Text>
                </View>
              </View>
            )}
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#4A148C',
    paddingTop: 20,
    paddingBottom: 25,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  logoText: {
    fontSize: 28,
    fontWeight: '900',
  },
  logoRepu: {
    color: '#FF6B35',
  },
  logoMovil: {
    color: '#FFE4B5',
  },
  logoPlus: {
    color: '#FFD700',
  },
  crownContainer: {
    backgroundColor: 'rgba(255, 215, 0, 0.2)',
    borderRadius: 20,
    padding: 8,
  },
  crownIcon: {
    fontSize: 24,
  },
  subtitle: {
    color: 'rgba(255, 255, 255, 0.9)',
    fontSize: 16,
    textAlign: 'center',
  },
  content: {
    flex: 1,
    paddingHorizontal: 15,
  },
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: 20,
    marginBottom: 25,
  },
  statCard: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 15,
    width: (width - 45) / 2,
    marginBottom: 10,
    alignItems: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: '900',
    color: '#4A148C',
    marginBottom: 5,
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  functionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingBottom: 30,
  },
  functionCard: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 20,
    width: (width - 45) / 2,
    marginBottom: 15,
    alignItems: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
  },
  functionCardHighlight: {
    borderWidth: 2,
    borderColor: '#4CAF50',
    backgroundColor: '#f8fff8',
  },
  functionCardPremium: {
    borderWidth: 2,
    borderColor: '#FFD700',
    backgroundColor: '#fffbf0',
  },
  functionIcon: {
    fontSize: 40,
    marginBottom: 10,
  },
  functionTitle: {
    fontSize: 14,
    fontWeight: '700',
    color: '#333',
    textAlign: 'center',
    marginBottom: 5,
  },
  functionSubtitle: {
    fontSize: 11,
    color: '#666',
    textAlign: 'center',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 20,
    width: width * 0.9,
    maxHeight: '80%',
    elevation: 10,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#333',
  },
  closeButton: {
    fontSize: 24,
    color: '#666',
    fontWeight: 'bold',
  },
  modalBody: {
    padding: 20,
  },
  searchInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    padding: 12,
    marginBottom: 15,
    fontSize: 16,
  },
  lista: {
    maxHeight: 300,
  },
  repuestoItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  repuestoInfo: {
    flex: 1,
  },
  repuestoNombre: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  repuestoCategoria: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  repuestoPrecio: {
    fontSize: 14,
    fontWeight: '700',
    color: '#4CAF50',
    marginTop: 4,
  },
  btnAgregar: {
    backgroundColor: '#4CAF50',
    borderRadius: 20,
    width: 35,
    height: 35,
    justifyContent: 'center',
    alignItems: 'center',
  },
  btnAgregarText: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
  },
  clienteItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  clienteInfo: {
    flex: 1,
  },
  clienteNombre: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  clienteVehiculo: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  clienteHora: {
    fontSize: 14,
    color: '#4CAF50',
    marginTop: 4,
  },
  btnWhatsapp: {
    backgroundColor: '#25D366',
    borderRadius: 20,
    width: 35,
    height: 35,
    justifyContent: 'center',
    alignItems: 'center',
  },
  btnWhatsappText: {
    fontSize: 16,
  },
  ordenItem: {
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  ordenInfo: {
    flex: 1,
  },
  ordenCliente: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  ordenVehiculo: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  ordenDescripcion: {
    fontSize: 14,
    color: '#333',
    marginTop: 4,
  },
  ordenFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  estadoBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  estadoText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
  },
  ordenPrecio: {
    fontSize: 14,
    fontWeight: '700',
    color: '#4CAF50',
  },
  changuitoResumen: {
    backgroundColor: '#f8f9fa',
    borderRadius: 10,
    padding: 15,
    marginTop: 15,
  },
  changuitoTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 5,
  },
  changuitoTotal: {
    fontSize: 18,
    fontWeight: '700',
    color: '#4CAF50',
    marginBottom: 10,
  },
  btnFinalizar: {
    backgroundColor: '#4CAF50',
    borderRadius: 10,
    padding: 12,
    alignItems: 'center',
  },
  btnFinalizarText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '700',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    padding: 12,
    marginBottom: 15,
    fontSize: 16,
  },
  btnRegistrar: {
    backgroundColor: '#FFD700',
    borderRadius: 10,
    padding: 15,
    alignItems: 'center',
    marginTop: 10,
  },
  btnRegistrarText: {
    color: '#333',
    fontSize: 16,
    fontWeight: '700',
  },
  comingSoon: {
    fontSize: 20,
    fontWeight: '700',
    textAlign: 'center',
    color: '#4A148C',
    marginBottom: 15,
  },
  comingSoonDesc: {
    fontSize: 16,
    color: '#666',
    lineHeight: 24,
    textAlign: 'center',
  },
  // Estilos para Detalle Diarios
  detalleContainer: {
    marginBottom: 20,
  },
  detalleItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 15,
    backgroundColor: '#f8f9fa',
    borderRadius: 10,
    marginBottom: 10,
  },
  detalleLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  detalleValue: {
    fontSize: 16,
    fontWeight: '700',
    color: '#4A148C',
  },
  btnExportar: {
    backgroundColor: '#4A148C',
    borderRadius: 10,
    padding: 12,
    alignItems: 'center',
    marginBottom: 10,
  },
  btnExportarText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  // Estilos para Ingresos del Mes
  ingresosContainer: {
    marginBottom: 20,
  },
  ingresoCard: {
    backgroundColor: '#f8f9fa',
    borderRadius: 15,
    padding: 20,
    marginBottom: 15,
    borderLeftWidth: 4,
    borderLeftColor: '#FFD700',
  },
  ingresoTitulo: {
    fontSize: 16,
    fontWeight: '700',
    color: '#333',
    marginBottom: 8,
  },
  ingresoMonto: {
    fontSize: 24,
    fontWeight: '900',
    color: '#4A148C',
    marginBottom: 5,
  },
  ingresoDetalle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 3,
  },
  // Estilos para Estadísticas
  estadisticasContainer: {
    marginBottom: 20,
  },
  statRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 15,
    backgroundColor: '#f8f9fa',
    borderRadius: 10,
    marginBottom: 10,
  },
  statValue: {
    fontSize: 16,
    fontWeight: '700',
    color: '#4A148C',
  },
  // Estilos para Inventario
  inventarioContainer: {
    marginBottom: 20,
  },
  inventarioItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 15,
    backgroundColor: '#f8f9fa',
    borderRadius: 10,
    marginBottom: 10,
  },
  inventarioNombre: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    flex: 1,
  },
  inventarioEstado: {
    fontSize: 14,
    fontWeight: '600',
  },
  btnInventario: {
    backgroundColor: '#FFD700',
    borderRadius: 10,
    padding: 12,
    alignItems: 'center',
  },
  btnInventarioText: {
    color: '#333',
    fontSize: 14,
    fontWeight: '600',
  },
  // Estilos para Calificaciones
  calificacionesContainer: {
    marginBottom: 20,
  },
  calificacionItem: {
    backgroundColor: '#f8f9fa',
    borderRadius: 10,
    padding: 15,
    marginBottom: 10,
  },
  clienteCalif: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 5,
  },
  estrellas: {
    fontSize: 16,
    marginBottom: 8,
  },
  comentario: {
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
  },
  promedioContainer: {
    backgroundColor: '#4A148C',
    borderRadius: 10,
    padding: 15,
    alignItems: 'center',
  },
  promedioText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '700',
    marginBottom: 5,
  },
  totalReviews: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
  },
});
