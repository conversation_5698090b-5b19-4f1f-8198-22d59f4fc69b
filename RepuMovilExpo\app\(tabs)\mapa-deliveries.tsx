import React, { useState, useEffect } from 'react';
import {
  SafeAreaView,
  ScrollView,
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import MapView, { <PERSON><PERSON>, <PERSON>yline } from 'react-native-maps';

interface DeliveryPerson {
  id: number;
  name: string;
  rating: number;
  status: 'online' | 'available' | 'busy';
  lat: number;
  lng: number;
  distance: number;
}

interface Pedido {
  id: number;
  estado: 'pendiente' | 'preparando' | 'empacado' | 'asignado' | 'en_camino' | 'entregado';
  cliente_nombre: string;
  direccion_entrega: string;
  repartidor_nombre?: string;
  total: number;
  created_at: string;
  lat: number;
  lng: number;
}

export default function MapaDeliveriesScreen() {
  const router = useRouter();
  const [pedidos, setPedidos] = useState<Pedido[]>([]);
  const [deliveries, setDeliveries] = useState<DeliveryPerson[]>([]);
  const [selectedPedido, setSelectedPedido] = useState<Pedido | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [trackingEnabled, setTrackingEnabled] = useState(false);

  // Coordenadas de San Juan, Argentina
  const sanJuanCenter = {
    latitude: -31.5375,
    longitude: -68.5364,
    latitudeDelta: 0.05,
    longitudeDelta: 0.05,
  };

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      
      // Simular carga de datos
      setTimeout(() => {
        // Pedidos de ejemplo
        setPedidos([
          {
            id: 1001,
            estado: 'empacado',
            cliente_nombre: 'Juan Pérez',
            direccion_entrega: 'Av. Libertador 1234',
            total: 13500,
            created_at: '2025-01-02T14:30:00Z',
            lat: -31.5355,
            lng: -68.5384
          },
          {
            id: 1002,
            estado: 'asignado',
            cliente_nombre: 'María González',
            direccion_entrega: 'Calle San Martín 567',
            repartidor_nombre: 'Carlos Rodríguez',
            total: 8200,
            created_at: '2025-01-02T15:15:00Z',
            lat: -31.5395,
            lng: -68.5344
          }
        ]);

        // Deliveries cercanos
        setDeliveries([
          { id: 1, name: 'Juan Pérez', rating: 4.8, status: 'online', lat: -31.5355, lng: -68.5384, distance: 1.2 },
          { id: 2, name: 'María González', rating: 4.9, status: 'available', lat: -31.5385, lng: -68.5344, distance: 2.1 },
          { id: 3, name: 'Carlos Rodríguez', rating: 4.7, status: 'busy', lat: -31.5395, lng: -68.5354, distance: 3.5 },
          { id: 4, name: 'Ana López', rating: 4.6, status: 'online', lat: -31.5345, lng: -68.5394, distance: 4.2 },
        ]);

        setLoading(false);
      }, 1000);
    } catch (error) {
      console.error('Error loading data:', error);
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  const toggleTracking = () => {
    setTrackingEnabled(!trackingEnabled);
    Alert.alert(
      trackingEnabled ? 'Tracking Desactivado' : 'Tracking Activado',
      trackingEnabled 
        ? 'Se detuvo el seguimiento en tiempo real' 
        : 'Ahora verás las posiciones actualizadas cada 5 segundos',
      [{ text: 'OK' }]
    );
  };

  const simulateAutoAssignment = (pedidoId: number) => {
    const availableDelivery = deliveries.find(d => d.status === 'online' || d.status === 'available');
    
    if (availableDelivery) {
      Alert.alert(
        '🚀 Asignación Automática',
        `Pedido #${pedidoId} asignado a ${availableDelivery.name}\n\n📍 Distancia: ${availableDelivery.distance} km\n⏱️ ETA: ${Math.floor(Math.random() * 20) + 15} min`,
        [
          { text: 'Ver Tracking', onPress: () => setSelectedPedido(pedidos.find(p => p.id === pedidoId) || null) },
          { text: 'OK' }
        ]
      );

      // Actualizar estado
      setPedidos(prev => prev.map(p => 
        p.id === pedidoId 
          ? { ...p, estado: 'asignado', repartidor_nombre: availableDelivery.name }
          : p
      ));

      setDeliveries(prev => prev.map(d => 
        d.id === availableDelivery.id 
          ? { ...d, status: 'busy' }
          : d
      ));
    }
  };

  const getMarkerColor = (status: string) => {
    switch (status) {
      case 'online': return '#28a745';
      case 'available': return '#17a2b8';
      case 'busy': return '#ffc107';
      default: return '#6c757d';
    }
  };

  const getEstadoColor = (estado: string) => {
    switch (estado) {
      case 'empacado': return '#28a745';
      case 'asignado': return '#17a2b8';
      case 'en_camino': return '#ffc107';
      case 'entregado': return '#6f42c1';
      default: return '#6c757d';
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#FF6B35" />
          <Text style={styles.loadingText}>Cargando mapa de deliveries...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="white" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Mapa Deliveries 🗺️</Text>
        <TouchableOpacity onPress={toggleTracking} style={[styles.trackingButton, trackingEnabled && styles.trackingActive]}>
          <Ionicons name={trackingEnabled ? "radio-button-on" : "radio-button-off"} size={24} color="white" />
        </TouchableOpacity>
      </View>

      <View style={styles.content}>
        {/* Mapa */}
        <View style={styles.mapContainer}>
          <MapView
            style={styles.map}
            initialRegion={sanJuanCenter}
            showsUserLocation={true}
            showsMyLocationButton={true}
          >
            {/* Marker del proveedor */}
            <Marker
              coordinate={{ latitude: -31.5375, longitude: -68.5364 }}
              title="RepuMovil Proveedor"
              description="Taller principal"
              pinColor="#FF6B35"
            />

            {/* Markers de pedidos */}
            {pedidos.map(pedido => (
              <Marker
                key={`pedido-${pedido.id}`}
                coordinate={{ latitude: pedido.lat, longitude: pedido.lng }}
                title={`Pedido #${pedido.id}`}
                description={`${pedido.cliente_nombre} - $${pedido.total}`}
                pinColor={getEstadoColor(pedido.estado)}
                onPress={() => setSelectedPedido(pedido)}
              />
            ))}

            {/* Markers de deliveries */}
            {deliveries.map(delivery => (
              <Marker
                key={`delivery-${delivery.id}`}
                coordinate={{ latitude: delivery.lat, longitude: delivery.lng }}
                title={delivery.name}
                description={`⭐ ${delivery.rating} - ${delivery.status}`}
                pinColor={getMarkerColor(delivery.status)}
              />
            ))}

            {/* Ruta si hay pedido seleccionado y asignado */}
            {selectedPedido && selectedPedido.repartidor_nombre && (
              <Polyline
                coordinates={[
                  { latitude: -31.5375, longitude: -68.5364 }, // Proveedor
                  { latitude: selectedPedido.lat, longitude: selectedPedido.lng } // Cliente
                ]}
                strokeColor="#FF6B35"
                strokeWidth={4}
                strokePattern={[1]}
              />
            )}
          </MapView>
        </View>

        {/* Panel inferior */}
        <ScrollView 
          style={styles.bottomPanel}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} colors={['#FF6B35']} />
          }
        >
          {/* Pedidos activos */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>
              <Ionicons name="list" size={20} color="#2c3e50" />
              {' '}Pedidos Activos ({pedidos.length})
            </Text>
            
            {pedidos.map(pedido => (
              <TouchableOpacity 
                key={pedido.id} 
                style={[styles.pedidoCard, selectedPedido?.id === pedido.id && styles.selectedCard]}
                onPress={() => setSelectedPedido(pedido)}
              >
                <View style={styles.pedidoHeader}>
                  <Text style={styles.pedidoId}>Pedido #{pedido.id}</Text>
                  <View style={[styles.estadoBadge, { backgroundColor: getEstadoColor(pedido.estado) }]}>
                    <Text style={styles.estadoText}>
                      {pedido.estado === 'empacado' ? '✅ Empacado' :
                       pedido.estado === 'asignado' ? '🚀 Asignado' :
                       pedido.estado === 'en_camino' ? '🏍️ En Camino' : pedido.estado}
                    </Text>
                  </View>
                </View>
                
                <Text style={styles.pedidoCliente}>{pedido.cliente_nombre}</Text>
                <Text style={styles.pedidoDireccion}>{pedido.direccion_entrega}</Text>
                
                {pedido.estado === 'empacado' && !pedido.repartidor_nombre && (
                  <TouchableOpacity 
                    style={styles.assignButton}
                    onPress={() => simulateAutoAssignment(pedido.id)}
                  >
                    <Ionicons name="flash" size={16} color="white" />
                    <Text style={styles.assignButtonText}>Asignar Automáticamente</Text>
                  </TouchableOpacity>
                )}
                
                {pedido.repartidor_nombre && (
                  <View style={styles.deliveryInfo}>
                    <Text style={styles.deliveryText}>🏍️ {pedido.repartidor_nombre}</Text>
                    <Text style={styles.etaText}>⏱️ ETA: {Math.floor(Math.random() * 20) + 15} min</Text>
                  </View>
                )}
              </TouchableOpacity>
            ))}
          </View>

          {/* Deliveries cercanos */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>
              <Ionicons name="bicycle" size={20} color="#2c3e50" />
              {' '}Deliveries Cercanos
            </Text>
            
            {deliveries.map(delivery => (
              <View key={delivery.id} style={styles.deliveryCard}>
                <View style={styles.deliveryHeader}>
                  <Text style={styles.deliveryName}>{delivery.name}</Text>
                  <View style={[styles.statusBadge, { backgroundColor: getMarkerColor(delivery.status) }]}>
                    <Text style={styles.statusText}>
                      {delivery.status === 'online' ? '🏍️ En línea' :
                       delivery.status === 'available' ? '🏍️ Disponible' : '🚫 Ocupado'}
                    </Text>
                  </View>
                </View>
                
                <View style={styles.deliveryStats}>
                  <Text style={styles.deliveryStat}>⭐ {delivery.rating}</Text>
                  <Text style={styles.deliveryStat}>📍 {delivery.distance} km</Text>
                </View>
              </View>
            ))}
          </View>
        </ScrollView>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f7fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#FF6B35',
    paddingHorizontal: 20,
    paddingVertical: 15,
    paddingTop: 50,
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
  },
  trackingButton: {
    padding: 5,
  },
  trackingActive: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    borderRadius: 20,
  },
  content: {
    flex: 1,
  },
  mapContainer: {
    height: '50%',
  },
  map: {
    flex: 1,
  },
  bottomPanel: {
    flex: 1,
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    marginTop: -20,
    paddingTop: 20,
  },
  section: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 15,
  },
  pedidoCard: {
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: 15,
    marginBottom: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#FF6B35',
  },
  selectedCard: {
    backgroundColor: '#fff3e0',
    borderLeftColor: '#FFA500',
  },
  pedidoHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  pedidoId: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  estadoBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  estadoText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  pedidoCliente: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#495057',
    marginBottom: 4,
  },
  pedidoDireccion: {
    fontSize: 12,
    color: '#6c757d',
    marginBottom: 8,
  },
  assignButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#28a745',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    alignSelf: 'flex-start',
    gap: 5,
  },
  assignButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  deliveryInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 107, 53, 0.1)',
    padding: 8,
    borderRadius: 6,
  },
  deliveryText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#FF6B35',
  },
  etaText: {
    fontSize: 12,
    color: '#666',
  },
  deliveryCard: {
    backgroundColor: '#f8f9fa',
    borderRadius: 10,
    padding: 12,
    marginBottom: 8,
  },
  deliveryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  deliveryName: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
  },
  deliveryStats: {
    flexDirection: 'row',
    gap: 15,
  },
  deliveryStat: {
    fontSize: 12,
    color: '#666',
  },
});
