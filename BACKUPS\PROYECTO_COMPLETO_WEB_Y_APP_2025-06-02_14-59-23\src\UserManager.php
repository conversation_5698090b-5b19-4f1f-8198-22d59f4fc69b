<?php

namespace Src;

/**
 * Clase UserManager compatible con el sistema antiguo
 * Esta clase ha sido adaptada para funcionar con el sistema basado en MySQL
 */
class UserManager {
    private $users = [];
    private $conn;

    public function __construct() {
        // Configuración de la base de datos
        $host = 'localhost';
        $dbname = 'autoconnect_db';
        $username = 'root';
        $password = '';

        try {
            $this->conn = new \PDO("mysql:host=$host;dbname=$dbname", $username, $password);
            $this->conn->setAttribute(\PDO::ATTR_ERRMODE, \PDO::ERRMODE_EXCEPTION);

            // Cargar usuarios desde la base de datos
            $this->loadUsers();
        } catch (\PDOException $e) {
            // Si la base de datos no existe, intentar crearla
            try {
                $conn = new \PDO("mysql:host=$host", $username, $password);
                $conn->setAttribute(\PDO::ATTR_ERRMODE, \PDO::ERRMODE_EXCEPTION);

                // Crear la base de datos
                $conn->exec("CREATE DATABASE IF NOT EXISTS $dbname");

                // Conectar a la base de datos recién creada
                $this->conn = new \PDO("mysql:host=$host;dbname=$dbname", $username, $password);
                $this->conn->setAttribute(\PDO::ATTR_ERRMODE, \PDO::ERRMODE_EXCEPTION);

                // Inicializar la base de datos
                $this->initializeDB();

                // Cargar usuarios desde la base de datos
                $this->loadUsers();
            } catch (\PDOException $e) {
                die("Error de conexión: " . $e->getMessage());
            }
        }
    }

    public function loadUsers() {
        $this->users = [];

        $query = "SELECT u.*, r.name as role_name
                 FROM users u
                 JOIN roles r ON u.role_id = r.id";

        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $dbUsers = $stmt->fetchAll(\PDO::FETCH_ASSOC);

        foreach ($dbUsers as $dbUser) {
            $user = new User(
                $dbUser['id'],
                $dbUser['username'],
                $dbUser['email'],
                $dbUser['role_name']
            );

            $user->setCreatedAt($dbUser['created_at']);
            $user->setLastLogin($dbUser['last_login']);
            $user->setStatus($dbUser['status']);

            $this->users[] = $user;
        }
    }

    public function saveUsers() {
        // No es necesario implementar esta función ya que los cambios se guardan directamente en la base de datos
        return true;
    }

    public function createSampleUsers() {
        // No es necesario implementar esta función ya que los usuarios de ejemplo se crean en initializeDB
        return true;
    }

    public function createUser($username, $email, $password, $userType) {
        // Verificar si el nombre de usuario ya existe
        if ($this->usernameExists($username)) {
            return false;
        }

        // Verificar si el correo ya existe
        if ($this->emailExists($email)) {
            return false;
        }

        // Obtener el ID del rol
        $roleQuery = "SELECT id FROM roles WHERE name = :name";
        $roleStmt = $this->conn->prepare($roleQuery);
        $roleStmt->execute(['name' => $userType]);
        $roleId = $roleStmt->fetchColumn();

        if (!$roleId) {
            return false;
        }

        // Crear usuario en la base de datos
        $query = "INSERT INTO users (username, email, password, role_id) VALUES (:username, :email, :password, :role_id)";
        $stmt = $this->conn->prepare($query);
        $result = $stmt->execute([
            'username' => $username,
            'email' => $email,
            'password' => password_hash($password, PASSWORD_DEFAULT),
            'role_id' => $roleId
        ]);

        if ($result) {
            $userId = $this->conn->lastInsertId();

            // Crear objeto User
            $user = new User($userId, $username, $email, $userType);
            $user->setCreatedAt();

            // Agregar a la lista de usuarios
            $this->users[] = $user;

            return $user;
        }

        return false;
    }

    public function usernameExists($username) {
        $query = "SELECT COUNT(*) FROM users WHERE username = :username";
        $stmt = $this->conn->prepare($query);
        $stmt->execute(['username' => $username]);
        return $stmt->fetchColumn() > 0;
    }

    public function emailExists($email) {
        $query = "SELECT COUNT(*) FROM users WHERE email = :email";
        $stmt = $this->conn->prepare($query);
        $stmt->execute(['email' => $email]);
        return $stmt->fetchColumn() > 0;
    }

    public function authenticateUser($username, $password) {
        $query = "SELECT u.*, r.name as role_name
                 FROM users u
                 JOIN roles r ON u.role_id = r.id
                 WHERE u.username = :username AND u.status = 'active'";

        $stmt = $this->conn->prepare($query);
        $stmt->execute(['username' => $username]);
        $user = $stmt->fetch(\PDO::FETCH_ASSOC);

        if ($user && password_verify($password, $user['password'])) {
            // Actualizar último inicio de sesión
            $updateQuery = "UPDATE users SET last_login = NOW() WHERE id = :id";
            $updateStmt = $this->conn->prepare($updateQuery);
            $updateStmt->execute(['id' => $user['id']]);

            // Crear objeto User
            $userObj = new User(
                $user['id'],
                $user['username'],
                $user['email'],
                $user['role_name']
            );

            $userObj->setCreatedAt($user['created_at']);
            $userObj->setLastLogin(date('Y-m-d H:i:s'));
            $userObj->setStatus($user['status']);

            return $userObj;
        }

        return null;
    }

    public function getUserById($userId) {
        $query = "SELECT u.*, r.name as role_name
                 FROM users u
                 JOIN roles r ON u.role_id = r.id
                 WHERE u.id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->execute(['id' => $userId]);
        $user = $stmt->fetch(\PDO::FETCH_ASSOC);

        if ($user) {
            $userObj = new User(
                $user['id'],
                $user['username'],
                $user['email'],
                $user['role_name']
            );

            $userObj->setCreatedAt($user['created_at']);
            $userObj->setLastLogin($user['last_login']);
            $userObj->setStatus($user['status']);

            return $userObj;
        }

        return null;
    }

    private function initializeDB() {
        // Crear tabla de roles
        $this->conn->exec("CREATE TABLE IF NOT EXISTS roles (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(50) NOT NULL,
            description VARCHAR(255)
        )");

        // Crear tabla de usuarios
        $this->conn->exec("CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) NOT NULL UNIQUE,
            email VARCHAR(100) NOT NULL UNIQUE,
            password VARCHAR(255) NOT NULL,
            role_id INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_login TIMESTAMP NULL,
            status ENUM('active', 'root', 'suspended') DEFAULT 'active',
            FOREIGN KEY (role_id) REFERENCES roles(id)
        )");

        // Crear tabla de talleres
        $this->conn->exec("CREATE TABLE IF NOT EXISTS workshops (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            name VARCHAR(100) NOT NULL,
            location VARCHAR(255),
            phone VARCHAR(20),
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )");

        // Crear tabla de proveedores
        $this->conn->exec("CREATE TABLE IF NOT EXISTS suppliers (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            name VARCHAR(100) NOT NULL,
            location VARCHAR(255),
            phone VARCHAR(20),
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )");

        // Verificar si ya existen roles
        $stmt = $this->conn->query("SELECT COUNT(*) FROM roles");
        $roleCount = $stmt->fetchColumn();

        if ($roleCount == 0) {
            // Insertar roles
            $this->conn->exec("INSERT INTO roles (name, description) VALUES
                ('root', 'Administrador del sistema'),
                ('root', 'Usuario de taller mecánico'),
                ('root', 'Proveedor de repuestos')");
        }

        // Verificar si ya existe el usuario administrador
        $stmt = $this->conn->query("SELECT COUNT(*) FROM users WHERE username = 'admin'");
        $adminCount = $stmt->fetchColumn();

        if ($adminCount == 0) {
            // Insertar usuario administrador
            // Contraseña: admin123
            $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
            $stmt = $this->conn->prepare("INSERT INTO users (username, email, password, role_id) VALUES
                ('admin', '<EMAIL>', :password, 1)");
            $stmt->execute(['password' => $adminPassword]);
        }

        // Verificar si ya existe el usuario de taller de ejemplo
        $stmt = $this->conn->query("SELECT COUNT(*) FROM users WHERE username = 'taller1'");
        $workshopCount = $stmt->fetchColumn();

        if ($workshopCount == 0) {
            // Insertar usuario de taller de ejemplo
            // Contraseña: taller123
            $workshopPassword = password_hash('taller123', PASSWORD_DEFAULT);

            // Iniciar transacción
            $this->conn->beginTransaction();

            try {
                // Insertar usuario
                $stmt = $this->conn->prepare("INSERT INTO users (username, email, password, role_id) VALUES
                    ('taller1', '<EMAIL>', :password, 2)");
                $stmt->execute(['password' => $workshopPassword]);
                $workshopUserId = $this->conn->lastInsertId();

                // Insertar datos del taller
                $stmt = $this->conn->prepare("INSERT INTO workshops (user_id, name, location, phone, description) VALUES
                    (:user_id, 'Taller Mecánico Ejemplo', 'Calle Ejemplo 123, Ciudad', '************',
                    'Taller especializado en reparación de motores y sistemas de frenos')");
                $stmt->execute(['user_id' => $workshopUserId]);

                $this->conn->commit();
            } catch (\Exception $e) {
                $this->conn->rollBack();
                throw $e;
            }
        }
    }
}
