<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RepuMovil Plus - Dashboard Mecánico Empresario</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #1A237E 0%, #3F51B5 50%, #FFD700 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .navbar-custom {
            background: linear-gradient(135deg, #1A237E 0%, #283593 100%);
            padding: 15px 0;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            position: relative;
            overflow: hidden;
        }

        .navbar-custom::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 215, 0, 0.1), transparent);
            animation: shine 3s infinite;
        }

        @keyframes shine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .logo-text {
            font-size: 32px;
            font-weight: 900;
            color: white;
            position: relative;
            z-index: 1;
        }

        .logo-repu {
            color: #3F51B5;
        }

        .logo-movil {
            color: #E8EAF6;
        }

        .logo-plus {
            color: #FFD700;
            margin-left: 8px;
        }

        .crown-icon {
            color: #FFD700;
            font-size: 28px;
            margin-left: 10px;
            position: relative;
            z-index: 1;
        }

        .welcome-section {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 251, 240, 0.95) 100%);
            border-radius: 25px;
            padding: 30px;
            margin: 25px 0;
            text-align: center;
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
            border: 3px solid #FFD700;
        }

        .welcome-title {
            background: linear-gradient(135deg, #1A237E, #3F51B5, #FFD700);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 28px;
            font-weight: 900;
            margin-bottom: 15px;
        }

        .welcome-subtitle {
            color: #5D4037;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 0;
        }

        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .stat-card {
            background: white;
            border-radius: 20px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .stat-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 50px rgba(0,0,0,0.2);
            border-color: #FFD700;
        }

        .stat-number {
            font-size: 36px;
            font-weight: 900;
            background: linear-gradient(135deg, #1A237E, #FFD700);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #5D4037;
            font-size: 14px;
            font-weight: 600;
        }

        .functions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }

        .function-card {
            background: white;
            border-radius: 25px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 15px 40px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .function-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 25px 60px rgba(0,0,0,0.2);
            border-color: #FFD700;
        }

        .function-card-premium {
            border: 3px solid #FFD700;
            background: linear-gradient(135deg, #ffffff 0%, #fffbf0 100%);
        }

        .function-card-premium::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 215, 0, 0.1), transparent);
            animation: shine 3s infinite;
        }

        .function-icon {
            font-size: 56px;
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
        }

        .function-icon-premium {
            color: #FFD700;
        }

        .function-icon-basic {
            color: #3F51B5;
        }

        .function-title {
            font-size: 20px;
            font-weight: 700;
            color: #1A237E;
            margin-bottom: 12px;
            position: relative;
            z-index: 1;
        }

        .function-description {
            color: #5D4037;
            font-size: 14px;
            line-height: 1.6;
            position: relative;
            z-index: 1;
        }

        .premium-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background: #FFD700;
            color: #1A237E;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 700;
            z-index: 2;
        }

        .container-custom {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .modal-custom .modal-content {
            border-radius: 25px;
            border: 3px solid #FFD700;
            box-shadow: 0 25px 80px rgba(0,0,0,0.3);
        }

        .modal-custom .modal-header {
            background: linear-gradient(135deg, #1A237E 0%, #3F51B5 50%, #FFD700 100%);
            color: white;
            border-radius: 22px 22px 0 0;
            padding: 25px 30px;
        }

        .modal-custom .modal-title {
            font-weight: 900;
            font-size: 22px;
        }

        .empresario-section {
            background: linear-gradient(135deg, #FFD700 0%, #FFA000 100%);
            color: #1A237E;
            border-radius: 20px;
            padding: 25px;
            margin: 25px 0;
            text-align: center;
            box-shadow: 0 15px 40px rgba(255, 215, 0, 0.3);
        }

        .empresario-title {
            font-size: 24px;
            font-weight: 900;
            margin-bottom: 10px;
        }

        .empresario-subtitle {
            font-size: 16px;
            font-weight: 600;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-custom">
        <div class="container-custom">
            <div class="d-flex justify-content-between align-items-center w-100">
                <div class="d-flex align-items-center">
                    <div class="logo-text">
                        <i class="fas fa-tools me-2"></i>
                        <span class="logo-repu">Repu</span><span class="logo-movil">Movil</span>
                        <span class="logo-plus">Plus</span>
                    </div>
                    <i class="fas fa-crown crown-icon"></i>
                </div>
                <div class="d-flex align-items-center">
                    <span class="text-white me-3">👑 Hola, Empresario</span>
                    <button class="btn btn-outline-light btn-sm">
                        <i class="fas fa-sign-out-alt"></i> Salir
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-custom">
        <!-- Welcome Section -->
        <div class="welcome-section">
            <h2 class="welcome-title">👑 De Mecánico a Empresario</h2>
            <p class="welcome-subtitle">Gestiona tu negocio como un profesional con herramientas empresariales</p>
            <div class="slogan-section mt-3">
                <h3 style="background: linear-gradient(135deg, #1A237E, #FFD700); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-weight: 900; font-size: 22px;">
                    DE MECÁNICO A EMPRESARIO EN UN TOQUE, <span style="color: #E74C3C; font-size: 24px;">NEEEÑO</span>
                </h3>
            </div>
        </div>

        <!-- Empresario Section -->
        <div class="empresario-section">
            <div class="empresario-title">🚀 Tu Transformación Empresarial</div>
            <div class="empresario-subtitle">Herramientas que usan los grandes talleres, adaptadas para ti</div>
        </div>

        <!-- Stats -->
        <div class="stats-container">
            <div class="stat-card">
                <div class="stat-number">8</div>
                <div class="stat-label">Trabajos Programados</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">$45,000</div>
                <div class="stat-label">Ingresos del Mes</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">4.9</div>
                <div class="stat-label">Calificación Empresarial</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">35</div>
                <div class="stat-label">Clientes en Base</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">92%</div>
                <div class="stat-label">Eficiencia</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">$3,750</div>
                <div class="stat-label">Ticket Promedio</div>
            </div>
        </div>

        <!-- Functions Grid -->
        <div class="functions-grid">
            <!-- Base de Clientes -->
            <div class="function-card function-card-premium" onclick="abrirBaseClientes()">
                <div class="premium-badge">PREMIUM</div>
                <div class="function-icon function-icon-premium">👥</div>
                <h3 class="function-title">Base de Clientes Completa</h3>
                <p class="function-description">Historial completo, vehículos, trabajos anteriores y recordatorios automáticos</p>
            </div>

            <!-- Agenda Inteligente -->
            <div class="function-card function-card-premium" onclick="abrirAgenda()">
                <div class="premium-badge">PREMIUM</div>
                <div class="function-icon function-icon-premium">📅</div>
                <h3 class="function-title">Agenda Inteligente</h3>
                <p class="function-description">Calendario profesional con turnos, recordatorios y optimización de rutas</p>
            </div>

            <!-- Finanzas Avanzadas -->
            <div class="function-card function-card-premium" onclick="abrirFinanzas()">
                <div class="premium-badge">PREMIUM</div>
                <div class="function-icon function-icon-premium">💰</div>
                <h3 class="function-title">Finanzas Empresariales</h3>
                <p class="function-description">Facturación automática, control de gastos y reportes de rentabilidad</p>
            </div>

            <!-- Perfil Profesional -->
            <div class="function-card function-card-premium" onclick="abrirPerfil()">
                <div class="premium-badge">PREMIUM</div>
                <div class="function-icon function-icon-premium">🌟</div>
                <h3 class="function-title">Perfil Profesional</h3>
                <p class="function-description">Portfolio de trabajos, certificaciones y especialidades destacadas</p>
            </div>

            <!-- WhatsApp Business -->
            <div class="function-card function-card-premium" onclick="abrirWhatsApp()">
                <div class="premium-badge">PREMIUM</div>
                <div class="function-icon function-icon-premium">📱</div>
                <h3 class="function-title">WhatsApp Business</h3>
                <p class="function-description">Mensajes automáticos, catálogo de servicios y comunicación profesional</p>
            </div>

            <!-- Galería Profesional -->
            <div class="function-card function-card-premium" onclick="abrirGaleria()">
                <div class="premium-badge">PREMIUM</div>
                <div class="function-icon function-icon-premium">📸</div>
                <h3 class="function-title">Galería Profesional</h3>
                <p class="function-description">Antes/después organizados, casos de éxito y portfolio para redes</p>
            </div>

            <!-- Zona de Trabajo Premium -->
            <div class="function-card function-card-premium" onclick="abrirZona()">
                <div class="premium-badge">PREMIUM</div>
                <div class="function-icon function-icon-premium">🗺️</div>
                <h3 class="function-title">Zona de Trabajo Premium</h3>
                <p class="function-description">Mapa de clientes, rutas optimizadas y clientes potenciales cercanos</p>
            </div>

            <!-- Diagnósticos Digitales -->
            <div class="function-card function-card-premium" onclick="abrirDiagnosticos()">
                <div class="premium-badge">PREMIUM</div>
                <div class="function-icon function-icon-premium">📋</div>
                <h3 class="function-title">Diagnósticos Digitales</h3>
                <p class="function-description">Plantillas profesionales, fotos organizadas y presupuestos automáticos</p>
            </div>

            <!-- Red de Mecánicos -->
            <div class="function-card function-card-premium" onclick="abrirRed()">
                <div class="premium-badge">PREMIUM</div>
                <div class="function-icon function-icon-premium">🤝</div>
                <h3 class="function-title">Red de Mecánicos</h3>
                <p class="function-description">Derivar trabajos complejos, colaboraciones e intercambio de conocimiento</p>
            </div>

            <!-- Análisis de Crecimiento -->
            <div class="function-card function-card-premium" onclick="abrirAnalisis()">
                <div class="premium-badge">PREMIUM</div>
                <div class="function-icon function-icon-premium">📈</div>
                <h3 class="function-title">Análisis de Crecimiento</h3>
                <p class="function-description">Tendencias de ingresos, servicios rentables y sugerencias de mejora</p>
            </div>

            <!-- Capacitación Continua -->
            <div class="function-card function-card-premium" onclick="abrirCapacitacion()">
                <div class="premium-badge">PREMIUM</div>
                <div class="function-icon function-icon-premium">🎓</div>
                <h3 class="function-title">Capacitación Continua</h3>
                <p class="function-description">Cursos especializados, nuevas tecnologías y certificaciones</p>
            </div>

            <!-- Pedido de Repuestos -->
            <div class="function-card" onclick="abrirRepuestos()">
                <div class="function-icon function-icon-basic">🛒</div>
                <h3 class="function-title">Pedido de Repuestos</h3>
                <p class="function-description">Solicita repuestos para tus trabajos con sistema changuito</p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function abrirBaseClientes() {
            alert('👥 Base de Clientes Completa\n\n✨ PREMIUM:\n• Historial completo de cada cliente\n• Vehículos y trabajos anteriores\n• Recordatorios de mantenimiento\n• "Nunca más olvides qué le hiciste a cada auto"');
        }

        function abrirAgenda() {
            alert('📅 Agenda Inteligente\n\n✨ PREMIUM:\n• Calendario profesional con turnos\n• Recordatorios automáticos\n• Optimización de rutas\n• "Organiza tu día como un profesional"');
        }

        function abrirFinanzas() {
            alert('💰 Finanzas Empresariales\n\n✨ PREMIUM:\n• Facturación automática\n• Control de gastos\n• Reportes de rentabilidad\n• "Sabe exactamente cuánto ganas"');
        }

        function abrirPerfil() {
            alert('🌟 Perfil Profesional\n\n✨ PREMIUM:\n• Portfolio de trabajos\n• Certificaciones\n• Especialidades destacadas\n• "Muestra tu experiencia"');
        }

        function abrirWhatsApp() {
            alert('📱 WhatsApp Business\n\n✨ PREMIUM:\n• Mensajes automáticos\n• Catálogo de servicios\n• Estados profesionales\n• "Comunícate como empresa"');
        }

        function abrirGaleria() {
            alert('📸 Galería Profesional\n\n✨ PREMIUM:\n• Antes/después organizados\n• Casos de éxito\n• Compartir en redes\n• "Tu trabajo habla por ti"');
        }

        function abrirZona() {
            alert('🗺️ Zona de Trabajo Premium\n\n✨ PREMIUM:\n• Mapa de clientes\n• Rutas optimizadas\n• Clientes potenciales cercanos\n• "Maximiza tu tiempo y combustible"');
        }

        function abrirDiagnosticos() {
            alert('📋 Diagnósticos Digitales\n\n✨ PREMIUM:\n• Plantillas de diagnóstico\n• Fotos organizadas\n• Presupuestos automáticos\n• "Diagnósticos como los grandes talleres"');
        }

        function abrirRed() {
            alert('🤝 Red de Mecánicos\n\n✨ PREMIUM:\n• Derivar trabajos complejos\n• Colaboraciones\n• Intercambio de conocimiento\n• "Conecta con otros profesionales"');
        }

        function abrirAnalisis() {
            alert('📈 Análisis de Crecimiento\n\n✨ PREMIUM:\n• Tendencias de ingresos\n• Servicios más rentables\n• Sugerencias de mejora\n• "Crece tu negocio con datos"');
        }

        function abrirCapacitacion() {
            alert('🎓 Capacitación Continua\n\n✨ PREMIUM:\n• Cursos especializados\n• Nuevas tecnologías\n• Certificaciones\n• "Mantente actualizado"');
        }

        function abrirRepuestos() {
            alert('🛒 Pedido de Repuestos\n\n• Buscar repuestos\n• Sistema changuito\n• Solicitar cotización\n• Entrega rápida');
        }
    </script>
</body>
</html>
