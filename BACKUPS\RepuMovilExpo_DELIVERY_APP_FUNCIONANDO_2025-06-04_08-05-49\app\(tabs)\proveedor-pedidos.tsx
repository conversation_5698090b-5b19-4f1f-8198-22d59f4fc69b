import React, { useState, useEffect } from 'react';
import {
  SafeAreaView,
  StatusBar,
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert,
  Dimensions,
  Modal,
  RefreshControl,
} from 'react-native';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';

const { width } = Dimensions.get('window');

interface Repartidor {
  id: string;
  nombre: string;
  telefono: string;
  ubicacion: {
    lat: number;
    lng: number;
    direccion: string;
  };
  estado: 'disponible' | 'en_camino' | 'ocupado';
  calificacion: number;
  vehiculo: string;
}

interface PedidoCompleto {
  id: string;
  cliente: {
    nombre: string;
    telefono: string;
    direccion: string;
    tipo: 'taller' | 'mecanico';
  };
  repuestos: Array<{
    id: string;
    nombre: string;
    cantidad: number;
    precio: number;
  }>;
  total: number;
  estado: 'nuevo' | 'en_preparacion' | 'en_camino' | 'entregado';
  fecha_pedido: string;
  hora_pedido: string;
  repartidor?: Repartidor;
  tiempo_estimado?: string;
  notas?: string;
  metodo_pago: 'efectivo' | 'transferencia' | 'tarjeta';
}

export default function ProveedorPedidos() {
  const router = useRouter();
  const [refreshing, setRefreshing] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedPedido, setSelectedPedido] = useState<PedidoCompleto | null>(null);
  const [filtroEstado, setFiltroEstado] = useState<'todos' | PedidoCompleto['estado']>('todos');

  // Datos de ejemplo - en producción vendrían de una API
  const [pedidos, setPedidos] = useState<PedidoCompleto[]>([
    {
      id: 'PED001',
      cliente: {
        nombre: 'Taller Mecánico Central',
        telefono: '+54 9 ************',
        direccion: 'Av. San Martín 1234, Centro',
        tipo: 'taller'
      },
      repuestos: [
        { id: '1', nombre: 'Bujía NGK', cantidad: 4, precio: 2500 },
        { id: '2', nombre: 'Filtro de aceite Mann', cantidad: 1, precio: 1200 }
      ],
      total: 11200,
      estado: 'nuevo',
      fecha_pedido: '2025-06-02',
      hora_pedido: '10:30',
      metodo_pago: 'transferencia',
      notas: 'Urgente para reparación de flota'
    },
    {
      id: 'PED002',
      cliente: {
        nombre: 'Mecánico Juan Pérez',
        telefono: '+54 9 ************',
        direccion: 'Calle Rivadavia 567, Barrio Norte',
        tipo: 'mecanico'
      },
      repuestos: [
        { id: '3', nombre: 'Pastillas de freno Bosch', cantidad: 1, precio: 4500 },
        { id: '4', nombre: 'Líquido de frenos DOT4', cantidad: 2, precio: 800 }
      ],
      total: 6100,
      estado: 'en_preparacion',
      fecha_pedido: '2025-06-02',
      hora_pedido: '11:15',
      metodo_pago: 'efectivo',
      tiempo_estimado: '15 min'
    },
    {
      id: 'PED003',
      cliente: {
        nombre: 'AutoService Plus',
        telefono: '+54 9 ************',
        direccion: 'Ruta 40 Km 15, Zona Industrial',
        tipo: 'taller'
      },
      repuestos: [
        { id: '5', nombre: 'Kit de embrague', cantidad: 1, precio: 15000 },
        { id: '6', nombre: 'Aceite motor 5W30', cantidad: 4, precio: 2200 }
      ],
      total: 23800,
      estado: 'en_camino',
      fecha_pedido: '2025-06-02',
      hora_pedido: '09:45',
      metodo_pago: 'tarjeta',
      tiempo_estimado: '8 min',
      repartidor: {
        id: 'REP001',
        nombre: 'Carlos Mendoza',
        telefono: '+54 9 ************',
        ubicacion: {
          lat: -31.5375,
          lng: -68.5364,
          direccion: 'Av. Libertador 890'
        },
        estado: 'en_camino',
        calificacion: 4.8,
        vehiculo: 'Moto Honda 150cc'
      }
    }
  ]);

  const repartidoresDisponibles: Repartidor[] = [
    {
      id: 'REP001',
      nombre: 'Carlos Mendoza',
      telefono: '+54 9 ************',
      ubicacion: {
        lat: -31.5375,
        lng: -68.5364,
        direccion: 'Av. Libertador 890'
      },
      estado: 'disponible',
      calificacion: 4.8,
      vehiculo: 'Moto Honda 150cc'
    },
    {
      id: 'REP002',
      nombre: 'María González',
      telefono: '+54 9 ************',
      ubicacion: {
        lat: -31.5402,
        lng: -68.5289,
        direccion: 'Calle Mitre 456'
      },
      estado: 'disponible',
      calificacion: 4.9,
      vehiculo: 'Auto Corsa'
    }
  ];

  const onRefresh = () => {
    setRefreshing(true);
    // Simular carga de datos
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  const cambiarEstadoPedido = (pedidoId: string, nuevoEstado: PedidoCompleto['estado']) => {
    setPedidos(prevPedidos => 
      prevPedidos.map(pedido => 
        pedido.id === pedidoId 
          ? { ...pedido, estado: nuevoEstado }
          : pedido
      )
    );
    
    Alert.alert(
      '✅ Estado Actualizado',
      `Pedido ${pedidoId} marcado como: ${nuevoEstado.replace('_', ' ')}`,
      [{ text: 'OK' }]
    );
  };

  const asignarRepartidor = (pedidoId: string, repartidor: Repartidor) => {
    setPedidos(prevPedidos => 
      prevPedidos.map(pedido => 
        pedido.id === pedidoId 
          ? { 
              ...pedido, 
              repartidor, 
              estado: 'en_camino',
              tiempo_estimado: '20 min'
            }
          : pedido
      )
    );
    
    Alert.alert(
      '🚚 Repartidor Asignado',
      `${repartidor.nombre} ha sido asignado al pedido ${pedidoId}`,
      [{ text: 'OK' }]
    );
  };

  const getEstadoColor = (estado: PedidoCompleto['estado']) => {
    switch (estado) {
      case 'nuevo': return '#2196F3';
      case 'en_preparacion': return '#FF9800';
      case 'en_camino': return '#9C27B0';
      case 'entregado': return '#4CAF50';
      default: return '#2196F3';
    }
  };

  const getEstadoTexto = (estado: PedidoCompleto['estado']) => {
    switch (estado) {
      case 'nuevo': return 'NUEVO';
      case 'en_preparacion': return 'EN PREPARACIÓN';
      case 'en_camino': return 'EN CAMINO';
      case 'entregado': return 'ENTREGADO';
      default: return estado.toUpperCase();
    }
  };

  const pedidosFiltrados = filtroEstado === 'todos' 
    ? pedidos 
    : pedidos.filter(pedido => pedido.estado === filtroEstado);

  const estadisticas = {
    nuevos: pedidos.filter(p => p.estado === 'nuevo').length,
    en_preparacion: pedidos.filter(p => p.estado === 'en_preparacion').length,
    en_camino: pedidos.filter(p => p.estado === 'en_camino').length,
    entregados: pedidos.filter(p => p.estado === 'entregado').length,
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#4CAF50" />
      
      {/* Header con gradiente */}
      <LinearGradient
        colors={['#4CAF50', '#45A049']}
        style={styles.header}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity onPress={() => router.back()}>
            <Text style={styles.backButton}>← Volver</Text>
          </TouchableOpacity>
          <View style={styles.logoContainer}>
            <Text style={styles.logoText}>
              📋 <Text style={styles.logoRepu}>Gestión de</Text>
              <Text style={styles.logoMovil}> Pedidos</Text>
            </Text>
          </View>
        </View>
        
        {/* Frase motivadora */}
        <View style={styles.motivationalSection}>
          <Text style={styles.motivationalText}>
            "Cada pedido es una oportunidad de hacer crecer tu negocio"
          </Text>
        </View>
      </LinearGradient>

      <ScrollView 
        style={styles.content} 
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Estadísticas rápidas */}
        <View style={styles.statsContainer}>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{estadisticas.nuevos}</Text>
            <Text style={styles.statLabel}>Nuevos</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{estadisticas.en_preparacion}</Text>
            <Text style={styles.statLabel}>Preparando</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{estadisticas.en_camino}</Text>
            <Text style={styles.statLabel}>En Camino</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{estadisticas.entregados}</Text>
            <Text style={styles.statLabel}>Entregados</Text>
          </View>
        </View>

        {/* Filtros */}
        <View style={styles.filtersContainer}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {['todos', 'nuevo', 'en_preparacion', 'en_camino', 'entregado'].map((estado) => (
              <TouchableOpacity
                key={estado}
                style={[
                  styles.filterButton,
                  filtroEstado === estado && styles.filterButtonActive
                ]}
                onPress={() => setFiltroEstado(estado as any)}
              >
                <Text style={[
                  styles.filterText,
                  filtroEstado === estado && styles.filterTextActive
                ]}>
                  {estado === 'todos' ? 'Todos' : getEstadoTexto(estado as any)}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Lista de pedidos */}
        <View style={styles.pedidosContainer}>
          {pedidosFiltrados.map((pedido) => (
            <TouchableOpacity
              key={pedido.id}
              style={styles.pedidoCard}
              onPress={() => {
                setSelectedPedido(pedido);
                setModalVisible(true);
              }}
            >
              <View style={styles.pedidoHeader}>
                <View>
                  <Text style={styles.pedidoId}>#{pedido.id}</Text>
                  <Text style={styles.pedidoCliente}>
                    {pedido.cliente.tipo === 'taller' ? '🏪' : '👨‍🔧'} {pedido.cliente.nombre}
                  </Text>
                </View>
                <View style={[styles.estadoBadge, { backgroundColor: getEstadoColor(pedido.estado) }]}>
                  <Text style={styles.estadoText}>{getEstadoTexto(pedido.estado)}</Text>
                </View>
              </View>
              
              <Text style={styles.pedidoHora}>🕐 {pedido.hora_pedido} - {pedido.fecha_pedido}</Text>
              <Text style={styles.pedidoDireccion}>📍 {pedido.cliente.direccion}</Text>
              
              <View style={styles.pedidoInfo}>
                <Text style={styles.pedidoTotal}>💰 ${pedido.total.toLocaleString()}</Text>
                <Text style={styles.pedidoPago}>💳 {pedido.metodo_pago}</Text>
              </View>

              {pedido.repartidor && (
                <View style={styles.repartidorInfo}>
                  <Text style={styles.repartidorNombre}>
                    🚚 {pedido.repartidor.nombre} - ⭐ {pedido.repartidor.calificacion}
                  </Text>
                  <Text style={styles.repartidorUbicacion}>
                    📍 {pedido.repartidor.ubicacion.direccion}
                  </Text>
                  {pedido.tiempo_estimado && (
                    <Text style={styles.tiempoEstimado}>
                      ⏱️ Llegada estimada: {pedido.tiempo_estimado}
                    </Text>
                  )}
                </View>
              )}

              <View style={styles.pedidoActions}>
                <TouchableOpacity
                  style={styles.btnContactar}
                  onPress={() => Alert.alert('📱 Contactar', `Llamando a ${pedido.cliente.nombre}...`)}
                >
                  <Text style={styles.btnText}>📱 Contactar</Text>
                </TouchableOpacity>
                
                {pedido.estado !== 'entregado' && (
                  <TouchableOpacity
                    style={styles.btnCambiarEstado}
                    onPress={() => {
                      const siguienteEstado = pedido.estado === 'nuevo' ? 'en_preparacion' :
                                            pedido.estado === 'en_preparacion' ? 'en_camino' :
                                            'entregado';
                      cambiarEstadoPedido(pedido.id, siguienteEstado);
                    }}
                  >
                    <Text style={styles.btnText}>✅ Siguiente</Text>
                  </TouchableOpacity>
                )}
              </View>
            </TouchableOpacity>
          ))}
        </View>

        {/* Mensaje con corazón */}
        <View style={styles.heartMessage}>
          <Text style={styles.heartText}>
            Hecho con ❤️ para hacer crecer tu negocio
          </Text>
        </View>
      </ScrollView>

      {/* Modal detallado del pedido */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                📋 Detalle del Pedido #{selectedPedido?.id}
              </Text>
              <TouchableOpacity onPress={() => setModalVisible(false)}>
                <Text style={styles.closeButton}>✕</Text>
              </TouchableOpacity>
            </View>

            {selectedPedido && (
              <ScrollView style={styles.modalBody} showsVerticalScrollIndicator={false}>
                {/* Información del cliente */}
                <View style={styles.modalSection}>
                  <Text style={styles.modalSectionTitle}>👤 Cliente</Text>
                  <Text style={styles.modalText}>
                    {selectedPedido.cliente.tipo === 'taller' ? '🏪' : '👨‍🔧'} {selectedPedido.cliente.nombre}
                  </Text>
                  <Text style={styles.modalText}>📱 {selectedPedido.cliente.telefono}</Text>
                  <Text style={styles.modalText}>📍 {selectedPedido.cliente.direccion}</Text>
                </View>

                {/* Repuestos */}
                <View style={styles.modalSection}>
                  <Text style={styles.modalSectionTitle}>🔧 Repuestos</Text>
                  {selectedPedido.repuestos.map((repuesto, index) => (
                    <View key={index} style={styles.repuestoItem}>
                      <Text style={styles.repuestoNombre}>{repuesto.nombre}</Text>
                      <Text style={styles.repuestoCantidad}>Cant: {repuesto.cantidad}</Text>
                      <Text style={styles.repuestoPrecio}>${repuesto.precio.toLocaleString()}</Text>
                    </View>
                  ))}
                  <View style={styles.totalContainer}>
                    <Text style={styles.totalText}>Total: ${selectedPedido.total.toLocaleString()}</Text>
                  </View>
                </View>

                {/* Estado y repartidor */}
                <View style={styles.modalSection}>
                  <Text style={styles.modalSectionTitle}>📊 Estado del Pedido</Text>
                  <View style={[styles.estadoBadge, { backgroundColor: getEstadoColor(selectedPedido.estado) }]}>
                    <Text style={styles.estadoText}>{getEstadoTexto(selectedPedido.estado)}</Text>
                  </View>

                  {selectedPedido.repartidor ? (
                    <View style={styles.repartidorDetalle}>
                      <Text style={styles.modalSectionTitle}>🚚 Repartidor Asignado</Text>
                      <Text style={styles.modalText}>👤 {selectedPedido.repartidor.nombre}</Text>
                      <Text style={styles.modalText}>📱 {selectedPedido.repartidor.telefono}</Text>
                      <Text style={styles.modalText}>🚗 {selectedPedido.repartidor.vehiculo}</Text>
                      <Text style={styles.modalText}>⭐ {selectedPedido.repartidor.calificacion}/5</Text>
                      <Text style={styles.modalText}>📍 {selectedPedido.repartidor.ubicacion.direccion}</Text>
                      {selectedPedido.tiempo_estimado && (
                        <Text style={styles.tiempoEstimadoModal}>
                          ⏱️ Tiempo estimado: {selectedPedido.tiempo_estimado}
                        </Text>
                      )}
                    </View>
                  ) : (
                    <View style={styles.sinRepartidor}>
                      <Text style={styles.modalText}>Sin repartidor asignado</Text>
                      <Text style={styles.modalSubtext}>Repartidores disponibles:</Text>
                      {repartidoresDisponibles.map((repartidor) => (
                        <TouchableOpacity
                          key={repartidor.id}
                          style={styles.repartidorOption}
                          onPress={() => {
                            asignarRepartidor(selectedPedido.id, repartidor);
                            setModalVisible(false);
                          }}
                        >
                          <Text style={styles.repartidorOptionText}>
                            🚚 {repartidor.nombre} - ⭐ {repartidor.calificacion}
                          </Text>
                          <Text style={styles.repartidorOptionSubtext}>
                            📍 {repartidor.ubicacion.direccion}
                          </Text>
                        </TouchableOpacity>
                      ))}
                    </View>
                  )}
                </View>

                {/* Información adicional */}
                <View style={styles.modalSection}>
                  <Text style={styles.modalSectionTitle}>ℹ️ Información Adicional</Text>
                  <Text style={styles.modalText}>🕐 Pedido realizado: {selectedPedido.hora_pedido}</Text>
                  <Text style={styles.modalText}>📅 Fecha: {selectedPedido.fecha_pedido}</Text>
                  <Text style={styles.modalText}>💳 Método de pago: {selectedPedido.metodo_pago}</Text>
                  {selectedPedido.notas && (
                    <Text style={styles.modalText}>📝 Notas: {selectedPedido.notas}</Text>
                  )}
                </View>

                {/* Acciones */}
                <View style={styles.modalActions}>
                  <TouchableOpacity
                    style={styles.modalActionButton}
                    onPress={() => {
                      Alert.alert('📱 Contactar', `Llamando a ${selectedPedido.cliente.nombre}...`);
                    }}
                  >
                    <Text style={styles.modalActionText}>📱 Contactar Cliente</Text>
                  </TouchableOpacity>

                  {selectedPedido.estado !== 'entregado' && (
                    <TouchableOpacity
                      style={[styles.modalActionButton, styles.modalActionButtonPrimary]}
                      onPress={() => {
                        const siguienteEstado = selectedPedido.estado === 'nuevo' ? 'en_preparacion' :
                                              selectedPedido.estado === 'en_preparacion' ? 'en_camino' :
                                              'entregado';
                        cambiarEstadoPedido(selectedPedido.id, siguienteEstado);
                        setModalVisible(false);
                      }}
                    >
                      <Text style={styles.modalActionTextPrimary}>✅ Cambiar Estado</Text>
                    </TouchableOpacity>
                  )}
                </View>
              </ScrollView>
            )}
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    paddingTop: 20,
    paddingBottom: 25,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  backButton: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  logoContainer: {
    flex: 1,
    alignItems: 'center',
  },
  logoText: {
    fontSize: 20,
    fontWeight: '800',
    color: 'white',
  },
  logoRepu: {
    color: '#ffffff',
  },
  logoMovil: {
    color: '#FFF3E0',
  },
  motivationalSection: {
    alignItems: 'center',
    marginTop: 10,
  },
  motivationalText: {
    color: 'white',
    fontSize: 14,
    fontStyle: 'italic',
    textAlign: 'center',
    opacity: 0.9,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 20,
    marginBottom: 20,
  },
  statCard: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 15,
    alignItems: 'center',
    flex: 1,
    marginHorizontal: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  statNumber: {
    fontSize: 20,
    fontWeight: '800',
    color: '#4CAF50',
    marginBottom: 5,
  },
  statLabel: {
    fontSize: 11,
    color: '#666',
    textAlign: 'center',
    fontWeight: '500',
  },
  filtersContainer: {
    marginBottom: 20,
  },
  filterButton: {
    backgroundColor: 'white',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 10,
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  filterButtonActive: {
    backgroundColor: '#4CAF50',
    borderColor: '#4CAF50',
  },
  filterText: {
    fontSize: 12,
    color: '#666',
    fontWeight: '600',
  },
  filterTextActive: {
    color: 'white',
  },
  pedidosContainer: {
    marginBottom: 20,
  },
  pedidoCard: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
    borderLeftWidth: 4,
    borderLeftColor: '#4CAF50',
  },
  pedidoHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 10,
  },
  pedidoId: {
    fontSize: 12,
    color: '#666',
    fontWeight: '600',
  },
  pedidoCliente: {
    fontSize: 16,
    fontWeight: '700',
    color: '#2D3748',
    marginTop: 2,
  },
  estadoBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
  },
  estadoText: {
    color: 'white',
    fontSize: 10,
    fontWeight: '700',
  },
  pedidoHora: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  pedidoDireccion: {
    fontSize: 14,
    color: '#666',
    marginBottom: 10,
    lineHeight: 20,
  },
  pedidoInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  pedidoTotal: {
    fontSize: 16,
    fontWeight: '700',
    color: '#4CAF50',
  },
  pedidoPago: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  repartidorInfo: {
    backgroundColor: '#f8f9fa',
    padding: 10,
    borderRadius: 10,
    marginBottom: 10,
  },
  repartidorNombre: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2D3748',
    marginBottom: 3,
  },
  repartidorUbicacion: {
    fontSize: 12,
    color: '#666',
    marginBottom: 3,
  },
  tiempoEstimado: {
    fontSize: 12,
    color: '#FF9800',
    fontWeight: '600',
  },
  pedidoActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  btnContactar: {
    backgroundColor: '#2196F3',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 8,
    flex: 1,
    marginRight: 5,
    alignItems: 'center',
  },
  btnCambiarEstado: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 8,
    flex: 1,
    marginLeft: 5,
    alignItems: 'center',
  },
  btnText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  heartMessage: {
    alignItems: 'center',
    paddingVertical: 20,
    marginBottom: 20,
  },
  heartText: {
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
    textAlign: 'center',
  },
  // Estilos del Modal
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: 'white',
    margin: 20,
    borderRadius: 20,
    maxHeight: '90%',
    width: width - 40,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#2D3748',
    flex: 1,
  },
  closeButton: {
    fontSize: 24,
    color: '#666',
    fontWeight: 'bold',
  },
  modalBody: {
    padding: 20,
  },
  modalSection: {
    marginBottom: 20,
  },
  modalSectionTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: '#2D3748',
    marginBottom: 10,
  },
  modalText: {
    fontSize: 14,
    color: '#4A5568',
    marginBottom: 5,
    lineHeight: 20,
  },
  modalSubtext: {
    fontSize: 12,
    color: '#666',
    marginBottom: 10,
    fontWeight: '600',
  },
  repuestoItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    marginBottom: 5,
  },
  repuestoNombre: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2D3748',
    flex: 1,
  },
  repuestoCantidad: {
    fontSize: 12,
    color: '#666',
    marginHorizontal: 10,
  },
  repuestoPrecio: {
    fontSize: 14,
    fontWeight: '700',
    color: '#4CAF50',
  },
  totalContainer: {
    marginTop: 10,
    paddingTop: 10,
    borderTopWidth: 1,
    borderTopColor: '#e2e8f0',
  },
  totalText: {
    fontSize: 16,
    fontWeight: '800',
    color: '#4CAF50',
    textAlign: 'right',
  },
  repartidorDetalle: {
    backgroundColor: '#f0f9ff',
    padding: 15,
    borderRadius: 10,
    marginTop: 10,
  },
  tiempoEstimadoModal: {
    fontSize: 14,
    color: '#FF9800',
    fontWeight: '700',
    marginTop: 5,
  },
  sinRepartidor: {
    marginTop: 10,
  },
  repartidorOption: {
    backgroundColor: '#f8f9fa',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  repartidorOptionText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2D3748',
    marginBottom: 3,
  },
  repartidorOptionSubtext: {
    fontSize: 12,
    color: '#666',
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  modalActionButton: {
    backgroundColor: '#f8f9fa',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 10,
    flex: 1,
    marginHorizontal: 5,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  modalActionButtonPrimary: {
    backgroundColor: '#4CAF50',
    borderColor: '#4CAF50',
  },
  modalActionText: {
    color: '#666',
    fontSize: 14,
    fontWeight: '600',
  },
  modalActionTextPrimary: {
    color: 'white',
    fontSize: 14,
    fontWeight: '700',
  },
});
