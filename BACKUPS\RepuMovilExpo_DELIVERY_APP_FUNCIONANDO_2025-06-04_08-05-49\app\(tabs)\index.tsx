import React from 'react';
import {
  SafeAreaView,
  StatusBar,
  StyleSheet,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Alert,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
// import { LinearGradient } from 'expo-linear-gradient';
import MapSelector from '../../components/MapSelector';

export default function HomeScreen() {
  // 🎯 CONTROL DE FUNCIONALIDADES
  const [showPlusFeatures, setShowPlusFeatures] = React.useState(false); // FALSE = Solo común

  const [showRegister, setShowRegister] = React.useState(false);

  // Estados para Login
  const [email, setEmail] = React.useState('');
  const [password, setPassword] = React.useState('');

  // Estados para Registro
  const [regName, setRegName] = React.useState('');
  const [regEmail, setRegEmail] = React.useState('');
  const [regPhone, setRegPhone] = React.useState('');
  const [regPassword, setRegPassword] = React.useState('');
  const [regConfirmPassword, setRegConfirmPassword] = React.useState('');
  const [userType, setUserType] = React.useState('usuario_regular');
  const [showUserTypeDropdown, setShowUserTypeDropdown] = React.useState(false);

  // Estados adicionales para talleres
  const [username, setUsername] = React.useState('');
  const [address, setAddress] = React.useState('');
  const [mainCategory, setMainCategory] = React.useState('autos');
  const [showCategoryDropdown, setShowCategoryDropdown] = React.useState(false);
  const [fiscalData, setFiscalData] = React.useState('');

  // Estados adicionales para proveedores
  const [storeName, setStoreName] = React.useState('');
  const [deliveryZones, setDeliveryZones] = React.useState('');
  const [contactData, setContactData] = React.useState('');
  const [businessHours, setBusinessHours] = React.useState('');
  const [storeLocation, setStoreLocation] = React.useState('');

  // 🎯 OPCIONES DE USUARIO - TODOS LOS TIPOS
  const userTypeOptions = [
    { label: 'Usuario regular', value: 'usuario_regular' },
    { label: 'Taller mecánico', value: 'taller_mecanico' },
    { label: 'Mecánico independiente', value: 'mecanico_independiente' },
    { label: 'Proveedor de repuestos', value: 'proveedor_repuestos' },
  ];

  const categoryOptions = [
    { label: 'Autos', value: 'autos' },
    { label: 'Motos', value: 'motos' },
    { label: 'Maquinaria', value: 'maquinaria' },
    { label: 'Otro', value: 'otro' },
  ];

  const handleLogin = () => {
    if (!email || !password) {
      Alert.alert('Error', 'Por favor complete todos los campos');
      return;
    }
    console.log('Iniciar Sesión:', {email, password});
    // Aquí irá la lógica de login
  };

  const handleShowRegister = () => {
    setShowRegister(true);
  };

  const handleBackToLogin = () => {
    setShowRegister(false);
    // Limpiar campos de registro
    setRegName('');
    setRegEmail('');
    setRegPhone('');
    setRegPassword('');
    setRegConfirmPassword('');
    setUserType('usuario_regular');
  };

  const handleRegisterSubmit = () => {
    if (!regName || !regEmail || !regPhone || !regPassword || !regConfirmPassword) {
      Alert.alert('Error', 'Por favor complete todos los campos');
      return;
    }

    if (regPassword !== regConfirmPassword) {
      Alert.alert('Error', 'Las contraseñas no coinciden');
      return;
    }

    if (regPassword.length < 6) {
      Alert.alert('Error', 'La contraseña debe tener al menos 6 caracteres');
      return;
    }

    console.log('Registro:', {
      name: regName,
      email: regEmail,
      phone: regPhone,
      password: regPassword,
      userType: userType
    });

    Alert.alert('Éxito', 'Usuario registrado correctamente', [
      { text: 'OK', onPress: handleBackToLogin }
    ]);
  };

  const handleForgotPassword = () => {
    Alert.alert('Recuperar Contraseña', 'Funcionalidad próximamente disponible');
  };

  const handleUserTypeSelect = (value: string) => {
    setUserType(value);
    setShowUserTypeDropdown(false);
  };

  const getUserTypeLabel = () => {
    const option = userTypeOptions.find(opt => opt.value === userType);
    return option ? option.label : 'Seleccionar tipo';
  };

  const handleCategorySelect = (value: string) => {
    setMainCategory(value);
    setShowCategoryDropdown(false);
  };

  const getCategoryLabel = () => {
    const option = categoryOptions.find(opt => opt.value === mainCategory);
    return option ? option.label : 'Seleccionar rubro';
  };

  if (showRegister) {
    return (
      <View style={styles.container}>
        <SafeAreaView style={styles.flex1}>
          <StatusBar barStyle="dark-content" backgroundColor="#f5f7fa" />

        <KeyboardAvoidingView
          style={styles.flex1}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
          {/* Logo */}
          <View style={styles.logoContainerSmall}>
            <View style={styles.logoCircleSmall}>
              <Text style={styles.logoTextSmall}>🔧</Text>
            </View>
            <Text style={styles.logoTitleSmall}>
              <Text style={styles.logoRepu}>Repu</Text>
              <Text style={styles.logoMovil}>Movil</Text>
            </Text>
          </View>

          {/* Título */}
          <Text style={styles.titleSmall}>Crear Cuenta</Text>

          {/* Formulario de Registro */}
          <ScrollView
            style={styles.formContainer}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.scrollContent}
          >
          {/* Nombre */}
          <Text style={styles.label}>
            {userType === 'taller_mecanico'
              ? 'Nombre del Taller'
              : userType === 'proveedor_repuestos'
              ? 'Nombre del Local'
              : 'Nombre Completo'}
          </Text>
          <TextInput
            style={styles.input}
            value={regName}
            onChangeText={setRegName}
            placeholder={
              userType === 'taller_mecanico'
                ? 'Ingrese el nombre del taller'
                : userType === 'proveedor_repuestos'
                ? 'Ingrese el nombre del local'
                : 'Ingrese su nombre completo'
            }
            autoCapitalize="words"
          />

          {/* Email */}
          <Text style={styles.label}>Correo Electrónico</Text>
          <TextInput
            style={styles.input}
            value={regEmail}
            onChangeText={setRegEmail}
            placeholder="<EMAIL>"
            keyboardType="email-address"
            autoCapitalize="none"
          />

          {/* Teléfono */}
          <Text style={styles.label}>Teléfono</Text>
          {userType === 'taller_mecanico' && (
            <Text style={styles.helperText}>
              📱 Recibirás un SMS con la confirmación de la cuenta
            </Text>
          )}
          <TextInput
            style={styles.input}
            value={regPhone}
            onChangeText={setRegPhone}
            placeholder="Número de teléfono"
            keyboardType="phone-pad"
          />

          {/* Tipo de Usuario */}
          <Text style={styles.label}>Tipo de Usuario</Text>
          <View style={styles.dropdownContainer}>
            <TouchableOpacity
              style={styles.dropdownButton}
              onPress={() => setShowUserTypeDropdown(!showUserTypeDropdown)}
            >
              <Text style={styles.dropdownButtonText}>{getUserTypeLabel()}</Text>
              <Text style={styles.dropdownArrow}>{showUserTypeDropdown ? '▲' : '▼'}</Text>
            </TouchableOpacity>

            {showUserTypeDropdown && (
              <View style={styles.dropdownList}>
                {userTypeOptions.map((option) => (
                  <TouchableOpacity
                    key={option.value}
                    style={[
                      styles.dropdownItem,
                      userType === option.value && styles.dropdownItemSelected
                    ]}
                    onPress={() => handleUserTypeSelect(option.value)}
                  >
                    <Text style={[
                      styles.dropdownItemText,
                      userType === option.value && styles.dropdownItemTextSelected
                    ]}>
                      {option.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            )}
          </View>

          {/* 🔒 CAMPOS PARA TALLERES - OCULTOS POR AHORA (PLUS) */}
          {userType === 'taller_mecanico' && showPlusFeatures && (
            <>
              {/* Nombre de Usuario */}
              <Text style={styles.label}>Nombre de Usuario</Text>
              <TextInput
                style={styles.input}
                value={username}
                onChangeText={setUsername}
                placeholder="Nombre de usuario único"
                autoCapitalize="none"
              />

              {/* Dirección con Google Maps */}
              <Text style={styles.label}>Dirección</Text>
              <MapSelector
                onAddressSelect={(selectedAddress, coordinates) => {
                  setAddress(selectedAddress);
                  console.log('Coordenadas:', coordinates);
                }}
                initialAddress={address}
              />

              {/* Rubro Principal */}
              <Text style={styles.label}>Rubro Principal</Text>
              <View style={styles.dropdownContainer}>
                <TouchableOpacity
                  style={styles.dropdownButton}
                  onPress={() => setShowCategoryDropdown(!showCategoryDropdown)}
                >
                  <Text style={styles.dropdownButtonText}>{getCategoryLabel()}</Text>
                  <Text style={styles.dropdownArrow}>{showCategoryDropdown ? '▲' : '▼'}</Text>
                </TouchableOpacity>

                {showCategoryDropdown && (
                  <View style={styles.dropdownList}>
                    {categoryOptions.map((option) => (
                      <TouchableOpacity
                        key={option.value}
                        style={[
                          styles.dropdownItem,
                          mainCategory === option.value && styles.dropdownItemSelected
                        ]}
                        onPress={() => handleCategorySelect(option.value)}
                      >
                        <Text style={[
                          styles.dropdownItemText,
                          mainCategory === option.value && styles.dropdownItemTextSelected
                        ]}>
                          {option.label}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                )}
              </View>

              {/* Datos Fiscales */}
              <Text style={styles.label}>Datos Fiscales (Opcional)</Text>
              <TextInput
                style={styles.input}
                value={fiscalData}
                onChangeText={setFiscalData}
                placeholder="CUIT, Razón Social, etc."
                multiline={true}
                numberOfLines={2}
              />
            </>
          )}

          {/* Campos adicionales para proveedores de repuestos */}
          {userType === 'proveedor_repuestos' && (
            <>
              {/* Ubicación del Local */}
              <Text style={styles.label}>Ubicación del Local</Text>
              <MapSelector
                onAddressSelect={(selectedAddress, coordinates) => {
                  setStoreLocation(selectedAddress);
                  console.log('Ubicación del local:', coordinates);
                }}
                initialAddress={storeLocation}
              />

              {/* Zonas de Entrega */}
              <Text style={styles.label}>Zonas de Entrega</Text>
              <Text style={styles.helperText}>
                📦 Especifica las zonas donde realizas entregas
              </Text>
              <TextInput
                style={styles.input}
                value={deliveryZones}
                onChangeText={setDeliveryZones}
                placeholder="Ej: Centro, Palermo, Belgrano, Villa Crespo..."
                multiline={true}
                numberOfLines={3}
              />

              {/* Datos de Contacto */}
              <Text style={styles.label}>Datos de Contacto</Text>
              <Text style={styles.helperText}>
                📞 WhatsApp, teléfono fijo, redes sociales, etc.
              </Text>
              <TextInput
                style={styles.input}
                value={contactData}
                onChangeText={setContactData}
                placeholder="WhatsApp: +54 9 11 1234-5678&#10;Instagram: @milocal&#10;Facebook: Mi Local de Repuestos"
                multiline={true}
                numberOfLines={4}
              />

              {/* Horarios de Atención */}
              <Text style={styles.label}>Horarios de Atención</Text>
              <Text style={styles.helperText}>
                🕒 Especifica días y horarios de atención
              </Text>
              <TextInput
                style={styles.input}
                value={businessHours}
                onChangeText={setBusinessHours}
                placeholder="Lunes a Viernes: 8:00 - 18:00&#10;Sábados: 8:00 - 13:00&#10;Domingos: Cerrado"
                multiline={true}
                numberOfLines={4}
              />
            </>
          )}

          {/* Contraseña */}
          <Text style={styles.label}>Contraseña</Text>
          <TextInput
            style={styles.input}
            value={regPassword}
            onChangeText={setRegPassword}
            placeholder="Mínimo 6 caracteres"
            secureTextEntry
          />

          {/* Confirmar Contraseña */}
          <Text style={styles.label}>Confirmar Contraseña</Text>
          <TextInput
            style={styles.input}
            value={regConfirmPassword}
            onChangeText={setRegConfirmPassword}
            placeholder="Repita su contraseña"
            secureTextEntry
          />

          {/* Botón Registrarse */}
          <TouchableOpacity style={styles.loginButton} onPress={handleRegisterSubmit}>
            <Text style={styles.loginButtonText}>Crear Cuenta</Text>
          </TouchableOpacity>

          {/* Volver al Login */}
          <TouchableOpacity onPress={handleBackToLogin} style={styles.registerContainer}>
            <Text style={styles.registerText}>
              ¿Ya tiene una cuenta? <Text style={styles.registerLink}>Iniciar Sesión</Text>
            </Text>
          </TouchableOpacity>
          </ScrollView>
        </KeyboardAvoidingView>
        </SafeAreaView>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <SafeAreaView style={styles.flex1}>
        <StatusBar barStyle="dark-content" backgroundColor="#f5f7fa" />

      {/* Logo */}
      <View style={styles.logoContainer}>
        <View style={styles.logoCircle}>
          <Text style={styles.logoText}>🔧</Text>
        </View>
        <Text style={styles.logoTitle}>
          <Text style={styles.logoRepu}>Repu</Text>
          <Text style={styles.logoMovil}>Movil</Text>
        </Text>
      </View>

      {/* Título */}
      <Text style={styles.title}>Iniciar Sesión</Text>

      {/* Formulario */}
      <View style={styles.formContainer}>
        {/* Email */}
        <Text style={styles.label}>Correo Electrónico</Text>
        <TextInput
          style={styles.input}
          value={email}
          onChangeText={setEmail}
          placeholder=""
          keyboardType="email-address"
          autoCapitalize="none"
        />

        {/* Contraseña */}
        <Text style={styles.label}>Contraseña</Text>
        <TextInput
          style={styles.input}
          value={password}
          onChangeText={setPassword}
          placeholder=""
          secureTextEntry
        />

        {/* ¿Olvidó su contraseña? */}
        <TouchableOpacity onPress={handleForgotPassword} style={styles.forgotContainer}>
          <Text style={styles.forgotText}>¿Olvidó su contraseña?</Text>
        </TouchableOpacity>

        {/* Botón Iniciar Sesión */}
        <TouchableOpacity style={styles.loginButton} onPress={handleLogin}>
          <Text style={styles.loginButtonText}>Iniciar Sesión</Text>
        </TouchableOpacity>

        {/* Registrarse */}
        <TouchableOpacity onPress={handleShowRegister} style={styles.registerContainer}>
          <Text style={styles.registerText}>
            ¿No tiene una cuenta? <Text style={styles.registerLink}>Regístrese aquí</Text>
          </Text>
        </TouchableOpacity>
      </View>
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f7fa',
    paddingHorizontal: 30,
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: 60,
    marginBottom: 40,
  },
  logoCircle: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#FF6B35',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
    shadowColor: '#FF6B35',
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.4,
    shadowRadius: 12,
    elevation: 10,
  },
  logoText: {
    fontSize: 40,
    color: 'white',
  },
  logoTitle: {
    fontSize: 32,
    fontWeight: 'bold',
  },
  logoRepu: {
    color: '#FF6B35',
  },
  logoMovil: {
    color: '#FFA500',
  },
  title: {
    fontSize: 28,
    fontWeight: '600',
    color: '#333',
    textAlign: 'center',
    marginBottom: 40,
  },
  formContainer: {
    flex: 1,
    paddingHorizontal: 5,
  },
  label: {
    fontSize: 14,
    color: '#666',
    marginBottom: 6,
    marginTop: 12,
  },
  input: {
    borderWidth: 2,
    borderColor: '#e9ecef',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    backgroundColor: 'white',
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  forgotContainer: {
    alignItems: 'flex-end',
    marginBottom: 30,
  },
  forgotText: {
    color: '#666',
    fontSize: 14,
  },
  loginButton: {
    backgroundColor: '#FF6B35',
    paddingVertical: 15,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 20,
    shadowColor: '#FF6B35',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  loginButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
  },
  registerContainer: {
    alignItems: 'center',
    marginTop: 10,
  },
  registerText: {
    fontSize: 14,
    color: '#666',
  },
  registerLink: {
    color: '#FF6B35',
    fontWeight: '600',
  },

  // Estilos para el registro - Dropdown
  dropdownContainer: {
    marginBottom: 20,
    position: 'relative',
    zIndex: 1000,
  },
  dropdownButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#e9ecef',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'white',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  dropdownButtonText: {
    fontSize: 16,
    color: '#333',
  },
  dropdownArrow: {
    fontSize: 12,
    color: '#666',
  },
  dropdownList: {
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#ddd',
    borderTopWidth: 0,
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8,
    maxHeight: 200,
    zIndex: 1001,
  },
  dropdownItem: {
    paddingHorizontal: 15,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  dropdownItemSelected: {
    backgroundColor: '#FFF8F0',
  },
  dropdownItemText: {
    fontSize: 16,
    color: '#333',
  },
  dropdownItemTextSelected: {
    color: '#FF8C00',
    fontWeight: 'bold',
  },

  // Estilo para texto de ayuda
  helperText: {
    fontSize: 12,
    color: '#666',
    marginBottom: 8,
    fontStyle: 'italic',
  },

  // Estilos para móvil
  flex1: {
    flex: 1,
  },
  logoContainerSmall: {
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 15,
  },
  logoCircleSmall: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#FF6B35',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  logoTextSmall: {
    fontSize: 25,
    color: 'white',
  },
  logoTitleSmall: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  titleSmall: {
    fontSize: 22,
    fontWeight: '600',
    color: '#333',
    textAlign: 'center',
    marginBottom: 20,
  },
  scrollContent: {
    paddingBottom: 30,
  },
});
