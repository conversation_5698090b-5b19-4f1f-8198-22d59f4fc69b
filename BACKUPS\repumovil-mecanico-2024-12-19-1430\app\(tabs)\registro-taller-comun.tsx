import React, { useState } from 'react';
import {
  SafeAreaView,
  StatusBar,
  StyleSheet,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  Dimensions,
} from 'react-native';
import { useRouter } from 'expo-router';

const { width } = Dimensions.get('window');

export default function RegistroTallerComun() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    nombre: '',
    email: '',
    telefono: '',
    password: '',
    username: '',
    direccion: '',
    rubro_principal: '',
    datos_fiscales: '',
  });

  const [loading, setLoading] = useState(false);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const validarFormulario = () => {
    if (!formData.nombre || !formData.email || !formData.password || !formData.username) {
      Alert.alert('⚠️ Campos requeridos', 'Completa todos los campos obligatorios');
      return false;
    }

    if (formData.password.length < 6) {
      Alert.alert('⚠️ Contraseña', 'La contraseña debe tener al menos 6 caracteres');
      return false;
    }

    if (!formData.email.includes('@')) {
      Alert.alert('⚠️ Email', 'Ingresa un email válido');
      return false;
    }

    return true;
  };

  const registrarTaller = async () => {
    if (!validarFormulario()) return;

    setLoading(true);
    
    try {
      // Simular registro exitoso
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      Alert.alert(
        '🎉 ¡Registro Exitoso!',
        `¡Bienvenido a RepuMovil!\n\nTu taller "${formData.username}" ha sido registrado exitosamente.\n\n✅ Incluye:\n• Pedido de repuestos\n• Sistema changuito\n• Calificaciones\n• Notificaciones básicas`,
        [
          {
            text: 'Ir al Dashboard',
            onPress: () => router.push('/dashboard-taller-comun')
          }
        ]
      );
    } catch (error) {
      Alert.alert('❌ Error', 'Hubo un problema al registrar el taller. Intenta nuevamente.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#667eea" />
      
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.logoText}>
            🔧 <Text style={styles.logoRepu}>Repu</Text><Text style={styles.logoMovil}>Movil</Text>
          </Text>
          <Text style={styles.subtitle}>Repuestos que llegan a tu taller, cuando los necesitas</Text>
        </View>

        {/* Características del Plan */}
        <View style={styles.featureHighlight}>
          <Text style={styles.featureTitle}>✨ Tu plan incluye:</Text>
          <View style={styles.featureList}>
            <Text style={styles.featureItem}>✅ Pedido de repuestos con changuito</Text>
            <Text style={styles.featureItem}>✅ Calificaciones de clientes</Text>
            <Text style={styles.featureItem}>✅ Búsqueda avanzada de repuestos</Text>
            <Text style={styles.featureItem}>✅ Notificaciones en tiempo real</Text>
          </View>
        </View>

        {/* Formulario */}
        <View style={styles.formContainer}>
          <Text style={styles.sectionTitle}>📝 Datos Básicos</Text>
          
          <TextInput
            style={styles.input}
            placeholder="Nombre Completo *"
            value={formData.nombre}
            onChangeText={(text) => handleInputChange('nombre', text)}
            placeholderTextColor="#999"
          />

          <TextInput
            style={styles.input}
            placeholder="Email *"
            value={formData.email}
            onChangeText={(text) => handleInputChange('email', text)}
            keyboardType="email-address"
            autoCapitalize="none"
            placeholderTextColor="#999"
          />

          <TextInput
            style={styles.input}
            placeholder="Teléfono *"
            value={formData.telefono}
            onChangeText={(text) => handleInputChange('telefono', text)}
            keyboardType="phone-pad"
            placeholderTextColor="#999"
          />

          <TextInput
            style={styles.input}
            placeholder="Contraseña *"
            value={formData.password}
            onChangeText={(text) => handleInputChange('password', text)}
            secureTextEntry
            placeholderTextColor="#999"
          />

          <Text style={styles.sectionTitle}>🔧 Datos del Taller</Text>

          <TextInput
            style={styles.input}
            placeholder="Nombre del Taller *"
            value={formData.username}
            onChangeText={(text) => handleInputChange('username', text)}
            placeholderTextColor="#999"
          />

          <TextInput
            style={styles.input}
            placeholder="Dirección del Taller"
            value={formData.direccion}
            onChangeText={(text) => handleInputChange('direccion', text)}
            placeholderTextColor="#999"
          />

          <TextInput
            style={styles.input}
            placeholder="Rubro Principal (ej: Mecánica General)"
            value={formData.rubro_principal}
            onChangeText={(text) => handleInputChange('rubro_principal', text)}
            placeholderTextColor="#999"
          />

          <TextInput
            style={styles.input}
            placeholder="Datos Fiscales (CUIT/DNI)"
            value={formData.datos_fiscales}
            onChangeText={(text) => handleInputChange('datos_fiscales', text)}
            placeholderTextColor="#999"
          />

          <TouchableOpacity 
            style={[styles.btnRegistrar, loading && styles.btnDisabled]} 
            onPress={registrarTaller}
            disabled={loading}
          >
            <Text style={styles.btnRegistrarText}>
              {loading ? '🔄 Creando cuenta...' : '🚀 Crear mi Cuenta RepuMovil'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={styles.btnVolver}
            onPress={() => router.push('/seleccionar-plan')}
          >
            <Text style={styles.btnVolverText}>← Volver a Planes</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#667eea',
  },
  content: {
    flex: 1,
  },
  header: {
    alignItems: 'center',
    paddingVertical: 30,
    paddingHorizontal: 20,
  },
  logoText: {
    fontSize: 32,
    fontWeight: '900',
    color: 'white',
    marginBottom: 10,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 2, height: 2 },
    textShadowRadius: 4,
  },
  logoRepu: {
    color: '#FF6B35',
  },
  logoMovil: {
    color: '#FFE4B5',
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    lineHeight: 22,
  },
  featureHighlight: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    marginHorizontal: 20,
    borderRadius: 15,
    padding: 20,
    marginBottom: 20,
    borderLeftWidth: 4,
    borderLeftColor: '#4CAF50',
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: '#333',
    marginBottom: 10,
  },
  featureList: {
    gap: 5,
  },
  featureItem: {
    fontSize: 14,
    color: '#4CAF50',
    fontWeight: '600',
  },
  formContainer: {
    backgroundColor: 'white',
    marginHorizontal: 20,
    borderRadius: 25,
    padding: 25,
    marginBottom: 30,
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 5 },
    shadowOpacity: 0.3,
    shadowRadius: 10,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#333',
    marginBottom: 15,
    marginTop: 10,
  },
  input: {
    borderWidth: 2,
    borderColor: '#e9ecef',
    borderRadius: 12,
    padding: 15,
    fontSize: 16,
    marginBottom: 15,
    backgroundColor: '#fff',
  },
  btnRegistrar: {
    backgroundColor: '#FF6B35',
    borderRadius: 12,
    padding: 18,
    alignItems: 'center',
    marginTop: 20,
    elevation: 3,
    shadowColor: '#FF6B35',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  btnDisabled: {
    backgroundColor: '#ccc',
    elevation: 0,
    shadowOpacity: 0,
  },
  btnRegistrarText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '700',
  },
  btnVolver: {
    alignItems: 'center',
    marginTop: 15,
    padding: 10,
  },
  btnVolverText: {
    color: '#666',
    fontSize: 16,
    fontWeight: '600',
  },
});
