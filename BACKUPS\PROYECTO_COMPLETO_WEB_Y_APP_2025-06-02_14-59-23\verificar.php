<?php
// Mostrar todos los errores
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Información básica
echo "<h1>Verificación del Servidor</h1>";

// Verificar la versión de PHP
echo "<h2>Información de PHP:</h2>";
echo "<p>Versión de PHP: " . phpversion() . "</p>";

// Verificar extensiones de PHP
echo "<h2>Extensiones de PHP:</h2>";
$extensiones = [
    'pdo',
    'pdo_mysql',
    'mysqli',
    'session',
    'json',
    'mbstring',
    'xml',
    'curl'
];

echo "<ul>";
foreach ($extensiones as $extension) {
    if (extension_loaded($extension)) {
        echo "<li>$extension: ✅ Cargada</li>";
    } else {
        echo "<li>$extension: ❌ No cargada</li>";
    }
}
echo "</ul>";

// Verificar permisos de archivos
echo "<h2>Permisos de archivos:</h2>";
$archivos = [
    '.' => 'Directorio actual',
    'pagina_simple.html' => 'Página simple',
    'info.php' => 'Información de PHP',
    'verificar.php' => 'Este archivo'
];

echo "<ul>";
foreach ($archivos as $archivo => $descripcion) {
    if (file_exists($archivo)) {
        echo "<li>$descripcion ($archivo): ✅ Existe";
        if (is_writable($archivo)) {
            echo " (Escritura: ✅)";
        } else {
            echo " (Escritura: ❌)";
        }
        if (is_readable($archivo)) {
            echo " (Lectura: ✅)";
        } else {
            echo " (Lectura: ❌)";
        }
        echo "</li>";
    } else {
        echo "<li>$descripcion ($archivo): ❌ No existe</li>";
    }
}
echo "</ul>";

// Verificar la configuración del servidor
echo "<h2>Configuración del servidor:</h2>";
echo "<p>Servidor: " . $_SERVER['SERVER_SOFTWARE'] . "</p>";
echo "<p>Documento raíz: " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p>Ruta del script: " . $_SERVER['SCRIPT_FILENAME'] . "</p>";
echo "<p>URI del script: " . $_SERVER['SCRIPT_NAME'] . "</p>";

// Verificar la configuración de PHP
echo "<h2>Configuración de PHP:</h2>";
$configuraciones = [
    'display_errors',
    'error_reporting',
    'max_execution_time',
    'memory_limit',
    'post_max_size',
    'upload_max_filesize',
    'session.save_path',
    'session.gc_maxlifetime'
];

echo "<ul>";
foreach ($configuraciones as $configuracion) {
    echo "<li>$configuracion: " . ini_get($configuracion) . "</li>";
}
echo "</ul>";

// Mostrar enlaces útiles
echo "<h2>Enlaces útiles:</h2>";
echo "<ul>";
echo "<li><a href='pagina_simple.html'>Página simple</a></li>";
echo "<li><a href='info.php'>Información de PHP</a></li>";
echo "</ul>";

// Verificar si se puede crear un archivo
echo "<h2>Prueba de creación de archivo:</h2>";
$archivo_prueba = 'prueba_' . time() . '.txt';
$contenido = "Este es un archivo de prueba creado el " . date('Y-m-d H:i:s');

try {
    $resultado = file_put_contents($archivo_prueba, $contenido);
    if ($resultado !== false) {
        echo "<p>✅ Archivo creado correctamente: $archivo_prueba</p>";
        echo "<p>Contenido: $contenido</p>";
        
        // Intentar eliminar el archivo
        if (unlink($archivo_prueba)) {
            echo "<p>✅ Archivo eliminado correctamente</p>";
        } else {
            echo "<p>❌ No se pudo eliminar el archivo</p>";
        }
    } else {
        echo "<p>❌ No se pudo crear el archivo</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Error al crear el archivo: " . $e->getMessage() . "</p>";
}

// Verificar si se puede usar sesiones
echo "<h2>Prueba de sesiones:</h2>";
session_start();
$_SESSION['prueba'] = 'Esto es una prueba de sesión';
echo "<p>Valor de sesión: " . $_SESSION['prueba'] . "</p>";
echo "<p>ID de sesión: " . session_id() . "</p>";
echo "<p>Ruta de guardado de sesiones: " . session_save_path() . "</p>";

// Verificar si se puede conectar a MySQL
echo "<h2>Prueba de conexión a MySQL:</h2>";
try {
    $conn = new PDO("mysql:host=localhost", "root", "");
    echo "<p>✅ Conexión a MySQL exitosa</p>";
    
    // Verificar si se puede crear una base de datos
    try {
        $conn->exec("CREATE DATABASE IF NOT EXISTS test_db");
        echo "<p>✅ Base de datos creada correctamente</p>";
        
        // Verificar si se puede eliminar la base de datos
        try {
            $conn->exec("DROP DATABASE test_db");
            echo "<p>✅ Base de datos eliminada correctamente</p>";
        } catch (PDOException $e) {
            echo "<p>❌ No se pudo eliminar la base de datos: " . $e->getMessage() . "</p>";
        }
    } catch (PDOException $e) {
        echo "<p>❌ No se pudo crear la base de datos: " . $e->getMessage() . "</p>";
    }
} catch (PDOException $e) {
    echo "<p>❌ Error de conexión a MySQL: " . $e->getMessage() . "</p>";
}
?>
