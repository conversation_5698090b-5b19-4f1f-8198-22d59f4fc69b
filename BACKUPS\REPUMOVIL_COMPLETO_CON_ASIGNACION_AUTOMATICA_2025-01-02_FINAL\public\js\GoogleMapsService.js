/**
 * Google Maps Service para RepuMovil Web
 * Maneja todas las funciones de mapas, ubicación y rutas
 */
class GoogleMapsService {
    constructor() {
        this.apiKey = 'AIzaSyDeF7xLqA58aAm5Gou7Nux3A7_jwGnnVxg'; // API Key real de Google Maps
        this.map = null;
        this.directionsService = null;
        this.directionsRenderer = null;
        this.markers = [];
        this.watchId = null;
        this.isTracking = false;
        this.lastKnownLocation = null;
        this.locationCallbacks = [];
    }

    // Inicializar Google Maps
    async initMap(containerId, options = {}) {
        const defaultOptions = {
            zoom: 15,
            center: { lat: -31.5375, lng: -68.5364 }, // San Juan, Argentina
            mapTypeId: google.maps.MapTypeId.ROADMAP,
            styles: this.getMapStyles()
        };

        const mapOptions = { ...defaultOptions, ...options };
        
        this.map = new google.maps.Map(document.getElementById(containerId), mapOptions);
        this.directionsService = new google.maps.DirectionsService();
        this.directionsRenderer = new google.maps.DirectionsRenderer({
            suppressMarkers: false,
            polylineOptions: {
                strokeColor: '#4CAF50',
                strokeWeight: 4,
                strokeOpacity: 0.8
            }
        });
        this.directionsRenderer.setMap(this.map);

        console.log('🗺️ Google Maps inicializado correctamente');
        return this.map;
    }

    // Estilos personalizados del mapa
    getMapStyles() {
        return [
            {
                featureType: 'poi',
                elementType: 'labels',
                stylers: [{ visibility: 'off' }]
            },
            {
                featureType: 'transit',
                elementType: 'labels',
                stylers: [{ visibility: 'off' }]
            }
        ];
    }

    // Obtener ubicación actual
    async getCurrentLocation() {
        return new Promise((resolve, reject) => {
            if (!navigator.geolocation) {
                reject(new Error('Geolocalización no soportada'));
                return;
            }

            navigator.geolocation.getCurrentPosition(
                (position) => {
                    const location = {
                        lat: position.coords.latitude,
                        lng: position.coords.longitude,
                        accuracy: position.coords.accuracy,
                        timestamp: position.timestamp
                    };
                    
                    this.lastKnownLocation = location;
                    console.log('📍 Ubicación obtenida:', location);
                    resolve(location);
                },
                (error) => {
                    console.error('❌ Error obteniendo ubicación:', error);
                    reject(error);
                },
                {
                    enableHighAccuracy: true,
                    timeout: 10000,
                    maximumAge: 60000
                }
            );
        });
    }

    // Iniciar tracking de ubicación
    startLocationTracking(callback) {
        if (this.isTracking) {
            console.log('⚠️ Ya se está rastreando la ubicación');
            return;
        }

        if (!navigator.geolocation) {
            console.error('❌ Geolocalización no soportada');
            return;
        }

        if (callback) {
            this.locationCallbacks.push(callback);
        }

        this.watchId = navigator.geolocation.watchPosition(
            (position) => {
                const location = {
                    lat: position.coords.latitude,
                    lng: position.coords.longitude,
                    accuracy: position.coords.accuracy,
                    timestamp: position.timestamp
                };

                this.lastKnownLocation = location;
                
                // Notificar a todos los callbacks
                this.locationCallbacks.forEach(cb => cb(location));
                
                console.log('📍 Ubicación actualizada:', location);
            },
            (error) => {
                console.error('❌ Error en tracking:', error);
            },
            {
                enableHighAccuracy: true,
                timeout: 5000,
                maximumAge: 0
            }
        );

        this.isTracking = true;
        console.log('🎯 Tracking de ubicación iniciado');
    }

    // Detener tracking
    stopLocationTracking() {
        if (this.watchId) {
            navigator.geolocation.clearWatch(this.watchId);
            this.watchId = null;
        }
        this.isTracking = false;
        this.locationCallbacks = [];
        console.log('⏹️ Tracking de ubicación detenido');
    }

    // Agregar marker al mapa
    addMarker(position, options = {}) {
        const defaultOptions = {
            position: position,
            map: this.map,
            title: 'Marker',
            icon: null
        };

        const markerOptions = { ...defaultOptions, ...options };
        const marker = new google.maps.Marker(markerOptions);
        
        this.markers.push(marker);
        return marker;
    }

    // Crear marker personalizado
    createCustomMarker(position, type, title) {
        let icon;
        
        switch (type) {
            case 'repartidor':
                icon = {
                    url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                        <svg width="40" height="40" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="20" cy="20" r="18" fill="#4CAF50" stroke="#fff" stroke-width="3"/>
                            <text x="20" y="28" text-anchor="middle" font-size="16">🏍️</text>
                        </svg>
                    `),
                    scaledSize: new google.maps.Size(40, 40),
                    anchor: new google.maps.Point(20, 20)
                };
                break;
            case 'cliente':
                icon = {
                    url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                        <svg width="40" height="40" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="20" cy="20" r="18" fill="#FF6B35" stroke="#fff" stroke-width="3"/>
                            <text x="20" y="28" text-anchor="middle" font-size="16">📍</text>
                        </svg>
                    `),
                    scaledSize: new google.maps.Size(40, 40),
                    anchor: new google.maps.Point(20, 20)
                };
                break;
            case 'proveedor':
                icon = {
                    url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                        <svg width="40" height="40" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="20" cy="20" r="18" fill="#9C27B0" stroke="#fff" stroke-width="3"/>
                            <text x="20" y="28" text-anchor="middle" font-size="16">🏪</text>
                        </svg>
                    `),
                    scaledSize: new google.maps.Size(40, 40),
                    anchor: new google.maps.Point(20, 20)
                };
                break;
            default:
                icon = null;
        }

        return this.addMarker(position, {
            title: title,
            icon: icon
        });
    }

    // Calcular y mostrar ruta
    async calculateRoute(origin, destination) {
        return new Promise((resolve, reject) => {
            const request = {
                origin: origin,
                destination: destination,
                travelMode: google.maps.TravelMode.DRIVING,
                unitSystem: google.maps.UnitSystem.METRIC,
                avoidHighways: false,
                avoidTolls: false
            };

            this.directionsService.route(request, (result, status) => {
                if (status === 'OK') {
                    this.directionsRenderer.setDirections(result);
                    
                    const route = result.routes[0];
                    const leg = route.legs[0];
                    
                    const routeInfo = {
                        distance: leg.distance.text,
                        duration: leg.duration.text,
                        distanceValue: leg.distance.value,
                        durationValue: leg.duration.value
                    };
                    
                    console.log('🛣️ Ruta calculada:', routeInfo);
                    resolve(routeInfo);
                } else {
                    console.error('❌ Error calculando ruta:', status);
                    reject(new Error('Error calculando ruta: ' + status));
                }
            });
        });
    }

    // Centrar mapa en ubicación
    centerMap(location, zoom = 15) {
        if (this.map) {
            this.map.setCenter(location);
            this.map.setZoom(zoom);
        }
    }

    // Ajustar mapa para mostrar todos los markers
    fitBounds() {
        if (this.markers.length === 0) return;

        const bounds = new google.maps.LatLngBounds();
        this.markers.forEach(marker => {
            bounds.extend(marker.getPosition());
        });
        
        this.map.fitBounds(bounds);
        
        // Asegurar zoom mínimo
        google.maps.event.addListenerOnce(this.map, 'bounds_changed', () => {
            if (this.map.getZoom() > 16) {
                this.map.setZoom(16);
            }
        });
    }

    // Limpiar todos los markers
    clearMarkers() {
        this.markers.forEach(marker => {
            marker.setMap(null);
        });
        this.markers = [];
    }

    // Calcular distancia entre dos puntos
    calculateDistance(point1, point2) {
        const R = 6371; // Radio de la Tierra en km
        const dLat = this.toRad(point2.lat - point1.lat);
        const dLng = this.toRad(point2.lng - point1.lng);
        
        const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                  Math.cos(this.toRad(point1.lat)) * Math.cos(this.toRad(point2.lat)) *
                  Math.sin(dLng / 2) * Math.sin(dLng / 2);
        
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        const distance = R * c;
        
        return Math.round(distance * 100) / 100; // Redondear a 2 decimales
    }

    // Convertir grados a radianes
    toRad(degrees) {
        return degrees * (Math.PI / 180);
    }

    // Formatear distancia
    formatDistance(meters) {
        if (meters < 1000) {
            return Math.round(meters) + ' m';
        } else {
            return (meters / 1000).toFixed(1) + ' km';
        }
    }

    // Formatear duración
    formatDuration(seconds) {
        const minutes = Math.round(seconds / 60);
        if (minutes < 60) {
            return minutes + ' min';
        } else {
            const hours = Math.floor(minutes / 60);
            const remainingMinutes = minutes % 60;
            return hours + 'h ' + remainingMinutes + 'min';
        }
    }

    // Obtener última ubicación conocida
    getLastKnownLocation() {
        return this.lastKnownLocation;
    }

    // Verificar si está rastreando
    isLocationTracking() {
        return this.isTracking;
    }

    // Destruir instancia
    destroy() {
        this.stopLocationTracking();
        this.clearMarkers();
        if (this.directionsRenderer) {
            this.directionsRenderer.setMap(null);
        }
        this.map = null;
    }
}

// Hacer disponible globalmente
window.GoogleMapsService = GoogleMapsService;
