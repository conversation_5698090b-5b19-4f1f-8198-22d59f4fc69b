import React, { useEffect, useState } from 'react';
import {
  SafeAreaView,
  StatusBar,
  StyleSheet,
  View,
  Text,
  Alert,
} from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { MaterialIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import * as ExpoSplashScreen from 'expo-splash-screen';

// Importar pantallas
import SplashScreenComponent from './src/screens/SplashScreen';
import LoginScreen from './src/screens/LoginScreen';
import RegisterScreen from './src/screens/RegisterScreen';
import DashboardScreen from './src/screens/DashboardScreen';
import PedidosScreen from './src/screens/PedidosScreen';
import GananciasScreen from './src/screens/GananciasScreen';
import PerfilScreen from './src/screens/PerfilScreen';
import MapaScreen from './src/screens/MapaScreen';

// Configuración de navegación
const Stack = createStackNavigator();
const Tab = createBottomTabNavigator();

// Colores del tema
const COLORS = {
  primary: '#FF6B35',
  secondary: '#F7931E',
  red: '#E53E3E',
  redLight: '#FC8181',
  white: '#FFFFFF',
  dark: '#2D3748',
  lightGray: '#F7FAFC',
  gradient: ['#FF6B35', '#E53E3E', '#F7931E'],
};

// Navegador de pestañas principales
function MainTabNavigator() {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName;

          switch (route.name) {
            case 'Dashboard':
              iconName = 'dashboard';
              break;
            case 'Pedidos':
              iconName = 'assignment';
              break;
            case 'Mapa':
              iconName = 'map';
              break;
            case 'Ganancias':
              iconName = 'attach-money';
              break;
            case 'Perfil':
              iconName = 'person';
              break;
            default:
              iconName = 'help';
          }

          return <MaterialIcons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: COLORS.red,
        tabBarInactiveTintColor: '#8E8E93',
        tabBarStyle: {
          backgroundColor: COLORS.white,
          borderTopWidth: 0,
          elevation: 10,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: -2 },
          shadowOpacity: 0.1,
          shadowRadius: 10,
          height: 60,
          paddingBottom: 8,
          paddingTop: 8,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '600',
        },
        headerShown: false,
      })}
    >
      <Tab.Screen 
        name="Dashboard" 
        component={DashboardScreen}
        options={{ tabBarLabel: 'Inicio' }}
      />
      <Tab.Screen 
        name="Pedidos" 
        component={PedidosScreen}
        options={{ tabBarLabel: 'Pedidos' }}
      />
      <Tab.Screen 
        name="Mapa" 
        component={MapaScreen}
        options={{ tabBarLabel: 'Mapa' }}
      />
      <Tab.Screen 
        name="Ganancias" 
        component={GananciasScreen}
        options={{ tabBarLabel: 'Ganancias' }}
      />
      <Tab.Screen 
        name="Perfil" 
        component={PerfilScreen}
        options={{ tabBarLabel: 'Perfil' }}
      />
    </Tab.Navigator>
  );
}

// Componente principal de la app
function App() {
  const [isLoading, setIsLoading] = useState(true);
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  useEffect(() => {
    checkLoginStatus();
  }, []);

  const checkLoginStatus = async () => {
    try {
      // Prevenir que el splash screen se oculte automáticamente
      await ExpoSplashScreen.preventAutoHideAsync();

      const token = await AsyncStorage.getItem('userToken');
      const userData = await AsyncStorage.getItem('userData');

      if (token && userData) {
        setIsLoggedIn(true);
      }
    } catch (error) {
      console.log('Error checking login status:', error);
    } finally {
      setTimeout(async () => {
        setIsLoading(false);
        // Ocultar el splash screen de Expo
        await ExpoSplashScreen.hideAsync();
      }, 2000); // Mostrar splash por 2 segundos
    }
  };

  if (isLoading) {
    return <SplashScreenComponent />;
  }

  return (
    <NavigationContainer>
      <StatusBar
        barStyle="light-content"
        backgroundColor={COLORS.red}
        translucent={false}
      />
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
          cardStyle: { backgroundColor: COLORS.white },
        }}
      >
        {isLoggedIn ? (
          // Usuario logueado - Mostrar app principal
          <Stack.Screen name="MainApp" component={MainTabNavigator} />
        ) : (
          // Usuario no logueado - Mostrar pantallas de auth
          <>
            <Stack.Screen name="Login" component={LoginScreen} />
            <Stack.Screen name="Register" component={RegisterScreen} />
          </>
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
});

export default App;
export { COLORS };
