<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RepuMovil - Mis Calificaciones ⭐</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #FF6B35;
            --secondary-color: #FFA500;
            --dark-color: #2c3e50;
            --light-color: #f8f9fa;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
            color: white;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .title {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .stats-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            color: var(--dark-color);
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #666;
        }

        .rating-breakdown {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            color: var(--dark-color);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .breakdown-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 20px;
            text-align: center;
        }

        .rating-row {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .rating-stars {
            min-width: 100px;
            font-size: 0.9rem;
        }

        .rating-bar {
            flex: 1;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            margin: 0 15px;
            overflow: hidden;
        }

        .rating-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            border-radius: 4px;
            transition: width 0.5s ease;
        }

        .rating-count {
            min-width: 40px;
            text-align: right;
            font-weight: bold;
            color: var(--primary-color);
        }

        .calificaciones-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
        }

        .calificacion-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            color: var(--dark-color);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: transform 0.3s ease;
        }

        .calificacion-card:hover {
            transform: translateY(-5px);
        }

        .calificacion-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .calificacion-stars {
            font-size: 1.2rem;
            color: #ffd700;
        }

        .calificacion-fecha {
            font-size: 0.8rem;
            color: #666;
        }

        .cliente-info {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }

        .cliente-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--info-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .cliente-details {
            flex: 1;
        }

        .cliente-nombre {
            font-weight: bold;
            color: var(--primary-color);
        }

        .pedido-info {
            font-size: 0.8rem;
            color: #666;
        }

        .comentario {
            background: var(--light-color);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            font-style: italic;
            border-left: 4px solid var(--primary-color);
        }

        .tags {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin-bottom: 15px;
        }

        .tag {
            background: var(--primary-color);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: bold;
        }

        .respuesta-section {
            border-top: 1px solid #eee;
            padding-top: 15px;
        }

        .respuesta-existente {
            background: #e8f5e8;
            padding: 12px;
            border-radius: 8px;
            border-left: 4px solid var(--success-color);
            margin-bottom: 10px;
        }

        .respuesta-label {
            font-size: 0.8rem;
            font-weight: bold;
            color: var(--success-color);
            margin-bottom: 5px;
        }

        .respuesta-texto {
            font-size: 0.9rem;
            color: #2d5a2d;
        }

        .respuesta-fecha {
            font-size: 0.7rem;
            color: #666;
            margin-top: 5px;
        }

        .respuesta-form {
            display: none;
        }

        .respuesta-textarea {
            width: 100%;
            min-height: 80px;
            padding: 10px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-family: inherit;
            font-size: 0.9rem;
            resize: vertical;
            margin-bottom: 10px;
        }

        .respuesta-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .respuesta-buttons {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 0.9rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: #e55a2b;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .btn-outline {
            background: transparent;
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
        }

        .btn-outline:hover {
            background: var(--primary-color);
            color: white;
        }

        .no-calificaciones {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .no-calificaciones-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        @media (max-width: 768px) {
            .calificaciones-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-overview {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">⭐📊</div>
            <h1 class="title">Mis Calificaciones</h1>
            <p class="subtitle">Juan Pérez - Delivery RepuMovil</p>
        </div>

        <!-- Estadísticas Generales -->
        <div class="stats-overview">
            <div class="stat-card">
                <div class="stat-number" id="promedio-general">4.8</div>
                <div class="stat-label">Calificación Promedio</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="total-calificaciones">24</div>
                <div class="stat-label">Total Calificaciones</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="calificaciones-mes">8</div>
                <div class="stat-label">Este Mes</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="tasa-respuesta">75%</div>
                <div class="stat-label">Tasa de Respuesta</div>
            </div>
        </div>

        <!-- Desglose de Calificaciones -->
        <div class="rating-breakdown">
            <div class="breakdown-title">
                <i class="fas fa-chart-bar"></i>
                Desglose de Calificaciones
            </div>
            
            <div class="rating-row">
                <div class="rating-stars">⭐⭐⭐⭐⭐ 5</div>
                <div class="rating-bar">
                    <div class="rating-fill" style="width: 75%"></div>
                </div>
                <div class="rating-count">18</div>
            </div>
            
            <div class="rating-row">
                <div class="rating-stars">⭐⭐⭐⭐ 4</div>
                <div class="rating-bar">
                    <div class="rating-fill" style="width: 20%"></div>
                </div>
                <div class="rating-count">5</div>
            </div>
            
            <div class="rating-row">
                <div class="rating-stars">⭐⭐⭐ 3</div>
                <div class="rating-bar">
                    <div class="rating-fill" style="width: 4%"></div>
                </div>
                <div class="rating-count">1</div>
            </div>
            
            <div class="rating-row">
                <div class="rating-stars">⭐⭐ 2</div>
                <div class="rating-bar">
                    <div class="rating-fill" style="width: 0%"></div>
                </div>
                <div class="rating-count">0</div>
            </div>
            
            <div class="rating-row">
                <div class="rating-stars">⭐ 1</div>
                <div class="rating-bar">
                    <div class="rating-fill" style="width: 0%"></div>
                </div>
                <div class="rating-count">0</div>
            </div>
        </div>

        <!-- Grid de Calificaciones -->
        <div class="calificaciones-grid" id="calificaciones-container">
            <!-- Las calificaciones se cargarán dinámicamente -->
        </div>
    </div>

    <script>
        // PASO 9: Datos de ejemplo de calificaciones
        const calificaciones = [
            {
                id: 1,
                calificacion: 5,
                comentario: 'Excelente servicio, muy rápido y amable. El pedido llegó en perfecto estado.',
                cliente_nombre: 'Carlos Méndez',
                pedido_id: '1024',
                direccion: 'Av. Libertador 1234',
                fecha: '2024-01-15 14:30:00',
                tags: ['rápido', 'amable', 'profesional'],
                respuesta: '¡Muchas gracias por tu comentario! Siempre trato de dar lo mejor en cada entrega. ¡Espero verte pronto!',
                fecha_respuesta: '2024-01-15 15:45:00'
            },
            {
                id: 2,
                calificacion: 4,
                comentario: 'Buen servicio, aunque se demoró un poco más de lo esperado.',
                cliente_nombre: 'Ana García',
                pedido_id: '1023',
                direccion: 'Calle San Martín 567',
                fecha: '2024-01-14 16:20:00',
                tags: ['buena comunicación', 'pedido perfecto'],
                respuesta: null,
                fecha_respuesta: null
            },
            {
                id: 3,
                calificacion: 5,
                comentario: 'Increíble! Juan es muy profesional y el tracking funcionó perfecto.',
                cliente_nombre: 'Roberto Silva',
                pedido_id: '1022',
                direccion: 'Barrio Norte, Casa 45',
                fecha: '2024-01-13 11:15:00',
                tags: ['profesional', 'tracking perfecto', 'muy recomendado'],
                respuesta: '¡Wow! Muchas gracias por tan lindo comentario. Me motiva a seguir mejorando cada día.',
                fecha_respuesta: '2024-01-13 12:30:00'
            },
            {
                id: 4,
                calificacion: 5,
                comentario: 'Súper rápido y muy amable. Definitivamente lo recomiendo.',
                cliente_nombre: 'Laura Fernández',
                pedido_id: '1021',
                direccion: 'Zona Industrial, Galpón 12',
                fecha: '2024-01-12 09:45:00',
                tags: ['súper rápido', 'muy amable', 'recomendado'],
                respuesta: null,
                fecha_respuesta: null
            }
        ];

        // PASO 9: Renderizar calificaciones
        function renderCalificaciones() {
            const container = document.getElementById('calificaciones-container');
            
            if (calificaciones.length === 0) {
                container.innerHTML = `
                    <div class="no-calificaciones">
                        <div class="no-calificaciones-icon">⭐</div>
                        <h3>Aún no tienes calificaciones</h3>
                        <p>Completa más entregas para recibir feedback de tus clientes</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = calificaciones.map(cal => `
                <div class="calificacion-card">
                    <div class="calificacion-header">
                        <div class="calificacion-stars">${'⭐'.repeat(cal.calificacion)}</div>
                        <div class="calificacion-fecha">${formatearFecha(cal.fecha)}</div>
                    </div>
                    
                    <div class="cliente-info">
                        <div class="cliente-avatar">${cal.cliente_nombre.charAt(0)}</div>
                        <div class="cliente-details">
                            <div class="cliente-nombre">${cal.cliente_nombre}</div>
                            <div class="pedido-info">Pedido #${cal.pedido_id} • ${cal.direccion}</div>
                        </div>
                    </div>

                    ${cal.comentario ? `<div class="comentario">"${cal.comentario}"</div>` : ''}

                    ${cal.tags && cal.tags.length > 0 ? `
                        <div class="tags">
                            ${cal.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                        </div>
                    ` : ''}

                    <div class="respuesta-section">
                        ${cal.respuesta ? `
                            <div class="respuesta-existente">
                                <div class="respuesta-label">Tu respuesta:</div>
                                <div class="respuesta-texto">${cal.respuesta}</div>
                                <div class="respuesta-fecha">${formatearFecha(cal.fecha_respuesta)}</div>
                            </div>
                        ` : `
                            <button class="btn btn-outline" onclick="mostrarFormularioRespuesta(${cal.id})">
                                <i class="fas fa-reply"></i> Responder
                            </button>
                            <div class="respuesta-form" id="form-${cal.id}">
                                <textarea 
                                    class="respuesta-textarea" 
                                    id="textarea-${cal.id}"
                                    placeholder="Escribe una respuesta amable y profesional..."
                                    maxlength="300"
                                ></textarea>
                                <div class="respuesta-buttons">
                                    <button class="btn btn-primary" onclick="enviarRespuesta(${cal.id})">
                                        <i class="fas fa-paper-plane"></i> Enviar
                                    </button>
                                    <button class="btn btn-secondary" onclick="cancelarRespuesta(${cal.id})">
                                        Cancelar
                                    </button>
                                </div>
                            </div>
                        `}
                    </div>
                </div>
            `).join('');
        }

        // PASO 9: Formatear fecha
        function formatearFecha(fechaStr) {
            const fecha = new Date(fechaStr);
            return fecha.toLocaleDateString('es-ES', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // PASO 9: Mostrar formulario de respuesta
        function mostrarFormularioRespuesta(calificacionId) {
            const form = document.getElementById(`form-${calificacionId}`);
            form.style.display = 'block';
            document.getElementById(`textarea-${calificacionId}`).focus();
        }

        // PASO 9: Cancelar respuesta
        function cancelarRespuesta(calificacionId) {
            const form = document.getElementById(`form-${calificacionId}`);
            form.style.display = 'none';
            document.getElementById(`textarea-${calificacionId}`).value = '';
        }

        // PASO 9: Enviar respuesta
        async function enviarRespuesta(calificacionId) {
            const textarea = document.getElementById(`textarea-${calificacionId}`);
            const respuesta = textarea.value.trim();
            
            if (!respuesta) {
                alert('Por favor, escribe una respuesta antes de enviar.');
                return;
            }

            try {
                // Simular llamada a API
                const response = await fetch('api/calificaciones.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'responder_calificacion',
                        calificacion_id: calificacionId,
                        delivery_id: 1, // ID del delivery actual
                        respuesta: respuesta
                    })
                });

                // Simular respuesta exitosa
                await new Promise(resolve => setTimeout(resolve, 1000));

                // Actualizar calificación local
                const calificacion = calificaciones.find(c => c.id === calificacionId);
                if (calificacion) {
                    calificacion.respuesta = respuesta;
                    calificacion.fecha_respuesta = new Date().toISOString();
                }

                // Re-renderizar
                renderCalificaciones();

                alert('¡Respuesta enviada exitosamente! El cliente recibirá una notificación.');

            } catch (error) {
                console.error('Error enviando respuesta:', error);
                alert('Error al enviar la respuesta. Por favor, intenta de nuevo.');
            }
        }

        // PASO 9: Inicializar
        document.addEventListener('DOMContentLoaded', function() {
            renderCalificaciones();
        });
    </script>
</body>
</html>
