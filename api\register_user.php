<?php
// Mostrar todos los errores para depuración
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Incluir archivo de configuración de la base de datos
require_once '../public/db_config.php';

// Configurar cabeceras para API
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Verificar método de solicitud
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405); // Method Not Allowed
    echo json_encode(['status' => 'error', 'message' => 'Método no permitido']);
    exit;
}

// Obtener datos JSON de la solicitud
$json_data = file_get_contents('php://input');
$data = json_decode($json_data, true);

// Verificar si los datos son válidos
if (!$data || !isset($data['username']) || !isset($data['email']) || !isset($data['password']) || !isset($data['role_id'])) {
    http_response_code(400); // Bad Request
    echo json_encode(['status' => 'error', 'message' => 'Datos incompletos']);
    exit;
}

// Extraer datos
$username = trim($data['username']);
$email = trim($data['email']);
$password = $data['password'];
$role_id = (int)$data['role_id'];
$business_name = isset($data['business_name']) ? trim($data['business_name']) : '';
$address = isset($data['address']) ? trim($data['address']) : '';
$phone = isset($data['phone']) ? trim($data['phone']) : '';
$maps_url = isset($data['maps_url']) ? trim($data['maps_url']) : '';
$description = isset($data['description']) ? trim($data['description']) : '';
$specialties = isset($data['specialties']) ? trim($data['specialties']) : '';
$experience = isset($data['experience']) ? trim($data['experience']) : '';
$first_name = isset($data['first_name']) ? trim($data['first_name']) : '';
$last_name = isset($data['last_name']) ? trim($data['last_name']) : '';

// Validar campos
if (empty($username) || empty($email) || empty($password) || empty($role_id)) {
    http_response_code(400);
    echo json_encode(['status' => 'error', 'message' => 'Por favor, complete todos los campos obligatorios']);
    exit;
}

if (strlen($password) < 6) {
    http_response_code(400);
    echo json_encode(['status' => 'error', 'message' => 'La contraseña debe tener al menos 6 caracteres']);
    exit;
}

if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    http_response_code(400);
    echo json_encode(['status' => 'error', 'message' => 'Por favor, ingrese un correo electrónico válido']);
    exit;
}

try {
    // Conectar a la base de datos
    $conn = connectDB();
    
    // Verificar si el nombre de usuario ya existe
    $stmt = $conn->prepare("SELECT COUNT(*) FROM users WHERE username = :username");
    $stmt->execute(['username' => $username]);
    if ($stmt->fetchColumn() > 0) {
        http_response_code(409); // Conflict
        echo json_encode(['status' => 'error', 'message' => 'Este nombre de usuario ya está registrado']);
        exit;
    }
    
    // Verificar si el correo ya existe
    $stmt = $conn->prepare("SELECT COUNT(*) FROM users WHERE email = :email");
    $stmt->execute(['email' => $email]);
    if ($stmt->fetchColumn() > 0) {
        http_response_code(409); // Conflict
        echo json_encode(['status' => 'error', 'message' => 'Este correo electrónico ya está registrado']);
        exit;
    }
    
    // Iniciar transacción
    $conn->beginTransaction();
    
    try {
        // Crear usuario
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        $stmt = $conn->prepare("INSERT INTO users (username, email, password, role_id, status, created_at) VALUES (:username, :email, :password, :role_id, 'active', NOW())");
        $stmt->execute([
            'username' => $username,
            'email' => $email,
            'password' => $hashedPassword,
            'role_id' => $role_id
        ]);
        
        $userId = $conn->lastInsertId();
        
        // Crear perfil según tipo de usuario
        if ($role_id == 2) { // Taller
            $stmt = $conn->prepare("INSERT INTO workshops (user_id, name, address, phone, maps_url, description, created_at) VALUES (:user_id, :name, :address, :phone, :maps_url, :description, NOW())");
            $stmt->execute([
                'user_id' => $userId,
                'name' => $business_name ?: $username,
                'address' => $address,
                'phone' => $phone,
                'maps_url' => $maps_url,
                'description' => $description
            ]);
        } else if ($role_id == 3) { // Mecánico
            $stmt = $conn->prepare("INSERT INTO mechanics (user_id, name, specialties, experience, address, phone, maps_url, description, created_at) VALUES (:user_id, :name, :specialties, :experience, :address, :phone, :maps_url, :description, NOW())");
            $stmt->execute([
                'user_id' => $userId,
                'name' => $business_name ?: $username,
                'specialties' => $specialties,
                'experience' => $experience,
                'address' => $address,
                'phone' => $phone,
                'maps_url' => $maps_url,
                'description' => $description
            ]);
        } else if ($role_id == 4) { // Persona
            $stmt = $conn->prepare("INSERT INTO persons (user_id, first_name, last_name, phone, address, created_at) VALUES (:user_id, :first_name, :last_name, :phone, :address, NOW())");
            $stmt->execute([
                'user_id' => $userId,
                'first_name' => $first_name,
                'last_name' => $last_name,
                'phone' => $phone,
                'address' => $address
            ]);
        } else if ($role_id == 5) { // Proveedor
            $stmt = $conn->prepare("INSERT INTO suppliers (user_id, name, address, phone, maps_url, description, created_at) VALUES (:user_id, :name, :address, :phone, :maps_url, :description, NOW())");
            $stmt->execute([
                'user_id' => $userId,
                'name' => $business_name ?: $username,
                'address' => $address,
                'phone' => $phone,
                'maps_url' => $maps_url,
                'description' => $description
            ]);
        }
        
        $conn->commit();
        
        // Respuesta exitosa
        http_response_code(201); // Created
        echo json_encode([
            'status' => 'success',
            'message' => 'Usuario creado exitosamente',
            'user_id' => $userId,
            'username' => $username,
            'email' => $email,
            'role_id' => $role_id
        ]);
        
    } catch (Exception $e) {
        $conn->rollBack();
        http_response_code(500); // Internal Server Error
        echo json_encode(['status' => 'error', 'message' => 'Error al crear el usuario: ' . $e->getMessage()]);
    }
} catch (PDOException $e) {
    http_response_code(500); // Internal Server Error
    echo json_encode(['status' => 'error', 'message' => 'Error de conexión: ' . $e->getMessage()]);
}
?>
