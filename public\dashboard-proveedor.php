<?php
session_start();
require_once 'db_config.php';

// Verificar si el usuario está logueado y es proveedor
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'proveedor_repuestos') {
    header('Location: login-dinamico.php');
    exit();
}

$user_id = $_SESSION['user_id'];
$user_name = $_SESSION['user_name'] ?? 'Proveedor';

// Conectar a la base de datos
try {
    $pdo = connectDB();
} catch (Exception $e) {
    die("Error de conexión: " . $e->getMessage());
}

// Inicializar variables por defecto
$total_pedidos = 0;
$total_productos = 0;
$ventas_mes = 0;

// Obtener estadísticas del proveedor
try {
    // Contar pedidos recibidos
    $stmt = $pdo->prepare("SELECT COUNT(*) as total_pedidos FROM pedidos WHERE proveedor_id = ?");
    $stmt->execute([$user_id]);
    $result = $stmt->fetch();
    $total_pedidos = $result ? $result['total_pedidos'] : 0;

    // Contar productos en inventario
    $stmt = $pdo->prepare("SELECT COUNT(*) as total_productos FROM repuestos WHERE supplier_id = ?");
    $stmt->execute([$user_id]);
    $result = $stmt->fetch();
    $total_productos = $result ? $result['total_productos'] : 0;

    // Calcular ventas del mes
    $stmt = $pdo->prepare("
        SELECT COALESCE(SUM(total), 0) as ventas_mes
        FROM pedidos
        WHERE proveedor_id = ?
        AND MONTH(fecha_pedido) = MONTH(CURRENT_DATE())
        AND YEAR(fecha_pedido) = YEAR(CURRENT_DATE())
        AND estado = 'entregado'
    ");
    $stmt->execute([$user_id]);
    $result = $stmt->fetch();
    $ventas_mes = $result ? $result['ventas_mes'] : 0;

} catch (Exception $e) {
    // En caso de error, mantener valores por defecto
    error_log("Error obteniendo estadísticas: " . $e->getMessage());
}

try {
    // Obtener pedidos recientes
    $stmt = $pdo->prepare("
        SELECT p.*, u.nombre as cliente_nombre, u.telefono as cliente_telefono
        FROM pedidos p
        LEFT JOIN users u ON p.user_id = u.id
        WHERE p.proveedor_id = ?
        ORDER BY p.fecha_pedido DESC
        LIMIT 5
    ");
    $stmt->execute([$user_id]);
    $pedidos_recientes = $stmt->fetchAll();

} catch (PDOException $e) {
    $error_message = "Error al cargar datos: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Proveedor - RepuMovil</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #FF6B35;
            --secondary-color: #FFA500;
            --dark-color: #2c3e50;
            --light-color: #f8f9fa;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
        }

        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .logo h1 {
            color: white;
            font-size: 1.8rem;
            font-weight: 900;
        }

        .logo .repu { color: #FFE082; }
        .logo .movil { color: #E8F5E8; }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
            color: white;
        }

        .proveedor-badge {
            background: white;
            color: var(--primary-color);
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .welcome-section {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .welcome-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .welcome-subtitle {
            color: #666;
            font-size: 1.1rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }

        .functions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .function-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
            cursor: pointer;
            border-left: 5px solid var(--primary-color);
        }

        .function-card:hover {
            transform: translateY(-5px);
        }

        .function-card.primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-left: 5px solid white;
        }

        .function-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }

        .function-title {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .function-description {
            font-size: 0.9rem;
            opacity: 0.8;
            line-height: 1.4;
        }

        .recent-orders {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .section-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .order-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid #eee;
            transition: background 0.3s ease;
        }

        .order-item:hover {
            background: #f8f9fa;
        }

        .order-item:last-child {
            border-bottom: none;
        }

        .order-info h4 {
            color: #333;
            margin-bottom: 0.3rem;
        }

        .order-info p {
            color: #666;
            font-size: 0.9rem;
        }

        .order-status {
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
            text-transform: uppercase;
        }

        .status-pendiente { background: #fff3cd; color: #856404; }
        .status-preparando { background: #cce7ff; color: #004085; }
        .status-empacado { background: #d4edda; color: #155724; }
        .status-asignado { background: #d1ecf1; color: #0c5460; }
        .status-en_camino { background: #e2e3e5; color: #383d41; }
        .status-entregado { background: #d1ecf1; color: #0c5460; }

        /* PASO 2: Estilos para botón EMPACADO y gestión mejorada */
        .pedido-actions {
            display: flex;
            gap: 10px;
            margin-top: 10px;
            flex-wrap: wrap;
        }

        .action-btn {
            padding: 8px 15px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.8rem;
            font-weight: bold;
            text-transform: uppercase;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .btn-empacado {
            background: var(--success-color);
            color: white;
        }

        .btn-empacado:hover {
            background: #218838;
            transform: translateY(-2px);
        }

        .btn-ver-detalle {
            background: var(--info-color);
            color: white;
        }

        .btn-ver-detalle:hover {
            background: #138496;
        }

        .btn-contactar {
            background: var(--warning-color);
            color: #212529;
        }

        .btn-contactar:hover {
            background: #e0a800;
        }

        .empacado-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--success-color);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            z-index: 1000;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }

        .empacado-notification.show {
            transform: translateX(0);
        }

        .timer-badge {
            background: var(--primary-color);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: bold;
            margin-left: 10px;
        }

        .pedido-timeline {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-top: 10px;
            font-size: 0.8rem;
            color: #666;
        }

        .timeline-step {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .timeline-step.completed {
            color: var(--success-color);
        }

        .timeline-step.active {
            color: var(--primary-color);
            font-weight: bold;
        }

        .logout-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 2rem;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #000;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .header {
                padding: 1rem;
                flex-direction: column;
                gap: 1rem;
            }
            
            .stats-grid,
            .functions-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="logo">
            <i class="fas fa-store" style="font-size: 2rem; color: #FFE082;"></i>
            <h1><span class="repu">Repu</span><span class="movil">Movil</span></h1>
        </div>
        <div class="user-info">
            <span class="proveedor-badge">PROVEEDOR</span>
            <span>Hola, <?php echo htmlspecialchars($user_name); ?></span>
            <button class="logout-btn" onclick="location.href='logout.php'">
                <i class="fas fa-sign-out-alt"></i> Salir
            </button>
        </div>
    </div>

    <div class="container">
        <!-- Welcome Section -->
        <div class="welcome-section">
            <h2 class="welcome-title">Panel de Proveedor</h2>
            <p class="welcome-subtitle">
                Gestiona tu inventario, pedidos y entregas desde un solo lugar
            </p>
        </div>

        <!-- Stats Grid -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">📋</div>
                <div class="stat-number"><?php echo $total_pedidos; ?></div>
                <div class="stat-label">Pedidos Recibidos</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">📦</div>
                <div class="stat-number"><?php echo $total_productos; ?></div>
                <div class="stat-label">Productos en Stock</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">💰</div>
                <div class="stat-number">$<?php echo number_format($ventas_mes, 0, ',', '.'); ?></div>
                <div class="stat-label">Ventas del Mes</div>
            </div>
        </div>

        <!-- Functions Grid -->
        <div class="functions-grid">
            <!-- Pedidos Recibidos -->
            <div class="function-card primary" onclick="openModal('pedidos')">
                <div class="function-icon">📋</div>
                <h3 class="function-title">🚀 Gestión de Pedidos</h3>
                <p class="function-description">
                    <strong>¡Sistema completo disponible!</strong><br>
                    Estados • Repartidores • Tracking • Tiempo real
                </p>
            </div>

            <!-- Mi Inventario -->
            <div class="function-card" onclick="location.href='inventario-proveedor.php'">
                <div class="function-icon">📦</div>
                <h3 class="function-title">Mi Inventario</h3>
                <p class="function-description">
                    Administra tu stock de repuestos. Agrega, edita y controla el inventario.
                </p>
            </div>

            <!-- Mis Clientes -->
            <div class="function-card" onclick="openModal('clientes')">
                <div class="function-icon">👥</div>
                <h3 class="function-title">Mis Clientes</h3>
                <p class="function-description">
                    Lista de talleres y mecánicos que compran tus productos regularmente.
                </p>
            </div>

            <!-- Estadísticas -->
            <div class="function-card" onclick="openModal('estadisticas')">
                <div class="function-icon">📊</div>
                <h3 class="function-title">Estadísticas</h3>
                <p class="function-description">
                    Reportes de ventas, productos más vendidos y análisis de rendimiento.
                </p>
            </div>

            <!-- Mapa de Entregas -->
            <div class="function-card primary" onclick="location.href='proveedor-mapa.php'">
                <div class="function-icon">🗺️</div>
                <h3 class="function-title">🚀 Mapa de Entregas</h3>
                <p class="function-description">
                    <strong>¡Sistema completo disponible!</strong><br>
                    Tracking • GPS • Rutas • Tiempo real
                </p>
            </div>

            <!-- Configuración -->
            <div class="function-card" onclick="openModal('configuracion')">
                <div class="function-icon">⚙️</div>
                <h3 class="function-title">Configuración</h3>
                <p class="function-description">
                    Ajustes del negocio, horarios, métodos de pago y preferencias.
                </p>
            </div>
        </div>

        <!-- Recent Orders -->
        <div class="recent-orders">
            <h3 class="section-title">
                <i class="fas fa-clock"></i>
                Pedidos Recientes
            </h3>
            
            <?php if (empty($pedidos_recientes)): ?>
                <p style="text-align: center; color: #666; padding: 2rem;">
                    No hay pedidos recientes
                </p>
            <?php else: ?>
                <?php foreach ($pedidos_recientes as $pedido): ?>
                    <div class="order-item">
                        <div style="flex: 1;">
                            <div class="order-info">
                                <h4>
                                    <i class="fas fa-user"></i>
                                    <?php echo htmlspecialchars($pedido['cliente_nombre'] ?? 'Cliente'); ?>
                                    <span class="timer-badge" id="timer-<?php echo $pedido['id']; ?>">
                                        ⏱️ <?php echo rand(5, 25); ?> min
                                    </span>
                                </h4>
                                <p>
                                    <i class="fas fa-clock"></i>
                                    <?php echo date('d/m/Y H:i', strtotime($pedido['fecha_pedido'])); ?>
                                    |
                                    <i class="fas fa-dollar-sign"></i>
                                    $<?php echo number_format($pedido['total'], 0, ',', '.'); ?>
                                    |
                                    <i class="fas fa-phone"></i>
                                    <?php echo htmlspecialchars($pedido['cliente_telefono'] ?? 'Sin teléfono'); ?>
                                </p>
                            </div>

                            <!-- PASO 2: Timeline del pedido -->
                            <div class="pedido-timeline">
                                <div class="timeline-step completed">
                                    <i class="fas fa-check-circle"></i>
                                    <span>Recibido</span>
                                </div>
                                <div class="timeline-step <?php echo in_array($pedido['estado'], ['preparando', 'empacado', 'asignado', 'en_camino', 'entregado']) ? 'completed' : 'active'; ?>">
                                    <i class="fas fa-box"></i>
                                    <span>Preparando</span>
                                </div>
                                <div class="timeline-step <?php echo in_array($pedido['estado'], ['empacado', 'asignado', 'en_camino', 'entregado']) ? 'completed' : ($pedido['estado'] == 'preparando' ? 'active' : ''); ?>">
                                    <i class="fas fa-package"></i>
                                    <span>Empacado</span>
                                </div>
                                <div class="timeline-step <?php echo in_array($pedido['estado'], ['asignado', 'en_camino', 'entregado']) ? 'completed' : ($pedido['estado'] == 'empacado' ? 'active' : ''); ?>">
                                    <i class="fas fa-motorcycle"></i>
                                    <span>Delivery</span>
                                </div>
                                <div class="timeline-step <?php echo $pedido['estado'] == 'entregado' ? 'completed' : ($pedido['estado'] == 'en_camino' ? 'active' : ''); ?>">
                                    <i class="fas fa-home"></i>
                                    <span>Entregado</span>
                                </div>
                            </div>

                            <!-- PASO 2: Botones de acción -->
                            <div class="pedido-actions">
                                <?php if ($pedido['estado'] == 'pendiente' || $pedido['estado'] == 'preparando'): ?>
                                    <button class="action-btn btn-empacado" onclick="marcarEmpacado(<?php echo $pedido['id']; ?>)">
                                        <i class="fas fa-box"></i>
                                        ¡EMPACADO!
                                    </button>
                                <?php endif; ?>

                                <button class="action-btn btn-ver-detalle" onclick="verDetallePedido(<?php echo $pedido['id']; ?>)">
                                    <i class="fas fa-eye"></i>
                                    Ver Detalle
                                </button>

                                <button class="action-btn btn-contactar" onclick="contactarCliente('<?php echo $pedido['cliente_telefono']; ?>')">
                                    <i class="fas fa-phone"></i>
                                    Contactar
                                </button>
                            </div>
                        </div>

                        <div class="order-status status-<?php echo $pedido['estado']; ?>">
                            <?php
                            $estados = [
                                'pendiente' => '⏳ Pendiente',
                                'preparando' => '📦 Preparando',
                                'empacado' => '✅ Empacado',
                                'asignado' => '🚀 Asignado',
                                'en_camino' => '🏍️ En Camino',
                                'entregado' => '🎉 Entregado'
                            ];
                            echo $estados[$pedido['estado']] ?? ucfirst($pedido['estado']);
                            ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <!-- Modal Universal -->
    <div id="universalModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <div id="modalContent">
                <!-- El contenido se carga dinámicamente -->
            </div>
        </div>
    </div>

    <script>
        // PASO 2: Función para marcar pedido como empacado
        async function marcarEmpacado(pedidoId) {
            if (!confirm('¿Confirmar que el pedido está empacado y listo para entrega?')) {
                return;
            }

            const button = event.target.closest('.btn-empacado');
            const originalText = button.innerHTML;
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Empacando...';

            try {
                const response = await fetch('../api/pedidos.php', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'cambiar_estado',
                        pedido_id: pedidoId,
                        nuevo_estado: 'empacado'
                    })
                });

                const result = await response.json();

                if (result.success) {
                    // Mostrar notificación de éxito
                    showEmpacadoNotification(pedidoId);

                    // PASO 3: Notificar al mapa si está abierto
                    try {
                        // Enviar mensaje al mapa si está en otra ventana/tab
                        if (window.opener) {
                            window.opener.postMessage({
                                type: 'pedido_empacado',
                                pedidoId: pedidoId
                            }, '*');
                        }

                        // También enviar a cualquier iframe del mapa
                        const mapFrame = document.querySelector('iframe[src*="mapa"]');
                        if (mapFrame && mapFrame.contentWindow) {
                            mapFrame.contentWindow.postMessage({
                                type: 'pedido_empacado',
                                pedidoId: pedidoId
                            }, '*');
                        }
                    } catch (e) {
                        console.log('No se pudo notificar al mapa:', e);
                    }

                    // Actualizar la interfaz
                    setTimeout(() => {
                        location.reload();
                    }, 2000);
                } else {
                    alert('Error al marcar como empacado: ' + result.message);
                    button.disabled = false;
                    button.innerHTML = originalText;
                }
            } catch (error) {
                alert('Error de conexión: ' + error.message);
                button.disabled = false;
                button.innerHTML = originalText;
            }
        }

        function showEmpacadoNotification(pedidoId) {
            const notification = document.createElement('div');
            notification.className = 'empacado-notification show';
            notification.innerHTML = `
                <i class="fas fa-check-circle"></i>
                <strong>¡Pedido #${pedidoId} empacado!</strong><br>
                🚀 Buscando delivery automáticamente...
            `;

            document.body.appendChild(notification);

            // Remover después de 4 segundos
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 4000);
        }

        function verDetallePedido(pedidoId) {
            // Simular carga de detalle del pedido
            alert(`📦 Detalle del Pedido #${pedidoId}\n\n(Próximamente: modal con información completa del pedido, productos, cliente, etc.)`);
        }

        function contactarCliente(telefono) {
            if (telefono && telefono !== 'Sin teléfono') {
                if (confirm(`¿Llamar al cliente al ${telefono}?`)) {
                    // En una app real, esto abriría la aplicación de teléfono
                    window.open(`tel:${telefono}`);
                }
            } else {
                alert('No hay número de teléfono disponible para este cliente.');
            }
        }

        // Actualizar timers cada minuto
        setInterval(() => {
            document.querySelectorAll('[id^="timer-"]').forEach(timer => {
                const currentTime = parseInt(timer.textContent.match(/\d+/)[0]);
                if (currentTime > 0) {
                    timer.innerHTML = `⏱️ ${currentTime - 1} min`;
                }
            });
        }, 60000);

        function openModal(type) {
            const modal = document.getElementById('universalModal');
            const content = document.getElementById('modalContent');
            
            let modalContent = '';
            
            switch(type) {
                case 'pedidos':
                    modalContent = `
                        <h2><i class="fas fa-list"></i> 🚀 Sistema Completo de Gestión de Pedidos</h2>
                        <p style="color: #4CAF50; font-weight: bold; margin-bottom: 1rem;">¡Ya está disponible! Accede al sistema completo con todas las funcionalidades:</p>
                        <div style="background: #f8f9fa; padding: 1rem; border-radius: 8px; margin: 1rem 0;">
                            <h4 style="color: #333; margin-bottom: 0.5rem;">✅ Funcionalidades Implementadas:</h4>
                            <ul style="margin: 0.5rem 0; padding-left: 2rem; line-height: 1.6;">
                                <li><strong>📋 Listado completo</strong> de pedidos entrantes</li>
                                <li><strong>📊 Estados de pedidos:</strong> Nuevo → En Preparación → En Camino → Entregado</li>
                                <li><strong>🚚 Asignación de repartidores</strong> con información completa</li>
                                <li><strong>📍 Ubicación del repartidor</strong> en tiempo real</li>
                                <li><strong>⏱️ Tiempo estimado</strong> de entrega</li>
                                <li><strong>🔍 Filtros por estado</strong> y estadísticas</li>
                                <li><strong>📱 Contacto directo</strong> con clientes</li>
                                <li><strong>📋 Modal detallado</strong> con toda la información</li>
                            </ul>
                        </div>
                        <div style="background: linear-gradient(135deg, #4CAF50, #45a049); padding: 1rem; border-radius: 8px; color: white; text-align: center; margin: 1rem 0;">
                            <p style="margin: 0; font-style: italic;">"Cada pedido es una oportunidad de hacer crecer tu negocio"</p>
                        </div>
                        <div style="display: flex; gap: 0.5rem; margin-top: 1.5rem;">
                            <button onclick="window.location.href='proveedor-pedidos.php'"
                                    style="flex: 2; background: #4CAF50; color: white; border: none; padding: 1rem; border-radius: 8px; cursor: pointer; font-weight: bold; font-size: 1rem;">
                                🚀 Acceder a Gestión de Pedidos
                            </button>
                            <button onclick="closeModal()"
                                    style="flex: 1; background: #666; color: white; border: none; padding: 1rem; border-radius: 8px; cursor: pointer;">
                                Cerrar
                            </button>
                        </div>
                    `;
                    break;
                case 'clientes':
                    modalContent = `
                        <h2><i class="fas fa-users"></i> Mis Clientes</h2>
                        <p>Lista de clientes frecuentes:</p>
                        <div style="margin: 1rem 0;">
                            <div style="padding: 1rem; background: #f8f9fa; border-radius: 8px; margin-bottom: 0.5rem;">
                                <strong>🔧 Taller Mecánico Central</strong><br>
                                <small>15 pedidos • $45.200 total</small>
                            </div>
                            <div style="padding: 1rem; background: #f8f9fa; border-radius: 8px; margin-bottom: 0.5rem;">
                                <strong>👨‍🔧 Mecánico Juan Pérez</strong><br>
                                <small>8 pedidos • $18.700 total</small>
                            </div>
                        </div>
                    `;
                    break;
                case 'estadisticas':
                    modalContent = `
                        <h2><i class="fas fa-chart-bar"></i> Estadísticas de Ventas</h2>
                        <div style="margin: 1rem 0;">
                            <p><strong>Resumen del Mes:</strong></p>
                            <ul style="list-style: none; padding: 0;">
                                <li style="padding: 0.5rem; border-bottom: 1px solid #eee;">
                                    <strong>Ventas Totales:</strong> $<?php echo number_format($ventas_mes, 0, ',', '.'); ?>
                                </li>
                                <li style="padding: 0.5rem; border-bottom: 1px solid #eee;">
                                    <strong>Pedidos Completados:</strong> <?php echo $total_pedidos; ?>
                                </li>
                                <li style="padding: 0.5rem; border-bottom: 1px solid #eee;">
                                    <strong>Productos en Stock:</strong> <?php echo $total_productos; ?>
                                </li>
                            </ul>
                        </div>
                    `;
                    break;
                case 'entregas':
                    modalContent = `
                        <h2><i class="fas fa-truck"></i> Gestión de Entregas</h2>
                        <p>Configuración de entregas:</p>
                        <ul style="margin: 1rem 0; padding-left: 2rem;">
                            <li>Zonas de cobertura</li>
                            <li>Horarios de entrega</li>
                            <li>Costos por zona</li>
                            <li>Tiempo estimado de entrega</li>
                        </ul>
                        <p><em>Funcionalidad en desarrollo...</em></p>
                    `;
                    break;
                case 'configuracion':
                    modalContent = `
                        <h2><i class="fas fa-cog"></i> Configuración</h2>
                        <p>Ajustes del negocio:</p>
                        <ul style="margin: 1rem 0; padding-left: 2rem;">
                            <li>Datos del negocio</li>
                            <li>Horarios de atención</li>
                            <li>Métodos de pago</li>
                            <li>Zonas de entrega</li>
                            <li>Notificaciones</li>
                        </ul>
                        <p><em>Funcionalidad en desarrollo...</em></p>
                    `;
                    break;
            }
            
            content.innerHTML = modalContent;
            modal.style.display = 'block';
        }
        
        function closeModal() {
            document.getElementById('universalModal').style.display = 'none';
        }
        
        // PASO 3: Función para abrir mapa de deliveries
        function abrirMapaDeliveries() {
            const mapaWindow = window.open('proveedor-mapa.php', 'MapaDeliveries', 'width=1200,height=800,scrollbars=yes,resizable=yes');

            if (mapaWindow) {
                // Enfocar la ventana del mapa
                mapaWindow.focus();

                // Mostrar notificación
                const notification = document.createElement('div');
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: var(--info-color);
                    color: white;
                    padding: 15px 20px;
                    border-radius: 10px;
                    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
                    z-index: 1000;
                    transform: translateX(400px);
                    transition: transform 0.3s ease;
                `;
                notification.innerHTML = `
                    <i class="fas fa-map-marked-alt"></i>
                    <strong>Mapa de Deliveries Abierto</strong><br>
                    🗺️ Tracking en tiempo real activado
                `;

                document.body.appendChild(notification);

                // Mostrar notificación
                setTimeout(() => {
                    notification.style.transform = 'translateX(0)';
                }, 100);

                // Remover después de 3 segundos
                setTimeout(() => {
                    notification.style.transform = 'translateX(400px)';
                    setTimeout(() => {
                        if (document.body.contains(notification)) {
                            document.body.removeChild(notification);
                        }
                    }, 300);
                }, 3000);
            } else {
                alert('No se pudo abrir el mapa. Verifica que no esté bloqueado por el navegador.');
            }
        }

        // Cerrar modal al hacer clic fuera
        window.onclick = function(event) {
            const modal = document.getElementById('universalModal');
            if (event.target == modal) {
                modal.style.display = 'none';
            }
        }
    </script>
</body>
</html>
