// PASO 1: Hook para manejo de notificaciones en RepuMovil
// Hook personalizado para integrar notificaciones fácilmente

import { useState, useEffect, useCallback } from 'react';
import { notificationService, PedidoNotification, NotificationResponse } from '../services/NotificationService';
import * as Notifications from 'expo-notifications';

export interface NotificationState {
  isInitialized: boolean;
  token: string | null;
  error: string | null;
  isLoading: boolean;
  lastNotification: Notifications.Notification | null;
  pendingNotifications: PedidoNotification[];
}

export interface NotificationActions {
  initializeNotifications: (deliveryId: number) => Promise<boolean>;
  sendTestNotification: (pedido: PedidoNotification) => Promise<void>;
  clearNotifications: () => Promise<void>;
  markNotificationAsRead: (pedidoId: string) => void;
  refreshToken: () => Promise<boolean>;
}

export function useNotifications(): NotificationState & NotificationActions {
  const [state, setState] = useState<NotificationState>({
    isInitialized: false,
    token: null,
    error: null,
    isLoading: false,
    lastNotification: null,
    pendingNotifications: [],
  });

  /**
   * PASO 1: Inicializar notificaciones
   */
  const initializeNotifications = useCallback(async (deliveryId: number): Promise<boolean> => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      console.log('🚀 Inicializando notificaciones para delivery:', deliveryId);

      // Inicializar servicio
      const result: NotificationResponse = await notificationService.initialize();

      if (!result.success) {
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: result.error || 'Error desconocido',
          isInitialized: false
        }));
        return false;
      }

      // Registrar token en servidor
      const registered = await notificationService.registerTokenWithServer(deliveryId);

      if (!registered) {
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: 'Error registrando token en servidor',
          isInitialized: false
        }));
        return false;
      }

      setState(prev => ({
        ...prev,
        isLoading: false,
        isInitialized: true,
        token: result.token || null,
        error: null
      }));

      console.log('✅ Notificaciones inicializadas correctamente');
      return true;

    } catch (error) {
      console.error('❌ Error en initializeNotifications:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: `Error de inicialización: ${error}`,
        isInitialized: false
      }));
      return false;
    }
  }, []);

  /**
   * PASO 1: Enviar notificación de prueba
   */
  const sendTestNotification = useCallback(async (pedido: PedidoNotification): Promise<void> => {
    try {
      console.log('🧪 Enviando notificación de prueba:', pedido.id);
      
      await notificationService.sendLocalNotification(pedido);
      
      // Agregar a notificaciones pendientes
      setState(prev => ({
        ...prev,
        pendingNotifications: [...prev.pendingNotifications, pedido]
      }));

      console.log('✅ Notificación de prueba enviada');
    } catch (error) {
      console.error('❌ Error enviando notificación de prueba:', error);
      setState(prev => ({
        ...prev,
        error: `Error enviando notificación: ${error}`
      }));
    }
  }, []);

  /**
   * PASO 1: Limpiar todas las notificaciones
   */
  const clearNotifications = useCallback(async (): Promise<void> => {
    try {
      await notificationService.clearAllNotifications();
      setState(prev => ({
        ...prev,
        pendingNotifications: [],
        lastNotification: null
      }));
      console.log('🧹 Notificaciones limpiadas');
    } catch (error) {
      console.error('❌ Error limpiando notificaciones:', error);
    }
  }, []);

  /**
   * PASO 1: Marcar notificación como leída
   */
  const markNotificationAsRead = useCallback((pedidoId: string): void => {
    setState(prev => ({
      ...prev,
      pendingNotifications: prev.pendingNotifications.filter(n => n.id !== pedidoId)
    }));
    console.log('👁️ Notificación marcada como leída:', pedidoId);
  }, []);

  /**
   * PASO 1: Refrescar token
   */
  const refreshToken = useCallback(async (): Promise<boolean> => {
    setState(prev => ({ ...prev, isLoading: true }));

    try {
      const result = await notificationService.initialize();
      
      setState(prev => ({
        ...prev,
        isLoading: false,
        token: result.token || null,
        error: result.success ? null : result.error || 'Error refrescando token'
      }));

      return result.success;
    } catch (error) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: `Error refrescando token: ${error}`
      }));
      return false;
    }
  }, []);

  /**
   * PASO 1: Configurar listeners de notificaciones
   */
  useEffect(() => {
    let notificationListener: any;
    let responseListener: any;

    if (state.isInitialized) {
      // Listener para notificaciones recibidas
      notificationListener = Notifications.addNotificationReceivedListener(notification => {
        console.log('📱 Notificación recibida en hook:', notification);
        setState(prev => ({ ...prev, lastNotification: notification }));
      });

      // Listener para respuestas a notificaciones
      responseListener = Notifications.addNotificationResponseReceivedListener(response => {
        console.log('👆 Respuesta a notificación en hook:', response);
        const data = response.notification.request.content.data as any;
        
        if (data?.pedido_id) {
          markNotificationAsRead(data.pedido_id);
        }
      });
    }

    return () => {
      if (notificationListener) {
        Notifications.removeNotificationSubscription(notificationListener);
      }
      if (responseListener) {
        Notifications.removeNotificationSubscription(responseListener);
      }
    };
  }, [state.isInitialized, markNotificationAsRead]);

  /**
   * PASO 1: Cleanup al desmontar
   */
  useEffect(() => {
    return () => {
      notificationService.cleanup();
    };
  }, []);

  return {
    // Estado
    isInitialized: state.isInitialized,
    token: state.token,
    error: state.error,
    isLoading: state.isLoading,
    lastNotification: state.lastNotification,
    pendingNotifications: state.pendingNotifications,
    
    // Acciones
    initializeNotifications,
    sendTestNotification,
    clearNotifications,
    markNotificationAsRead,
    refreshToken,
  };
}

/**
 * PASO 1: Hook simplificado para casos básicos
 */
export function useSimpleNotifications(deliveryId: number) {
  const {
    isInitialized,
    error,
    isLoading,
    pendingNotifications,
    initializeNotifications,
    sendTestNotification
  } = useNotifications();

  // Auto-inicializar
  useEffect(() => {
    if (!isInitialized && !isLoading && deliveryId) {
      initializeNotifications(deliveryId);
    }
  }, [deliveryId, isInitialized, isLoading, initializeNotifications]);

  return {
    isReady: isInitialized,
    hasError: !!error,
    errorMessage: error,
    pendingCount: pendingNotifications.length,
    sendTest: sendTestNotification,
  };
}

export default useNotifications;
