import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Text, TouchableOpacity, Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Location from 'expo-location';

interface Coordinates {
  latitude: number;
  longitude: number;
}

interface MapaComponentProps {
  clienteLocation?: Coordinates;
  pedidoId?: string;
  onLocationUpdate?: (location: any) => void;
  showRoute?: boolean;
}

const MapaComponentSafe: React.FC<MapaComponentProps> = ({
  clienteLocation,
  pedidoId,
  onLocationUpdate,
  showRoute = true,
}) => {
  const [currentLocation, setCurrentLocation] = useState<Coordinates | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isTracking, setIsTracking] = useState(false);

  useEffect(() => {
    initializeLocation();
  }, []);

  const initializeLocation = async () => {
    try {
      setIsLoading(true);
      
      // Solicitar permisos de ubicación
      const { status } = await Location.requestForegroundPermissionsAsync();
      
      if (status === 'granted') {
        // Obtener ubicación actual
        const location = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.High,
        });
        
        const coords = {
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
        };
        
        setCurrentLocation(coords);
        
        if (onLocationUpdate) {
          onLocationUpdate({
            coords,
            timestamp: location.timestamp,
            accuracy: location.coords.accuracy,
          });
        }
      } else {
        // Usar ubicación por defecto (San Juan, Argentina)
        const defaultLocation = { latitude: -31.5375, longitude: -68.5364 };
        setCurrentLocation(defaultLocation);
      }
      
      setIsLoading(false);
    } catch (error) {
      console.error('Error obteniendo ubicación:', error);
      // Usar ubicación por defecto
      setCurrentLocation({ latitude: -31.5375, longitude: -68.5364 });
      setIsLoading(false);
    }
  };

  const startTracking = async () => {
    setIsTracking(true);
    // Simular tracking actualizando la ubicación cada 3 segundos
    const interval = setInterval(async () => {
      try {
        const location = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.High,
        });
        
        const coords = {
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
        };
        
        setCurrentLocation(coords);
        
        if (onLocationUpdate) {
          onLocationUpdate({
            coords,
            timestamp: location.timestamp,
            accuracy: location.coords.accuracy,
          });
        }
      } catch (error) {
        console.log('Error en tracking:', error);
      }
    }, 3000);

    // Limpiar interval después de 30 segundos para demo
    setTimeout(() => {
      clearInterval(interval);
      setIsTracking(false);
    }, 30000);
  };

  const calculateDistance = (point1: Coordinates, point2: Coordinates): number => {
    const R = 6371; // Radio de la Tierra en km
    const dLat = (point2.latitude - point1.latitude) * Math.PI / 180;
    const dLon = (point2.longitude - point1.longitude) * Math.PI / 180;
    
    const a = 
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(point1.latitude * Math.PI / 180) * Math.cos(point2.latitude * Math.PI / 180) *
      Math.sin(dLon / 2) * Math.sin(dLon / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;
    
    return Math.round(distance * 100) / 100;
  };

  const distance = currentLocation && clienteLocation 
    ? calculateDistance(currentLocation, clienteLocation)
    : 2.5;

  const estimatedTime = Math.round(distance * 2.5); // 2.5 min por km

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>🗺️ Cargando mapa...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Mapa Simulado */}
      <View style={styles.mapContainer}>
        <View style={styles.mapContent}>
          <Text style={styles.mapTitle}>🗺️ RepuMovil Maps</Text>
          <Text style={styles.mapSubtitle}>Sistema de navegación y tracking</Text>
          
          {/* Información de ubicación */}
          <View style={styles.locationInfo}>
            <Text style={styles.locationTitle}>📍 Ubicación Actual</Text>
            <Text style={styles.locationText}>
              San Juan, Argentina{'\n'}
              Lat: {currentLocation?.latitude.toFixed(4)}{'\n'}
              Lng: {currentLocation?.longitude.toFixed(4)}
            </Text>
          </View>
          
          {/* Iconos de ubicaciones */}
          <View style={styles.iconsContainer}>
            <View style={styles.iconItem}>
              <Text style={styles.iconEmoji}>🏍️</Text>
              <Text style={styles.iconLabel}>Tu ubicación</Text>
            </View>
            {clienteLocation && (
              <View style={styles.iconItem}>
                <Text style={styles.iconEmoji}>📍</Text>
                <Text style={styles.iconLabel}>Cliente</Text>
              </View>
            )}
            <View style={styles.iconItem}>
              <Text style={styles.iconEmoji}>🏪</Text>
              <Text style={styles.iconLabel}>Proveedor</Text>
            </View>
          </View>
          
          {/* Información de ruta */}
          {showRoute && clienteLocation && (
            <View style={styles.routeContainer}>
              <Text style={styles.routeTitle}>🛣️ Ruta Activa</Text>
              <Text style={styles.routeInfo}>
                📏 Distancia: {distance.toFixed(1)} km • ⏱️ Tiempo: {estimatedTime} min
              </Text>
            </View>
          )}
        </View>
      </View>

      {/* Controles */}
      <View style={styles.controls}>
        <TouchableOpacity
          style={styles.controlButton}
          onPress={initializeLocation}
        >
          <Ionicons name="locate" size={24} color="#4CAF50" />
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.controlButton,
            isTracking ? styles.trackingActive : null
          ]}
          onPress={startTracking}
          disabled={isTracking}
        >
          <Ionicons 
            name={isTracking ? "pause" : "play"} 
            size={24} 
            color={isTracking ? "#FF6B35" : "#4CAF50"} 
          />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
    marginTop: 10,
  },
  mapContainer: {
    flex: 1,
    backgroundColor: '#E8F5E8',
  },
  mapContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  mapTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#4CAF50',
    textAlign: 'center',
    marginBottom: 10,
  },
  mapSubtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 30,
  },
  locationInfo: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 10,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  locationTitle: {
    fontSize: 14,
    color: '#4CAF50',
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 10,
  },
  locationText: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  iconsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    marginBottom: 20,
  },
  iconItem: {
    alignItems: 'center',
  },
  iconEmoji: {
    fontSize: 24,
    marginBottom: 5,
  },
  iconLabel: {
    fontSize: 12,
    color: '#666',
  },
  routeContainer: {
    backgroundColor: '#4CAF50',
    padding: 15,
    borderRadius: 10,
    width: '100%',
  },
  routeTitle: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 5,
  },
  routeInfo: {
    color: 'white',
    fontSize: 12,
    textAlign: 'center',
  },
  controls: {
    position: 'absolute',
    bottom: 30,
    right: 20,
    flexDirection: 'column',
    gap: 10,
  },
  controlButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
  trackingActive: {
    backgroundColor: 'rgba(255, 107, 53, 0.1)',
  },
});

export default MapaComponentSafe;
