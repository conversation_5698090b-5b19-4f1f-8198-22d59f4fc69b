import React, { useState } from 'react';
import {
  SafeAreaView,
  StatusBar,
  StyleSheet,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  Dimensions,
} from 'react-native';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { AuthAPI, handleApiError, debugLog } from '../../config/api';

const { width } = Dimensions.get('window');

export default function DeliveryRegistro() {
  const router = useRouter();
  
  // Estados para el formulario multi-step
  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(false);

  // Estados para datos de acceso
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');

  // Estados para datos personales
  const [nombreCompleto, setNombreCompleto] = useState('');
  const [dni, setDni] = useState('');
  const [fechaNacimiento, setFechaNacimiento] = useState('');
  const [telefono, setTelefono] = useState('');
  const [direccion, setDireccion] = useState('');

  // Estados para datos de pago
  const [cbuAlias, setCbuAlias] = useState('');
  const [cuilCuit, setCuilCuit] = useState('');

  // Estados para vehículo
  const [tipoVehiculo, setTipoVehiculo] = useState('');
  const [marca, setMarca] = useState('');
  const [modelo, setModelo] = useState('');
  const [patente, setPatente] = useState('');
  const [licencia, setLicencia] = useState('');

  // Estados para términos
  const [aceptaTerminos, setAceptaTerminos] = useState(false);

  const totalSteps = 5;

  const nextStep = () => {
    if (validateCurrentStep()) {
      setCurrentStep(prev => Math.min(prev + 1, totalSteps));
    }
  };

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const validateCurrentStep = () => {
    switch (currentStep) {
      case 1:
        if (!email || !password || !confirmPassword) {
          Alert.alert('Error', 'Todos los campos son obligatorios');
          return false;
        }
        if (password !== confirmPassword) {
          Alert.alert('Error', 'Las contraseñas no coinciden');
          return false;
        }
        if (password.length < 6) {
          Alert.alert('Error', 'La contraseña debe tener al menos 6 caracteres');
          return false;
        }
        return true;
      case 2:
        if (!nombreCompleto || !dni || !fechaNacimiento || !telefono || !direccion) {
          Alert.alert('Error', 'Todos los campos son obligatorios');
          return false;
        }
        return true;
      case 3:
        if (!cbuAlias) {
          Alert.alert('Error', 'CBU o Alias bancario es obligatorio');
          return false;
        }
        return true;
      case 4:
        if (!tipoVehiculo) {
          Alert.alert('Error', 'Tipo de vehículo es obligatorio');
          return false;
        }
        if ((tipoVehiculo === 'moto' || tipoVehiculo === 'auto') && !licencia) {
          Alert.alert('Error', 'Licencia de conducir es obligatoria para moto/auto');
          return false;
        }
        return true;
      case 5:
        if (!aceptaTerminos) {
          Alert.alert('Error', 'Debes aceptar los términos y condiciones');
          return false;
        }
        return true;
      default:
        return true;
    }
  };

  const handleSubmit = async () => {
    if (!validateCurrentStep()) return;

    setLoading(true);

    try {
      // Preparar datos para enviar a la API
      const userData = {
        email,
        password,
        nombre_completo: nombreCompleto,
        dni,
        fecha_nacimiento: fechaNacimiento,
        telefono,
        direccion,
        cbu_alias: cbuAlias,
        cuil_cuit: cuilCuit,
        tipo_vehiculo: tipoVehiculo,
        marca,
        modelo,
        patente,
        licencia,
        acepta_terminos: aceptaTerminos
      };

      debugLog('Enviando registro', { email: userData.email });

      const response = await AuthAPI.register(userData);

      if (response.success) {
        debugLog('Registro exitoso', response.data);

        Alert.alert(
          '🎉 ¡Registro Exitoso!',
          'Tu solicitud ha sido enviada. Te contactaremos pronto para completar el proceso.',
          [
            {
              text: 'OK',
              onPress: () => router.push('/delivery-welcome')
            }
          ]
        );
      } else {
        Alert.alert('Error', response.message || 'Error en el registro');
      }
    } catch (error) {
      debugLog('Error en registro', error);
      const errorMessage = handleApiError(error);

      // Si es error de conexión, mostrar opción de continuar
      if (errorMessage.includes('Network') || errorMessage.includes('fetch')) {
        Alert.alert(
          'Error de Conexión',
          'No se pudo conectar con el servidor. Los datos se guardarán cuando tengas conexión.',
          [
            { text: 'Entendido', onPress: () => router.push('/delivery-welcome') }
          ]
        );
      } else {
        Alert.alert('Error', errorMessage);
      }
    } finally {
      setLoading(false);
    }
  };

  const renderStepIndicator = () => (
    <View style={styles.stepIndicator}>
      {[1, 2, 3, 4, 5].map((step) => (
        <View key={step} style={styles.stepContainer}>
          <View style={[
            styles.stepCircle,
            currentStep >= step ? styles.stepActive : styles.stepInactive
          ]}>
            <Text style={[
              styles.stepNumber,
              currentStep >= step ? styles.stepNumberActive : styles.stepNumberInactive
            ]}>
              {step}
            </Text>
          </View>
          {step < 5 && (
            <View style={[
              styles.stepLine,
              currentStep > step ? styles.stepLineActive : styles.stepLineInactive
            ]} />
          )}
        </View>
      ))}
    </View>
  );

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <View style={styles.stepContent}>
            <Text style={styles.stepTitle}>🔐 Datos de Acceso</Text>
            <Text style={styles.stepDescription}>
              Crea tu cuenta para acceder a la plataforma
            </Text>

            <TextInput
              style={styles.input}
              placeholder="Correo Electrónico *"
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
              placeholderTextColor="#999"
            />

            <TextInput
              style={styles.input}
              placeholder="Contraseña *"
              value={password}
              onChangeText={setPassword}
              secureTextEntry
              placeholderTextColor="#999"
            />

            <TextInput
              style={styles.input}
              placeholder="Confirmar Contraseña *"
              value={confirmPassword}
              onChangeText={setConfirmPassword}
              secureTextEntry
              placeholderTextColor="#999"
            />
          </View>
        );

      case 2:
        return (
          <View style={styles.stepContent}>
            <Text style={styles.stepTitle}>👤 Datos Personales</Text>
            <Text style={styles.stepDescription}>
              Información básica para tu perfil
            </Text>

            <TextInput
              style={styles.input}
              placeholder="Nombre Completo *"
              value={nombreCompleto}
              onChangeText={setNombreCompleto}
              placeholderTextColor="#999"
            />

            <TextInput
              style={styles.input}
              placeholder="DNI *"
              value={dni}
              onChangeText={setDni}
              keyboardType="numeric"
              placeholderTextColor="#999"
            />

            <TextInput
              style={styles.input}
              placeholder="Fecha de Nacimiento (DD/MM/AAAA) *"
              value={fechaNacimiento}
              onChangeText={setFechaNacimiento}
              placeholderTextColor="#999"
            />

            <TextInput
              style={styles.input}
              placeholder="Teléfono *"
              value={telefono}
              onChangeText={setTelefono}
              keyboardType="phone-pad"
              placeholderTextColor="#999"
            />

            <TextInput
              style={[styles.input, styles.textArea]}
              placeholder="Dirección Completa *"
              value={direccion}
              onChangeText={setDireccion}
              multiline
              numberOfLines={3}
              placeholderTextColor="#999"
            />
          </View>
        );

      case 3:
        return (
          <View style={styles.stepContent}>
            <Text style={styles.stepTitle}>💳 Datos de Pago</Text>
            <Text style={styles.stepDescription}>
              Para recibir tus pagos por entregas
            </Text>

            <TextInput
              style={styles.input}
              placeholder="CBU o Alias Bancario *"
              value={cbuAlias}
              onChangeText={setCbuAlias}
              placeholderTextColor="#999"
            />

            <TextInput
              style={styles.input}
              placeholder="CUIL/CUIT (opcional)"
              value={cuilCuit}
              onChangeText={setCuilCuit}
              keyboardType="numeric"
              placeholderTextColor="#999"
            />

            <View style={styles.infoBox}>
              <Text style={styles.infoText}>
                💡 El CBU o Alias es necesario para transferir tus ganancias. 
                El CUIL/CUIT es opcional al inicio.
              </Text>
            </View>
          </View>
        );

      case 4:
        return (
          <View style={styles.stepContent}>
            <Text style={styles.stepTitle}>🏍️ Datos del Vehículo</Text>
            <Text style={styles.stepDescription}>
              ¿Con qué vas a hacer las entregas?
            </Text>

            <View style={styles.vehicleOptions}>
              {['bicicleta', 'moto', 'auto'].map((tipo) => (
                <TouchableOpacity
                  key={tipo}
                  style={[
                    styles.vehicleOption,
                    tipoVehiculo === tipo && styles.vehicleOptionSelected
                  ]}
                  onPress={() => setTipoVehiculo(tipo)}
                >
                  <Text style={styles.vehicleIcon}>
                    {tipo === 'bicicleta' ? '🚲' : tipo === 'moto' ? '🏍️' : '🚗'}
                  </Text>
                  <Text style={[
                    styles.vehicleText,
                    tipoVehiculo === tipo && styles.vehicleTextSelected
                  ]}>
                    {tipo.charAt(0).toUpperCase() + tipo.slice(1)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            {tipoVehiculo && tipoVehiculo !== 'bicicleta' && (
              <>
                <TextInput
                  style={styles.input}
                  placeholder="Marca"
                  value={marca}
                  onChangeText={setMarca}
                  placeholderTextColor="#999"
                />

                <TextInput
                  style={styles.input}
                  placeholder="Modelo"
                  value={modelo}
                  onChangeText={setModelo}
                  placeholderTextColor="#999"
                />

                <TextInput
                  style={styles.input}
                  placeholder="Patente"
                  value={patente}
                  onChangeText={setPatente}
                  placeholderTextColor="#999"
                />

                <TextInput
                  style={styles.input}
                  placeholder="Número de Licencia *"
                  value={licencia}
                  onChangeText={setLicencia}
                  placeholderTextColor="#999"
                />
              </>
            )}
          </View>
        );

      case 5:
        return (
          <View style={styles.stepContent}>
            <Text style={styles.stepTitle}>📋 Documentos y Términos</Text>
            <Text style={styles.stepDescription}>
              Último paso para completar tu registro
            </Text>

            <View style={styles.documentsInfo}>
              <Text style={styles.documentsTitle}>📸 Documentos requeridos:</Text>
              <Text style={styles.documentItem}>• Foto del DNI (frente y dorso)</Text>
              <Text style={styles.documentItem}>• Selfie con DNI en mano</Text>
              {tipoVehiculo !== 'bicicleta' && (
                <>
                  <Text style={styles.documentItem}>• Foto de la licencia de conducir</Text>
                  <Text style={styles.documentItem}>• Foto del seguro del vehículo</Text>
                </>
              )}
              <Text style={styles.documentItem}>• Antecedentes penales (opcional)</Text>
            </View>

            <View style={styles.noteBox}>
              <Text style={styles.noteText}>
                📝 Los documentos se subirán en la siguiente pantalla después del registro inicial.
              </Text>
            </View>

            <TouchableOpacity
              style={styles.checkboxContainer}
              onPress={() => setAceptaTerminos(!aceptaTerminos)}
            >
              <View style={[styles.checkbox, aceptaTerminos && styles.checkboxChecked]}>
                {aceptaTerminos && <Text style={styles.checkmark}>✓</Text>}
              </View>
              <Text style={styles.checkboxText}>
                Acepto los términos y condiciones, política de privacidad y declaración jurada
              </Text>
            </TouchableOpacity>
          </View>
        );

      default:
        return null;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#FF6B35" />
      
      {/* Header */}
      <LinearGradient
        colors={['#FF6B35', '#E53E3E']}
        style={styles.header}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
      >
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <Text style={styles.backButtonText}>← Volver</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Registro de Repartidor</Text>
        <Text style={styles.headerSubtitle}>Paso {currentStep} de {totalSteps}</Text>
      </LinearGradient>

      {renderStepIndicator()}

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderStepContent()}
      </ScrollView>

      {/* Botones de navegación */}
      <View style={styles.navigationButtons}>
        {currentStep > 1 && (
          <TouchableOpacity style={styles.prevButton} onPress={prevStep}>
            <Text style={styles.prevButtonText}>← Anterior</Text>
          </TouchableOpacity>
        )}
        
        <TouchableOpacity
          style={[styles.nextButton, currentStep === 1 && styles.nextButtonFull]}
          onPress={currentStep === totalSteps ? handleSubmit : nextStep}
          disabled={loading}
        >
          <Text style={styles.nextButtonText}>
            {loading ? '⏳ Enviando...' : currentStep === totalSteps ? '🚀 Registrarme' : 'Siguiente →'}
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    paddingTop: 10,
    paddingBottom: 20,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  backButton: {
    alignSelf: 'flex-start',
    marginBottom: 10,
  },
  backButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '800',
    color: 'white',
    marginBottom: 5,
  },
  headerSubtitle: {
    fontSize: 16,
    color: 'white',
    opacity: 0.9,
  },
  stepIndicator: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
    paddingHorizontal: 20,
  },
  stepContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  stepCircle: {
    width: 30,
    height: 30,
    borderRadius: 15,
    justifyContent: 'center',
    alignItems: 'center',
  },
  stepActive: {
    backgroundColor: '#FF6B35',
  },
  stepInactive: {
    backgroundColor: '#e2e8f0',
  },
  stepNumber: {
    fontSize: 14,
    fontWeight: '600',
  },
  stepNumberActive: {
    color: 'white',
  },
  stepNumberInactive: {
    color: '#999',
  },
  stepLine: {
    width: 30,
    height: 2,
    marginHorizontal: 5,
  },
  stepLineActive: {
    backgroundColor: '#FF6B35',
  },
  stepLineInactive: {
    backgroundColor: '#e2e8f0',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  stepContent: {
    paddingBottom: 20,
  },
  stepTitle: {
    fontSize: 22,
    fontWeight: '700',
    color: '#2D3748',
    marginBottom: 10,
    textAlign: 'center',
  },
  stepDescription: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 30,
    lineHeight: 22,
  },
  input: {
    backgroundColor: 'white',
    borderWidth: 2,
    borderColor: '#e2e8f0',
    borderRadius: 12,
    paddingHorizontal: 15,
    paddingVertical: 12,
    fontSize: 16,
    marginBottom: 15,
    color: '#2D3748',
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  infoBox: {
    backgroundColor: '#E3F2FD',
    padding: 15,
    borderRadius: 10,
    marginTop: 10,
  },
  infoText: {
    fontSize: 14,
    color: '#1976D2',
    lineHeight: 20,
  },
  vehicleOptions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 20,
  },
  vehicleOption: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 15,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#e2e8f0',
    minWidth: 80,
  },
  vehicleOptionSelected: {
    borderColor: '#FF6B35',
    backgroundColor: '#FFF3E0',
  },
  vehicleIcon: {
    fontSize: 30,
    marginBottom: 10,
  },
  vehicleText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666',
  },
  vehicleTextSelected: {
    color: '#FF6B35',
  },
  documentsInfo: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 15,
    marginBottom: 20,
  },
  documentsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2D3748',
    marginBottom: 15,
  },
  documentItem: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
    lineHeight: 20,
  },
  noteBox: {
    backgroundColor: '#FFF3E0',
    padding: 15,
    borderRadius: 10,
    marginBottom: 20,
  },
  noteText: {
    fontSize: 14,
    color: '#F57C00',
    textAlign: 'center',
    lineHeight: 20,
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 20,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderWidth: 2,
    borderColor: '#e2e8f0',
    borderRadius: 6,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 2,
  },
  checkboxChecked: {
    backgroundColor: '#FF6B35',
    borderColor: '#FF6B35',
  },
  checkmark: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  checkboxText: {
    flex: 1,
    fontSize: 14,
    color: '#2D3748',
    lineHeight: 20,
  },
  navigationButtons: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: '#e2e8f0',
  },
  prevButton: {
    flex: 1,
    backgroundColor: '#f8f9fa',
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: 'center',
    marginRight: 10,
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  prevButtonText: {
    color: '#666',
    fontSize: 16,
    fontWeight: '600',
  },
  nextButton: {
    flex: 1,
    backgroundColor: '#FF6B35',
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: 'center',
    marginLeft: 10,
  },
  nextButtonFull: {
    marginLeft: 0,
  },
  nextButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '700',
  },
});
